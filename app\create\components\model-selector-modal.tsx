import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Model } from "@/types/model";
import { Loader, X } from "lucide-react";
import { useEffect, useRef } from "react";
import { ModelCard } from "./model-card";
import { ModelSearch } from "./model-search";
import { ModelTypeFilter } from "./model-type-filter";

interface ModelSelectorModalProps {
    isOpen: boolean;
    onClose: () => void;
    models: Model[];
    selectedModelId: string;
    onSelectModel: (id: string) => void;
    searchTerm: string;
    onSearchChange: (term: string) => void;
    isLoading: boolean;
    hasMore: boolean;
    onLoadMore: () => void;
    totalCount: number;

    // 过滤器相关
    filters?: {
        type?: string[];
        features?: string[];
        source?: string[];
    };
    onFilterChange?: (filterType: string, value: string, isActive: boolean) => void;
    onResetFilters?: () => void;

    // 模型类型筛选相关
    activeModelType?: string;
    onModelTypeChange?: (type: string) => void;
}

export function ModelSelectorModal({
    isOpen,
    onClose,
    models,
    selectedModelId,
    onSelectModel,
    searchTerm,
    onSearchChange,
    isLoading,
    hasMore,
    onLoadMore,
    totalCount,
    filters = {},
    onFilterChange,
    onResetFilters,
    activeModelType = '',
    onModelTypeChange
}: ModelSelectorModalProps) {
    const loaderRef = useRef<HTMLDivElement>(null);
    // 设置Intersection Observer来检测滚动到底部
    useEffect(() => {
        if (!isOpen) return;

        const observer = new IntersectionObserver(
            entries => {
                if (entries[0].isIntersecting && hasMore && !isLoading) {
                    onLoadMore();
                }
            },
            { threshold: 0.5 }
        );

        if (loaderRef.current) {
            observer.observe(loaderRef.current);
        }

        return () => {
            if (loaderRef.current) {
                observer.unobserve(loaderRef.current);
            }
        };
    }, [isOpen, hasMore, onLoadMore, isLoading]);

    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-[90vw] w-[90vw] h-[90vh] sm:max-w-[90vw] p-0 rounded-lg overflow-hidden">
                <div className="flex flex-col h-full">
                    {/* 模态框头部 */}
                    <div className="bg-card/20 p-4 border-b border-foreground/10">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-xl font-medium">Select model</h2>
                            <button
                                onClick={onClose}
                                className="p-2 rounded-full hover:bg-card/50"
                                aria-label="Close"
                                title="Close model selector"
                            >
                                <X className="h-5 w-5" />
                            </button>
                        </div>

                        {/* 增强版搜索组件 */}
                        <ModelSearch
                            searchTerm={searchTerm}
                            onSearchChange={onSearchChange}
                            totalCount={totalCount}
                            filteredCount={models.length}
                            isSearching={isLoading && !!searchTerm}
                            activeFilters={filters}
                            onFilterChange={onFilterChange}
                            onResetFilters={onResetFilters}
                        />

                        {/* 模型类型筛选组件 */}
                        {onModelTypeChange && (
                            <ModelTypeFilter
                                activeType={activeModelType}
                                onTypeChange={onModelTypeChange}
                                className="mt-4"
                            />
                        )}
                    </div>

                    {/* 模态框内容 - 模型网格 */}
                    <div className="flex-1 overflow-y-auto p-6">
                        <div className="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                            {models.map((model) => (
                                <ModelCard
                                    key={model.id}
                                    model={model}
                                    isSelected={selectedModelId === model.id}
                                    onClick={() => {
                                        onSelectModel(model.id);
                                        onClose();
                                    }}
                                />
                            ))}
                        </div>

                        {/* 底部加载更多指示器 */}
                        <div ref={loaderRef} className="flex justify-center my-8 h-10">
                            {isLoading && (
                                <div className="flex items-center">
                                    <Loader className="h-5 w-5 animate-spin mr-2 text-muted-foreground" />
                                    <span className="text-sm text-muted-foreground">Loading more models...</span>
                                </div>
                            )}
                            {!hasMore && models.length > 0 && (
                                <span className="text-sm text-muted-foreground">No more models to load</span>
                            )}
                            {models.length === 0 && !isLoading && (
                                <span className="text-sm text-muted-foreground">No models found matching your criteria</span>
                            )}
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
