import { IsOptional, IsString, IsInt, IsIn, Min } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * 标签过滤DTO
 */
export class TagFilterDto {
    /**
     * 每页显示数量
     */
    @IsOptional()
    @IsInt()
    @Min(1)
    @Transform(({ value }) => parseInt(value))
    limit?: number;

    /**
     * 偏移量
     */
    @IsOptional()
    @IsInt()
    @Min(0)
    @Transform(({ value }) => parseInt(value))
    offset?: number;

    /**
     * 排序字段
     */
    @IsOptional()
    @IsString()
    @IsIn(['id', 'name', 'usage_count', 'created_at'])
    sort_by?: string;

    /**
     * 排序方向
     */
    @IsOptional()
    @IsString()
    @IsIn(['asc', 'desc'])
    sort_direction?: string;
} 