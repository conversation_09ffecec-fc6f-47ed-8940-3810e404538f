# FLUX模型使用限制功能部署检查清单

## 部署前检查

### 后端代码检查
- [ ] `src/lego/lego.service.ts` - 添加了FLUX限制检查方法
- [ ] `src/lego/lego.controller.ts` - 添加了新的API端点
- [ ] 代码通过TypeScript编译检查
- [ ] 代码通过ESLint检查
- [ ] 单元测试通过（如果有）

### 前端代码检查
- [ ] `app/lego/api.ts` - 添加了新的API方法
- [ ] `app/lego/hooks.ts` - 添加了新的React Query hook
- [ ] `app/lego/components/control-panel.tsx` - 添加了UI限制检查
- [ ] 代码通过TypeScript编译检查
- [ ] 代码通过ESLint检查
- [ ] 组件渲染测试通过（如果有）

### 数据库检查
- [ ] 确认`pic_gen_tasks`表结构正确
- [ ] 确认`user_memberships`表结构正确
- [ ] 数据库连接正常
- [ ] 相关索引已创建（如果需要）

## 部署步骤

### 1. 后端部署
```bash
# 1. 备份当前版本
git tag backup-before-flux-limit-$(date +%Y%m%d-%H%M%S)

# 2. 部署新版本
git add .
git commit -m "feat: add FLUX model usage limit for non-members"
git push origin main

# 3. 重启后端服务
# (根据具体部署方式执行)
```

### 2. 前端部署
```bash
# 1. 构建前端
npm run build

# 2. 部署到Vercel/其他平台
# (根据具体部署方式执行)
```

## 部署后验证

### 1. 基础功能验证
- [ ] 后端服务正常启动
- [ ] 前端页面正常加载
- [ ] API端点可访问
- [ ] 数据库连接正常

### 2. FLUX限制功能验证
- [ ] 非会员用户可以查看FLUX使用限制信息
- [ ] 非会员用户第1次使用FLUX模型成功
- [ ] 非会员用户第2次使用FLUX模型成功
- [ ] 非会员用户第3次使用FLUX模型被拒绝
- [ ] 会员用户可以无限制使用FLUX模型
- [ ] UI正确显示限制状态

### 3. 回归测试
- [ ] UNO模型正常工作
- [ ] 其他现有功能未受影响
- [ ] 用户认证正常
- [ ] 会员系统正常

## 监控和日志

### 1. 关键指标监控
- [ ] API响应时间
- [ ] 错误率
- [ ] FLUX模型使用统计
- [ ] 用户转化率（非会员->会员）

### 2. 日志检查
- [ ] 应用日志无异常错误
- [ ] 数据库查询日志正常
- [ ] API访问日志正常

## 回滚计划

### 如果出现问题需要回滚：

1. **立即回滚步骤**：
```bash
# 回滚到上一个稳定版本
git revert <commit-hash>
git push origin main
```

2. **数据库回滚**（如果有数据库变更）：
```sql
-- 如果有数据库变更，执行相应的回滚脚本
-- 本次更新没有数据库结构变更，无需回滚
```

3. **前端回滚**：
```bash
# 重新部署上一个版本
git checkout <previous-commit>
npm run build
# 重新部署
```

## 用户通知

### 1. 功能说明
- [ ] 更新用户文档
- [ ] 发布功能公告（如果需要）
- [ ] 客服团队培训

### 2. 会员推广
- [ ] 准备会员升级推广材料
- [ ] 设置相关营销活动

## 风险评估

### 高风险项
- [ ] 数据库查询性能影响
- [ ] 用户体验负面影响
- [ ] 现有功能破坏

### 中风险项
- [ ] API响应时间增加
- [ ] 前端UI显示问题
- [ ] 边界情况处理

### 低风险项
- [ ] 日志量增加
- [ ] 缓存失效

## 成功标准

### 技术指标
- [ ] API响应时间 < 500ms
- [ ] 错误率 < 0.1%
- [ ] 系统可用性 > 99.9%

### 业务指标
- [ ] 非会员用户正确受限
- [ ] 会员用户体验无影响
- [ ] 用户投诉 < 5个/天

## 联系信息

### 紧急联系人
- 开发团队：[联系方式]
- 运维团队：[联系方式]
- 产品团队：[联系方式]

### 相关文档
- API文档：[链接]
- 用户手册：[链接]
- 故障处理手册：[链接]
