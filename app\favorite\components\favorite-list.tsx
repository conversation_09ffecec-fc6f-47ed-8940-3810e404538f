"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { FavoriteItemCard } from "./favorite-item-card"
import { favoriteApi, FavoriteTargetType, FavoritesResponse } from "@/lib/api/favorite"
import { useAuth } from "@/contexts/auth-context"
import { FavoriteSkeleton } from "./favorite-skeleton"
import { motion } from "framer-motion"
import { AlertCircle, Heart } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"

export function FavoriteList() {
    const { user } = useAuth()
    const [favorites, setFavorites] = useState<FavoritesResponse>({ posts: [], models: [] })
    const [activeTab, setActiveTab] = useState<string>("post")
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    // 使用ref来跟踪每个标签页是否已经加载过数据
    const loadedTabs = useRef<Record<string, boolean>>({
        post: false,
        model: false
    })

    // 记录正在请求中的promise
    const pendingRequests = useRef<Record<string, boolean>>({})

    // 加载收藏列表
    const loadFavorites = async (item_type: string) => {
        if (!user) return

        // 如果该标签页已有请求正在进行中，则不重复请求
        if (pendingRequests.current[item_type]) {
            return
        }

        setIsLoading(true)
        setError(null)

        try {
            // 标记该类型正在请求中
            pendingRequests.current[item_type] = true

            // 执行请求
            const data = await favoriteApi.getFavorites({ item_type })

            // 标记该标签页已加载过数据
            loadedTabs.current[item_type] = true

            // 更新状态
            if (item_type === 'post') {
                setFavorites(prev => ({ ...prev, posts: data.posts }))
            } else {
                setFavorites(prev => ({ ...prev, models: data.models }))
            }
        } catch (err) {
            console.error("加载收藏列表失败", err)
            setError("无法加载收藏列表，请稍后再试")
        } finally {
            // 标记请求已完成
            pendingRequests.current[item_type] = false
            setIsLoading(false)
        }
    }

    // 处理移除收藏
    const handleRemoveFavorite = async (itemId: string, itemType: FavoriteTargetType) => {
        const key = itemType === FavoriteTargetType.POST ? "post_id" : "model_id";
        try {
            await favoriteApi.removeFavorite({ [key]: itemId, itemType })

            // 更新本地状态
            setFavorites(prev => {
                if (itemType === FavoriteTargetType.POST) {
                    return {
                        ...prev,
                        posts: prev.posts.filter(post => post.postId !== itemId)
                    }
                } else {
                    return {
                        ...prev,
                        models: prev.models.filter(model => model.modelId !== itemId)
                    }
                }
            })
        } catch (err) {
            console.error("移除收藏失败", err)
            alert("无法移除收藏，请稍后再试")
        }
    }

    // 处理标签切换
    const handleTabChange = (value: string) => {
        setActiveTab(value)
        // 只有当该标签页未加载过数据时，才请求数据
        if (!loadedTabs.current[value]) {
            loadFavorites(value)
        }
    }

    // 初始加载
    useEffect(() => {
        if (user && !loadedTabs.current[activeTab]) {
            loadFavorites(activeTab)
        }
    }, [user])

    // 重试加载
    const handleRetry = () => {
        // 重置该标签的加载状态
        loadedTabs.current[activeTab] = false
        loadFavorites(activeTab)
    };

    // 渲染内容部分
    const renderContent = () => {
        if (isLoading) {
            return <FavoriteSkeleton type={activeTab === "post" ? "post" : "model"} />
        }

        if (error) {
            return (
                <Alert variant="destructive" className="mb-6">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>
                        {error}
                        <Button
                            variant="outline"
                            size="sm"
                            className="ml-4"
                            onClick={handleRetry}
                        >
                            Retry
                        </Button>
                    </AlertDescription>
                </Alert>
            )
        }

        return (
            <>
                <TabsContent value="post">
                    {favorites.posts.length === 0 ? (
                        <div className="flex h-60 flex-col items-center justify-center rounded-xl border border-dashed border-border bg-muted/50">
                            <Heart className="mb-4 h-12 w-12 text-muted-foreground" />
                            <p className="text-lg text-muted-foreground">You have no favorites yet</p>
                        </div>
                    ) : (
                        <motion.div
                            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.5 }}
                        >
                            {favorites.posts.map((post, index) => (
                                <motion.div
                                    key={post.favoriteId}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: index * 0.05 }}
                                >
                                    <FavoriteItemCard
                                        item={post}
                                        type={FavoriteTargetType.POST}
                                        onRemove={handleRemoveFavorite}
                                    />
                                </motion.div>
                            ))}
                        </motion.div>
                    )}
                </TabsContent>

                <TabsContent value="model">
                    {favorites.models.length === 0 ? (
                        <div className="flex h-60 flex-col items-center justify-center rounded-xl border border-dashed border-border bg-muted/50">
                            <Heart className="mb-4 h-12 w-12 text-muted-foreground" />
                            <p className="text-lg text-muted-foreground">You have no favorites yet</p>
                        </div>
                    ) : (
                        <motion.div
                            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.5 }}
                        >
                            {favorites.models.map((model, index) => (
                                <motion.div
                                    key={model.favoriteId}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: index * 0.05 }}
                                >
                                    <FavoriteItemCard
                                        item={model}
                                        type={FavoriteTargetType.MODEL}
                                        onRemove={handleRemoveFavorite}
                                    />
                                </motion.div>
                            ))}
                        </motion.div>
                    )}
                </TabsContent>
            </>
        )
    }

    return (
        <div className="container mx-auto py-8 max-w-6xl">
            <Tabs defaultValue="post" value={activeTab} onValueChange={handleTabChange}>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-8 gap-4">
                    <div>
                        <h1 className="text-3xl font-bold mb-1">My Favorites</h1>
                        <p className="text-sm text-muted-foreground">Here you can manage your favorite videos and models</p>
                    </div>
                    <TabsList className="grid w-[300px] grid-cols-2">
                        <TabsTrigger value="post">Video</TabsTrigger>
                        <TabsTrigger value="model">Model</TabsTrigger>
                    </TabsList>
                </div>

                {renderContent()}
            </Tabs>
        </div>
    )
} 