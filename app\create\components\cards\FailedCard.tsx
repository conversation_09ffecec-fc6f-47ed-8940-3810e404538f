"use client"

import { Spark<PERSON> } from "lucide-react";
import type { VideoTask } from "@/types/video-task";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { getStatusText } from "../../utils/status-utils";
import { CardInfoSection } from "./CardInfoSection";
import { ErrorDetails } from "./ErrorDetails";

interface FailedCardProps {
    video: VideoTask;
    onRemixClick?: (videoId: string) => void;
}

/**
 * FailedCard Component - 显示失败状态的视频任务
 * 不再查询状态更新
 */
export const FailedCard = ({
    video,
    onRemixClick
}: FailedCardProps) => {
    const status = video.status; // 直接使用视频任务的状态，不需要轮询

    // 检查是否是内容政策违规错误或运行时错误
    const errorType = video.error_log?.details?.detail?.[0]?.type;
    const isContentPolicyViolation = errorType === "content_policy_violation";
    const isValueErrorMissing = errorType === "value_error.missing";
    const shouldHideTryAgain = isContentPolicyViolation || isValueErrorMissing;

    return (
        <>
            {/* Video preview area */}
            <div className="relative w-full overflow-hidden bg-muted/60 cursor-pointer">
                {/* Status badge */}
                <div
                    className={cn(
                        "absolute top-2 right-2 z-10 px-2.5 py-1 rounded-full text-xs font-medium",
                        "transition-opacity duration-300",
                        status === "failed" ? "bg-red-500/20 text-red-500 border border-red-500/50" :
                            "bg-gray-500/20 text-gray-500 border border-gray-500/50"
                    )}
                >
                    <span className="inline-flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                            <circle cx="12" cy="12" r="10" />
                            <line x1="15" y1="9" x2="9" y2="15" />
                            <line x1="9" y1="9" x2="15" y2="15" />
                        </svg>
                        {getStatusText(status)}
                    </span>
                </div>

                {/* Failed status display */}
                <div className="aspect-video">
                    <Skeleton className="w-full h-full" />
                    {/* Display reference image if available */}
                    {video.input_params.refer_img_url && (
                        <div className="absolute inset-0 flex items-center justify-center">
                            <img
                                src={video.input_params.refer_img_url}
                                alt="Reference image"
                                className="max-h-full max-w-full object-contain opacity-40"
                            />
                        </div>
                    )}
                </div>
                <div className="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-b from-background/70 to-background/40 backdrop-blur-sm">
                    <div className="flex items-center justify-center w-12 h-12 bg-red-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-500">
                            <circle cx="12" cy="12" r="10" />
                            <line x1="15" y1="9" x2="9" y2="15" />
                            <line x1="9" y1="9" x2="15" y2="15" />
                        </svg>
                    </div>
                    <div className="mt-3 text-base font-medium text-red-500">
                        Generation Failed
                    </div>
                    <div className="mt-2 text-xs text-muted-foreground max-w-[80%] text-center">
                        There was a problem generating your video. Please try again with different settings.
                    </div>
                </div>
            </div>

            {/* Information area */}
            <CardInfoSection video={video}>
                {/* Error details */}
                <ErrorDetails video={video} className="mb-3" />

                {/* Action buttons row */}
                {!shouldHideTryAgain && (
                    <div className="flex items-center gap-2 h-11">
                        {/* Try Again button - 只在非特殊错误类型时显示 */}
                        <button
                            className={cn(
                                "flex-1 flex items-center justify-center gap-2 px-4 py-2.5",
                                "text-sm font-medium rounded-md transition-all",
                                "border border-black/5 dark:border-gray-800/30",
                                status === "failed"
                                    ? "bg-red-500/10 hover:bg-red-500/20 text-red-500"
                                    : "bg-gray-500/10 hover:bg-gray-500/20 text-gray-500"
                            )}
                            onClick={(e) => {
                                e.stopPropagation();
                                if (onRemixClick) onRemixClick(video.id);
                            }}
                            title="Try again with this prompt"
                        >
                            <Sparkles size={16} />
                            Try Again
                        </button>
                    </div>
                )}
            </CardInfoSection>
        </>
    );
};
