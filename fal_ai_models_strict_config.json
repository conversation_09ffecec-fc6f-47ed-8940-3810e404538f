{"models": {"fal-ai/kling-video/v1.6/pro/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Snowflakes fall as a car moves along the road."}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5", "enum": ["5", "10"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video frame", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "tail_image_url": {"type": "string", "required": false, "description": "URL of the image to be used for the end of the video", "format": "uri"}, "image_url": {"type": "string", "required": true, "example": "https://storage.googleapis.com/falserverless/kling/kling_input.jpeg", "format": "uri"}, "negative_prompt": {"type": "string", "required": false, "default": "blur, distort, and low quality"}, "cfg_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ", "default": 0.5, "minimum": 0, "maximum": 1}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pika/v2/turbo/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A young woman in a pale blue corset and denim, her vibrant blue bob framed against a dusky desert landscape, walks slowly, her gaze unwavering and enigmatic as the camera remains fixed on her deliberate pace.  The warm glow of a stucco house contrasts with the cool desert air, hinting at both refuge and isolation, while a blurred figure retreating inside adds a layer of unspoken narrative to her solitary journey."}, "duration": {"type": "integer", "required": false, "description": "The duration of the generated video in seconds", "default": 5}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "1:1", "4:5", "5:4", "3:2", "2:3"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["720p", "1080p"]}, "seed": {"type": "integer", "required": false, "description": "The seed for the random number generator"}, "negative_prompt": {"type": "string", "required": false, "description": "A negative prompt to guide the model", "default": ""}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/cogvideox-5b/video-to-video": {"category": "video-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The prompt to generate the video from.", "example": "An astronaut stands triumphantly at the peak of a towering mountain. Panorama of rugged peaks and valleys. Very futuristic vibe and animated aesthetic. Highlights of purple and golden colors in the scene. The sky is looks like an animated/cartoonish dream of galaxies, nebulae, stars, planets, moons, but the remainder of the scene is mostly realistic. "}, "video_url": {"type": "string", "required": true, "description": "The video to generate the video from.", "example": "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/diffusers/hiker.mp4"}, "use_rife": {"type": "boolean", "required": false, "description": "Use RIFE for video interpolation", "default": true}, "loras": {"type": "array", "required": false, "description": "\n            The LoRAs to use for the image generation. We currently support one lora.\n        ", "default": []}, "video_size": {"type": "string", "required": false, "description": "The size of the generated video.", "default": {"height": 480, "width": 720}}, "strength": {"type": "number", "required": false, "description": "The strength to use for Video to Video.  1.0 completely remakes the video while 0.0 preserves the original.", "default": 0.8, "minimum": 0.05, "maximum": 1}, "guidance_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt when looking for a related video to show you.\n        ", "default": 7, "minimum": 0, "maximum": 20}, "num_inference_steps": {"type": "integer", "required": false, "description": "The number of inference steps to perform.", "default": 50, "minimum": 1, "maximum": 50}, "export_fps": {"type": "integer", "required": false, "description": "The target FPS of the video", "default": 16, "minimum": 4, "maximum": 32}, "negative_prompt": {"type": "string", "required": false, "description": "The negative prompt to generate video from", "default": "", "example": "Distorted, discontinuous, Ugly, blurry, low resolution, motionless, static, disfigured, disconnected limbs, Ugly faces, incomplete arms"}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}}, "output": {"prompt": {"type": "string", "description": "The prompt used for generating the video."}, "timings": {"type": "object"}, "seed": {"type": "integer", "description": "\n            Seed of the generated video. It will be the same value of the one passed in the\n            input or the randomly generated that was used in case none was passed.\n        "}, "video": {"type": "object", "description": "The URL to the generated video"}}}, "fal-ai/wan-flf2v": {"category": "image-to-video", "input": {"shift": {"type": "number", "required": false, "description": "Shift parameter for video generation.", "default": 5, "minimum": 1, "maximum": 10}, "prompt": {"type": "string", "required": true, "description": "The text prompt to guide video generation.", "example": "A tabby cat is confidely strolling toward the camera, when it spins and with a flash of magic reveals itself to be a cat-dragon hybrid with glistening amber scales."}, "acceleration": {"type": "string", "required": false, "description": "Acceleration level to use. The more acceleration, the faster the generation, but with lower quality. The recommended value is 'regular'.", "default": "regular", "enum": ["none", "regular"], "example": "regular"}, "frames_per_second": {"type": "integer", "required": false, "description": "Frames per second of the generated video. Must be between 5 to 24.", "default": 16, "minimum": 5, "maximum": 24}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": false, "example": true}, "start_image_url": {"type": "string", "required": true, "description": "URL of the starting image. If the input image does not match the chosen aspect ratio, it is resized and center cropped.", "example": "https://storage.googleapis.com/falserverless/web-examples/wan_flf/first_frame.png"}, "end_image_url": {"type": "string", "required": true, "description": "URL of the ending image. If the input image does not match the chosen aspect ratio, it is resized and center cropped.", "example": "https://storage.googleapis.com/falserverless/web-examples/wan_flf/last_frame.png"}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt for video generation.", "default": "bright colors, overexposed, static, blurred details, subtitles, style, artwork, painting, picture, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, malformed limbs, fused fingers, still picture, cluttered background, three legs, many people in the background, walking backwards", "example": "bright colors, overexposed, static, blurred details, subtitles, style, artwork, painting, picture, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, malformed limbs, fused fingers, still picture, cluttered background, three legs, many people in the background, walking backwards"}, "num_frames": {"type": "integer", "required": false, "description": "Number of frames to generate. Must be between 81 to 100 (inclusive). If the number of frames is greater than 81, the video will be generated with 1.25x more billing units.", "default": 81, "minimum": 81, "maximum": 100}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p or 720p). 480p is 0.5 billing units, and 720p is 1 billing unit.", "default": "720p", "enum": ["480p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video. If 'auto', the aspect ratio will be determined automatically based on the input image.", "default": "auto", "enum": ["auto", "16:9", "9:16", "1:1"]}, "enable_prompt_expansion": {"type": "boolean", "required": false, "description": "Whether to enable prompt expansion.", "default": false, "example": true}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps for sampling. Higher values give better quality but take longer.", "default": 30, "minimum": 2, "maximum": 40}, "guide_scale": {"type": "number", "required": false, "description": "Classifier-free guidance scale. Higher values give better adherence to the prompt but may decrease quality.", "default": 5, "minimum": 1, "maximum": 10}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/wan/v2.1/1.3b/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt for generating the video", "example": "Two anthropomorphic cats in comfy boxing gear and bright gloves fight intensely on a spotlighted stage."}, "aspect_ratio": {"type": "string", "required": false, "description": "Video resolution aspect ratio", "default": "16:9", "enum": ["16:9", "9:16"]}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": false, "example": true}, "guidance_scale": {"type": "number", "required": false, "description": "Guidance scale (1-20)", "default": 5, "minimum": 0, "maximum": 20}, "shift": {"type": "number", "required": false, "description": "Noise schedule shift parameter. Affects temporal dynamics.", "default": 5, "minimum": 0, "maximum": 10}, "enable_prompt_expansion": {"type": "boolean", "required": false, "description": "Whether to enable prompt expansion.", "default": false}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}, "num_inference_steps": {"type": "integer", "required": false, "description": "Diffusion sampling steps (1-40)", "default": 30, "minimum": 2, "maximum": 40}, "negative_prompt": {"type": "string", "required": false, "description": "\n            The negative prompt to use. Use it to address details that you don't want\n            in the image. This could be colors, objects, scenery and even the small\n            details (e.g. moustache, blurry, low resolution).\n        ", "default": "", "example": ""}, "sampler": {"type": "string", "required": false, "description": "The sampler to use for generation.", "default": "unipc", "enum": ["unipc", "dpm++"]}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/hunyuan-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The prompt to generate the video from.", "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage. She wears a black leather jacket, a long red dress, and black boots, and carries a black purse."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the video to generate.", "default": "16:9", "enum": ["16:9", "9:16"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the video to generate.", "default": "720p", "enum": ["480p", "580p", "720p"]}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": false, "example": true}, "num_inference_steps": {"type": "integer", "required": false, "description": "The number of inference steps to run. Lower gets faster results, higher gets better results.", "default": 30, "minimum": 2, "maximum": 30}, "seed": {"type": "integer", "required": false, "description": "The seed to use for generating the video."}, "num_frames": {"type": "string", "required": false, "description": "The number of frames to generate.", "default": 129, "enum": ["129", "85"]}, "pro_mode": {"type": "boolean", "required": false, "description": "By default, generations are done with 35 steps. Pro mode does 55 steps which results in higher quality videos but will take more time and cost 2x more billing units.", "default": false}}, "output": {"seed": {"type": "integer", "description": "The seed used for generating the video."}, "video": {"type": "object"}}}, "fal-ai/framepack": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt for video generation (max 500 characters).", "example": "A mesmerising video of a deep sea jellyfish moving through an inky-black ocean. The jellyfish glows softly with an amber bioluminescence. The overall scene is lifelike."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the video to generate.", "default": "16:9", "enum": ["16:9", "9:16"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the video to generate. 720p generations cost 1.5x more than 480p generations.", "default": "480p", "enum": ["720p", "480p"]}, "num_frames": {"type": "integer", "required": false, "description": "The number of frames to generate.", "default": 180, "minimum": 30, "maximum": 900}, "image_url": {"type": "string", "required": true, "description": "URL of the image input.", "example": "https://storage.googleapis.com/falserverless/framepack/framepack.jpg"}, "guidance_scale": {"type": "number", "required": false, "description": "Guidance scale for the generation.", "default": 10, "minimum": 0, "maximum": 32}, "seed": {"type": "integer", "required": false, "description": "The seed to use for generating the video."}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": false, "example": true}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt for video generation.", "default": "", "example": "Ugly, blurry distorted, bad quality"}, "cfg_scale": {"type": "number", "required": false, "description": "Classifier-Free Guidance scale for the generation.", "default": 1, "minimum": 0, "maximum": 7}}, "output": {"seed": {"type": "integer", "description": "The seed used for generating the video."}, "video": {"type": "object"}}}, "fal-ai/magi-distilled/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The text prompt to guide video generation.", "example": "Close-up shot: the old sea captain stares intently, pipe in mouth, wisps of smoke curling around his weathered face. The camera begins to pull back out over the ocean. Finally, the camera sinks below the waves deeply, fading to dark blue and finally to black."}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p or 720p). 480p is 0.5 billing units, and 720p is 1 billing unit.", "default": "720p", "enum": ["480p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video. If 'auto', the aspect ratio will be determined automatically based on the input image.", "default": "auto", "enum": ["auto", "16:9", "9:16", "1:1"]}, "image_url": {"type": "string", "required": true, "description": "URL of the input image to represent the first frame of the video. If the input image does not match the chosen aspect ratio, it is resized and center cropped.", "example": "https://raw.githubusercontent.com/painebenjamin/pointy-seeds/refs/heads/main/captain-start.jpg"}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": true, "example": true}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps for sampling. Higher values give better quality but take longer.", "default": 16, "enum": [4, 8, 16, 32]}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}, "num_frames": {"type": "integer", "required": false, "description": "Number of frames to generate. Must be between 96 and 192 (inclusive). Each additional 24 frames beyond 96 incurs an additional billing unit.", "default": 96, "minimum": 96, "maximum": 192}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/pika/v2.1/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A pink heart exploding."}, "duration": {"type": "integer", "required": false, "description": "The duration of the generated video in seconds", "default": 5}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["720p", "1080p"]}, "seed": {"type": "integer", "required": false, "description": "The seed for the random number generator"}, "negative_prompt": {"type": "string", "required": false, "description": "A negative prompt to guide the model", "default": ""}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://v3.fal.media/files/elephant/dJjBQXNHRbGJn4aUv4-g9_hearth.jpg"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/wan-i2v": {"category": "image-to-video", "input": {"shift": {"type": "number", "required": false, "description": "Shift parameter for video generation.", "default": 5, "minimum": 1, "maximum": 10}, "prompt": {"type": "string", "required": true, "description": "The text prompt to guide video generation.", "example": "Cars racing in slow motion"}, "acceleration": {"type": "string", "required": false, "description": "Acceleration level to use. The more acceleration, the faster the generation, but with lower quality. The recommended value is 'regular'.", "default": "regular", "enum": ["none", "regular"], "example": "regular"}, "frames_per_second": {"type": "integer", "required": false, "description": "Frames per second of the generated video. Must be between 5 to 24.", "default": 16, "minimum": 5, "maximum": 24}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": false, "example": true}, "num_frames": {"type": "integer", "required": false, "description": "Number of frames to generate. Must be between 81 to 100 (inclusive). If the number of frames is greater than 81, the video will be generated with 1.25x more billing units.", "default": 81, "minimum": 81, "maximum": 100}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt for video generation.", "default": "bright colors, overexposed, static, blurred details, subtitles, style, artwork, painting, picture, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, malformed limbs, fused fingers, still picture, cluttered background, three legs, many people in the background, walking backwards", "example": "bright colors, overexposed, static, blurred details, subtitles, style, artwork, painting, picture, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, malformed limbs, fused fingers, still picture, cluttered background, three legs, many people in the background, walking backwards"}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p or 720p). 480p is 0.5 billing units, and 720p is 1 billing unit.", "default": "720p", "enum": ["480p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video. If 'auto', the aspect ratio will be determined automatically based on the input image.", "default": "auto", "enum": ["auto", "16:9", "9:16", "1:1"]}, "image_url": {"type": "string", "required": true, "description": "URL of the input image. If the input image does not match the chosen aspect ratio, it is resized and center cropped.", "example": "https://storage.googleapis.com/falserverless/gallery/car_720p.png"}, "enable_prompt_expansion": {"type": "boolean", "required": false, "description": "Whether to enable prompt expansion.", "default": false, "example": false}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}, "guide_scale": {"type": "number", "required": false, "description": "Classifier-free guidance scale. Higher values give better adherence to the prompt but may decrease quality.", "default": 5, "minimum": 1, "maximum": 10}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps for sampling. Higher values give better quality but take longer.", "default": 30, "minimum": 2, "maximum": 40}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/minimax/video-01-director/image-to-video": {"category": "image-to-video", "input": {"prompt_optimizer": {"type": "boolean", "required": false, "description": "Whether to use the model's prompt optimizer", "default": true}, "prompt": {"type": "string", "required": true, "description": "Text prompt for video generation. Camera movement instructions can be added using square brackets (e.g. [<PERSON> left] or [Zoom in]). You can use up to 3 combined movements per prompt. Supported movements: Truck left/right, Pan left/right, Push in/Pull out, Pedestal up/down, Tilt up/down, Zoom in/out, Shake, Tracking shot, Static shot. For example: [Truck left, Pan right, Zoom in]. For a more detailed guide, refer https://sixth-switch-2ac.notion.site/T2V-01-Director-Model-Tutorial-with-camera-movement-1886c20a98eb80f395b8e05291ad8645", "example": "[Push in, Follow]A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage. She wears a black leather jacket, a long red dress, and black boots, and carries a black purse.[<PERSON> left] The street opens into a small plaza where street vendors sell steaming food under colorful awnings."}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://fal.media/files/elephant/8kkhB12hEZI2kkbU8pZPA_test.jpeg"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/kling-video/v1.6/pro/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage. She wears a black leather jacket, a long red dress, and black boots, and carries a black purse."}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5", "enum": ["5", "10"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video frame", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "negative_prompt": {"type": "string", "required": false, "default": "blur, distort, and low quality"}, "cfg_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ", "default": 0.5, "minimum": 0, "maximum": 1}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/magi-distilled": {"category": "video-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The text prompt to guide video generation.", "example": "Close-up shot: the old sea captain stares intently, pipe in mouth, wisps of smoke curling around his weathered face. The camera begins a slow clockwise orbit, pulling back. Finally, the camera rises high above, revealing the entire wooden sailing ship cutting through the waves, the captain unmoved, gazing toward the distant horizon."}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p or 720p). 480p is 0.5 billing units, and 720p is 1 billing unit.", "default": "720p", "enum": ["480p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video. If 'auto', the aspect ratio will be determined automatically based on the input image.", "default": "auto", "enum": ["auto", "16:9", "9:16", "1:1"]}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": true, "example": true}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps for sampling. Higher values give better quality but take longer.", "default": 16, "enum": [4, 8, 16, 32]}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}, "num_frames": {"type": "integer", "required": false, "description": "Number of frames to generate. Must be between 96 and 192 (inclusive). Each additional 24 frames beyond 96 incurs an additional billing unit.", "default": 96, "minimum": 96, "maximum": 192}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/luma-dream-machine": {"category": "video-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A teddy bear in sunglasses playing electric guitar and dancing"}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "4:3", "3:4", "21:9", "9:21"]}, "loop": {"type": "boolean", "required": false, "description": "Whether the video should loop (end of video is blended with the beginning)", "default": false}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/wan-t2v": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The text prompt to guide video generation.", "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage. She wears a black leather jacket, a long red dress, and black boots, and carries a black purse."}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video (16:9 or 9:16).", "default": "16:9", "enum": ["9:16", "16:9"]}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p, 580p, or 720p).", "default": "720p", "enum": ["480p", "580p", "720p"]}, "num_frames": {"type": "integer", "required": false, "description": "Number of frames to generate. Must be between 81 to 100 (inclusive).", "default": 81, "minimum": 81, "maximum": 100}, "turbo_mode": {"type": "boolean", "required": false, "description": "If true, the video will be generated faster with no noticeable degradation in the visual quality.", "default": false}, "frames_per_second": {"type": "integer", "required": false, "description": "Frames per second of the generated video. Must be between 5 to 24.", "default": 16, "minimum": 5, "maximum": 24}, "enable_prompt_expansion": {"type": "boolean", "required": false, "description": "Whether to enable prompt expansion.", "default": false, "example": true}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps for sampling. Higher values give better quality but take longer.", "default": 30, "minimum": 2, "maximum": 40}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt for video generation.", "default": "bright colors, overexposed, static, blurred details, subtitles, style, artwork, painting, picture, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, malformed limbs, fused fingers, still picture, cluttered background, three legs, many people in the background, walking backwards", "example": "bright colors, overexposed, static, blurred details, subtitles, style, artwork, painting, picture, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, malformed limbs, fused fingers, still picture, cluttered background, three legs, many people in the background, walking backwards"}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": false, "example": true}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/pixverse/v4.5/text-to-video/fast": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Epic low-cut camera capture of a girl clad in ultraviolet threads, <PERSON> art style depiction, luminous diamond skin glistening under a vast moon's radiance, embodied in a superhuman flight among mystical ruins, symbolizing a deity's ritual ascent, hyper-detailed"}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pika/v2.2/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "a woman looking into camera slowly smiling"}, "duration": {"type": "integer", "required": false, "description": "The duration of the generated video in seconds", "default": 5}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["720p", "1080p"]}, "seed": {"type": "integer", "required": false, "description": "The seed for the random number generator"}, "negative_prompt": {"type": "string", "required": false, "description": "A negative prompt to guide the model", "default": ""}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://storage.googleapis.com/falserverless/web-examples/pika/pika%202.2/pika_input.png"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/kling-video/v2/master/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A slow-motion drone shot descending from above a maze of neon-lit Tokyo alleyways at night during heavy rainfall. The camera gradually focuses on a lone figure in a luminescent white raincoat standing perfectly still amid the bustling crowd, all carrying black umbrellas. As the camera continues its downward journey, we see the raindrops creating rippling patterns on puddles that reflect the kaleidoscope of colors from the surrounding signs, creating a mirror world beneath the city."}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5", "enum": ["5", "10"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video frame", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "negative_prompt": {"type": "string", "required": false, "default": "blur, distort, and low quality"}, "cfg_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ", "default": 0.5, "minimum": 0, "maximum": 1}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/ltx-video-v095/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt to guide generation", "example": "The astronaut gets up and walks away"}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p or 720p).", "default": "720p", "enum": ["480p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video (16:9 or 9:16).", "default": "16:9", "enum": ["9:16", "16:9"]}, "expand_prompt": {"type": "boolean", "required": false, "description": "Whether to expand the prompt using the model's own capabilities.", "default": true}, "image_url": {"type": "string", "required": true, "description": "Image URL for Image-to-Video task", "example": "https://h2.inkwai.com/bs2/upload-ylab-stunt/se/ai_portal_queue_mmu_image_upscale_aiweb/3214b798-e1b4-4b00-b7af-72b5b0417420_raw_image_0.jpg"}, "seed": {"type": "integer", "required": false, "description": "Random seed for generation"}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps", "default": 40, "minimum": 2, "maximum": 50}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt for generation", "default": "worst quality, inconsistent motion, blurry, jittery, distorted"}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/pika/v2/turbo/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A pink heart exploding."}, "duration": {"type": "integer", "required": false, "description": "The duration of the generated video in seconds", "default": 5}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["720p", "1080p"]}, "seed": {"type": "integer", "required": false, "description": "The seed for the random number generator"}, "negative_prompt": {"type": "string", "required": false, "description": "A negative prompt to guide the model", "default": ""}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://v3.fal.media/files/elephant/dJjBQXNHRbGJn4aUv4-g9_hearth.jpg"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/vidu/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt for video generation, max 1500 characters", "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage. She wears a black leather jacket, a long red dress, and black boots, and carries a black purse."}, "seed": {"type": "integer", "required": false, "description": "Random seed for generation"}, "movement_amplitude": {"type": "string", "required": false, "description": "The movement amplitude of objects in the frame", "default": "auto", "enum": ["auto", "small", "medium", "large"]}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://storage.googleapis.com/falserverless/web-examples/vidu/stylish_woman.webp"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pixverse/v3.5/image-to-video/fast": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage."}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://fal.media/files/elephant/8kkhB12hEZI2kkbU8pZPA_test.jpeg"}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/luma-dream-machine/ray-2/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "4:3", "3:4", "21:9", "9:21"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video (720p costs 2x more, 1080p costs 4x more)", "default": "540p", "enum": ["540p", "720p", "1080p"]}, "loop": {"type": "boolean", "required": false, "description": "Whether the video should loop (end of video is blended with the beginning)", "default": false}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video", "default": "5s", "enum": ["5s"]}, "image_url": {"type": "string", "required": false, "description": "Initial image to start the video from. Can be used together with end_image_url.", "example": "https://fal.media/files/elephant/8kkhB12hEZI2kkbU8pZPA_test.jpeg"}, "end_image_url": {"type": "string", "required": false, "description": "Final image to end the video with. Can be used together with image_url."}}, "output": {"video": {"type": "object", "description": "URL of the generated video"}}}, "fal-ai/topaz/upscale/video": {"category": "video-to-video", "input": {"video_url": {"type": "string", "required": true, "description": "URL of the video to upscale", "example": "https://v3.fal.media/files/kangaroo/y5-1YTGpun17eSeggZMzX_video-1733468228.mp4"}, "upscale_factor": {"type": "number", "required": false, "description": "Factor to upscale the video by (e.g. 2.0 doubles width and height)", "default": 2, "minimum": 1, "maximum": 4}, "target_fps": {"type": "integer", "required": false, "description": "Target FPS for frame interpolation. If set, frame interpolation will be enabled.", "minimum": 16, "maximum": 60}}, "output": {"video": {"type": "object", "description": "The upscaled video file"}}}, "fal-ai/wan-pro/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The prompt to generate the video", "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage."}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "Whether to enable the safety checker", "default": true}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}, "image_url": {"type": "string", "required": true, "description": "The URL of the image to generate the video from", "example": "https://fal.media/files/elephant/8kkhB12hEZI2kkbU8pZPA_test.jpeg"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/kling-video/v1.6/standard/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage. She wears a black leather jacket, a long red dress, and black boots, and carries a black purse."}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5", "enum": ["5", "10"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video frame", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "negative_prompt": {"type": "string", "required": false, "default": "blur, distort, and low quality"}, "cfg_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ", "default": 0.5, "minimum": 0, "maximum": 1}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/veo2": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The text prompt describing the video you want to generate", "example": "The camera floats gently through rows of pastel-painted wooden beehives, buzzing honeybees gliding in and out of frame. The motion settles on the refined farmer standing at the center, his pristine white beekeeping suit gleaming in the golden afternoon light. He lifts a jar of honey, tilting it slightly to catch the light. Behind him, tall sunflowers sway rhythmically in the breeze, their petals glowing in the warm sunlight. The camera tilts upward to reveal a retro farmhouse with mint-green shutters, its walls dappled with shadows from swaying trees. Shot with a 35mm lens on Kodak Portra 400 film, the golden light creates rich textures on the farmer's gloves, marmalade jar, and weathered wood of the beehives."}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5s", "enum": ["5s", "6s", "7s", "8s"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16"]}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/ltx-video-v095": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt to guide generation", "example": "A cute cat walking on a sidewalk"}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p or 720p).", "default": "720p", "enum": ["480p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video (16:9 or 9:16).", "default": "16:9", "enum": ["9:16", "16:9"]}, "expand_prompt": {"type": "boolean", "required": false, "description": "Whether to expand the prompt using the model's own capabilities.", "default": true}, "seed": {"type": "integer", "required": false, "description": "Random seed for generation"}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps", "default": 40, "minimum": 2, "maximum": 50}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt for generation", "default": "worst quality, inconsistent motion, blurry, jittery, distorted"}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/pika/v2.2/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Sunlight streams down on a woman with flowing auburn hair as she runs effortlessly along a tree-lined street, her joyous expression reflecting the freedom of the moment; the simple, steady camerawork emphasizes her grace and the beauty of the everyday."}, "duration": {"type": "integer", "required": false, "description": "The duration of the generated video in seconds", "default": 5}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "1:1", "4:5", "5:4", "3:2", "2:3"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["720p", "1080p"]}, "seed": {"type": "integer", "required": false, "description": "The seed for the random number generator"}, "negative_prompt": {"type": "string", "required": false, "description": "A negative prompt to guide the model", "default": ""}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/ltx-video-v095/extend": {"category": "video-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt to guide generation", "example": "Woman walking on a street in Tokyo"}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p or 720p).", "default": "720p", "enum": ["480p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video (16:9 or 9:16).", "default": "16:9", "enum": ["9:16", "16:9"]}, "expand_prompt": {"type": "boolean", "required": false, "description": "Whether to expand the prompt using the model's own capabilities.", "default": true}, "seed": {"type": "integer", "required": false, "description": "Random seed for generation"}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps", "default": 40, "minimum": 2, "maximum": 50}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt for generation", "default": "worst quality, inconsistent motion, blurry, jittery, distorted"}, "video": {"type": "object", "required": true, "description": "Video to be extended.", "example": {"video_url": "https://storage.googleapis.com/falserverless/web-examples/wan/t2v.mp4", "start_frame_num": 24}}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/veo2/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The text prompt describing how the image should be animated", "example": "A lego chef cooking eggs"}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5s", "enum": ["5s", "6s", "7s", "8s"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "auto", "enum": ["auto", "auto_prefer_portrait", "16:9", "9:16"]}, "image_url": {"type": "string", "required": true, "description": "URL of the input image to animate. Should be 720p or higher resolution.", "example": "https://fal.media/files/elephant/6fq8JDSjb1osE_c3J_F2H.png", "format": "uri"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/minimax/video-01-live/image-to-video": {"category": "image-to-video", "input": {"prompt_optimizer": {"type": "boolean", "required": false, "description": "Whether to use the model's prompt optimizer", "default": true}, "prompt": {"type": "string", "required": true, "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage."}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://fal.media/files/elephant/8kkhB12hEZI2kkbU8pZPA_test.jpeg"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/luma-dream-machine/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Low-angle shot of a majestic tiger prowling through a snowy landscape, leaving paw prints on the white blanket"}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "4:3", "3:4", "21:9", "9:21"]}, "loop": {"type": "boolean", "required": false, "description": "Whether the video should loop (end of video is blended with the beginning)", "default": false}, "end_image_url": {"type": "string", "required": false, "description": "An image to blend the end of the video with"}, "image_url": {"type": "string", "required": true, "example": "https://fal.media/files/koala/1oLY4Bjp4XdGBBTSsrGlE.jpeg"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/luma-dream-machine/ray-2-flash/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "4:3", "3:4", "21:9", "9:21"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video (720p costs 2x more, 1080p costs 4x more)", "default": "540p", "enum": ["540p", "720p", "1080p"]}, "loop": {"type": "boolean", "required": false, "description": "Whether the video should loop (end of video is blended with the beginning)", "default": false}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video", "default": "5s", "enum": ["5s"]}, "image_url": {"type": "string", "required": false, "description": "Initial image to start the video from. Can be used together with end_image_url.", "example": "https://fal.media/files/elephant/8kkhB12hEZI2kkbU8pZPA_test.jpeg"}, "end_image_url": {"type": "string", "required": false, "description": "Final image to end the video with. Can be used together with image_url."}}, "output": {"video": {"type": "object", "description": "URL of the generated video"}}}, "fal-ai/minimax/video-01-live": {"category": "text-to-video", "input": {"prompt_optimizer": {"type": "boolean", "required": false, "description": "Whether to use the model's prompt optimizer", "default": true}, "prompt": {"type": "string", "required": true, "example": "A rugged middle-aged man with wheat-colored skin and a full beard streaked with gray stands in the harsh sunlight of a desert outpost. His curly hair is windswept, and sweat drips down the bridge of his slightly crooked nose. His faded utility jacket and weathered boots are caked in dust, while his sharp, watchful eyes scan the horizon."}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pixverse/v4/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A woman warrior with her hammer walking with his glacier wolf."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p", "1080p"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds. 8s videos cost double. 1080p videos are limited to 5 seconds", "default": "5", "enum": ["5", "8"]}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://v3.fal.media/files/zebra/qL93Je8ezvzQgDOEzTjKF_KhGKZTEebZcDw6T5rwQPK_output.png"}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/hunyuan-video-image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The prompt to generate the video from.", "example": "Two muscular cats boxing in a boxing ring."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the video to generate.", "default": "16:9", "enum": ["16:9", "9:16"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the video to generate.", "default": "720p", "enum": ["720p"]}, "image_url": {"type": "string", "required": true, "description": "URL of the image input.", "example": "https://storage.googleapis.com/falserverless/example_inputs/hunyuan_i2v.jpg"}, "seed": {"type": "integer", "required": false, "description": "The seed to use for generating the video."}, "num_frames": {"type": "string", "required": false, "description": "The number of frames to generate.", "default": 129, "enum": ["129"]}, "i2v_stability": {"type": "boolean", "required": false, "description": "Turning on I2V Stability reduces hallucination but also reduces motion.", "default": false}}, "output": {"seed": {"type": "integer", "description": "The seed used for generating the video."}, "video": {"type": "object"}}}, "fal-ai/kling-video/v2.1/master/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Warm, earthy tones bathe the scene as the potter's hands, rough and calloused, coax a shapeless lump of clay into a vessel of elegant curves, the slow, deliberate movements highlighted by the subtle shifting light; the clay’s cool, damp texture contrasts sharply with the warmth of the potter's touch, creating a captivating interplay between material and maker."}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5", "enum": ["5", "10"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video frame", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "negative_prompt": {"type": "string", "required": false, "default": "blur, distort, and low quality"}, "cfg_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ", "default": 0.5, "minimum": 0, "maximum": 1}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pixverse/v4.5/image-to-video/fast": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A woman warrior with her hammer walking with his glacier wolf."}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://v3.fal.media/files/zebra/qL93Je8ezvzQgDOEzTjKF_KhGKZTEebZcDw6T5rwQPK_output.png"}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/vidu/q1/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt for video generation, max 1500 characters", "example": "In an ultra-realistic fashion photography style featuring light blue and pale amber tones, an astronaut in a spacesuit walks through the fog. The background consists of enchanting white and golden lights, creating a minimalist still life and an impressive panoramic scene."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the output video", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "style": {"type": "string", "required": false, "description": "The style of output video", "default": "general", "enum": ["general", "anime"]}, "seed": {"type": "integer", "required": false, "description": "Seed for the random number generator"}, "movement_amplitude": {"type": "string", "required": false, "description": "The movement amplitude of objects in the frame", "default": "auto", "enum": ["auto", "small", "medium", "large"]}}, "output": {"video": {"type": "object", "description": "The generated video using the Q1 model"}}}, "fal-ai/wan-vace": {"category": "video-to-video", "input": {"shift": {"type": "number", "required": false, "description": "Shift parameter for video generation.", "default": 5, "minimum": 1, "maximum": 10}, "video_url": {"type": "string", "required": false, "description": "URL to the source video file. If provided, the model will use this video as a reference.", "example": "https://storage.googleapis.com/falserverless/vace/src_video.mp4"}, "prompt": {"type": "string", "required": true, "description": "The text prompt to guide video generation.", "example": "The video shows a man riding a horse on a vast grassland. He has long lavender hair and wears a traditional dress of a white top and black pants. The animation style makes him look like he is doing some kind of outdoor activity or performing. The background is a spectacular mountain range and cloud sky, giving a sense of tranquility and vastness. The entire video is shot from a fixed angle, focusing on the rider and his horse."}, "mask_image_url": {"type": "string", "required": false, "description": "URL to the guiding mask file. If provided, the model will use this mask as a reference to create masked video. If provided mask video url will be ignored."}, "task": {"type": "string", "required": false, "description": "Task type for the model.", "default": "depth", "enum": ["depth", "inpainting"]}, "frames_per_second": {"type": "integer", "required": false, "description": "Frames per second of the generated video. Must be between 5 to 24.", "default": 16, "minimum": 5, "maximum": 24}, "ref_image_urls": {"type": "array", "required": false, "description": "Urls to source reference image. If provided, the model will use this image as reference.", "example": ["https://storage.googleapis.com/falserverless/vace/src_ref_image_1.png"]}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": false, "example": true}, "num_frames": {"type": "integer", "required": false, "description": "Number of frames to generate. Must be between 81 to 100 (inclusive). Works only with only reference images as input if source video or mask video is provided output len would be same as source up to 241 frames", "default": 81, "minimum": 81, "maximum": 240}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt for video generation.", "default": "bright colors, overexposed, static, blurred details, subtitles, style, artwork, painting, picture, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, malformed limbs, fused fingers, still picture, cluttered background, three legs, many people in the background, walking backwards", "example": "bright colors, overexposed, static, blurred details, subtitles, style, artwork, painting, picture, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, malformed limbs, fused fingers, still picture, cluttered background, three legs, many people in the background, walking backwards"}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p,580p, or 720p).", "default": "720p", "enum": ["480p", "580p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video (16:9 or 9:16).", "default": "16:9", "enum": ["auto", "9:16", "16:9"]}, "mask_video_url": {"type": "string", "required": false, "description": "URL to the source mask file. If provided, the model will use this mask as a reference.", "example": "https://storage.googleapis.com/falserverless/vace/src_mask.mp4"}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps for sampling. Higher values give better quality but take longer.", "default": 30, "minimum": 2, "maximum": 40}, "preprocess": {"type": "boolean", "required": false, "description": "Whether to preprocess the input video.", "default": false}, "enable_prompt_expansion": {"type": "boolean", "required": false, "description": "Whether to enable prompt expansion.", "default": false, "example": true}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/ltx-video-v095/multiconditioning": {"category": "video-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt to guide generation", "example": "\n            A vibrant, abstract composition featuring a person with outstretched arms, rendered in a kaleidoscope of colors against a deep, dark background. The figure is composed of intricate, swirling patterns reminiscent of a mosaic, with hues of orange, yellow, blue, and green that evoke the style of artists such as <PERSON><PERSON><PERSON> or <PERSON>. \n\nThe camera zooms into the face striking portrait of a man, reimagined through the lens of old-school video-game graphics. The subject's face is rendered in a kaleidoscope of colors, with bold blues and reds set against a vibrant yellow backdrop. His dark hair is pulled back, framing his profile in a dramatic pose\n        "}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p or 720p).", "default": "720p", "enum": ["480p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video (16:9 or 9:16).", "default": "16:9", "enum": ["9:16", "16:9"]}, "expand_prompt": {"type": "boolean", "required": false, "description": "Whether to expand the prompt using the model's own capabilities.", "default": true}, "images": {"type": "array", "required": false, "description": "URL of images to use as conditioning", "default": [], "example": [{"start_frame_num": 0, "image_url": "https://storage.googleapis.com/falserverless/model_tests/ltx/NswO1P8sCLzrh1WefqQFK_9a6bdbfa54b944c9a770338159a113fd.jpg"}, {"start_frame_num": 120, "image_url": "https://storage.googleapis.com/falserverless/model_tests/ltx/YAPOGvmS2tM_Krdp7q6-d_267c97e017c34f679844a4477dfcec38.jpg"}]}, "videos": {"type": "array", "required": false, "description": "Videos to use as conditioning", "default": []}, "seed": {"type": "integer", "required": false, "description": "Random seed for generation"}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps", "default": 40, "minimum": 2, "maximum": 50}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt for generation", "default": "worst quality, inconsistent motion, blurry, jittery, distorted"}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}, "fal-ai/cogvideox-5b/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The prompt to generate the video from.", "example": "A low angle shot of a man walking down a street, illuminated by the neon signs of the bars around him"}, "use_rife": {"type": "boolean", "required": false, "description": "Use RIFE for video interpolation", "default": true}, "image_url": {"type": "string", "required": true, "description": "The URL to the image to generate the video from.", "example": "https://d3phaj0sisr2ct.cloudfront.net/research/eugene.jpg"}, "loras": {"type": "array", "required": false, "description": "\n            The LoRAs to use for the image generation. We currently support one lora.\n        ", "default": []}, "video_size": {"type": "string", "required": false, "description": "The size of the generated video.", "default": {"height": 480, "width": 720}}, "guidance_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt when looking for a related video to show you.\n        ", "default": 7, "minimum": 0, "maximum": 20}, "num_inference_steps": {"type": "integer", "required": false, "description": "The number of inference steps to perform.", "default": 50, "minimum": 1, "maximum": 50}, "export_fps": {"type": "integer", "required": false, "description": "The target FPS of the video", "default": 16, "minimum": 4, "maximum": 32}, "negative_prompt": {"type": "string", "required": false, "description": "The negative prompt to generate video from", "default": "", "example": "Distorted, discontinuous, Ugly, blurry, low resolution, motionless, static, disfigured, disconnected limbs, Ugly faces, incomplete arms"}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}}, "output": {"prompt": {"type": "string", "description": "The prompt used for generating the video."}, "timings": {"type": "object"}, "seed": {"type": "integer", "description": "\n            Seed of the generated video. It will be the same value of the one passed in the\n            input or the randomly generated that was used in case none was passed.\n        "}, "video": {"type": "object", "description": "The URL to the generated video"}}}, "fal-ai/vidu/q1/start-end-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt for video generation, max 1500 characters", "example": "Dragon lands on a rock"}, "start_image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://v3.fal.media/files/zebra/sgsdKvPigPhJ1S7Hl5bWc_first_frame_q1.png"}, "end_image_url": {"type": "string", "required": true, "description": "URL of the image to use as the last frame", "example": "https://v3.fal.media/files/kangaroo/CASBu_OmOnZ8IafirarFL_last_frame_q1.png"}, "movement_amplitude": {"type": "string", "required": false, "description": "The movement amplitude of objects in the frame", "default": "auto", "enum": ["auto", "small", "medium", "large"]}, "seed": {"type": "integer", "required": false, "description": "Seed for the random number generator"}}, "output": {"video": {"type": "object", "description": "The generated transition video between start and end frames using the Q1 model"}}}, "fal-ai/pixverse/v4.5/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A woman warrior with her hammer walking with his glacier wolf."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p", "1080p"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds. 8s videos cost double. 1080p videos are limited to 5 seconds", "default": "5", "enum": ["5", "8"]}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://v3.fal.media/files/zebra/qL93Je8ezvzQgDOEzTjKF_KhGKZTEebZcDw6T5rwQPK_output.png"}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/kling-video/v2.1/standard/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "As the sun dips below the horizon, painting the sky in fiery hues of orange and purple, powerful waves relentlessly crash against jagged, dark rocks, their white foam a stark contrast to the deepening twilight; the textured surface of the rocks, wet and glistening, reflects the vibrant colors, creating a mesmerizing spectacle of nature's raw power and breathtaking beauty"}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5", "enum": ["5", "10"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video frame", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "cfg_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ", "default": 0.5, "minimum": 0, "maximum": 1}, "negative_prompt": {"type": "string", "required": false, "default": "blur, distort, and low quality"}, "image_url": {"type": "string", "required": true, "description": "URL of the image to be used for the video", "example": "https://v3.fal.media/files/panda/W-_J46zuJDQnUhqkKm9Iv_image.webp", "format": "uri"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pixverse/v4/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Epic low-cut camera capture of a girl clad in ultraviolet threads, <PERSON> art style depiction, luminous diamond skin glistening under a vast moon's radiance, embodied in a superhuman flight among mystical ruins, symbolizing a deity's ritual ascent, hyper-detailed"}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p", "1080p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds. 8s videos cost double. 1080p videos are limited to 5 seconds", "default": "5", "enum": ["5", "8"]}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/minimax/video-01-director": {"category": "image-to-video", "input": {"prompt_optimizer": {"type": "boolean", "required": false, "description": "Whether to use the model's prompt optimizer", "default": true}, "prompt": {"type": "string", "required": true, "description": "Text prompt for video generation. Camera movement instructions can be added using square brackets (e.g. [<PERSON> left] or [Zoom in]). You can use up to 3 combined movements per prompt. Supported movements: Truck left/right, Pan left/right, Push in/Pull out, Pedestal up/down, Tilt up/down, Zoom in/out, Shake, Tracking shot, Static shot. For example: [Truck left, Pan right, Zoom in]. For a more detailed guide, refer https://sixth-switch-2ac.notion.site/T2V-01-Director-Model-Tutorial-with-camera-movement-1886c20a98eb80f395b8e05291ad8645", "example": "[Push in]Close up of a tense woman looks to the left, startled by a sound, in a darkened kitchen, Pots and pans hang ominously, the window in the kitchen is open and the wind softly blows the pans and creates an ominous mood. [Shake]the woman's shock turns to fear. Black-and-white film noir shot dimly lit, 1950s-style, with dramatic, high-contrast shadows. The overall atmosphere is reminiscent of <PERSON>'s suspenseful storytelling, evoking a looming sense of dread with stark chiaroscuro lighting and a slight film-grain texture."}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pixverse/v4/image-to-video/fast": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A woman warrior with her hammer walking with his glacier wolf."}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://v3.fal.media/files/zebra/qL93Je8ezvzQgDOEzTjKF_KhGKZTEebZcDw6T5rwQPK_output.png"}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/vidu/q1/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "Text prompt for video generation, max 1500 characters", "example": "The astronaut waved and the camera moved up."}, "seed": {"type": "integer", "required": false, "description": "Seed for the random number generator"}, "movement_amplitude": {"type": "string", "required": false, "description": "The movement amplitude of objects in the frame", "default": "auto", "enum": ["auto", "small", "medium", "large"]}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://prod-ss-images.s3.cn-northwest-1.amazonaws.com.cn/vidu-maas/template/image2video.png"}}, "output": {"video": {"type": "object", "description": "The generated video using the Q1 model from a single image"}}}, "fal-ai/pixverse/v4.5/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Epic low-cut camera capture of a girl clad in ultraviolet threads, <PERSON> art style depiction, luminous diamond skin glistening under a vast moon's radiance, embodied in a superhuman flight among mystical ruins, symbolizing a deity's ritual ascent, hyper-detailed"}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p", "1080p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds. 8s videos cost double. 1080p videos are limited to 5 seconds", "default": "5", "enum": ["5", "8"]}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/ltx-video/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The prompt to generate the video from.", "example": "A lone astronaut in a white spacesuit with gold-tinted visor drifts weightlessly through a sleek, cylindrical corridor of a spaceship. Their movements are slow and graceful as they gently push off the metallic walls with their gloved hands, rotating slightly as they float from right to left across the frame. The corridor features brushed aluminum panels with blue LED strips running along the ceiling, casting a cool glow on the astronaut's suit. Various cables, pipes, and control panels line the walls. The camera follows the astronaut's movement in a handheld style, slightly swaying and adjusting focus, maintaining a medium shot that captures both the astronaut and the corridor's depth. Small particles of dust catch the light as they float in the zero-gravity environment. The scene appears cinematic, with lens flares occasionally reflecting off the metallic surfaces and the astronaut's visor."}, "guidance_scale": {"type": "number", "required": false, "description": "The guidance scale to use.", "default": 3, "maximum": 10}, "seed": {"type": "integer", "required": false, "description": "The seed to use for random number generation."}, "num_inference_steps": {"type": "integer", "required": false, "description": "The number of inference steps to take.", "default": 30, "minimum": 1, "maximum": 50}, "negative_prompt": {"type": "string", "required": false, "description": "The negative prompt to generate the video from.", "default": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"}, "image_url": {"type": "string", "required": true, "description": "The URL of the image to generate the video from.", "example": "https://fal.media/files/kangaroo/4OePu2ifG7SKxTM__TQrQ_72929fec9fb74790bb8c8b760450c9b9.jpg"}}, "output": {"seed": {"type": "integer", "description": "The seed used for random number generation."}, "video": {"type": "object", "description": "The generated video."}}}, "fal-ai/ltx-video": {"category": "video-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The prompt to generate the video from.", "example": "A man stands waist-deep in a crystal-clear mountain pool, his back turned to a massive, thundering waterfall that cascades down jagged cliffs behind him. He wears a dark blue swimming shorts and his muscular back glistens with water droplets. The camera moves in a dynamic circular motion around him, starting from his right side and sweeping left, maintaining a slightly low angle that emphasizes the towering height of the waterfall. As the camera moves, the man slowly turns his head to follow its movement, his expression one of awe as he gazes up at the natural wonder. The waterfall creates a misty atmosphere, with sunlight filtering through the spray to create rainbow refractions. The water churns and ripples around him, reflecting the dramatic landscape. The handheld camera movement adds a subtle shake that enhances the raw, untamed energy of the scene. The lighting is natural and bright, with the sun positioned behind the waterfall, creating a backlit effect that silhouettes the falling water and illuminates the mist."}, "guidance_scale": {"type": "number", "required": false, "description": "The guidance scale to use.", "default": 3, "maximum": 10}, "seed": {"type": "integer", "required": false, "description": "The seed to use for random number generation."}, "num_inference_steps": {"type": "integer", "required": false, "description": "The number of inference steps to take.", "default": 30, "minimum": 1, "maximum": 50}, "negative_prompt": {"type": "string", "required": false, "description": "The negative prompt to generate the video from.", "default": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly"}}, "output": {"seed": {"type": "integer", "description": "The seed used for random number generation."}, "video": {"type": "object", "description": "The generated video."}}}, "fal-ai/kling-video/v2/master/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "slow-motion sequence captures the catastrophic implosion of a skyscraper, dust and debris billowing outwards in a chaotic ballet of destruction, while a haunting, orchestral score underscores the sheer power and finality of the event."}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5", "enum": ["5", "10"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video frame", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "cfg_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ", "default": 0.5, "minimum": 0, "maximum": 1}, "negative_prompt": {"type": "string", "required": false, "default": "blur, distort, and low quality"}, "image_url": {"type": "string", "required": true, "description": "URL of the image to be used for the video", "example": "https://v3.fal.media/files/elephant/rkH-9qoXtXu3rAYTsx9V5_image.webp", "format": "uri"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pixverse/v3.5/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p", "1080p"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds. 8s videos cost double. 1080p videos are limited to 5 seconds", "default": "5", "enum": ["5", "8"]}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://fal.media/files/elephant/8kkhB12hEZI2kkbU8pZPA_test.jpeg"}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/luma-dream-machine/ray-2": {"category": "video-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A herd of wild horses galloping across a dusty desert plain under a blazing midday sun, their manes flying in the wind; filmed in a wide tracking shot with dynamic motion, warm natural lighting, and an epic."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "4:3", "3:4", "21:9", "9:21"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video (720p costs 2x more, 1080p costs 4x more)", "default": "540p", "enum": ["540p", "720p", "1080p"]}, "loop": {"type": "boolean", "required": false, "description": "Whether the video should loop (end of video is blended with the beginning)", "default": false}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video (9s costs 2x more)", "default": "5s", "enum": ["5s", "9s"]}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pika/v2.1/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A young woman in a pale blue corset and denim, her vibrant blue bob framed against a dusky desert landscape, walks slowly, her gaze unwavering and enigmatic as the camera remains fixed on her deliberate pace.  The warm glow of a stucco house contrasts with the cool desert air, hinting at both refuge and isolation, while a blurred figure retreating inside adds a layer of unspoken narrative to her solitary journey."}, "duration": {"type": "integer", "required": false, "description": "The duration of the generated video in seconds", "default": 5}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "1:1", "4:5", "5:4", "3:2", "2:3"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["720p", "1080p"]}, "seed": {"type": "integer", "required": false, "description": "The seed for the random number generator"}, "negative_prompt": {"type": "string", "required": false, "description": "A negative prompt to guide the model", "default": ""}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/veo3": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The text prompt describing the video you want to generate", "example": "A casual street interview on a busy New York City sidewalk in the afternoon. The interviewer holds a plain, unbranded microphone and asks: Have you seen Google's new Veo3 model It is a super good model. Person replies: Yeah I saw it, it's already available on fal. It's crazy good."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "8s", "enum": ["8s"]}, "generate_audio": {"type": "boolean", "required": false, "description": "Whether to generate audio for the video. If false, %33 less credits will be used.", "default": true}, "seed": {"type": "integer", "required": false, "description": "A seed to use for the video generation"}, "negative_prompt": {"type": "string", "required": false, "description": "A negative prompt to guide the video generation"}, "enhance_prompt": {"type": "boolean", "required": false, "description": "Whether to enhance the video generation", "default": true}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pixverse/v4/text-to-video/fast": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Epic low-cut camera capture of a girl clad in ultraviolet threads, <PERSON> art style depiction, luminous diamond skin glistening under a vast moon's radiance, embodied in a superhuman flight among mystical ruins, symbolizing a deity's ritual ascent, hyper-detailed"}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/kling-video/v1.6/standard/image-to-video": {"category": "image-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Snowflakes fall as a car moves forward along the road."}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds", "default": "5", "enum": ["5", "10"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video frame", "default": "16:9", "enum": ["16:9", "9:16", "1:1"]}, "cfg_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt.\n        ", "default": 0.5, "minimum": 0, "maximum": 1}, "negative_prompt": {"type": "string", "required": false, "default": "blur, distort, and low quality"}, "image_url": {"type": "string", "required": true, "example": "https://storage.googleapis.com/falserverless/kling/kling_input.jpeg", "format": "uri"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/minimax/video-01/image-to-video": {"category": "image-to-video", "input": {"prompt_optimizer": {"type": "boolean", "required": false, "description": "Whether to use the model's prompt optimizer", "default": true}, "prompt": {"type": "string", "required": true, "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage."}, "image_url": {"type": "string", "required": true, "description": "URL of the image to use as the first frame", "example": "https://fal.media/files/elephant/8kkhB12hEZI2kkbU8pZPA_test.jpeg"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/minimax/video-01": {"category": "image-to-video", "input": {"prompt_optimizer": {"type": "boolean", "required": false, "description": "Whether to use the model's prompt optimizer", "default": true}, "prompt": {"type": "string", "required": true, "example": "A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage. She wears a black leather jacket, a long red dress, and black boots, and carries a black purse."}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/wan-pro/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The prompt to generate the video", "example": "A lone astronaut in a detailed NASA spacesuit performs an exuberant dance on the lunar surface, arms outstretched in joyful abandon against the stark moonscape. The Earth hangs dramatically in the black sky, appearing to streak past due to the motion of the dance, creating a sense of dynamic movement. The scene captures extreme contrasts between the brilliant white of the spacesuit reflecting harsh sunlight and the deep shadows of the lunar craters. Every detail is rendered with photorealistic precision: the texture of the regolith disturbed by the astronaut's boots, the reflections on the helmet visor."}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "Whether to enable the safety checker", "default": true}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/cogvideox-5b": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The prompt to generate the video from.", "example": "A garden comes to life as a kaleidoscope of butterflies flutters amidst the blossoms, their delicate wings casting shadows on the petals below. In the background, a grand fountain cascades water with a gentle splendor, its rhythmic sound providing a soothing backdrop. Beneath the cool shade of a mature tree, a solitary wooden chair invites solitude and reflection, its smooth surface worn by the touch of countless visitors seeking a moment of tranquility in nature's embrace."}, "use_rife": {"type": "boolean", "required": false, "description": "Use RIFE for video interpolation", "default": true}, "loras": {"type": "array", "required": false, "description": "\n            The LoRAs to use for the image generation. We currently support one lora.\n        ", "default": []}, "video_size": {"type": "string", "required": false, "description": "The size of the generated video.", "default": {"height": 480, "width": 720}}, "guidance_scale": {"type": "number", "required": false, "description": "\n            The CFG (Classifier Free Guidance) scale is a measure of how close you want\n            the model to stick to your prompt when looking for a related video to show you.\n        ", "default": 7, "minimum": 0, "maximum": 20}, "num_inference_steps": {"type": "integer", "required": false, "description": "The number of inference steps to perform.", "default": 50, "minimum": 1, "maximum": 50}, "export_fps": {"type": "integer", "required": false, "description": "The target FPS of the video", "default": 16, "minimum": 4, "maximum": 32}, "negative_prompt": {"type": "string", "required": false, "description": "The negative prompt to generate video from", "default": "", "example": "Distorted, discontinuous, Ugly, blurry, low resolution, motionless, static, disfigured, disconnected limbs, Ugly faces, incomplete arms"}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}}, "output": {"prompt": {"type": "string", "description": "The prompt used for generating the video."}, "timings": {"type": "object"}, "seed": {"type": "integer", "description": "\n            Seed of the generated video. It will be the same value of the one passed in the\n            input or the randomly generated that was used in case none was passed.\n        "}, "video": {"type": "object", "description": "The URL to the generated video"}}}, "fal-ai/pixverse/v3.5/text-to-video": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Epic low-cut camera capture of a girl clad in ultraviolet threads, <PERSON> art style depiction, luminous diamond skin glistening under a vast moon's radiance, embodied in a superhuman flight among mystical ruins, symbolizing a deity's ritual ascent, hyper-detailed"}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p", "1080p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video in seconds. 8s videos cost double. 1080p videos are limited to 5 seconds", "default": "5", "enum": ["5", "8"]}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/pixverse/v3.5/text-to-video/fast": {"category": "text-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "Epic low-cut camera capture of a girl clad in ultraviolet threads, <PERSON> art style depiction, luminous diamond skin glistening under a vast moon's radiance, embodied in a superhuman flight among mystical ruins, symbolizing a deity's ritual ascent, hyper-detailed"}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video", "default": "720p", "enum": ["360p", "540p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "4:3", "1:1", "3:4", "9:16"]}, "style": {"type": "string", "required": false, "description": "The style of the generated video", "enum": ["anime", "3d_animation", "clay", "comic", "cyberpunk"]}, "seed": {"type": "integer", "required": false, "description": "\n            The same seed and the same prompt given to the same version of the model\n            will output the same video every time.\n        "}, "negative_prompt": {"type": "string", "required": false, "description": "Negative prompt to be used for the generation", "default": "", "example": "blurry, low quality, low resolution, pixelated, noisy, grainy, out of focus, poorly lit, poorly exposed, poorly composed, poorly framed, poorly cropped, poorly color corrected, poorly color graded"}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/luma-dream-machine/ray-2-flash": {"category": "video-to-video", "input": {"prompt": {"type": "string", "required": true, "example": "A herd of wild horses galloping across a dusty desert plain under a blazing midday sun, their manes flying in the wind; filmed in a wide tracking shot with dynamic motion, warm natural lighting, and an epic."}, "aspect_ratio": {"type": "string", "required": false, "description": "The aspect ratio of the generated video", "default": "16:9", "enum": ["16:9", "9:16", "4:3", "3:4", "21:9", "9:21"]}, "resolution": {"type": "string", "required": false, "description": "The resolution of the generated video (720p costs 2x more, 1080p costs 4x more)", "default": "540p", "enum": ["540p", "720p", "1080p"]}, "loop": {"type": "boolean", "required": false, "description": "Whether the video should loop (end of video is blended with the beginning)", "default": false}, "duration": {"type": "string", "required": false, "description": "The duration of the generated video (9s costs 2x more)", "default": "5s", "enum": ["5s", "9s"]}}, "output": {"video": {"type": "object", "description": "The generated video"}}}, "fal-ai/magi-distilled/extend-video": {"category": "video-to-video", "input": {"prompt": {"type": "string", "required": true, "description": "The text prompt to guide video generation.", "example": ""}, "resolution": {"type": "string", "required": false, "description": "Resolution of the generated video (480p or 720p). 480p is 0.5 billing units, and 720p is 1 billing unit.", "default": "720p", "enum": ["480p", "720p"]}, "aspect_ratio": {"type": "string", "required": false, "description": "Aspect ratio of the generated video. If 'auto', the aspect ratio will be determined automatically based on the input image.", "default": "auto", "enum": ["auto", "16:9", "9:16", "1:1"]}, "video_url": {"type": "string", "required": true, "description": "URL of the input video to represent the beginning of the video. If the input video does not match the chosen aspect ratio, it is resized and center cropped.", "example": "https://v3.fal.media/files/rabbit/lTH9PY_LQG0FjueBxMfDN_0395dec3-0c4a-4c25-8399-ebb198b73a30.mp4"}, "start_frame": {"type": "integer", "required": false, "description": "The frame to begin the generation from, with the remaining frames will be treated as the prefix video. The final video will contain the frames up until this number unchanged, followed by the generated frames. The default start frame is 32 frames before the end of the video, which gives optimal results.", "minimum": 0}, "enable_safety_checker": {"type": "boolean", "required": false, "description": "If set to true, the safety checker will be enabled.", "default": true, "example": true}, "num_inference_steps": {"type": "integer", "required": false, "description": "Number of inference steps for sampling. Higher values give better quality but take longer.", "default": 16, "enum": [4, 8, 16, 32]}, "seed": {"type": "integer", "required": false, "description": "Random seed for reproducibility. If None, a random seed is chosen."}, "num_frames": {"type": "integer", "required": false, "description": "Number of frames to generate. Must be between 96 and 192 (inclusive). Each additional 24 frames beyond 96 incurs an additional billing unit.", "default": 96, "minimum": 96, "maximum": 192}}, "output": {"seed": {"type": "integer", "description": "The seed used for generation."}, "video": {"type": "object", "description": "The generated video file."}}}}, "categories": {"image-to-video": ["fal-ai/kling-video/v1.6/pro/image-to-video", "fal-ai/wan-flf2v", "fal-ai/framepack", "fal-ai/magi-distilled/image-to-video", "fal-ai/pika/v2.1/image-to-video", "fal-ai/wan-i2v", "fal-ai/minimax/video-01-director/image-to-video", "fal-ai/pika/v2.2/image-to-video", "fal-ai/ltx-video-v095/image-to-video", "fal-ai/pika/v2/turbo/image-to-video", "fal-ai/vidu/image-to-video", "fal-ai/pixverse/v3.5/image-to-video/fast", "fal-ai/luma-dream-machine/ray-2/image-to-video", "fal-ai/wan-pro/image-to-video", "fal-ai/veo2", "fal-ai/veo2/image-to-video", "fal-ai/minimax/video-01-live/image-to-video", "fal-ai/luma-dream-machine/image-to-video", "fal-ai/luma-dream-machine/ray-2-flash/image-to-video", "fal-ai/pixverse/v4/image-to-video", "fal-ai/hunyuan-video-image-to-video", "fal-ai/pixverse/v4.5/image-to-video/fast", "fal-ai/cogvideox-5b/image-to-video", "fal-ai/vidu/q1/start-end-to-video", "fal-ai/pixverse/v4.5/image-to-video", "fal-ai/kling-video/v2.1/standard/image-to-video", "fal-ai/minimax/video-01-director", "fal-ai/pixverse/v4/image-to-video/fast", "fal-ai/vidu/q1/image-to-video", "fal-ai/ltx-video/image-to-video", "fal-ai/kling-video/v2/master/image-to-video", "fal-ai/pixverse/v3.5/image-to-video", "fal-ai/kling-video/v1.6/standard/image-to-video", "fal-ai/minimax/video-01/image-to-video", "fal-ai/minimax/video-01"], "text-to-video": ["fal-ai/pika/v2/turbo/text-to-video", "fal-ai/wan/v2.1/1.3b/text-to-video", "fal-ai/hunyuan-video", "fal-ai/kling-video/v1.6/pro/text-to-video", "fal-ai/wan-t2v", "fal-ai/pixverse/v4.5/text-to-video/fast", "fal-ai/kling-video/v2/master/text-to-video", "fal-ai/kling-video/v1.6/standard/text-to-video", "fal-ai/ltx-video-v095", "fal-ai/pika/v2.2/text-to-video", "fal-ai/minimax/video-01-live", "fal-ai/kling-video/v2.1/master/text-to-video", "fal-ai/vidu/q1/text-to-video", "fal-ai/pixverse/v4/text-to-video", "fal-ai/pixverse/v4.5/text-to-video", "fal-ai/pika/v2.1/text-to-video", "fal-ai/veo3", "fal-ai/pixverse/v4/text-to-video/fast", "fal-ai/wan-pro/text-to-video", "fal-ai/cogvideox-5b", "fal-ai/pixverse/v3.5/text-to-video", "fal-ai/pixverse/v3.5/text-to-video/fast"], "video-to-video": ["fal-ai/cogvideox-5b/video-to-video", "fal-ai/magi-distilled", "fal-ai/luma-dream-machine", "fal-ai/topaz/upscale/video", "fal-ai/ltx-video-v095/extend", "fal-ai/wan-vace", "fal-ai/ltx-video-v095/multiconditioning", "fal-ai/ltx-video", "fal-ai/luma-dream-machine/ray-2", "fal-ai/luma-dream-machine/ray-2-flash", "fal-ai/magi-distilled/extend-video"]}, "last_updated": "2025-06-11T04:46:29.113Z"}