import { useState, useRef, useEffect } from 'react';
import Uppy from '@uppy/core';
import AwsS3 from '@uppy/aws-s3';
import { UploadMetadata, UploadState } from '@/types/upload';
import { MAX_UPLOAD_FILE_SIZE_MB } from '@/constants/upload';
/**
 * 视频上传Hook - 使用Cloudflare R2存储视频文件
 */
export const useVideoUpload = () => {
    const [uploadState, setUploadState] = useState<UploadState>({
        videoFile: null,
        videoPreviewUrl: null,
        uploadProgress: 0,
        isUploading: false,
        uploadComplete: false,
        uploadError: null,
    });

    const fileInputRef = useRef<HTMLInputElement>(null);
    const uppyRef = useRef<Uppy | null>(null);

    // 初始化 Uppy 实例
    useEffect(() => {
        // 创建 Uppy 实例
        uppyRef.current = new Uppy({
            id: 'videoUploader',
            autoProceed: false, // 不自动上传
            restrictions: {
                maxFileSize: MAX_UPLOAD_FILE_SIZE_MB * 1024 * 1024, // 500MB
                maxNumberOfFiles: 1,
                allowedFileTypes: ['video/*']
            }
        });

        // 添加 AWS S3 插件 - 必须使用类型断言解决类型错误
        // @ts-ignore - 由于@uppy/aws-s3类型声明问题，临时忽略类型检查
        uppyRef.current.use(AwsS3, {
            limit: 1, // 同时只上传一个文件
            getUploadParameters: async (file) => {
                try {
                    // 向后端API请求预签名URL (App Router路径)
                    const response = await fetch('/api/upload/presign', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            filename: file.name,
                            fileType: file.type,
                            fileSize: file.size,
                        }),
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        // 特殊处理401错误
                        if (response.status === 401) {
                            console.error('用户未登录或会话已过期');

                            // 触发全局错误提示
                            const authError = errorData.message || 'Please login first';
                            setUploadState(prev => ({
                                ...prev,
                                uploadError: authError,
                                isUploading: false
                            }));

                            // 如果项目中有全局状态管理或事件系统，可以在这里触发
                            // 例如：dispatch({ type: 'AUTH_ERROR', payload: authError });

                            // 也可以在这里触发重定向到登录页面
                            // window.location.href = '/auth/signin';

                            throw new Error(authError);
                        }
                        throw new Error(errorData.message || `上传失败: ${response.statusText}`);
                    }

                    // 获取后端返回的上传参数
                    const responseData = await response.json();
                    // 检查是否成功并获取presignedData
                    const data = responseData.data || responseData;

                    return {
                        method: 'PUT', // Cloudflare R2使用PUT方法
                        url: data.url,
                        fields: data.fields || {},
                        headers: data.headers || {},
                        // 保存返回的公共URL，供成功后使用
                        meta: {
                            publicUrl: data.publicUrl,
                            key: data.key,
                        }
                    };
                } catch (error) {
                    console.error('获取上传参数失败:', error);
                    throw new Error('获取上传参数失败');
                }
            }
        });

        // 监听上传进度
        uppyRef.current.on('upload-progress', (file, progress) => {
            const bytesTotal = progress.bytesTotal || 0;
            const progressValue = Math.floor((progress.bytesUploaded / bytesTotal) * 100);
            setUploadState(prev => ({ ...prev, uploadProgress: progressValue }));
        });

        // 监听上传成功
        uppyRef.current.on('upload-success', (file, response) => {
            if (!file) return;

            const meta = file.meta || {};
            const publicUrl = meta.publicUrl || '';

            setUploadState(prev => ({
                ...prev,
                uploadComplete: true,
                isUploading: false
            }));

            console.log('上传成功:', {
                url: publicUrl,
                response,
            });
        });

        // 监听上传错误
        uppyRef.current.on('upload-error', (file, error, response) => {
            setUploadState(prev => ({
                ...prev,
                uploadError: error.message || '上传失败，请重试',
                isUploading: false
            }));
            console.error('上传错误:', error, response);
        });

        // 组件卸载时清理 Uppy 实例
        return () => {
            if (uppyRef.current) {
                uppyRef.current.destroy();
            }
        };
    }, []);

    // 处理文件选择
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const videoUrl = URL.createObjectURL(file);
            setUploadState(prev => ({
                ...prev,
                videoFile: file,
                videoPreviewUrl: videoUrl,
                uploadComplete: false,
                uploadProgress: 0,
                uploadError: null
            }));
        }
    };

    // 移除视频
    const handleRemoveVideo = () => {
        if (uploadState.videoPreviewUrl) {
            URL.revokeObjectURL(uploadState.videoPreviewUrl);
        }

        setUploadState({
            videoFile: null,
            videoPreviewUrl: null,
            uploadProgress: 0,
            isUploading: false,
            uploadComplete: false,
            uploadError: null
        });

        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }

        // 清除 Uppy 中的文件
        if (uppyRef.current) {
            uppyRef.current.cancelAll();
        }
    };

    // 触发文件选择
    const triggerFileInput = () => {
        fileInputRef.current?.click();
    };

    // 上传视频
    const uploadVideo = async (metadata: UploadMetadata) => {
        if (!uploadState.videoFile || !uppyRef.current) return;

        setUploadState(prev => ({ ...prev, isUploading: true, uploadError: null }));

        // 添加文件到 Uppy
        try {
            // 先清除之前的文件
            uppyRef.current.cancelAll();

            // 添加元数据
            const fileWithMeta = {
                name: uploadState.videoFile.name,
                type: uploadState.videoFile.type,
                data: uploadState.videoFile,
                meta: {
                    ...metadata,
                    userId: 'current-user-id', // 可以从session或auth获取
                    contentType: uploadState.videoFile.type,
                    timestamp: Date.now().toString(),
                }
            };

            uppyRef.current.addFile(fileWithMeta);

            // 开始上传
            await uppyRef.current.upload();
        } catch (error) {
            console.error('添加文件失败:', error);
            setUploadState(prev => ({
                ...prev,
                uploadError: '添加文件失败，请重试',
                isUploading: false
            }));
        }
    };

    return {
        uploadState,
        fileInputRef,
        handleFileChange,
        handleRemoveVideo,
        triggerFileInput,
        uploadVideo
    };
}; 