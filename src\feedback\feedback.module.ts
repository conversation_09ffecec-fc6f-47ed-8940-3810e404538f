import { Module } from '@nestjs/common';
import { FeedbackController } from './feedback.controller';
import { FeedbackService } from './feedback.service';
import { CustomLogger } from '../common/services/logger.service';
import { LoggerModule } from '../common/services/logger.module';

@Module({
    imports: [LoggerModule],
    controllers: [FeedbackController],
    providers: [FeedbackService, CustomLogger],
    exports: [FeedbackService],
})
export class FeedbackModule {}
