import { Injectable, Inject, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { SUPABASE_CLIENT } from '../providers/supabase.provider';
import { SupabaseClient } from '@supabase/supabase-js';
import { EmailValidatorService } from './email-validator.service';

@Injectable()
export class AuthService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly emailValidatorService: EmailValidatorService,
    ) {}

    /**
     * 使用邮箱和密码注册
     */
    async signUpWithEmail(email: string, password: string) {
        // 验证邮箱是否为可疑的机器人邮箱
        const emailValidation = this.emailValidatorService.validateEmail(email);
        if (!emailValidation.valid) {
            throw new BadRequestException(emailValidation.message || 'Invalid email address');
        }

        const { data, error } = await this.supabase.auth.signUp({
            email,
            password,
        });

        if (error) {
            throw new UnauthorizedException(error.message);
        }

        return data;
    }

    /**
     * 使用邮箱和密码登录
     */
    async signInWithEmail(email: string, password: string) {
        // 验证邮箱是否为可疑的机器人邮箱
        const emailValidation = this.emailValidatorService.validateEmail(email);
        if (!emailValidation.valid) {
            throw new BadRequestException(emailValidation.message || 'Invalid email address');
        }

        const { data, error } = await this.supabase.auth.signInWithPassword({
            email,
            password,
        });

        if (error) {
            throw new UnauthorizedException(error.message);
        }

        return data;
    }

    /**
     * 退出登录
     */
    async signOut(token: string) {
        const { error } = await this.supabase.auth.admin.signOut(token);

        if (error) {
            throw new UnauthorizedException(error.message);
        }

        return { success: true };
    }

    /**
     * 通过访问令牌获取用户
     */
    async getUserByToken(token: string) {
        const { data, error } = await this.supabase.auth.getUser(token);

        if (error) {
            throw new UnauthorizedException(error.message);
        }

        return data.user;
    }

    /**
     * 验证会话
     */
    async verifySession(token: string) {
        const { data, error } = await this.supabase.auth.getSession();

        if (error || !data.session) {
            throw new UnauthorizedException('无效的会话');
        }

        return data.session;
    }

    /**
     * 刷新访问令牌
     */
    async refreshToken(refreshToken: string) {
        const { data, error } = await this.supabase.auth.refreshSession({
            refresh_token: refreshToken,
        });

        if (error) {
            throw new UnauthorizedException(error.message);
        }

        return data;
    }
}