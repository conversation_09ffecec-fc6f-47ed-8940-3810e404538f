import { ReactNode } from "react";
import { metadata as baseMetadata } from "../models/metadata";
import type { Metadata } from "next";

// 修改基础元数据以适应效果页面
const title = "AI Effects - ReelMind";
const description = "Browse and use AI video effects for enhancing your AI-generated videos on ReelMind platform.";
const url = "https://reelmind.ai/effects";

export const metadata: Metadata = {
    ...baseMetadata,
    title,
    description,
    alternates: {
        canonical: url,
    },
    openGraph: {
        ...baseMetadata.openGraph,
        title,
        description,
        url,
    },
    twitter: {
        ...baseMetadata.twitter,
        title,
        description,
    },
};

export default function EffectsLayout({
    children,
}: {
    children: ReactNode;
}) {
    return <>{children}</>;
} 