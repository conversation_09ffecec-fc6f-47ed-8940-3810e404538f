export type BlogPost = {
    cid: number;
    id: string;
    title: string;
    slug: string;
    excerpt: string;
    content: string; // Markdown content
    cover_image: string;
    cover_img_prompt?: string; // 封面图片的生成提示词
    created_at: string;
    author?: {
        name: string;
        avatar?: string;
    };
    meta_title?: string; // For SEO
    meta_description?: string; // For SEO
    meta_keywords?: string[]; // For SEO
};

export type BlogList = BlogPost[]

export type BlogCategory = {
    id: string;
    name: string;
    slug: string;
    description?: string;
};

export type PaginatedBlogResponse = {
    posts: BlogList;
    page: number;
    pageSize: number;
};

export type BlogSitemapEntry = {
    id: string;
    slug: string;
    created_at: string;
    cid?: number; // 自增ID字段，用于优化查询
};