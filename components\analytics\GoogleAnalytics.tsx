'use client';

import Script from 'next/script';

const GA_MEASUREMENT_ID = 'G-25J79Z3KTQ';

// 为window.dataLayer添加类型定义
declare global {
    interface Window {
        dataLayer: any[];
    }
}

export default function GoogleAnalytics() {
    return (
        <>
            <Script
                strategy="afterInteractive"
                src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
            />
            <Script
                id="google-analytics"
                strategy="afterInteractive"
                dangerouslySetInnerHTML={{
                    __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}');
          `,
                }}
            />
        </>
    );
} 