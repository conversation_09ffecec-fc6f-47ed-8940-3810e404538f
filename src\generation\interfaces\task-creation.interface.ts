import { VideoTaskStatus } from '../../common/constants/video';
import { VideoTaskDto } from '../dto/video-task.dto';
import { CreditConsumptionResult } from './credit-transaction.interface';

/**
 * 任务创建结果接口
 */
export interface TaskCreationResult {
    id: string;
    status: VideoTaskStatus;
    progress: number;
    queued_at?: Date;
    started_at?: Date;
    completed_at?: Date;
    last_activity_at?: Date;
    request_id?: string;
}

/**
 * Fal.ai API响应接口
 */
export interface FalAiResponse {
    request_id: string;
}

/**
 * 数据库插入结果接口
 */
export interface DatabaseInsertResult<T> {
    data: T | null;
    error: any | null;
}

/**
 * 任务创建上下文接口
 */
export interface TaskCreationContext {
    taskId: string;
    userId: string;
    modelId: string;
    creditResult: CreditConsumptionResult;
    priority: number;
}

/**
 * 任务创建失败处理结果接口
 */
export interface TaskCreationFailureResult {
    success: boolean;
    error?: any;
    refundResult?: any;
}

/**
 * 视频任务详情接口
 */
export interface VideoTaskDetail extends VideoTaskDto {
    // 扩展字段，如果有需要
}
