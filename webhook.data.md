# response

```typescript
data:  {
  id: 'cs_test_a155ZYWyqaMI7W9290TaCDBh53dD9siHbQwowcOTOv5ifdWiFJ7d4stsDp',
  object: 'checkout.session',
  adaptive_pricing: null,
  after_expiration: null,
  allow_promotion_codes: null,
  amount_subtotal: 12000,
  amount_total: 12000,
  automatic_tax: { enabled: false, liability: null, status: null },
  billing_address_collection: null,
  cancel_url: 'https://reelmind.ai/payment/cancel?payment_id=ad3c96cb-b5ab-4e3a-b4ea-6542f9b2ca1b',
  client_reference_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
  client_secret: null,
  collected_information: { shipping_details: null },
  consent: null,
  consent_collection: null,
  created: 1741446083,
  currency: 'usd',
  currency_conversion: null,
  custom_fields: [],
  custom_text: {
    after_submit: null,
    shipping_address: null,
    submit: null,
    terms_of_service_acceptance: null
  },
  customer: 'cus_Ru9hOpFI9kCBFc',
  customer_creation: null,
  customer_details: {
    address: {
      city: null,
      country: 'US',
      line1: null,
      line2: null,
      postal_code: '12412',
      state: null
    },
    email: '<EMAIL>',
    name: '*********',
    phone: null,
    tax_exempt: 'none',
    tax_ids: []
  },
  customer_email: null,
  discounts: [],
  expires_at: 1741532483,
  invoice: 'in_1R0Osi07FEPKXdAjbBa688IM',
  invoice_creation: null,
  livemode: false,
  locale: null,
  metadata: {
    plan_id: '7952222e-7e29-4d6f-83f6-ca418d44ffa2',
    billing_cycle: 'yearly',
    price_id: 'price_1R0LZB07FEPKXdAj5KNDWoo2',
    user_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
    payment_id: 'ad3c96cb-b5ab-4e3a-b4ea-6542f9b2ca1b'
  },
  mode: 'subscription',
  payment_intent: null,
  payment_link: null,
  payment_method_collection: 'always',
  payment_method_configuration_details: null,
  payment_method_options: { card: { request_three_d_secure: 'automatic' } },
  payment_method_types: [ 'card' ],
  payment_status: 'paid',
  phone_number_collection: { enabled: false },
  recovered_from: null,
  saved_payment_method_options: {
    allow_redisplay_filters: [ 'always' ],
    payment_method_remove: null,
    payment_method_save: null
  },
  setup_intent: null,
  shipping_address_collection: null,
  shipping_cost: null,
  shipping_details: null,
  shipping_options: [],
  status: 'complete',
  submit_type: null,
  subscription: 'sub_1R0Osi07FEPKXdAjfue1NwFH',
  success_url: 'https://reelmind.ai/payment/success?payment_id=ad3c96cb-b5ab-4e3a-b4ea-6542f9b2ca1b',
  total_details: { amount_discount: 0, amount_shipping: 0, amount_tax: 0 },
  ui_mode: 'hosted',
  url: null
} {
  id: 'sub_1R0Osi07FEPKXdAjfue1NwFH',
  object: 'subscription',
  application: null,
  application_fee_percent: null,
  automatic_tax: { disabled_reason: null, enabled: false, liability: null },
  billing_cycle_anchor: **********,
  billing_cycle_anchor_config: null,
  billing_thresholds: null,
  cancel_at: null,
  cancel_at_period_end: false,
  canceled_at: null,
  cancellation_details: { comment: null, feedback: null, reason: null },
  collection_method: 'charge_automatically',
  created: **********,
  currency: 'usd',
  current_period_end: **********,
  current_period_start: **********,
  customer: 'cus_Ru9hOpFI9kCBFc',
  days_until_due: null,
  default_payment_method: 'pm_1R0Osh07FEPKXdAjf4YSiU86',
  default_source: null,
  default_tax_rates: [],
  description: null,
  discount: null,
  discounts: [],
  ended_at: null,
  invoice_settings: { account_tax_ids: null, issuer: { type: 'self' } },
  items: {
    object: 'list',
    data: [ [Object] ],
    has_more: false,
    total_count: 1,
    url: '/v1/subscription_items?subscription=sub_1R0Osi07FEPKXdAjfue1NwFH'
  },
  latest_invoice: 'in_1R0Osi07FEPKXdAjbBa688IM',
  livemode: false,
  metadata: {
    billing_cycle: 'yearly',
    payment_id: 'ad3c96cb-b5ab-4e3a-b4ea-6542f9b2ca1b',
    plan_id: '7952222e-7e29-4d6f-83f6-ca418d44ffa2',
    user_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927'
  },
  next_pending_invoice_item_invoice: null,
  on_behalf_of: null,
  pause_collection: null,
  payment_settings: {
    payment_method_options: {
      acss_debit: null,
      bancontact: null,
      card: [Object],
      customer_balance: null,
      konbini: null,
      sepa_debit: null,
      us_bank_account: null
    },
    payment_method_types: null,
    save_default_payment_method: 'off'
  },
  pending_invoice_item_interval: null,
  pending_setup_intent: null,
  pending_update: null,
  plan: {
    id: 'price_1R0LZB07FEPKXdAj5KNDWoo2',
    object: 'plan',
    active: true,
    aggregate_usage: null,
    amount: 12000,
    amount_decimal: '12000',
    billing_scheme: 'per_unit',
    created: **********,
    currency: 'usd',
    interval: 'year',
    interval_count: 1,
    livemode: false,
    metadata: {},
    meter: null,
    nickname: 'Reelmind PRO year',
    product: 'prod_RtosbyrsLQRf36',
    tiers_mode: null,
    transform_usage: null,
    trial_period_days: null,
    usage_type: 'licensed'
  },
  quantity: 1,
  schedule: null,
  start_date: **********,
  status: 'active',
  test_clock: null,
  transfer_data: null,
  trial_end: null,
  trial_settings: { end_behavior: { missing_payment_method: 'create_invoice' } },
  trial_start: null
}
```

[NestWinston] 21208 2025/3/8 21:34:29 LOG [PaymentController] 处理Stripe webhook事件: checkout.session.completed
收到Stripe webhook事件： checkout.session.completed
收到Stripe webhook事件[checkout.session.completed]： {
id: 'cs_test_a1I4L4CwAf4uz9bsn4S68Lpfy9lZ3FUq9vFejK23KDd5XeFow8WLYv0qT7',  
 object: 'checkout.session',
adaptive_pricing: null,
after_expiration: null,
allow_promotion_codes: null,
amount_subtotal: 12000,
amount_total: 12000,
automatic_tax: { enabled: false, liability: null, status: null },
billing_address_collection: null,
cancel_url: 'https://reelmind.ai/payment/cancel?payment_id=86c09ca7-f9cf-4474-92d2-59cb577ea6d1',
client_reference_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
client_secret: null,
collected_information: { shipping_details: null },
consent: null,
consent_collection: null,
created: 1741440840,
currency: 'usd',
currency_conversion: null,
custom_fields: [],
custom_text: {
after_submit: null,
shipping_address: null,
submit: null,
terms_of_service_acceptance: null
},
customer: 'cus_Ru9hOpFI9kCBFc',
customer_creation: null,
customer_details: {
address: {
city: null,
country: 'US',
line1: null,
line2: null,
postal_code: '22222',
state: null
},
email: '<EMAIL>',
name: '222',
phone: null,
tax_exempt: 'none',
tax_ids: []
},
customer_email: null,
discounts: [],
expires_at: 1741527240,
invoice: 'in_1R0NWF07FEPKXdAjrPFnVfmc',
invoice_creation: null,
livemode: false,
locale: null,
metadata: {
plan_id: '7952222e-7e29-4d6f-83f6-ca418d44ffa2',
user_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
price_id: 'price_1R0LZB07FEPKXdAj5KNDWoo2',
billing_cycle: 'yearly'
},
mode: 'subscription',
payment_intent: null,
payment_link: null,
payment_method_collection: 'always',
payment_method_configuration_details: null,
payment_method_options: { card: { request_three_d_secure: 'automatic' } },  
 payment_method_types: [ 'card' ],
payment_status: 'paid',
phone_number_collection: { enabled: false },
recovered_from: null,
saved_payment_method_options: {
allow_redisplay_filters: [ 'always' ],
payment_method_remove: null,
payment_method_save: null
},
setup_intent: null,
shipping_address_collection: null,
shipping_cost: null,
shipping_details: null,
shipping_options: [],
status: 'complete',
submit_type: null,
subscription: 'sub_1R0NWF07FEPKXdAjxgHmM4nf',
success_url: 'https://reelmind.ai/payment/success?payment_id=86c09ca7-f9cf-4474-92d2-59cb577ea6d1',
total_details: { amount_discount: 0, amount_shipping: 0, amount_tax: 0 },  
 ui_mode: 'hosted',
url: null
}
收到Stripe webhook事件[invoice.paid]： {
id: 'cs_test_a1I4L4CwAf4uz9bsn4S68Lpfy9lZ3FUq9vFejK23KDd5XeFow8WLYv0qT7',  
 object: 'checkout.session',
adaptive_pricing: null,
after_expiration: null,
allow_promotion_codes: null,
amount_subtotal: 12000,
amount_total: 12000,
automatic_tax: { enabled: false, liability: null, status: null },
billing_address_collection: null,
cancel_url: 'https://reelmind.ai/payment/cancel?payment_id=86c09ca7-f9cf-4474-92d2-59cb577ea6d1',
client_reference_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
client_secret: null,
collected_information: { shipping_details: null },
consent: null,
consent_collection: null,
created: 1741440840,
currency: 'usd',
currency_conversion: null,
custom_fields: [],
custom_text: {
after_submit: null,
shipping_address: null,
submit: null,
terms_of_service_acceptance: null
},
customer: 'cus_Ru9hOpFI9kCBFc',
customer_creation: null,
customer_details: {
address: {
city: null,
country: 'US',
line1: null,
line2: null,
postal_code: '22222',
state: null
},
email: '<EMAIL>',
name: '222',
phone: null,
tax_exempt: 'none',
tax_ids: []
},
customer_email: null,
discounts: [],
expires_at: 1741527240,
invoice: 'in_1R0NWF07FEPKXdAjrPFnVfmc',
invoice_creation: null,
livemode: false,
locale: null,
metadata: {
plan_id: '7952222e-7e29-4d6f-83f6-ca418d44ffa2',
user_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
price_id: 'price_1R0LZB07FEPKXdAj5KNDWoo2',
billing_cycle: 'yearly'
},
mode: 'subscription',
payment_intent: null,
payment_link: null,
payment_method_collection: 'always',
payment_method_configuration_details: null,
payment_method_options: { card: { request_three_d_secure: 'automatic' } },  
 payment_method_types: [ 'card' ],
payment_status: 'paid',
phone_number_collection: { enabled: false },
recovered_from: null,
saved_payment_method_options: {
allow_redisplay_filters: [ 'always' ],
payment_method_remove: null,
payment_method_save: null
},
setup_intent: null,
shipping_address_collection: null,
shipping_cost: null,
shipping_details: null,
shipping_options: [],
status: 'complete',
submit_type: null,
subscription: 'sub_1R0NWF07FEPKXdAjxgHmM4nf',
success_url: 'https://reelmind.ai/payment/success?payment_id=86c09ca7-f9cf-4474-92d2-59cb577ea6d1',
total_details: { amount_discount: 0, amount_shipping: 0, amount_tax: 0 },  
 ui_mode: 'hosted',
url: null
}
收到Stripe webhook事件[customer.subscription.created]： {
id: 'cs_test_a1I4L4CwAf4uz9bsn4S68Lpfy9lZ3FUq9vFejK23KDd5XeFow8WLYv0qT7',  
 object: 'checkout.session',
adaptive_pricing: null,
after_expiration: null,
allow_promotion_codes: null,
amount_subtotal: 12000,
amount_total: 12000,
automatic_tax: { enabled: false, liability: null, status: null },
billing_address_collection: null,
cancel_url: 'https://reelmind.ai/payment/cancel?payment_id=86c09ca7-f9cf-4474-92d2-59cb577ea6d1',
client_reference_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
client_secret: null,
collected_information: { shipping_details: null },
consent: null,
consent_collection: null,
created: 1741440840,
currency: 'usd',
currency_conversion: null,
custom_fields: [],
custom_text: {
after_submit: null,
shipping_address: null,
submit: null,
terms_of_service_acceptance: null
},
customer: 'cus_Ru9hOpFI9kCBFc',
customer_creation: null,
customer_details: {
address: {
city: null,
country: 'US',
line1: null,
line2: null,
postal_code: '22222',
state: null
},
email: '<EMAIL>',
name: '222',
phone: null,
tax_exempt: 'none',
tax_ids: []
},
customer_email: null,
discounts: [],
expires_at: 1741527240,
invoice: 'in_1R0NWF07FEPKXdAjrPFnVfmc',
invoice_creation: null,
livemode: false,
locale: null,
metadata: {
plan_id: '7952222e-7e29-4d6f-83f6-ca418d44ffa2',
user_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
price_id: 'price_1R0LZB07FEPKXdAj5KNDWoo2',
billing_cycle: 'yearly'
},
mode: 'subscription',
payment_intent: null,
payment_link: null,
payment_method_collection: 'always',
payment_method_configuration_details: null,
payment_method_options: { card: { request_three_d_secure: 'automatic' } },  
 payment_method_types: [ 'card' ],
payment_status: 'paid',
phone_number_collection: { enabled: false },
recovered_from: null,
saved_payment_method_options: {
allow_redisplay_filters: [ 'always' ],
payment_method_remove: null,
payment_method_save: null
},
setup_intent: null,
shipping_address_collection: null,
shipping_cost: null,
shipping_details: null,
shipping_options: [],
status: 'complete',
submit_type: null,
subscription: 'sub_1R0NWF07FEPKXdAjxgHmM4nf',
success_url: 'https://reelmind.ai/payment/success?payment_id=86c09ca7-f9cf-4474-92d2-59cb577ea6d1',
total_details: { amount_discount: 0, amount_shipping: 0, amount_tax: 0 },  
 ui_mode: 'hosted',
url: null
}
[NestWinston] 21208 2025/3/8 21:34:29 LOG [PaymentController] 成功处理Stripe事件: checkout.session.completed, ID: evt_1R0NWJ07FEPKXdAjU6VBzFoe
[NestWinston] 21208 2025/3/8 21:34:29 LOG [LoggerMiddleware] [Response] {"requestId":"e7bb76b4-a1fe-4448-b1ac-9d2495ff5c5b","timestamp":"2025-03-08T13:34:29.151Z","statusCode":201,"responseTime":"21ms","headers":{"x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","content-type":"application/json; charset=utf-8","content-length":"100","etag":"W/\"64-AyhD3SbvOipfWyEOT96zi7premA\""}}
[NestWinston] 21208 2025/3/8 21:34:29 LOG [LoggerMiddleware] [Request] {"requestId":"afabf828-f3b9-4298-8514-d59adc48d338","timestamp":"2025-03-08T13:34:29.210Z","method":"POST","url":"/payment/webhooks/stripe","ip":"::1","userAgent":"Stripe/1.0 (+https://stripe.com/docs/webhooks)","body":"Logger省略[Buffer]内容...","query":{},"params":{"path":["payment","webhooks","stripe"]}}
[NestWinston] 21208 2025/3/8 21:34:29 LOG [PaymentController] 处理Stripe webhook事件: customer.subscription.updated
收到Stripe webhook事件： customer.subscription.updated
收到Stripe webhook事件[customer.subscription.updated]： {
id: 'sub_1R0NWF07FEPKXdAjxgHmM4nf',
object: 'subscription',
application: null,
application_fee_percent: null,
automatic_tax: { disabled_reason: null, enabled: false, liability: null },  
 billing_cycle_anchor: **********,
billing_cycle_anchor_config: null,
billing_thresholds: null,
cancel_at: null,
cancel_at_period_end: false,
canceled_at: null,
cancellation_details: { comment: null, feedback: null, reason: null },
collection_method: 'charge_automatically',
created: **********,
currency: 'usd',
current_period_end: **********,
current_period_start: **********,
customer: 'cus_Ru9hOpFI9kCBFc',
days_until_due: null,
default_payment_method: 'pm_1R0NWE07FEPKXdAjdIinH2cv',
default_source: null,
default_tax_rates: [],
description: null,
discount: null,
discounts: [],
ended_at: null,
invoice_settings: { account_tax_ids: null, issuer: { type: 'self' } },
items: {
object: 'list',
data: [ [Object] ],
has_more: false,
total_count: 1,
url: '/v1/subscription_items?subscription=sub_1R0NWF07FEPKXdAjxgHmM4nf'  
 },
latest_invoice: 'in_1R0NWF07FEPKXdAjrPFnVfmc',
livemode: false,
metadata: {
plan_id: '7952222e-7e29-4d6f-83f6-ca418d44ffa2',
user_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
billing_cycle: 'yearly'
},
next_pending_invoice_item_invoice: null,
on_behalf_of: null,
pause_collection: null,
payment_settings: {
payment_method_options: {
acss_debit: null,
bancontact: null,
card: [Object],
customer_balance: null,
konbini: null,
sepa_debit: null,
us_bank_account: null
},
payment_method_types: null,
save_default_payment_method: 'off'
},
pending_invoice_item_interval: null,
pending_setup_intent: null,
pending_update: null,
plan: {
id: 'price_1R0LZB07FEPKXdAj5KNDWoo2',
object: 'plan',
active: true,
aggregate_usage: null,
amount: 12000,
amount_decimal: '12000',
billing_scheme: 'per_unit',
created: **********,
currency: 'usd',
interval: 'year',
interval_count: 1,
livemode: false,
metadata: {},
meter: null,
nickname: 'Reelmind PRO year',
product: 'prod_RtosbyrsLQRf36',
tiers_mode: null,
transform_usage: null,
trial_period_days: null,
usage_type: 'licensed'
},
quantity: 1,
schedule: null,
start_date: **********,
status: 'active',
test_clock: null,
transfer_data: null,
trial_end: null,
trial_settings: { end_behavior: { missing_payment_method: 'create_invoice' } },
trial_start: null
}
收到Stripe webhook事件[customer.subscription.deleted]： {
id: 'sub_1R0NWF07FEPKXdAjxgHmM4nf',
object: 'subscription',
application: null,
application_fee_percent: null,
automatic_tax: { disabled_reason: null, enabled: false, liability: null },  
 billing_cycle_anchor: **********,
billing_cycle_anchor_config: null,
billing_thresholds: null,
cancel_at: null,
cancel_at_period_end: false,
canceled_at: null,
cancellation_details: { comment: null, feedback: null, reason: null },
collection_method: 'charge_automatically',
created: **********,
currency: 'usd',
current_period_end: **********,
current_period_start: **********,
customer: 'cus_Ru9hOpFI9kCBFc',
days_until_due: null,
default_payment_method: 'pm_1R0NWE07FEPKXdAjdIinH2cv',
default_source: null,
default_tax_rates: [],
description: null,
discount: null,
discounts: [],
ended_at: null,
invoice_settings: { account_tax_ids: null, issuer: { type: 'self' } },
items: {
object: 'list',
data: [ [Object] ],
has_more: false,
total_count: 1,
url: '/v1/subscription_items?subscription=sub_1R0NWF07FEPKXdAjxgHmM4nf'  
 },
latest_invoice: 'in_1R0NWF07FEPKXdAjrPFnVfmc',
livemode: false,
metadata: {
plan_id: '7952222e-7e29-4d6f-83f6-ca418d44ffa2',
user_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
billing_cycle: 'yearly'
},
next_pending_invoice_item_invoice: null,
on_behalf_of: null,
pause_collection: null,
payment_settings: {
payment_method_options: {
acss_debit: null,
bancontact: null,
card: [Object],
customer_balance: null,
konbini: null,
sepa_debit: null,
us_bank_account: null
},
payment_method_types: null,
save_default_payment_method: 'off'
},
pending_invoice_item_interval: null,
pending_setup_intent: null,
pending_update: null,
plan: {
id: 'price_1R0LZB07FEPKXdAj5KNDWoo2',
object: 'plan',
active: true,
aggregate_usage: null,
amount: 12000,
amount_decimal: '12000',
billing_scheme: 'per_unit',
created: **********,
currency: 'usd',
interval: 'year',
interval_count: 1,
livemode: false,
metadata: {},
meter: null,
nickname: 'Reelmind PRO year',
product: 'prod_RtosbyrsLQRf36',
tiers_mode: null,
transform_usage: null,
trial_period_days: null,
usage_type: 'licensed'
},
quantity: 1,
schedule: null,
start_date: **********,
status: 'active',
test_clock: null,
transfer_data: null,
trial_end: null,
trial_settings: { end_behavior: { missing_payment_method: 'create_invoice' } },
trial_start: null
}
[NestWinston] 21208 2025/3/8 21:34:29 LOG [PaymentController] 无Handler的Stripe事件类型[customer.subscription.updated]
[NestWinston] 21208 2025/3/8 21:34:29 LOG [PaymentController] 成功处理Stripe事件: customer.subscription.updated, ID: evt_1R0NWJ07FEPKXdAjSrs8OuVp
[NestWinston] 21208 2025/3/8 21:34:29 LOG [LoggerMiddleware] [Response] {"requestId":"afabf828-f3b9-4298-8514-d59adc48d338","timestamp":"2025-03-08T13:34:29.224Z","statusCode":201,"responseTime":"14ms","headers":{"x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","content-type":"application/json; charset=utf-8","content-length":"100","etag":"W/\"64-EN369xzB/dkhSPRBptjz5yTp7Jc\""}}
[NestWinston] 21208 2025/3/8 21:34:30 LOG [LoggerMiddleware] [Request] {"requestId":"cdd80c7c-ff00-4f40-9e5e-52ddc94c77ca","timestamp":"2025-03-08T13:34:30.218Z","method":"POST","url":"/payment/webhooks/stripe","ip":"::1","userAgent":"Stripe/1.0 (+https://stripe.com/docs/webhooks)","body":"Logger省略[Buffer]内容...","query":{},"params":{"path":["payment","webhooks","stripe"]}}
[NestWinston] 21208 2025/3/8 21:34:30 LOG [PaymentController] 处理Stripe webhook事件: customer.subscription.created
收到Stripe webhook事件： customer.subscription.created
收到Stripe webhook事件[customer.subscription.created]： {
id: 'sub_1R0NWF07FEPKXdAjxgHmM4nf',
object: 'subscription',
application: null,
application_fee_percent: null,
automatic_tax: { disabled_reason: null, enabled: false, liability: null },  
 billing_cycle_anchor: **********,
billing_cycle_anchor_config: null,
billing_thresholds: null,
cancel_at: null,
cancel_at_period_end: false,
canceled_at: null,
cancellation_details: { comment: null, feedback: null, reason: null },
collection_method: 'charge_automatically',
created: **********,
currency: 'usd',
current_period_end: **********,
current_period_start: **********,
customer: 'cus_Ru9hOpFI9kCBFc',
days_until_due: null,
default_payment_method: null,
default_source: null,
default_tax_rates: [],
description: null,
discount: null,
discounts: [],
ended_at: null,
invoice_settings: { account_tax_ids: null, issuer: { type: 'self' } },
items: {
object: 'list',
data: [ [Object] ],
has_more: false,
total_count: 1,
url: '/v1/subscription_items?subscription=sub_1R0NWF07FEPKXdAjxgHmM4nf'  
 },
latest_invoice: 'in_1R0NWF07FEPKXdAjrPFnVfmc',
livemode: false,
metadata: {
plan_id: '7952222e-7e29-4d6f-83f6-ca418d44ffa2',
user_id: 'd9b62895-3784-438c-9d6e-71d3f8c71927',
billing_cycle: 'yearly'
},
next_pending_invoice_item_invoice: null,
on_behalf_of: null,
pause_collection: null,
payment_settings: {
payment_method_options: {
acss_debit: null,
bancontact: null,
card: [Object],
customer_balance: null,
konbini: null,
sepa_debit: null,
us_bank_account: null
},
payment_method_types: null,
save_default_payment_method: 'off'
},
pending_invoice_item_interval: null,
pending_setup_intent: null,
pending_update: null,
plan: {
id: 'price_1R0LZB07FEPKXdAj5KNDWoo2',
object: 'plan',
active: true,
aggregate_usage: null,
amount: 12000,
amount_decimal: '12000',
billing_scheme: 'per_unit',
created: **********,
currency: 'usd',
interval: 'year',
interval_count: 1,
livemode: false,
metadata: {},
meter: null,
nickname: 'Reelmind PRO year',
product: 'prod_RtosbyrsLQRf36',
tiers_mode: null,
transform_usage: null,
trial_period_days: null,
usage_type: 'licensed'
},
quantity: 1,
schedule: null,
start_date: **********,
status: 'incomplete',
test_clock: null,
transfer_data: null,
trial_end: null,
trial_settings: { end_behavior: { missing_payment_method: 'create_invoice' } },
trial_start: null
}
[NestWinston] 21208 2025/3/8 21:34:30 LOG [PaymentController] 成功处理Stripe事件: customer.subscription.created, ID: evt_1R0NWJ07FEPKXdAjKpphdpEQ
