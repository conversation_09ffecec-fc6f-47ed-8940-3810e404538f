import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 获取所有文章
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const pageSize = parseInt(searchParams.get('pageSize') || '10');
        const category = searchParams.get('category') || undefined;
        const fields = 'id, excerpt, cover_image, title, slug, published_at, created_at, category, tags';

        let query = supabase
            .from('blog_posts')
            .select(fields, { count: 'exact' })
            .order('published_at', { ascending: false });

        if (category) {
            query = query.eq('category', category);
        }

        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;

        const { data, count, error } = await query.range(from, to);

        if (error) {
            console.error('Error fetching blog posts:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json({
            posts: data,
            total: count || 0,
            page,
            pageSize,
            hasMore: (count || 0) > (page * pageSize),
        });
    } catch (error) {
        console.error('Error in GET /api/blog:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}

// 创建新文章
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();

        // 验证必填字段
        const requiredFields = ['title', 'slug', 'content', 'excerpt'];
        for (const field of requiredFields) {
            if (!body[field]) {
                return NextResponse.json({ error: `${field} is required` }, { status: 400 });
            }
        }

        // 检查slug是否已存在
        const { data: existingPost } = await supabase
            .from('blog_posts')
            .select('slug')
            .eq('slug', body.slug)
            .maybeSingle();

        if (existingPost) {
            return NextResponse.json({ error: 'A post with this slug already exists' }, { status: 400 });
        }

        // 准备数据
        const newPost = {
            id: crypto.randomUUID(),
            title: body.title,
            slug: body.slug,
            content: body.content,
            excerpt: body.excerpt,
            cover_image: body.cover_image || null,
            created_at: body.created_at || new Date().toISOString(),
            category: body.category || null,
            tags: body.tags || null,
            seo_title: body.meta_title || null,
            seo_description: body.meta_description || null,
            seo_keywords: body.meta_keywords || null
        };

        // 插入数据
        const { data, error } = await supabase
            .from('blog_posts')
            .insert(newPost)
            .select()
            .single();

        if (error) {
            console.error('Error creating blog post:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json(data, { status: 201 });
    } catch (error) {
        console.error('Error in POST /api/blog:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}

// 更新文章
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json();

        // 检查ID是否存在
        if (!body.id) {
            return NextResponse.json({ error: 'Post ID is required' }, { status: 400 });
        }

        // 验证必填字段
        const requiredFields = ['title', 'slug', 'content', 'excerpt'];
        for (const field of requiredFields) {
            if (!body[field]) {
                return NextResponse.json({ error: `${field} is required` }, { status: 400 });
            }
        }

        // 检查slug是否已被其他文章使用
        const { data: existingPost } = await supabase
            .from('blog_posts')
            .select('id, slug')
            .eq('slug', body.slug)
            .neq('id', body.id)
            .maybeSingle();

        if (existingPost) {
            return NextResponse.json({ error: 'A different post with this slug already exists' }, { status: 400 });
        }

        // 准备更新数据
        const updatePost = {
            title: body.title,
            slug: body.slug,
            content: body.content,
            excerpt: body.excerpt,
            cover_image: body.cover_image,
            created_at: body.created_at,
            category: body.category,
            tags: body.tags,
            seo_title: body.meta_title,
            seo_description: body.meta_description,
            seo_keywords: body.meta_keywords
        };

        // 更新数据
        const { data, error } = await supabase
            .from('blog_posts')
            .update(updatePost)
            .eq('id', body.id)
            .select()
            .single();

        if (error) {
            console.error('Error updating blog post:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json(data);
    } catch (error) {
        console.error('Error in PUT /api/blog:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}

// 删除文章
export async function DELETE(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const id = searchParams.get('id');

        if (!id) {
            return NextResponse.json({ error: 'Post ID is required' }, { status: 400 });
        }

        const { error } = await supabase
            .from('blog_posts')
            .delete()
            .eq('id', id);

        if (error) {
            console.error('Error deleting blog post:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('Error in DELETE /api/blog:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
} 