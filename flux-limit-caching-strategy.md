# FLUX模型使用限制缓存策略

## 问题分析

### 原始5分钟缓存的问题
1. **数据延迟**：用户使用FLUX模型后，需要等5分钟才能看到最新使用次数
2. **用户困惑**：界面显示"可以使用"，但点击生成时被拒绝
3. **体验不一致**：前端显示与后端实际状态不同步

## 优化后的缓存策略

### 1. 缓存时间调整
```typescript
// 从5分钟缓存调整为30秒缓存
staleTime: 1000 * 30, // 30秒缓存，保持数据相对新鲜
```

**优势**：
- 数据更新更及时
- 减少用户困惑
- 保持合理的性能

### 2. 自动刷新机制
```typescript
refetchOnWindowFocus: true, // 窗口获得焦点时刷新
refetchInterval: 1000 * 60, // 每分钟自动刷新一次
```

**优势**：
- 用户切换回页面时获取最新数据
- 定期自动更新，无需用户手动刷新

### 3. 主动缓存失效
```typescript
// 在FLUX模型生成成功后立即刷新
if (isFluxModel) {
    refetchFluxUsage();
}
```

**优势**：
- 操作后立即更新数据
- 确保UI状态与实际状态同步

## 完整的数据流

### 用户操作流程
1. **页面加载**：获取FLUX使用限制信息（30秒缓存）
2. **选择FLUX模型**：显示当前使用状态
3. **点击生成**：
   - 前端检查限制状态
   - 后端再次验证限制
   - 生成成功后立即刷新限制信息
4. **达到限制**：
   - 前端按钮变为"Limit Reached"
   - 显示升级提示

### 缓存更新时机
1. **被动更新**：
   - 30秒后数据过期，下次访问时重新获取
   - 每分钟自动刷新
   - 窗口获得焦点时刷新

2. **主动更新**：
   - FLUX模型生成成功后立即刷新
   - 用户升级会员后刷新（如果有相关逻辑）

## 性能考虑

### API调用频率
- **正常使用**：每30秒最多1次API调用
- **活跃使用**：每次FLUX生成后1次额外调用
- **后台刷新**：每分钟1次自动调用

### 网络优化
```typescript
// 使用React Query的智能缓存
// 多个组件共享同一个查询，不会重复请求
const { data: fluxUsageLimit } = useFluxUsageLimit();
```

### 错误处理
```typescript
// API调用失败时使用缓存数据
// 避免因网络问题影响用户体验
return {
    canUse: true,
    usageCount: 0,
    limit: 2
};
```

## 边界情况处理

### 1. 网络延迟
- 使用乐观更新：生成请求发送后立即更新UI
- 如果请求失败，回滚UI状态

### 2. 并发请求
- 后端使用数据库事务确保原子性
- 前端使用React Query的重复请求去重

### 3. 数据不一致
- 定期自动刷新确保最终一致性
- 关键操作后立即刷新

## 监控指标

### 缓存效果监控
1. **缓存命中率**：监控API调用频率
2. **数据一致性**：前端显示vs后端实际状态
3. **用户体验**：错误率和用户投诉

### 性能监控
1. **API响应时间**：FLUX限制查询接口
2. **前端渲染性能**：UI更新流畅度
3. **网络请求量**：总体API调用次数

## 进一步优化建议

### 1. WebSocket实时更新
```typescript
// 可以考虑使用WebSocket推送限制状态变更
// 适用于高频使用场景
useEffect(() => {
    const ws = new WebSocket('/ws/flux-limit');
    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        // 更新限制状态
    };
}, []);
```

### 2. 本地状态预测
```typescript
// 在发送请求时立即更新本地状态
// 提供更好的用户体验
const [optimisticUsageCount, setOptimisticUsageCount] = useState(0);

const handleGenerate = () => {
    // 乐观更新
    setOptimisticUsageCount(prev => prev + 1);
    
    generateImage(/* ... */, {
        onError: () => {
            // 回滚乐观更新
            setOptimisticUsageCount(prev => prev - 1);
        }
    });
};
```

### 3. 智能预加载
```typescript
// 在用户选择FLUX模型时预加载限制信息
useEffect(() => {
    if (selectedModel?.name.includes('flux')) {
        // 预加载限制信息
        refetchFluxUsage();
    }
}, [selectedModel]);
```

## 总结

优化后的缓存策略平衡了以下几个方面：

1. **数据新鲜度**：30秒缓存 + 主动刷新
2. **用户体验**：及时的状态更新和清晰的提示
3. **性能效率**：合理的API调用频率
4. **系统稳定性**：错误处理和降级方案

这个策略确保用户看到的限制状态与实际状态基本同步，同时保持良好的性能表现。
