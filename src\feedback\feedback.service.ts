import { Injectable, Inject, BadRequestException, HttpException, HttpStatus } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import {
    CreateFeedbackDto,
    FeedbackResponseDto
} from './dto/feedback.dto';

@Injectable()
export class FeedbackService {
    // Rate limiting storage (in production, use Redis or database)
    private static userSubmissions = new Map<string, number[]>();
    private static readonly MAX_SUBMISSIONS_PER_HOUR = 5;
    private static readonly COOLDOWN_PERIOD = 30 * 1000; // 30 seconds

    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {}

    /**
     * Security: Sanitize input to prevent XSS
     */
    private sanitizeInput(input: string): string {
        if (!input || typeof input !== 'string') {
            return '';
        }

        // Simple but effective sanitization without external dependencies
        let sanitized = input
            .replace(/<[^>]*>/g, '') // Remove HTML tags
            .replace(/javascript:/gi, '') // Remove javascript: protocol
            .replace(/vbscript:/gi, '') // Remove vbscript: protocol
            .replace(/on\w+\s*=/gi, '') // Remove event handlers
            .replace(/data:text\/html/gi, '') // Remove data URLs
            .replace(/expression\s*\(/gi, ''); // Remove CSS expressions

        return sanitized.trim();
    }

    /**
     * Security: Check for suspicious patterns
     */
    private containsSuspiciousContent(input: string): boolean {
        const dangerousPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript:/gi,
            /vbscript:/gi,
            /on\w+\s*=/gi,
            /<iframe\b[^>]*>/gi,
            /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
            /(--|\/\*|\*\/|;)/g,
        ];

        return dangerousPatterns.some(pattern => pattern.test(input));
    }

    /**
     * Security: Rate limiting check
     */
    private checkRateLimit(userId: string): void {
        const now = Date.now();
        const userSubmissions = FeedbackService.userSubmissions.get(userId) || [];

        // Filter submissions within the last hour
        const recentSubmissions = userSubmissions.filter(
            timestamp => now - timestamp < 60 * 60 * 1000
        );

        // Check hourly limit
        if (recentSubmissions.length >= FeedbackService.MAX_SUBMISSIONS_PER_HOUR) {
            this.logger.warn(`Rate limit exceeded for user ${userId}`);
            throw new HttpException('Too many feedback submissions. Please try again later.', HttpStatus.TOO_MANY_REQUESTS);
        }

        // Check cooldown period
        if (recentSubmissions.length > 0) {
            const lastSubmission = Math.max(...recentSubmissions);
            const timeSinceLastSubmission = now - lastSubmission;

            if (timeSinceLastSubmission < FeedbackService.COOLDOWN_PERIOD) {
                this.logger.warn(`Cooldown period not met for user ${userId}`);
                throw new HttpException('Please wait before submitting another feedback.', HttpStatus.TOO_MANY_REQUESTS);
            }
        }
    }

    /**
     * Security: Record submission for rate limiting
     */
    private recordSubmission(userId: string): void {
        const now = Date.now();
        const userSubmissions = FeedbackService.userSubmissions.get(userId) || [];

        userSubmissions.push(now);
        FeedbackService.userSubmissions.set(userId, userSubmissions);

        // Cleanup old submissions
        setTimeout(() => {
            const cutoff = Date.now() - 60 * 60 * 1000; // 1 hour ago
            const recentSubmissions = userSubmissions.filter(timestamp => timestamp > cutoff);

            if (recentSubmissions.length === 0) {
                FeedbackService.userSubmissions.delete(userId);
            } else {
                FeedbackService.userSubmissions.set(userId, recentSubmissions);
            }
        }, 60 * 60 * 1000); // Cleanup after 1 hour
    }

    /**
     * 创建反馈 - 增强安全版本
     * @param userId 用户ID
     * @param createFeedbackDto 创建反馈DTO
     * @returns 创建的反馈信息
     */
    async createFeedback(userId: string, createFeedbackDto: CreateFeedbackDto): Promise<FeedbackResponseDto> {
        try {
            // Security: Check rate limiting
            this.checkRateLimit(userId);

            // Security: Validate and sanitize input
            const sanitizedSubject = this.sanitizeInput(createFeedbackDto.subject);
            const sanitizedMessage = this.sanitizeInput(createFeedbackDto.message);

            // Security: Check for suspicious content
            if (this.containsSuspiciousContent(sanitizedSubject) ||
                this.containsSuspiciousContent(sanitizedMessage)) {
                this.logger.warn(`Suspicious content detected from user ${userId}`);
                throw new BadRequestException('Content contains invalid characters');
            }

            // Security: Additional length validation
            if (sanitizedSubject.length < 3 || sanitizedSubject.length > 200) {
                throw new BadRequestException('Subject must be between 3 and 200 characters');
            }

            if (sanitizedMessage.length < 5 || sanitizedMessage.length > 2000) {
                throw new BadRequestException('Message must be between 5 and 2000 characters');
            }

            // Prepare sanitized data for database
            const feedbackData = {
                user_id: userId,
                type: createFeedbackDto.type,
                subject: sanitizedSubject,
                message: sanitizedMessage,
            };

            // Insert into database using parameterized query (Supabase handles this)
            const { data, error } = await this.supabase
                .from('feedbacks')
                .insert(feedbackData)
                .select('id, type, subject, message, created_at')
                .single();

            if (error) {
                this.logger.error(`Failed to create feedback for user ${userId}: ${error.message}`);
                throw new BadRequestException('Failed to create feedback');
            }

            // Security: Record submission for rate limiting
            this.recordSubmission(userId);

            // Log success (without sensitive data)
            this.logger.log(`User ${userId} created feedback: ${data.id}`);

            return data;
        } catch (error) {
            // Security: Don't expose internal errors to client
            if (error instanceof BadRequestException || error instanceof HttpException) {
                throw error;
            }

            this.logger.error(`Unexpected error creating feedback for user ${userId}: ${error.message}`);
            throw new BadRequestException('Failed to create feedback');
        }
    }

}
