import { IsString, IsArray, IsU<PERSON>D, IsNumber, IsObject, IsOptional, Min, Max, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * LoRA模型训练设置
 */
export class TrainSettingsDto {
    @ApiProperty({ description: '触发词', example: 'sks person' })
    @IsString()
    trigger_word: string;

    @ApiPropertyOptional({ description: '基础模型ID', example: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6' })
    @IsOptional()
    @IsUUID()
    model_id?: string;

    @ApiPropertyOptional({ description: '学习率', example: 0.0001, default: 0.0001 })
    @IsOptional()
    @IsNumber()
    @Min(0.00001)
    @Max(0.01)
    learning_rate?: number;

    @ApiPropertyOptional({ description: '训练步数', example: 1500, default: 1500 })
    @IsOptional()
    @IsNumber()
    @Min(500)
    @Max(5000)
    steps?: number;
}

/**
 * 开始训练请求
 */
export class StartTrainRequestDto {
    @ApiProperty({ description: '视频URL列表', type: [String] })
    @IsArray()
    videos: { url: string; }[];

    @ApiProperty({ description: '训练设置', type: TrainSettingsDto })
    @IsObject()
    @Type(() => TrainSettingsDto)
    settings: TrainSettingsDto;
}

/**
 * 训练价格请求
 */
export class TrainPriceRequestDto {
    @ApiProperty({ description: '视频URL列表', type: [String] })
    @IsArray()
    videos: { url: string; }[];

    @ApiProperty({ description: '训练设置', type: TrainSettingsDto })
    @IsObject()
    @Type(() => TrainSettingsDto)
    settings: TrainSettingsDto;
}

/**
 * 训练价格响应
 */
export class TrainPriceResponseDto {
    @ApiProperty({ description: '训练需要的积分', example: 1000 })
    @IsNumber()
    credits: number;

    @ApiPropertyOptional({ description: '会员折扣信息', example: '会员用户可享受8折优惠' })
    @IsOptional()
    @IsString()
    discount_info?: string;
}

/**
 * LoRA训练任务
 */
export class LoraTrainTaskDto {
    @ApiProperty({ description: '任务ID' })
    @IsUUID()
    id: string;

    @ApiProperty({ description: '用户ID' })
    @IsUUID()
    user_id: string;

    @ApiProperty({ description: '任务状态', example: 'pending' })
    @IsString()
    status: string;

    @ApiProperty({ description: '训练进度', example: 0 })
    @IsNumber()
    progress: number;

    @ApiProperty({ description: '视频列表', type: [String] })
    @IsArray()
    @IsString({ each: true })
    videos: string[];

    @ApiProperty({ description: '训练设置' })
    @IsObject()
    settings: TrainSettingsDto;

    @ApiPropertyOptional({ description: '模型ID', example: null })
    @IsOptional()
    @IsUUID()
    model_id?: string;

    @ApiProperty({ description: '优先级', example: 0 })
    @IsNumber()
    priority: number;

    @ApiProperty({ description: '创建时间' })
    created_at: Date;

    @ApiPropertyOptional({ description: '开始训练时间' })
    @IsOptional()
    started_at?: Date;

    @ApiPropertyOptional({ description: '完成时间' })
    @IsOptional()
    completed_at?: Date;
}

/**
 * 获取训练任务请求
 */
export class PopTrainTaskRequestDto {
    @ApiProperty({ description: 'API密钥', example: 'your-api-key' })
    @IsString()
    api_key: string;
}

/**
 * 完成训练任务请求
 */
export class FinishTrainTaskRequestDto {
    @ApiProperty({ description: '任务ID' })
    @IsUUID()
    task_id: string;

    @ApiProperty({ description: '是否成功' })
    @IsBoolean()
    success: boolean;

    @ApiPropertyOptional({ description: '错误信息' })
    @IsOptional()
    @IsString()
    error_message?: string;

    @ApiPropertyOptional({ description: '模型存储路径' })
    @IsOptional()
    @IsString()
    storage_url?: string;

    @ApiPropertyOptional({ description: '模型元数据' })
    @IsOptional()
    @IsObject()
    metadata?: any;
}

/**
 * 更新训练任务请求
 */
export class UpdateTrainTaskRequestDto {
    @ApiProperty({ description: '任务ID' })
    @IsUUID()
    task_id: string;

    @ApiProperty({ description: '任务状态', example: 'processing', enum: ['processing', 'completed', 'failed'] })
    @IsString()
    status: string;

    @ApiProperty({ description: '训练进度', example: 50 })
    @IsNumber()
    @Min(5)
    @Max(100)
    progress: number;

    @ApiPropertyOptional({ description: '错误信息' })
    @IsOptional()
    @IsString()
    error_message?: string;

    @ApiPropertyOptional({ description: '模型存储URL' })
    @IsOptional()
    @IsString()
    storage_url?: string;
} 