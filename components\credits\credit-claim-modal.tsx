"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Gift, Loader2, CheckCircle, XCircle } from "lucide-react";
import useCreditsStore from "@/store/useCreditsStore";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/components/ui/toast";
import FingerprintJS from '@fingerprintjs/fingerprintjs';

interface CreditClaimModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreditClaimModal({ isOpen, onClose }: CreditClaimModalProps) {
  const { claimBonus, isClaimingBonus } = useCreditsStore();
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const [claimResult, setClaimResult] = useState<{
    success: boolean;
    amount?: number;
    reason?: string;
  } | null>(null);

  const handleClaimBonus = async () => {
    try {
      // Generate professional device fingerprint using FingerprintJS
      const fp = await FingerprintJS.load();
      const { components, ...rest } = await fp.get();
      const result = await claimBonus(rest);
      setClaimResult(result);

      // 如果积分领取成功，使 react-query 的积分余额缓存失效
      if (result.success) {
        // 使积分余额查询失效，强制重新获取最新余额
        queryClient.invalidateQueries({ queryKey: ["credits", "balance"] });

        // 显示成功 toast
        success(
          "Credits Claimed Successfully!",
          `Make your first video now!`
        );

        // Auto close modal after 3 seconds if successful
        setTimeout(() => {
          onClose();
          setClaimResult(null);
        }, 3000);
      } else {
        // 显示失败 toast
        error(
          "Failed to Claim Credits",
          result.reason || "Something went wrong. Please try again."
        );
      }
    } catch (err) {
      console.error('Failed to claim bonus:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred. Please try again.';

      // 显示错误 toast
      error(
        "Failed to Claim Credits",
        errorMessage
      );

      setClaimResult({
        success: false,
        reason: errorMessage
      });
    }
  };

  const handleClose = () => {
    onClose();
    setClaimResult(null);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5 text-amber-500" />
            Get Your Credits
          </DialogTitle>
          <DialogDescription>
            Here are 88 credits to help you explore AI video generation. No strings attached.
          </DialogDescription>
        </DialogHeader>

        {claimResult ? (
          <div className="py-6">
            {claimResult.success ? (
              <div className="text-center space-y-4">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
                <div>
                  <h3 className="text-lg font-semibold text-green-600">
                    All set!
                  </h3>
                  <p className="text-muted-foreground">
                    {claimResult.amount} credits added to your account.
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center space-y-4">
                <XCircle className="h-12 w-12 text-red-500 mx-auto" />
                <div>
                  <h3 className="text-lg font-semibold text-red-600">
                    Something went wrong
                  </h3>
                  <p className="text-muted-foreground">
                    {claimResult.reason || 'Please try again later.'}
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="py-6">
            <div className="text-center space-y-4">
              <div className="bg-gradient-to-r from-amber-100 to-orange-100 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg p-6">
                <Gift className="h-16 w-16 text-amber-500 mx-auto mb-4" />
                <div className="text-3xl font-bold text-amber-600 dark:text-amber-400">
                  88 Credits
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Ready to use for AI video generation
                </p>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          {claimResult ? (
            <Button onClick={handleClose} className="w-full">
              {claimResult.success ? 'Got it' : 'Close'}
            </Button>
          ) : (
            <div className="flex gap-2 w-full">
              <Button variant="outline" onClick={handleClose} className="flex-1">
                Not now
              </Button>
              <Button
                onClick={handleClaimBonus}
                disabled={isClaimingBonus}
                className="flex-1 bg-gradient-to-r from-amber-500 to-orange-600 hover:brightness-110"
              >
                {isClaimingBonus ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Getting credits...
                  </>
                ) : (
                  'Get credits'
                )}
              </Button>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
