"use client"

import { useCallback, useState, useEffect } from "react"
import useFeedStore from "@/store/useFeedStore"
import { cn } from "@/lib/utils"
import type { Category } from "@/types"
import { setCookie } from 'cookies-next'

interface CategoryButtonProps {
    category: Category;
    isSelected: boolean;
}

export function CategoryItem({ category, isSelected }: CategoryButtonProps) {
    const [isSelectedLocal, setIsSelectedLocal] = useState(isSelected)
    const setCategory = useFeedStore(state => state.setCategory)
    const selectedCategoryId = useFeedStore(state => state.selectedCategoryId)

    useEffect(() => {
        const isActive = selectedCategoryId === category.id ||
            (category.id === 'all' && !selectedCategoryId)
        setIsSelectedLocal(isActive)
    }, [selectedCategoryId, category.id])

    useEffect(() => {
        setIsSelectedLocal(isSelected)
    }, [isSelected])

    const handleCategorySelect = useCallback(
        (categoryId: string) => {
            setCategory(categoryId)
            setIsSelectedLocal(true)

            // 使用更安全的方式设置 cookie
            setCookie('selected_category_id', categoryId, {
                path: '/',
                maxAge: 60 * 60 * 24 * 30, // 30天
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict'
            })
        },
        [setCategory]
    )

    return (
        <button
            key={category.id}
            className={cn(
                "w-auto px-5 py-2.5 rounded-sm text-[14px] font-bold tracking-wider uppercase whitespace-nowrap cursor-pointer transition-all duration-300",
                isSelectedLocal
                    ? "bg-zinc-800 text-white dark:bg-white dark:text-black shadow-[0_2px_10px_rgba(0,0,0,0.15)] scale-[1.02] border border-zinc-300/50 dark:border-zinc-600"
                    : "text-zinc-700 dark:text-gray-100 hover:bg-zinc-100 dark:hover:bg-gray-700/40 hover:text-zinc-900 dark:hover:text-white hover:scale-[1.02] hover:shadow-[0_2px_10px_rgba(0,0,0,0.15)] border border-transparent"
            )}
            onClick={() => handleCategorySelect(category.id)}
        >
            {category.name}
        </button>
    )
} 