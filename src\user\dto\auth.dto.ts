import { IsEmail, IsNotEmpty, IsString, Length, IsOptional } from 'class-validator';

export class SignUpDto {
    @IsEmail({}, { message: '请输入有效的邮箱地址' })
    @IsNotEmpty({ message: '邮箱地址不能为空' })
    email: string;

    @IsString({ message: '密码必须是字符串' })
    @Length(6, 20, { message: '密码长度必须在6到20个字符之间' })
    @IsNotEmpty({ message: '密码不能为空' })
    password: string;

    @IsString({ message: '用户名必须是字符串' })
    @IsOptional()
    username?: string;
}

export class SignInDto {
    @IsEmail({}, { message: '请输入有效的邮箱地址' })
    @IsNotEmpty({ message: '邮箱地址不能为空' })
    email: string;

    @IsString({ message: '密码必须是字符串' })
    @IsNotEmpty({ message: '密码不能为空' })
    password: string;
}

export class RefreshTokenDto {
    @IsString({ message: '刷新令牌必须是字符串' })
    @IsNotEmpty({ message: '刷新令牌不能为空' })
    refreshToken: string;
} 