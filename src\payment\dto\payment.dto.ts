import { IsEnum, IsNumber, IsObject, IsOptional, IsUUID } from 'class-validator';

export enum PaymentStatus {
    PENDING = 'pending',
    SUCCESS = 'success',
    FAILED = 'failed',
    REFUNDED = 'refunded',
    CANCELED = 'canceled',
    PAID = 'paid',
    COMPLETED = 'completed'
}

export enum ProductType {
    MEMBERSHIP = 'membership',
    CREDITS = 'credits',
    OTHER = 'other'
}

/**
 * 支付信息响应DTO
 */
export class PaymentResponseDto {
    @IsUUID()
    id: string;

    @IsUUID()
    user_id: string;

    @IsEnum(ProductType)
    type: ProductType;

    @IsNumber()
    amount: number;

    @IsEnum(PaymentStatus)
    status: PaymentStatus;

    @IsObject()
    @IsOptional()
    metadata?: Record<string, any>;

    created_at: Date;

    updated_at: Date;
} 