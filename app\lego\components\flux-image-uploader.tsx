"use client";

import { useRef, useState, useImperative<PERSON><PERSON><PERSON>, forwardRef } from "react";
import { X } from "lucide-react";
import { useLegoStore } from "../store";
import { uploadGenerationImage } from "@/lib/upload";

export interface FluxImageUploaderRef {
    triggerFileInput: () => void;
}

interface FluxImageUploaderProps {
    maxImages?: number; // 最大图片数量，默认为2
}

const defaultMaxImages = 4;

export const FluxImageUploader = forwardRef<FluxImageUploaderRef, FluxImageUploaderProps>(({ maxImages = defaultMaxImages }, ref) => {
    const {
        fluxImages,
        isFluxImageUploading,
        addFluxImage,
        removeFluxImage,
        setIsFluxImageUploading
    } = useLegoStore();

    const fileInputRef = useRef<HTMLInputElement>(null);
    const [uploadingIndex, setUploadingIndex] = useState<number | null>(null);

    // 处理图片上传
    const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        // 检查文件类型
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file');
            return;
        }

        // 检查文件大小 (10MB限制)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('Image size cannot exceed 10MB');
            return;
        }

        try {
            setIsFluxImageUploading(true);
            setUploadingIndex(fluxImages.length);

            // 上传到Cloudflare
            const publicUrl = await uploadGenerationImage(file);

            // 添加到FLUX图片列表
            addFluxImage(publicUrl);
        } catch (error) {
            console.error("Failed to upload image:", error);
            // Note: This component should use toast instead of alert
            // TODO: Import and use useToast hook
            alert('Failed to upload image. Please try again.');
        } finally {
            setIsFluxImageUploading(false);
            setUploadingIndex(null);
            // 清空文件输入
            if (e.target) {
                e.target.value = "";
            }
        }
    };

    // 删除图片
    const handleRemoveImage = (index: number) => {
        removeFluxImage(index);
    };

    const canAddMore = fluxImages.length < maxImages;

    // 触发文件选择
    const triggerFileInput = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
        triggerFileInput
    }));

    return (
        <div className="flex items-start gap-3">
            {/* 图片列表 - 水平排列 */}
            <div className="flex gap-2">
                {/* 已上传的图片 */}
                {fluxImages.map((imageUrl, index) => (
                    <div
                        key={index}
                        className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 group flex-shrink-0 border border-gray-200 dark:border-gray-700"
                    >
                        <img
                            src={imageUrl}
                            alt={`Reference ${index + 1}`}
                            className="w-full h-full object-cover"
                        />
                        {/* 删除按钮 */}
                        <button
                            onClick={() => handleRemoveImage(index)}
                            className="absolute top-1 right-1 w-4 h-4 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center opacity-100 md:opacity-0 md:group-hover:opacity-100 transition-opacity"
                        >
                            <X size={8} className="text-white" />
                        </button>
                    </div>
                ))}

                {/* 上传中的占位符 */}
                {isFluxImageUploading && uploadingIndex !== null && (
                    <div className="w-16 h-16 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center flex-shrink-0">
                        <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                )}
            </div>

            {/* 隐藏的文件输入 */}
            <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                disabled={!canAddMore || isFluxImageUploading}
            />
        </div>
    );
});
