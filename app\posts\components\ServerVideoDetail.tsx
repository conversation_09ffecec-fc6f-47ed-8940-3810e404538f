import { PostItemDto } from "@/types/posts"
import { VideoJsonLd } from "./VideoJsonLd"
import { ServerVideoContainer } from "./VideoContainer"
import { ServerInfoPanel } from "./InfoPanel"
import { PostStatsTracker } from "./PostStatsTracker"

interface ServerVideoDetailProps {
    post: PostItemDto
    comments: any[]
}

export function ServerVideoDetail({ post, comments }: ServerVideoDetailProps) {
    return (
        <main className="w-full h-auto sm:h-full relative overflow-auto bg-gradient-to-br from-slate-900 via-slate-800 to-slate-950 text-slate-900 dark:text-white">
            {/* 结构化数据 - 服务端渲染确保被搜索引擎抓取 */}
            <VideoJsonLd post={post} />

            {/* 统计跟踪组件 - 客户端渲染，不影响服务端渲染 */}
            <PostStatsTracker post={post} />

            <section className="flex relative px-2 py-2 flex-col lg:flex-row gap-2 h-full rounded-xl overflow-hidden z-10">
                {/* 视频容器 */}
                <ServerVideoContainer post={post} />

                {/* 信息面板 */}
                <ServerInfoPanel post={post} comments={comments} />
            </section>
        </main>
    )
}