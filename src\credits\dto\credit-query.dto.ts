import { IsEnum, IsInt, IsOptional, IsString, IsUUID, Min } from 'class-validator';
import { CreditTransactionType, CreditTransactionStatus } from '../constant';
import { Type } from 'class-transformer';

export class CreditTransactionQueryDto {
    @IsOptional()
    @IsEnum(CreditTransactionType)
    type?: CreditTransactionType;

    @IsOptional()
    @IsEnum(CreditTransactionStatus)
    status?: CreditTransactionStatus;

    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    page?: number = 1;

    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    limit?: number = 20;

    @IsOptional()
    @IsString()
    fromDate?: string;

    @IsOptional()
    @IsString()
    toDate?: string;
} 