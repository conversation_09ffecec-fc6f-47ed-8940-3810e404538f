"use client"

import { createContext, useContext } from "react"
import { User, Session } from "@supabase/supabase-js"
import useAuthStore from "@/store/useAuthStore"
import { AuthListener } from "@/providers/AuthListener"

/**
 * 认证上下文接口
 * 仅作为UI层的访问接口，委托所有实际操作给AuthStore
 */
interface AuthContextType {
    user: User | null
    session: Session | null
    isAuthenticated: boolean
    isLoading: boolean
    signIn: (email: string, password: string) => Promise<void>
    signUp: (email: string, password: string) => Promise<{ data: any; error: Error | null }>
    signOut: () => Promise<void>
    signInWithGoogle: () => Promise<void>
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | null>(null)

/**
 * 认证提供者组件
 * 使用Zustand管理状态，提供简化的UI访问接口
 */
export function AuthProvider({ children }: { children: React.ReactNode }) {
    // 直接从AuthStore读取状态和方法
    const {
        user,
        session,
        isAuthenticated,
        isLoading,
        signIn,
        signUp,
        signOut,
        signInWithGoogle
    } = useAuthStore()

    // 构造上下文值
    const contextValue = {
        user,
        session,
        isAuthenticated,
        isLoading,
        signIn,
        signUp,
        signOut,
        signInWithGoogle
    }

    return (
        <AuthContext.Provider value={contextValue}>
            {/* 添加认证监听器，负责监听Supabase认证状态变化 */}
            <AuthListener />
            {children}
        </AuthContext.Provider>
    )
}

/**
 * 使用认证上下文的Hook
 */
export const useAuth = () => {
    const context = useContext(AuthContext)
    if (!context) {
        throw new Error("useAuth must be used within an AuthProvider")
    }
    return context
} 