import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { InputSanitizer } from '@/lib/security/input-sanitizer';
import { IPRateLimiter } from '@/lib/security/rate-limiter';

// 处理POST请求 - 创建反馈 (增强安全版)
export async function POST(request: NextRequest) {
  // Security: Get client IP for rate limiting
  const clientIP = IPRateLimiter.getClientIP(request);

  try {

    // Security: Check IP-based rate limiting
    const ipRateLimit = IPRateLimiter.canSubmit(clientIP);
    if (!ipRateLimit.allowed) {
      return NextResponse.json({
        code: 429,
        message: ipRateLimit.reason || 'Too many requests',
        data: null
      }, {
        status: 429,
        headers: {
          'Retry-After': ipRateLimit.retryAfter?.toString() || '3600'
        }
      });
    }

    const supabase = await createClient();

    // 验证用户会话
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({
        code: 401,
        message: 'Authentication required',
        data: null
      }, { status: 401 });
    }

    // Security: Validate request body size and structure
    let body;
    try {
      const text = await request.text();
      if (text.length > 10000) { // 10KB limit
        return NextResponse.json({
          code: 400,
          message: 'Request body too large',
          data: null
        }, { status: 400 });
      }
      body = JSON.parse(text);
    } catch (error) {
      return NextResponse.json({
        code: 400,
        message: 'Invalid JSON format',
        data: null
      }, { status: 400 });
    }

    const { type, subject, message } = body;

    // Security: Comprehensive input validation
    const validation = InputSanitizer.validateFeedbackInput({ type, subject, message });
    if (!validation.isValid) {
      return NextResponse.json({
        code: 400,
        message: 'Invalid input data',
        data: validation.errors
      }, { status: 400 });
    }

    // Security: Record IP submission for rate limiting
    IPRateLimiter.recordSubmission(clientIP);

    // 转发请求到后端 API - 使用清理后的数据
    const sanitizedData = validation.sanitizedData!;

    const response = await fetch(`${process.env.REELAPI_SERVER_URL}/api/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
        'X-Client-IP': clientIP, // Pass client IP to backend
        'X-Request-ID': crypto.randomUUID(), // Add request ID for tracking
      },
      body: JSON.stringify(sanitizedData),
    });

    if (!response.ok) {
      // Security: Don't expose internal error details
      const status = response.status;
      let message = 'Failed to submit feedback';

      if (status === 429) {
        message = 'Too many submissions. Please try again later.';
      } else if (status === 400) {
        message = 'Invalid feedback data';
      } else if (status === 401) {
        message = 'Authentication failed';
      }

      return NextResponse.json({
        code: status,
        message,
        data: null
      }, { status });
    }

    const data = await response.json();

    // Security: Sanitize response data before sending to client
    const sanitizedResponse = {
      ...data,
      // Remove any sensitive fields if they exist
    };

    return NextResponse.json(sanitizedResponse);

  } catch (error) {
    // Security: Log error details server-side but don't expose to client
    console.error('Feedback submission error:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      clientIP,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      code: 500,
      message: 'Internal server error',
      data: null
    }, { status: 500 });
  }
}


