'use client';

import { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, RefreshCw, Settings } from 'lucide-react';
import { adminModelApi } from '@/lib/api/admin-model';
import { ModelTable } from './components/ModelTable';
import { ModelEditModal } from './components/ModelEditModal';
import { AddModelModal } from './components/AddModelModal';
import { toast } from 'sonner';
import type { Model } from '@/types/model';

export default function AdminModelsPage() {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedModel, setSelectedModel] = useState<Model | null>(null);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const queryClient = useQueryClient();

    // 获取所有模型列表
    const { data: modelsData, isLoading, refetch } = useQuery({
        queryKey: ['admin-models', searchQuery],
        queryFn: () => adminModelApi.getAllModels(searchQuery),
        staleTime: 1000 * 60 * 5, // 5分钟
    });

    // 处理搜索
    const handleSearch = (value: string) => {
        setSearchQuery(value);
    };

    // 处理编辑模型
    const handleEditModel = (model: Model) => {
        setSelectedModel(model);
        setIsEditModalOpen(true);
    };

    // 处理删除模型
    const handleDeleteModel = async (modelId: string) => {
        if (!confirm('Are you sure you want to delete this model?')) {
            return;
        }

        try {
            await adminModelApi.deleteModel(modelId);
            toast.success('Model deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['admin-models'] });
        } catch (error) {
            console.error('Failed to delete model:', error);
            toast.error('Failed to delete model');
        }
    };

    // 处理模型更新成功
    const handleModelUpdated = () => {
        queryClient.invalidateQueries({ queryKey: ['admin-models'] });
        setIsEditModalOpen(false);
        setSelectedModel(null);
        toast.success('Model updated successfully');
    };

    // 处理模型创建成功
    const handleModelCreated = () => {
        queryClient.invalidateQueries({ queryKey: ['admin-models'] });
        setIsAddModalOpen(false);
        toast.success('Model created successfully');
    };



    return (
        <div className="container mx-auto py-8 px-4">
            <div className="mb-8">
                <h1 className="text-3xl font-bold mb-2">Model Management</h1>
                <p className="text-muted-foreground">
                    Manage video generation models, pricing, and configurations
                </p>
            </div>

            {/* 统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Models</CardTitle>
                        <Settings className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{modelsData?.total || 0}</div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Public Models</CardTitle>
                        <Badge variant="secondary">Active</Badge>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {modelsData?.models.filter(m => m.is_public).length || 0}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">fal.ai Models</CardTitle>
                        <Badge variant="outline">Source</Badge>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {modelsData?.models.filter(m => m.source === 'fal.ai').length || 0}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Custom Models</CardTitle>
                        <Badge variant="outline">Custom</Badge>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {modelsData?.models.filter(m => m.source !== 'fal.ai').length || 0}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* 操作栏 */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                        <Input
                            placeholder="Search models by name or description..."
                            value={searchQuery}
                            onChange={(e) => handleSearch(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </div>

                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        onClick={() => refetch()}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>

                    <Button onClick={() => setIsAddModalOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Model
                    </Button>
                </div>
            </div>

            {/* 模型表格 */}
            <ModelTable
                models={modelsData?.models || []}
                isLoading={isLoading}
                onEdit={handleEditModel}
                onDelete={handleDeleteModel}
            />

            {/* 编辑模型弹窗 */}
            <ModelEditModal
                model={selectedModel}
                isOpen={isEditModalOpen}
                onClose={() => {
                    setIsEditModalOpen(false);
                    setSelectedModel(null);
                }}
                onSuccess={handleModelUpdated}
            />

            {/* 添加模型弹窗 */}
            <AddModelModal
                isOpen={isAddModalOpen}
                onClose={() => setIsAddModalOpen(false)}
                onSuccess={handleModelCreated}
            />
        </div>
    );
}
