import React from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Metadata } from 'next';
import { getBlogPostById } from '@/lib/blog';
import BlogForm from '../../components/BlogForm';

export const metadata: Metadata = {
    title: 'Edit Blog Post - Reelmind Admin',
    description: 'Edit your blog post content and settings',
};

export default async function EditBlogPostPage({
    params,
}: {
    params: { id: string };
}) {
    const post = await getBlogPostById(params.id);

    if (!post) {
        notFound();
    }

    return (
        <div className="container mx-auto px-4 py-8 max-w-screen-xl">
            <div className="flex items-center justify-between mb-8">
                <h1 className="text-2xl font-bold">Edit Blog Post</h1>
                <div className="flex gap-2">
                    <Link
                        href="/admin/blog"
                        className="px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600"
                    >
                        Cancel
                    </Link>
                </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <BlogForm mode="edit" post={post} />
            </div>
        </div>
    );
} 