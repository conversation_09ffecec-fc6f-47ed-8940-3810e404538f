export default function Placeholder() {
    return (
        Array(3).fill(0).map((_, index) => (
            <div key={index} className="rounded-xl overflow-hidden border border-primary/30 bg-card shadow-lg p-6 animate-pulse">
                <div className="h-8 bg-muted rounded mb-4 w-1/3"></div>
                <div className="h-10 bg-muted rounded mb-6 w-1/2"></div>
                <div className="h-10 bg-muted rounded mb-6"></div>
                <div className="space-y-3">
                    {Array(5).fill(0).map((_, i) => (
                        <div key={i} className="flex items-start gap-2">
                            <div className="h-5 w-5 bg-muted rounded-full shrink-0 mt-0.5"></div>
                            <div className="h-5 bg-muted rounded w-full"></div>
                        </div>
                    ))}
                </div>
            </div>
        ))
    )
}
