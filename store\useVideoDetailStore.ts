import { create } from 'zustand';
import { PostItemDto, Comment as CommentType } from '@/types/posts';
import { postApi } from '@/lib/api/post';
import { favoriteApi, FavoriteTargetType } from '@/lib/api/favorite';
import useUserStore from './useUserStore'; // 导入用户状态管理

// 初始状态
const initialState = {
    post: null,
    postLoading: false,
    error: null,
    comments: [],
    loadingComments: false,
    submittingComment: false,
    commentContent: '',
    isLiking: false,
    isLiked: false,
    isFavorite: false,
    isTogglingFavorite: false,
    checkingFavorite: false,
};

interface VideoDetailState {
    // 当前加载的帖子
    post: PostItemDto | null;
    // 帖子加载状态
    postLoading: boolean;
    // 错误信息
    error: string | null;
    // 评论列表
    comments: CommentType[];
    // 评论加载状态
    loadingComments: boolean;
    // 评论提交状态
    submittingComment: boolean;
    // 评论内容
    commentContent: string;
    // 点赞状态
    isLiking: boolean;
    // 是否点赞
    isLiked: boolean;
    // 收藏状态
    isFavorite: boolean;
    // 收藏加载状态
    isTogglingFavorite: boolean;
    // 收藏检查加载状态
    checkingFavorite: boolean;

    // Actions
    fetchPost: (postId: string) => Promise<void>;
    fetchComments: (postId: string) => Promise<void>;
    submitComment: (postId: string, content: string) => Promise<void>;
    setCommentContent: (content: string) => void;
    likePost: (postId: string) => Promise<void>;
    unlikePost: (postId: string) => Promise<void>;
    checkFavorite: (postId: string) => Promise<void>;
    addFavorite: (postId: string) => Promise<void>;
    removeFavorite: (postId: string) => Promise<void>;
    resetState: () => void;
}

export const useVideoDetailStore = create<VideoDetailState>((set, get) => ({
    // 初始状态
    ...initialState,

    // 获取帖子详情
    fetchPost: async (postId: string) => {
        try {
            set({ postLoading: true, error: null });

            // 从用户状态商店获取当前用户ID
            const currentUserId = useUserStore.getState().user?.id;

            const response = await postApi.getPost(postId);

            if (response && response.data) {
                const post = response.data as unknown as PostItemDto;

                // 检查当前用户是否点赞该帖子
                // 从likes数组中查找是否存在当前用户的ID
                const userLiked = post.likes?.some(like => like.user_id === currentUserId) || false;

                set({
                    post,
                    isLiked: userLiked,
                    postLoading: false,
                });
            } else {
                set({ error: 'Cannot load post data', postLoading: false });
            }
        } catch (err) {
            console.error('Error fetching post:', err);
            set({ error: 'Failed to load post', postLoading: false });
        }
    },

    // 获取评论
    fetchComments: async (postId: string) => {
        try {
            set({ loadingComments: true });

            const response = await postApi.getPostComments(postId);

            if (response && response.data) {
                set({
                    comments: response.data.comments as unknown as CommentType[],
                    loadingComments: false
                });
            } else {
                set({ loadingComments: false });
            }
        } catch (err) {
            console.error('Failed to load comments:', err);
            set({ loadingComments: false });
        }
    },

    // 提交评论
    submitComment: async (postId: string, content: string) => {
        if (!content.trim() || get().submittingComment) return;

        try {
            set({ submittingComment: true });
            await postApi.createComment({
                content,
                targetId: postId,
                targetType: 'post'
            });

            // 评论提交成功后，重新获取评论列表并清空输入框
            await get().fetchComments(postId);
            set({ commentContent: '', submittingComment: false });
        } catch (err) {
            console.error('Failed to submit comment:', err);
            set({ submittingComment: false });
        }
    },

    // 设置评论内容
    setCommentContent: (content: string) => {
        set({ commentContent: content });
    },

    // 点赞帖子
    likePost: async (postId: string) => {
        if (get().isLiking) return;

        try {
            set({ isLiking: true });

            // 从用户状态商店获取当前用户ID
            const currentUserId = useUserStore.getState().user?.id;

            if (!currentUserId) {
                return;
            }

            await postApi.likePost(postId);

            // 更新帖子点赞状态和数量
            const { post } = get();
            if (post) {
                // 创建新的likes数组，添加当前用户的点赞记录
                const updatedLikes = [...(post.likes || [])];

                // 确保不重复添加
                if (!updatedLikes.some(like => like.user_id === currentUserId)) {
                    updatedLikes.push({ user_id: currentUserId });
                }

                set({
                    isLiked: true,
                    post: {
                        ...post,
                        like_count: post.like_count + 1,
                        likes: updatedLikes
                    }
                });
            }
        } catch (err) {
            console.error('Error liking post:', err);
        } finally {
            set({ isLiking: false });
        }
    },

    // 取消点赞
    unlikePost: async (postId: string) => {
        if (get().isLiking) return;

        try {
            set({ isLiking: true });

            // 从用户状态商店获取当前用户ID
            const currentUserId = useUserStore.getState().user?.id;

            if (!currentUserId) {
                return;
            }

            await postApi.unlikePost(postId);

            // 更新帖子点赞状态和数量
            const { post } = get();
            if (post) {
                // 从likes数组中移除当前用户的点赞记录
                const updatedLikes = post.likes?.filter(like => like.user_id !== currentUserId) || [];

                set({
                    isLiked: false,
                    post: {
                        ...post,
                        like_count: Math.max(0, post.like_count - 1),
                        likes: updatedLikes
                    }
                });
            }
        } catch (err) {
            console.error('Error unliking post:', err);
        } finally {
            set({ isLiking: false });
        }
    },

    // 检查收藏状态
    checkFavorite: async (postId: string) => {
        try {
            set({ checkingFavorite: true });
            const response = await favoriteApi.checkFavorite({
                post_id: postId,
                item_type: FavoriteTargetType.POST
            });

            set({
                isFavorite: response.favorite,
                checkingFavorite: false
            });
        } catch (err) {
            console.error('Checking favorite status failed:', err);
            set({ checkingFavorite: false });
        }
    },

    // 添加收藏
    addFavorite: async (postId: string) => {
        if (get().isTogglingFavorite) return;

        try {
            set({ isTogglingFavorite: true });
            await favoriteApi.addFavorite({
                post_id: postId,
                item_type: FavoriteTargetType.POST
            });

            set({ isFavorite: true });
        } catch (err) {
            console.error('Failed to add favorite:', err);
        } finally {
            set({ isTogglingFavorite: false });
        }
    },

    // 移除收藏
    removeFavorite: async (postId: string) => {
        if (get().isTogglingFavorite) return;

        try {
            set({ isTogglingFavorite: true });
            await favoriteApi.removeFavorite({
                post_id: postId,
                item_type: FavoriteTargetType.POST
            });

            set({ isFavorite: false });
        } catch (err) {
            console.error('Failed to remove favorite:', err);
        } finally {
            set({ isTogglingFavorite: false });
        }
    },

    // 重置状态
    resetState: () => {
        // 使用初始状态重置所有值
        set({
            ...initialState,
            // 确保清除所有可能的引用类型数据
            post: null,
            comments: [],
            error: null,
            commentContent: '',
        });
    }
})); 