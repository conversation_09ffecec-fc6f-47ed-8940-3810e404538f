"use client"

import { memo, useState, useCallback } from "react";
import type { VideoTask } from "@/types/video-task";
import { PendingCard } from "@/app/create/components/cards/PendingCard";
import { ProcessingCard } from "@/app/create/components/cards/ProcessingCard";
import { CompletedCard } from "@/app/create/components/cards/CompletedCard";
import { FailedCard } from "@/app/create/components/cards/FailedCard";
import "./history-card.css";

// History card component props
export interface HistoryCardProps {
    video: VideoTask;
    onClick: () => void;
    onPublishClick: (video: VideoTask, e: React.MouseEvent) => void;
    onShareClick?: (video: VideoTask, e: React.MouseEvent) => void;
    onDownloadClick: (video: VideoTask) => void;
    onRemixClick?: (videoId: string) => void;
    isDownloading?: boolean;
}

/**
 * HistoryCard Component - Container component that renders the appropriate card based on task status
 * Follows single responsibility principle by delegating rendering to specialized components
 */
export const HistoryCard = memo(({
    video,
    onClick,
    onPublishClick,
    onShareClick,
    onDownloadClick,
    onRemixClick,
    isDownloading = false
}: HistoryCardProps) => {
    // 本地状态，用于跟踪当前任务状态和完整的视频数据
    const [currentStatus, setCurrentStatus] = useState(video.status);
    const [currentVideo, setCurrentVideo] = useState(video);

    // 处理子组件状态变更的回调函数
    const handleStatusChange = useCallback((videoId: string, newStatus: string, updatedVideo?: VideoTask) => {
        if (videoId === video.id && newStatus !== currentStatus) {
            // 类型转换为 TaskStatus
            setCurrentStatus(newStatus as "pending" | "queued" | "processing" | "completed" | "failed" | "canceled");

            // 如果提供了完整的更新数据，使用它；否则只更新状态
            if (updatedVideo) {
                setCurrentVideo(updatedVideo);
            } else {
                setCurrentVideo(prev => ({
                    ...prev,
                    status: newStatus as "pending" | "queued" | "processing" | "completed" | "failed" | "canceled"
                }));
            }
        }
    }, [video.id, currentStatus]);

    // Determine which card to render based on task status
    const renderCard = () => {
        switch (currentStatus) {
            case "pending":
            case "queued":
                return (
                    <PendingCard
                        video={currentVideo}
                        onStatusChange={handleStatusChange}
                    />
                );
            case "processing":
                return (
                    <ProcessingCard
                        video={currentVideo}
                        onStatusChange={handleStatusChange}
                    />
                );
            case "completed":
                return (
                    <CompletedCard
                        video={currentVideo}
                        onPublishClick={onPublishClick}
                        onShareClick={onShareClick}
                        onDownloadClick={onDownloadClick}
                        onRemixClick={onRemixClick}
                        isDownloading={isDownloading}
                    />
                );
            case "failed":
                return (
                    <FailedCard
                        video={currentVideo}
                        onRemixClick={onRemixClick}
                    />
                );
            default:
                return (
                    <PendingCard
                        video={currentVideo}
                        onStatusChange={handleStatusChange}
                    />
                );
        }
    };

    return (
        <div
            className="flex flex-col relative"
            onClick={onClick}
        >
            {renderCard()}
        </div>
    );
});