# ReelMind 服务端项目架构概览

## 1. 项目概述

ReelMind 服务端是一个基于 NestJS 框架开发的视频生成和管理系统，使用 TypeScript 语言，遵循模块化、清晰的代码组织结构和设计模式。系统提供了视频生成、用户管理、会员管理、支付系统等功能。

## 2. 技术栈

- **框架**: NestJS
- **语言**: TypeScript
- **数据库**: PostgreSQL (通过 Supabase 进行访问)
- **认证**: JWT
- **错误监控**: Sentry
- **日志**: 自定义日志系统

## 3. 项目结构

```
src/
├── app.module.ts            # 应用根模块
├── main.ts                  # 应用入口
├── common/                  # 公共组件、服务、常量等
├── user/                    # 用户模块
├── membership/              # 会员模块
├── generation/              # 视频生成模块
├── credits/                 # 积分模块
├── payment/                 # 支付模块
├── post/                    # 帖子模块
├── video/                   # 视频模块
├── tags/                    # 标签模块
├── models/                  # 模型模块
└── scheduler/               # 调度器模块
```

## 4. 模块依赖关系

- **AppModule**: 作为根模块，导入所有其他模块
- **GenerationModule**: 依赖于 UserModule, MembershipModule, CreditsModule, ErrorAuditModule
- **UserModule**: 提供用户身份验证和用户信息管理
- **MembershipModule**: 提供会员服务，控制用户权限和服务优先级
- **CreditsModule**: 管理积分系统，控制资源消费
- **PaymentModule**: 处理支付相关功能
- **VideoModule**: 管理视频资源
- **PostModule**: 管理帖子内容
- **TagsModule**: 管理标签系统
- **ModelsModule**: 管理模型数据
- **SchedulerModule**: 处理定时任务

## 5. Generation 模块详解

Generation 模块是系统的核心功能之一，负责处理视频生成任务。

### 5.1 文件结构

```
generation/
├── generation.module.ts      # 模块定义
├── generation.controller.ts  # API控制器
├── generation.service.ts     # 业务逻辑服务
├── dto/                      # 数据传输对象
│   ├── gen-video.dto.ts      # 视频生成请求DTO
│   ├── video-task.dto.ts     # 视频任务DTO
│   └── cancel.dto.ts         # 取消任务DTO
└── sql/                      # SQL定义
    └── videos.sql            # 视频表结构定义
```

### 5.2 功能流程

1. **视频生成流程**:

   - 用户提交视频生成请求 (`createVideoGenerationTask`)
   - 验证用户权限和积分余额
   - 消费用户积分 (`consumeCreditsForVideoGeneration`)
   - 创建视频生成任务并存入数据库
   - 任务由外部服务处理 (Python视频生成服务)
   - 外部服务完成后调用完成接口 (`finishVideoGenerationTask`)
   - 如生成成功，创建视频记录；如失败，退还用户积分

2. **任务管理流程**:
   - 查询任务列表 (`getUserTasks`)
   - 查询任务详情 (`getTaskById`)
   - 取消任务 (`cancelTask`)，并退还积分

### 5.3 关键API

| 端点                                | 方法 | 描述                 | 权限    |
| ----------------------------------- | ---- | -------------------- | ------- |
| `/generation/gen-video`             | POST | 创建视频生成任务     | JWT认证 |
| `/generation/user-tasks`            | GET  | 获取用户任务列表     | JWT认证 |
| `/generation/task/:taskId`          | GET  | 获取任务详情         | JWT认证 |
| `/generation/task/cancel`           | POST | 取消任务             | JWT认证 |
| `/generation/gen_video_task/pop`    | POST | 获取下一个待处理任务 | API密钥 |
| `/generation/gen_video_task/finish` | POST | 完成视频生成任务     | API密钥 |

### 5.4 数据流转

1. **视频生成请求**:

   - 前端 → GenerationController → GenerationService → 数据库
   - GenerationService → CreditsService (积分扣除)
   - GenerationService → MembershipService (获取优先级)

2. **视频生成完成**:

   - Python服务 → GenerationController → GenerationService → 数据库
   - 生成失败: GenerationService → CreditsService (积分退还)

3. **任务取消**:
   - 前端 → GenerationController → GenerationService → 数据库
   - GenerationService → CreditsService (积分退还)

## 6. 关键业务逻辑

### 6.1 积分系统

- 视频生成消耗积分，不同清晰度消耗不同积分
- 任务失败或取消时退还积分
- 使用幂等性机制确保不重复扣费
- 通过CreditsService统一管理积分交易

### 6.2 会员优先级

- 会员等级决定任务处理优先级
- 通过MembershipService获取用户会员等级和对应优先级

### 6.3 错误处理

- 使用ErrorAuditService记录系统错误
- 对关键错误（如退款失败）使用更高级别的日志和通知机制
- 使用自定义异常过滤器统一处理异常

## 7. 数据库设计

### 7.1.视频任务表 (video_gen_tasks)

- 存储视频生成任务信息
- 记录任务状态、进度、参数等信息
- 与用户表关联

### 7.2.视频表 (videos)

```sql
create table public.videos (
  id uuid not null default gen_random_uuid (),
  user_id uuid null,
  task_id uuid null,
  url text null,
  prompt text null,
  source text null,
  input_params jsonb null,
  created_at timestamp with time zone not null default now(),
  constraint videos_pkey primary key (id),
  constraint videos_task_id_fkey foreign KEY (task_id) references video_gen_tasks (id),
  constraint videos_user_id_fkey foreign KEY (user_id) references user_profiles (user_id)
) TABLESPACE pg_default;
```

- 存储视频信息
- 与任务表和用户表关联

## 8. 关键代码文件索引

| 文件路径                                     | 描述           | 关键功能                     |
| -------------------------------------------- | -------------- | ---------------------------- |
| `src/generation/generation.service.ts`       | 视频生成服务   | 创建任务、完成任务、积分管理 |
| `src/generation/generation.controller.ts`    | 视频生成控制器 | API端点定义、参数校验        |
| `src/generation/dto/gen-video.dto.ts`        | 视频生成DTO    | 定义视频生成请求参数         |
| `src/generation/dto/video-task.dto.ts`       | 视频任务DTO    | 定义任务相关数据结构         |
| `src/credits/credits.service.ts`             | 积分服务       | 积分消费、退还、交易记录     |
| `src/membership/membership.service.ts`       | 会员服务       | 会员等级、权限、优先级       |
| `src/common/services/error-audit.service.ts` | 错误审计服务   | 错误记录、严重性分级         |

## 9. 系统优化点

1. 使用幂等性设计确保任务不重复处理
2. 完善的错误处理和日志机制
3. 使用积分系统控制资源消费
4. 会员优先级机制确保高级用户体验
5. 模块化设计便于扩展和维护
