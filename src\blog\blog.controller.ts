import {
    Controller,
    Post,
    Body,
} from '@nestjs/common';
import { BlogService } from './blog.service';
import {
    CreateBlogPostDto,
    BlogPostResponseDto,
} from './dto/blog-post.dto';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('博客')
@Controller('blog')
export class BlogController {
    constructor(private readonly blogService: BlogService) {}

    /**
     * 管理员创建博客文章
     * @param req 请求对象，包含用户信息
     * @param createBlogPostDto 创建博客文章的参数
     */
    @Post('admin/create')
    async createBlogPost(
        @Body() createBlogPostDto: CreateBlogPostDto
    ): Promise<BlogPostResponseDto> {
        const userId = '406df7a4-aaa1-48e7-a62a-a4a8b2e82012';

        return this.blogService.createBlogPost(userId, createBlogPostDto);
    }
}
