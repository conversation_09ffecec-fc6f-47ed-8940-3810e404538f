import { Injectable, Inject, BadRequestException, InternalServerErrorException, Logger, forwardRef } from '@nestjs/common';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { SupabaseClient } from '@supabase/supabase-js';
import {
    CreditTransactionStatus,
    CreditTransactionType,
    CREDITS_CONFIG,
} from './constant';
import { CreateCreditTransactionDto, ConsumeCreditsDto } from './dto/credit-transaction.dto';
import { CreditTransactionQueryDto } from './dto/credit-query.dto';
import { PaymentService } from '../payment/payment.service';
import { CreditStrategyFactory } from './strategies';
import { AntiAbuseService, DeviceFingerprintData } from './anti-abuse.service';

@Injectable()
export class CreditsService {
    private readonly logger = new Logger(CreditsService.name);

    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        @Inject(forwardRef(() => PaymentService))
        private readonly paymentService: PaymentService,
        private readonly strategyFactory: CreditStrategyFactory,
        private readonly antiAbuseService: AntiAbuseService
    ) {}

    /**
     * 发起购买积分
     * 注意：这里创建一个待支付的交易记录，并返回支付URL
     */
    async purchaseCredits(
        userId: string,
        amountUsd: number,
        origin: string,
    ): Promise<any> {
        try {
            if (amountUsd <= 0) {
                throw new BadRequestException('购买金额必须大于0');
            }

            // 使用PaymentService创建积分购买支付
            const { checkoutUrl } = await this.paymentService.createCreditsPurchase(
                userId,
                amountUsd,
                origin
            );

            // 计算积分数量（1USD = 100积分）
            const creditsAmount = amountUsd * CREDITS_CONFIG.EXCHANGE_RATE;

            return {
                checkoutUrl,
                amountUsd,
                creditsAmount,
            };
        } catch (error) {
            this.logger.error(`购买积分失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 创建积分交易记录
     */
    async createCreditTransaction(
        createCreditTransactionDto: CreateCreditTransactionDto,
    ): Promise<any> {
        try {
            const { userId, type, amount, description, paymentId, status = CreditTransactionStatus.COMPLETED } = createCreditTransactionDto;
            // 使用add_credits存储过程添加积分
            const { data, error } = await this.supabase.rpc('add_credits', {
                p_user_id: userId,
                p_amount: amount,
                p_type: type,
                p_description: description || '',
                p_payment_id: paymentId || null,
                p_status: status
            });

            if (error) {
                this.logger.error(`创建积分交易记录失败: ${error.message}`, error);
                throw new InternalServerErrorException(`创建积分交易记录失败: ${error.message}`);
            }

            // 检查是否返回"交易已存在"的结果
            if (data.status === 'already_exists') {
                this.logger.log(`交易记录已存在: ${paymentId || '未指定支付ID'}`);
                return {
                    status: 'already_exists',
                    transaction: data.transaction,
                    balance: data.balance,
                    message: data.message
                };
            }

            return {
                transaction: data.transaction,
                newBalance: data.new_balance
            };
        } catch (error) {
            this.logger.error(`创建积分交易记录失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 获取用户积分余额
     */
    async getUserCreditBalance(userId: string): Promise<number> {
        try {
            // 从用户余额表中直接查询余额
            const { data, error } = await this.supabase
                .from('user_credit_balances')
                .select('credits')
                .eq('user_id', userId)
                .single();

            if (error) {
                // 如果用户没有余额记录，可能是新用户或者数据迁移问题
                if (error.code === 'PGRST116') { // 单一行记录的错误代码
                    // 尝试初始化新用户的余额记录
                    await this.initializeUserBalance(userId);
                    // 重新查询
                    const { data: newData, error: newError } = await this.supabase
                        .from('user_credit_balances')
                        .select('credits')
                        .eq('user_id', userId)
                        .single();

                    if (newError) {
                        this.logger.error(`初始化后仍无法获取用户积分余额: ${newError.message}`, newError.stack);
                        throw new InternalServerErrorException(`获取用户积分余额失败: ${newError.message}`);
                    }

                    return newData?.credits || 0;
                }

                this.logger.error(`获取用户积分余额失败: ${error.message}`, error.stack);
                throw new InternalServerErrorException(`获取用户积分余额失败: ${error.message}`);
            }

            return data?.credits || 0;
        } catch (error) {
            this.logger.error(`获取用户积分余额失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 初始化用户余额记录（新用户或迁移时使用）
     */
    private async initializeUserBalance(userId: string): Promise<void> {
        try {
            // 计算用户的初始余额（从交易记录）
            const { data: transactionsData, error: transactionsError } = await this.supabase
                .from('credit_transactions')
                .select('amount')
                .eq('user_id', userId)
                .eq('status', CreditTransactionStatus.COMPLETED);

            if (transactionsError) {
                this.logger.error(`计算用户初始余额失败: ${transactionsError.message}`);
                throw new InternalServerErrorException(`初始化用户余额失败: ${transactionsError.message}`);
            }

            // 计算初始余额
            const initialBalance = transactionsData.reduce((sum, transaction) => sum + transaction.amount, 0);

            // 获取最后一笔交易的ID
            const { data: lastTransactionData, error: lastTransactionError } = await this.supabase
                .from('credit_transactions')
                .select('id')
                .eq('user_id', userId)
                .eq('status', CreditTransactionStatus.COMPLETED)
                .order('created_at', { ascending: false })
                .limit(1)
                .single();

            if (lastTransactionError && lastTransactionError.code !== 'PGRST116') {
                this.logger.error(`获取最后一笔交易失败: ${lastTransactionError.message}`);
                throw new InternalServerErrorException(`初始化用户余额失败: ${lastTransactionError.message}`);
            }

            // 创建用户余额记录
            const { error: insertError } = await this.supabase
                .from('user_credit_balances')
                .insert([{
                    user_id: userId,
                    credits: initialBalance,
                    last_transaction_id: lastTransactionData?.id || null,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }]);

            if (insertError) {
                this.logger.error(`创建用户余额记录失败: ${insertError.message}`);
                throw new InternalServerErrorException(`初始化用户余额失败: ${insertError.message}`);
            }
        } catch (error) {
            this.logger.error(`初始化用户余额失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 获取用户积分交易记录
     */
    async getCreditTransactions(
        userId: string,
        queryDto: CreditTransactionQueryDto,
    ): Promise<any> {
        try {
            const { type, status, page = 1, limit = 20, fromDate, toDate } = queryDto;
            const offset = (page - 1) * limit;

            let query = this.supabase
                .from('credit_transactions')
                .select('*', { count: 'exact' })
                .eq('user_id', userId);

            if (type) {
                query = query.eq('type', type);
            }

            if (status) {
                query = query.eq('status', status);
            }

            if (fromDate) {
                query = query.gte('created_at', fromDate);
            }

            if (toDate) {
                query = query.lte('created_at', toDate);
            }

            const { data, error, count } = await query
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) {
                this.logger.error(`获取用户积分交易记录失败: ${error.message}`, error.stack);
                throw new InternalServerErrorException(`获取用户积分交易记录失败: ${error.message}`);
            }

            return {
                transactions: data,
                pagination: {
                    total: count || 0,
                    page,
                    limit,
                },
            };
        } catch (error) {
            this.logger.error(`获取用户积分交易记录失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 消费积分
     */
    async consumeCredits(consumeDto: ConsumeCreditsDto): Promise<any> {
        const { userId, type, amount, description, paymentId } = consumeDto;

        try {
            // 检查参数
            if (amount <= 0) {
                throw new BadRequestException('消费积分必须大于零');
            }

            // 检查用户积分余额是否足够
            const balance = await this.getUserCreditBalance(userId);
            if (balance < amount) {
                throw new BadRequestException('积分余额不足');
            }

            // 使用乐观锁和事务来确保操作的原子性
            // 由于Supabase JS客户端不直接支持事务，我们需要使用自定义SQL
            const { data, error } = await this.supabase.rpc('consume_credits', {
                p_user_id: userId,
                p_amount: amount,
                p_type: type,
                p_description: description || '',
                p_payment_id: paymentId || null
            });

            if (error) {
                // 处理乐观锁冲突
                if (error.message.includes('optimistic lock') || error.message.includes('version mismatch')) {
                    this.logger.warn(`乐观锁冲突，尝试重新消费积分: ${error.message}`);
                    // 可以选择重试或返回特定错误
                    throw new BadRequestException('操作冲突，请重试');
                }

                this.logger.error(`消费积分失败: ${error.message}`, error.stack);
                throw new InternalServerErrorException(`消费积分失败: ${error.message}`);
            }

            return {
                transaction: data.transaction,
                remainingBalance: data.new_balance,
            };
        } catch (error) {
            this.logger.error(`消费积分失败: ${error.message}`, error.stack);
            throw error;
        }
    }



    /**
     * 会员首次订阅时发放积分
     * 在用户完成会员订阅后调用此方法
     * @param userId 用户ID
     * @param planId 会员计划ID
     * @param subscriptionId 订阅ID
     * @param plan 会员计划对象（可选，如果提供则不再查询数据库）
     * @returns 交易详情
     */
    async grantMembershipInitialCredits(
        userId: string,
        planId: string,
        paymentId: string,
        plan?: any,
    ): Promise<any> {
        try {
            // 使用策略模式处理会员首次积分发放
            const membershipInitialStrategy = this.strategyFactory.getStrategy(CreditTransactionType.MEMBERSHIP_INITIAL);

            // 执行积分发放
            const result = await membershipInitialStrategy.execute(userId, {
                planId,
                paymentId,
                plan, // 传递已查询的plan对象，避免重复查询
            });

            return result;
        } catch (error) {
            this.logger.error(`发放会员首次开通积分失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 会员月度积分发放
     * 在会员订阅周期更新时调用此方法
     * @param userId 用户ID
     * @param planId 会员计划ID
     * @param planOrParams 会员计划对象或参数对象
     * @returns 交易详情
     */
    async grantMembershipMonthlyCredits(
        userId: string,
        planId: string,
        planOrParams?: any,
    ): Promise<any> {
        try {
            // 使用策略模式处理会员月度积分发放
            const membershipMonthlyStrategy = this.strategyFactory.getStrategy(CreditTransactionType.MEMBERSHIP_MONTHLY);

            // 准备参数
            let params: any = { planId };

            // 如果传入的是对象且包含planId属性，则视为完整的参数对象
            if (planOrParams && typeof planOrParams === 'object') {
                if (planOrParams.planId) {
                    // 这是一个完整的参数对象
                    params = planOrParams;
                } else {
                    // 这是一个plan对象
                    params.plan = planOrParams;
                }
            }

            // 执行积分发放
            const result = await membershipMonthlyStrategy.execute(userId, params);

            return result;
        } catch (error) {
            this.logger.error(`发放会员月度积分失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 给新用户发放奖励积分
     */
    async grantNewUserBonus(userId: string): Promise<any> {
        try {
            const bonusAmount = CREDITS_CONFIG.NEW_USER_BONUS_AMOUNT; // 新用户奖励88积分

            // 检查是否已经发放过新用户积分
            const { data: existingBonus, error: checkError } = await this.supabase
                .from('credit_transactions')
                .select('id')
                .eq('user_id', userId)
                .eq('type', CreditTransactionType.NEW_USER_BONUS)
                .limit(1);

            if (checkError) {
                this.logger.error(`检查新用户奖励状态失败: ${checkError.message}`, checkError.stack);
                throw new InternalServerErrorException(`检查新用户奖励状态失败: ${checkError.message}`);
            }

            // 如果已经发放过，则返回已存在信息
            if (existingBonus && existingBonus.length > 0) {
                return {
                    status: 'exists',
                    message: '已经发放过新用户奖励积分'
                };
            }

            // 使用add_credits存储过程记录积分奖励
            const { data, error } = await this.supabase.rpc('add_credits', {
                p_user_id: userId,
                p_amount: bonusAmount,
                p_type: CreditTransactionType.NEW_USER_BONUS,
                p_description: `新用户注册奖励${bonusAmount}积分`,
                p_status: CreditTransactionStatus.COMPLETED
            });

            if (error) {
                this.logger.error(`发放新用户奖励积分失败: ${error.message}`, error.stack);
                throw new InternalServerErrorException(`发放新用户奖励积分失败: ${error.message}`);
            }

            return {
                transaction: data.transaction,
                newBalance: data.new_balance,
                bonusAmount
            };
        } catch (error) {
            this.logger.error(`发放新用户奖励积分失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 给用户发放发帖奖励
     */
    async grantPostReward(userId: string): Promise<any> {
        try {
            const rewardAmount = 50; // 发帖奖励50积分

            // 检查今日是否已经超过发帖奖励次数限制
            const { count, error: countError } = await this.supabase
                .from('credit_transactions')
                .select('*', { count: 'exact', head: true })
                .eq('user_id', userId)
                .eq('type', CreditTransactionType.POST_REWARD)
                .eq('status', CreditTransactionStatus.COMPLETED)
                .gte('created_at', new Date(new Date().setHours(0, 0, 0, 0)).toISOString())
                .lte('created_at', new Date(new Date().setHours(23, 59, 59, 999)).toISOString());

            if (countError) {
                this.logger.error(`检查发帖奖励次数失败: ${countError.message}`, countError.stack);
                throw new InternalServerErrorException(`检查发帖奖励次数失败: ${countError.message}`);
            }

            // 如果已经超过每日限制，则返回超限信息
            if (count >= CREDITS_CONFIG.DAILY_POST_REWARD_LIMIT) {
                return {
                    status: 'limit_exceeded',
                    message: '今日发帖奖励已达上限'
                };
            }

            // 使用add_credits存储过程记录积分奖励
            const { data, error } = await this.supabase.rpc('add_credits', {
                p_user_id: userId,
                p_amount: rewardAmount,
                p_type: CreditTransactionType.POST_REWARD,
                p_description: `发布帖子奖励${rewardAmount}积分`,
                p_status: CreditTransactionStatus.COMPLETED
            });

            if (error) {
                this.logger.error(`发放发帖奖励积分失败: ${error.message}`, error.stack);
                throw new InternalServerErrorException(`发放发帖奖励积分失败: ${error.message}`);
            }

            return {
                transaction: data.transaction,
                newBalance: data.new_balance,
                rewardAmount
            };
        } catch (error) {
            this.logger.error(`发放发帖奖励积分失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 发放平台奖励积分
     */
    async grantPlatformReward(
        userId: string,
        amount: number,
        description: string,
    ): Promise<any> {
        try {
            if (amount <= 0) {
                throw new BadRequestException('奖励积分数量必须大于0');
            }

            // 使用add_credits存储过程记录积分奖励
            const { data, error } = await this.supabase.rpc('add_credits', {
                p_user_id: userId,
                p_amount: amount,
                p_type: CreditTransactionType.PLATFORM_REWARD,
                p_description: description || `平台奖励${amount}积分`,
                p_status: CreditTransactionStatus.COMPLETED
            });

            if (error) {
                this.logger.error(`发放平台奖励积分失败: ${error.message}`, error.stack);
                throw new InternalServerErrorException(`发放平台奖励积分失败: ${error.message}`);
            }

            return {
                transaction: data.transaction,
                newBalance: data.new_balance,
                rewardAmount: amount
            };
        } catch (error) {
            this.logger.error(`发放平台奖励积分失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 验证用户积分余额与交易记录是否一致
     * 仅供管理员使用
     */
    async verifyUserCreditBalance(userId: string): Promise<any> {
        try {
            // 从余额表获取当前余额
            const { data: balanceData, error: balanceError } = await this.supabase
                .from('user_credit_balances')
                .select('credits, version, updated_at')
                .eq('user_id', userId)
                .single();

            if (balanceError && balanceError.code !== 'PGRST116') {
                this.logger.error(`获取用户余额记录失败: ${balanceError.message}`, balanceError.stack);
                throw new InternalServerErrorException(`验证用户余额失败: ${balanceError.message}`);
            }

            // 从交易记录计算余额
            const { data: transactionsData, error: transactionsError } = await this.supabase
                .from('credit_transactions')
                .select('amount')
                .eq('user_id', userId)
                .eq('status', CreditTransactionStatus.COMPLETED);

            if (transactionsError) {
                this.logger.error(`获取用户交易记录失败: ${transactionsError.message}`, transactionsError.stack);
                throw new InternalServerErrorException(`验证用户余额失败: ${transactionsError.message}`);
            }

            // 计算交易记录的余额
            const calculatedBalance = transactionsData.reduce((sum, transaction) => sum + transaction.amount, 0);

            // 当前表中的余额
            const currentBalance = balanceData?.credits || 0;

            // 检查是否一致
            const isConsistent = currentBalance === calculatedBalance;

            return {
                userId,
                currentBalance,
                calculatedBalance,
                difference: currentBalance - calculatedBalance,
                isConsistent,
                lastUpdated: balanceData?.updated_at || null,
                version: balanceData?.version || 0,
            };
        } catch (error) {
            this.logger.error(`验证用户余额失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 修复用户积分余额（使其与交易记录一致）
     * 仅供管理员使用
     */
    async fixUserCreditBalance(userId: string): Promise<any> {
        try {
            // 先获取验证结果
            const verifyResult = await this.verifyUserCreditBalance(userId);

            // 如果已经一致，无需修复
            if (verifyResult.isConsistent) {
                return {
                    status: 'no_action_needed',
                    message: '用户余额已经与交易记录一致，无需修复',
                    ...verifyResult
                };
            }

            // 使用事务更新用户余额
            const { error } = await this.supabase.rpc('fix_user_credit_balance', {
                p_user_id: userId,
                p_expected_balance: verifyResult.calculatedBalance
            });

            if (error) {
                this.logger.error(`修复用户余额失败: ${error.message}`, error.stack);
                throw new InternalServerErrorException(`修复用户余额失败: ${error.message}`);
            }

            // 获取修复后的状态
            const afterFixVerify = await this.verifyUserCreditBalance(userId);

            return {
                status: 'fixed',
                before: verifyResult,
                after: afterFixVerify,
                isFixed: afterFixVerify.isConsistent
            };
        } catch (error) {
            this.logger.error(`修复用户余额失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 手动领取新用户积分（防滥用版本）
     */
    async claimNewUserBonus(data: {
        userId: string;
        fingerprint: DeviceFingerprintData;
        ipAddress: string;
        userAgent: string;
    }): Promise<any> {
        try {
            const { userId, fingerprint, ipAddress, userAgent } = data;

            // 1. 防滥用验证
            const validationResult = await this.antiAbuseService.validateCreditClaim({
                userId,
                fingerprint,
                ipAddress,
                userAgent
            });

            if (!validationResult.allowed) {
                return {
                    success: false,
                    reason: validationResult.reason,
                    riskFactors: validationResult.riskFactors
                };
            }

            // 2. 发放积分
            const bonusAmount = CREDITS_CONFIG.NEW_USER_BONUS_AMOUNT;
            const { data: creditData, error } = await this.supabase.rpc('add_credits', {
                p_user_id: userId,
                p_amount: bonusAmount,
                p_type: CreditTransactionType.NEW_USER_BONUS,
                p_description: `手动领取新用户奖励${bonusAmount}积分`,
                p_status: CreditTransactionStatus.COMPLETED
            });

            if (error) {
                this.logger.error(`发放新用户积分失败: ${error.message}`, error);
                throw new InternalServerErrorException(`发放积分失败: ${error.message}`);
            }

            // 3. 记录成功的领取
            await this.antiAbuseService.recordSuccessfulClaim({
                userId,
                fingerprint,
                ipAddress,
                userAgent
            });

            return {
                success: true,
                newBalance: creditData.new_balance,
            };

        } catch (error) {
            this.logger.error(`手动领取新用户积分失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 获取用户积分领取状态
     */
    async getUserClaimStatus(userId: string): Promise<any> {
        try {
            return await this.antiAbuseService.getUserClaimStatus(userId);
        } catch (error) {
            this.logger.error(`获取用户积分领取状态失败: ${error.message}`, error.stack);
            throw error;
        }
    }
}