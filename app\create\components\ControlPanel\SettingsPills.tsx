"use client"

import React, { useState, useEffect } from 'react';
import { ChevronDown, Ratio, Clock, Layers } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useControlPanel } from './ControlPanelContext';

export const SettingsPills: React.FC = () => {
  const {
    aspectRatio,
    setAspectRatio,
    duration,
    setDuration,
    creativityLevel,
    setCreativityLevel,
    steps,
    setSteps,
    seed,
    setSeed,
    negativePrompt,
    setNegativePrompt,
    isVeo3Model,
    isVeo2Model,
    getAvailableDurations
  } = useControlPanel();

  // Add state for tracking screen width
  const [screenWidth, setScreenWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 1024);
  const isMobile = screenWidth < 768;

  // Monitor screen width changes
  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    // Set initial width
    handleResize();

    // Add event listener for window resize
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="flex gap-2">
      {/* Negative Prompt Pill */}
      <Popover>
        <PopoverTrigger asChild>
          <button className="text-sm px-2.5 py-1.5 rounded-full bg-card/30 flex items-center hover:bg-card/40 transition-colors">
            <span>Negative Prompt</span>
            <ChevronDown size={12} className="ml-1" />
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-72 p-3" align="start" side="top">
          <label className="block text-xs font-medium mb-2">
            Negative Prompt (Optional)
          </label>
          <textarea
            placeholder="Elements you want to exclude from the video"
            value={negativePrompt}
            onChange={(e) => setNegativePrompt(e.target.value)}
            className="w-full h-20 px-3 py-2 text-xs rounded-lg bg-card/30 focus:outline-none focus:ring-2 focus:ring-gray-500/50 resize-none"
          />
        </PopoverContent>
      </Popover>

      {/* Aspect Ratio Pill */}
      <Popover>
        <PopoverTrigger asChild>
          <button className="text-sm px-2.5 py-1.5 rounded-full bg-card/30 flex items-center hover:bg-card/40 transition-colors">
            <Ratio size={12} className="mr-1.5" />
            <span>{aspectRatio}</span>
            <ChevronDown size={12} className="ml-1" />
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-48 p-2" align="start" side="top">
          <h4 className="text-xs font-medium mb-2">Aspect Ratio</h4>
          <div className="grid grid-cols-3 gap-1">
            <button
              onClick={() => !isVeo3Model && setAspectRatio("9:16")}
              disabled={isVeo3Model}
              className={cn(
                "p-1.5 rounded-lg text-xs flex flex-col items-center justify-center",
                aspectRatio === "9:16"
                  ? "bg-gray-200/10"
                  : isVeo3Model
                    ? "bg-card/10 text-muted-foreground cursor-not-allowed opacity-50"
                    : "bg-card/30 hover:bg-card/50"
              )}
            >
              <div className="w-3.5 h-6 bg-muted mb-1 rounded"></div>
              9:16
            </button>
            <button
              onClick={() => setAspectRatio("16:9")}
              className={cn(
                "p-1.5 rounded-lg text-xs flex flex-col items-center justify-center",
                aspectRatio === "16:9"
                  ? "bg-gray-200/10"
                  : "bg-card/30 hover:bg-card/50"
              )}
            >
              <div className="w-6 h-3.5 bg-muted mb-1 rounded"></div>
              16:9
              {isVeo3Model && <span className="text-xs text-green-400 mt-0.5">Required</span>}
            </button>
            <button
              onClick={() => !isVeo3Model && setAspectRatio("1:1")}
              disabled={isVeo3Model}
              className={cn(
                "p-1.5 rounded-lg text-xs flex flex-col items-center justify-center",
                aspectRatio === "1:1"
                  ? "bg-gray-200/10"
                  : isVeo3Model
                    ? "bg-card/10 text-muted-foreground cursor-not-allowed opacity-50"
                    : "bg-card/30 hover:bg-card/50"
              )}
            >
              <div className="w-4 h-4 bg-muted mb-1 rounded"></div>
              1:1
            </button>
          </div>
        </PopoverContent>
      </Popover>

      {/* Duration Pill */}
      <Popover>
        <PopoverTrigger asChild>
          <button className="text-sm px-2.5 py-1.5 rounded-full bg-card/30 flex items-center hover:bg-card/40 transition-colors">
            <Clock size={12} className="mr-1.5" />
            <span>{duration}s</span>
            {(isVeo3Model || isVeo2Model) && <span className="text-xs text-green-400 ml-1">
              {isVeo3Model ? 'Required' : 'Limited'}
            </span>}
            <ChevronDown size={12} className="ml-1" />
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-48 p-2" align="start" side="top">
          <h4 className="text-sm font-medium mb-2">Duration</h4>
          <div className={cn(
            "grid gap-1",
            getAvailableDurations.length <= 2 ? "grid-cols-2" :
              getAvailableDurations.length === 3 ? "grid-cols-3" : "grid-cols-2"
          )}>
            {getAvailableDurations.map((durationOption) => (
              <button
                key={durationOption}
                onClick={() => setDuration(durationOption)}
                className={cn(
                  "p-1.5 rounded-lg text-xs flex items-center justify-center",
                  duration === durationOption
                    ? "bg-gray-200/10"
                    : "bg-card/30 hover:bg-card/50"
                )}
              >
                {durationOption} second{durationOption !== '1' ? 's' : ''}
                {isVeo3Model && durationOption === '8' && (
                  <span className="text-xs text-green-400 ml-1">Required</span>
                )}
              </button>
            ))}
          </div>
        </PopoverContent>
      </Popover>

      {/* Advanced Settings Pill */}
      <Popover>
        <PopoverTrigger asChild>
          <button className="text-sm px-2.5 py-1.5 rounded-full bg-card/30 flex items-center hover:bg-card/40 transition-colors">
            <Layers size={12} className="mr-1.5" />
            <span>{isMobile ? "Adv" : "Advanced"}</span>
            <ChevronDown size={12} className="ml-1" />
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-72 p-3" align="start" side="top">
          <div className="space-y-4">
            {/* Creativity Level */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium">Creativity Level</h4>
                <span className="text-xs text-muted-foreground">
                  {(creativityLevel * 100).toFixed(0)}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={creativityLevel}
                onChange={(e) => setCreativityLevel(parseFloat(e.target.value))}
                className="w-full h-1.5 bg-muted rounded-full appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-gray-400"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>More Accurate</span>
                <span>More Creative</span>
              </div>
            </div>

            {/* Sampling Steps */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-xs font-medium">Sampling Steps</h4>
                <span className="text-xs text-muted-foreground">{steps}</span>
              </div>
              <input
                type="range"
                min="20"
                max="60"
                step="1"
                value={steps}
                onChange={(e) => setSteps(parseInt(e.target.value))}
                className="w-full h-1.5 bg-muted rounded-full appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-gray-400"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>Faster</span>
                <span>Higher Quality</span>
              </div>
            </div>

            {/* Seed */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Random Seed
              </label>
              <div className="flex gap-1">
                <input
                  type="text"
                  placeholder="Leave empty for random generation"
                  value={seed}
                  onChange={(e) => setSeed(+e.target.value)}
                  className="flex-1 px-2 py-1.5 text-sm rounded-lg bg-card/30 focus:outline-none focus:ring-2 focus:ring-gray-500/50"
                />
                <button
                  onClick={() => setSeed(Math.floor(Math.random() * 1000000))}
                  className="bg-card/30 hover:bg-card/50 px-2 py-1.5 rounded-md transition-colors"
                  title="Generate random seed"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                    <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                    <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                    <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                    <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                    <line x1="12" y1="22.08" x2="12" y2="12"></line>
                  </svg>
                </button>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Same seed will produce similar results
              </p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
