'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { adminModelApi } from '@/lib/api/admin-model';
import { toast } from 'sonner';

const manualModelSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    description: z.string().optional(),
    source: z.string().min(1, 'Source is required'),
    model_type: z.string().min(1, 'Model type is required'),
    storage_path: z.string().min(1, 'Storage path is required'),
    price: z.number().min(0, 'Price must be non-negative').optional(),
    weight: z.number().min(0, 'Weight must be non-negative').optional(),
    cover_img: z.string().url('Must be a valid URL').optional().or(z.literal('')),
    cover_video: z.string().url('Must be a valid URL').optional().or(z.literal('')),
    is_public: z.boolean(),
    nsfw_level: z.number().min(0).max(5),
    category: z.string().optional(),
    group: z.string().optional(),
});

type ManualModelFormData = z.infer<typeof manualModelSchema>;

interface ManualModelFormProps {
    onSuccess: () => void;
    onCancel: () => void;
}

export function ManualModelForm({ onSuccess, onCancel }: ManualModelFormProps) {
    const [isLoading, setIsLoading] = useState(false);

    const form = useForm<ManualModelFormData>({
        resolver: zodResolver(manualModelSchema),
        defaultValues: {
            name: '',
            description: '',
            source: 'custom',
            model_type: 'image-to-video',
            storage_path: '',
            price: 0,
            weight: 1,
            cover_img: '',
            cover_video: '',
            is_public: true,
            nsfw_level: 1,
            category: '',
            group: '',
        },
    });

    const onSubmit = async (data: ManualModelFormData) => {
        setIsLoading(true);
        try {
            const modelData = {
                ...data,
                cover_img: data.cover_img || undefined,
                cover_video: data.cover_video || undefined,
                metadata: {},
                supported_features: [],
                default_config: {
                    min_guidance_scale: 0,
                    max_guidance_scale: 20,
                    default_guidance_scale: 7,
                    min_steps: 10,
                    max_steps: 50,
                    default_steps: 30,
                    allow_negative_prompt: true,
                    allow_seed_input: true,
                    allow_reference_image: data.model_type === 'image-to-video'
                },
                trainable: false,
            };

            await adminModelApi.createModel(modelData);
            toast.success('Model created successfully');
            onSuccess();
        } catch (error) {
            console.error('Failed to create model:', error);
            toast.error('Failed to create model');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Name *</FormLabel>
                                <FormControl>
                                    <Input {...field} placeholder="Model name" />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="source"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Source *</FormLabel>
                                <Select value={field.value} onValueChange={field.onChange}>
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="custom">Custom</SelectItem>
                                        <SelectItem value="upload">Upload</SelectItem>
                                        <SelectItem value="train">Train</SelectItem>
                                        <SelectItem value="fal.ai">fal.ai</SelectItem>
                                        <SelectItem value="other">Other</SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                                <Textarea {...field} rows={3} placeholder="Model description" />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="model_type"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Model Type *</FormLabel>
                                <Select value={field.value} onValueChange={field.onChange}>
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="image-to-video">Image to Video</SelectItem>
                                        <SelectItem value="text-to-video">Text to Video</SelectItem>
                                        <SelectItem value="video-to-video">Video to Video</SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="storage_path"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Storage Path *</FormLabel>
                                <FormControl>
                                    <Input {...field} placeholder="model/path/identifier" />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="category"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Category</FormLabel>
                                <FormControl>
                                    <Input {...field} placeholder="Model category" />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="group"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Group</FormLabel>
                                <FormControl>
                                    <Input {...field} placeholder="Model group" />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="cover_img"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Cover Image URL</FormLabel>
                                <FormControl>
                                    <Input {...field} placeholder="https://example.com/image.jpg" />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="cover_video"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Cover Video URL</FormLabel>
                                <FormControl>
                                    <Input {...field} placeholder="https://example.com/video.mp4" />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                        control={form.control}
                        name="price"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Price (Credits)</FormLabel>
                                <FormControl>
                                    <Input 
                                        type="number" 
                                        min="0" 
                                        step="0.01"
                                        {...field}
                                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="weight"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Weight</FormLabel>
                                <FormControl>
                                    <Input 
                                        type="number" 
                                        min="0" 
                                        step="0.1"
                                        {...field}
                                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 1)}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="nsfw_level"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>NSFW Level</FormLabel>
                                <Select 
                                    value={field.value.toString()} 
                                    onValueChange={(value) => field.onChange(parseInt(value))}
                                >
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="0">0 - Safe</SelectItem>
                                        <SelectItem value="1">1 - Low</SelectItem>
                                        <SelectItem value="2">2 - Medium</SelectItem>
                                        <SelectItem value="3">3 - High</SelectItem>
                                        <SelectItem value="4">4 - Explicit</SelectItem>
                                        <SelectItem value="5">5 - Extreme</SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <FormField
                    control={form.control}
                    name="is_public"
                    render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                                <FormLabel className="text-base">Public Model</FormLabel>
                                <div className="text-sm text-muted-foreground">
                                    Make this model available to all users
                                </div>
                            </div>
                            <FormControl>
                                <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                />
                            </FormControl>
                        </FormItem>
                    )}
                />

                <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button type="submit" disabled={isLoading}>
                        {isLoading ? 'Creating...' : 'Create Model'}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
