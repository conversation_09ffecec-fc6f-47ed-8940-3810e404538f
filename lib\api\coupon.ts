import { apiClient, ApiResponse } from './client';
import { API_CONFIG } from '@/lib/config';
import { queryDeduplicationManager } from '../query-deduplication';
import {
    UserCoupon,
    CouponUsageHistory,
    ClaimCouponRequest,
    ClaimCouponResponse,
    CouponStatusResponse,
    PriceCalculation,
    ApplyCouponToPaymentRequest,
    ApplyCouponToPaymentResponse,
    CouponType
} from '@/app/membership/types/coupon';

/**
 * 优惠券API服务
 */
export const couponApi = {
    /**
     * 检查用户优惠券状态
     */
    getCouponStatus: async (): Promise<CouponStatusResponse> => {
        return queryDeduplicationManager.executeQuery(
            'coupon-status',
            async () => {
                const response = await apiClient.get<ApiResponse<CouponStatusResponse>>(
                    API_CONFIG.ENDPOINTS.COUPON.GET_STATUS
                );
                return response.data;
            }
        );
    },

    /**
     * 领取优惠券
     */
    claimCoupon: async (request: ClaimCouponRequest): Promise<ClaimCouponResponse> => {
        const response = await apiClient.post<ApiResponse<ClaimCouponResponse>>(
            API_CONFIG.ENDPOINTS.COUPON.CLAIM,
            request
        );
        return response.data;
    },

    /**
     * 获取用户的优惠券列表
     */
    getUserCoupons: async (): Promise<UserCoupon[]> => {
        const response = await apiClient.get<ApiResponse<UserCoupon[]>>(
            API_CONFIG.ENDPOINTS.COUPON.GET_USER_COUPONS
        );
        return response.data;
    },

    /**
     * 获取优惠券使用历史
     */
    getCouponUsageHistory: async (): Promise<CouponUsageHistory[]> => {
        const response = await apiClient.get<ApiResponse<CouponUsageHistory[]>>(
            API_CONFIG.ENDPOINTS.COUPON.GET_USAGE_HISTORY
        );
        return response.data;
    },

    /**
     * 计算应用优惠券后的价格
     */
    calculatePrice: async (originalPrice: number, couponId?: string): Promise<PriceCalculation> => {
        const response = await apiClient.post<ApiResponse<PriceCalculation>>(
            API_CONFIG.ENDPOINTS.COUPON.CALCULATE_PRICE,
            {
                original_price: originalPrice,
                coupon_id: couponId
            }
        );
        return response.data;
    },

    /**
     * 应用优惠券到支付
     */
    applyCouponToPayment: async (request: ApplyCouponToPaymentRequest): Promise<ApplyCouponToPaymentResponse> => {
        const response = await apiClient.post<ApiResponse<ApplyCouponToPaymentResponse>>(
            API_CONFIG.ENDPOINTS.COUPON.APPLY_TO_PAYMENT,
            request
        );
        return response.data;
    },

    /**
     * 检查用户是否有资格领取首月折扣优惠券
     */
    checkFirstMonthDiscountEligibility: async (): Promise<{ is_eligible: boolean; message?: string }> => {
        return queryDeduplicationManager.executeQuery(
            'coupon-eligibility',
            async () => {
                const response = await apiClient.get<ApiResponse<{ is_eligible: boolean; message?: string }>>(
                    API_CONFIG.ENDPOINTS.COUPON.CHECK_ELIGIBILITY
                );
                return response.data;
            }
        );
    },

    /**
     * 领取首月折扣优惠券
     */
    claimFirstMonthDiscount: async (): Promise<ClaimCouponResponse> => {
        const response = await apiClient.post<ApiResponse<ClaimCouponResponse>>(
            API_CONFIG.ENDPOINTS.COUPON.CLAIM,
            {
                coupon_type: CouponType.FIRST_MONTH_DISCOUNT
            },
        );
        return response.data;
    }
};
