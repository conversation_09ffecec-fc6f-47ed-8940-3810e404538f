"use client"

import { Card, CardContent } from "@/components/ui/card"
import {
  Film, Send, ArrowRight, Clock, CheckCircle2, Info, PenTool, Scissors,
  Camera, Music, Play, Layers, Clapperboard, Video
} from "lucide-react"
import Link from "next/link"

export default function AgentStudioPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-background/50 to-background flex flex-col">
      {/* Header */}
      <header className="border-b p-4">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <h1 className="text-xl font-semibold">ReelMind Studio</h1>
          <div className="flex gap-4">
            <Link href="/agents/conversation" className="px-4 py-2 bg-primary/10 hover:bg-primary/20 text-primary-foreground rounded-md text-sm font-medium transition-colors">
              Start Project
            </Link>
            <Link href="/agents/think-plan" className="px-4 py-2 bg-primary/10 hover:bg-primary/20 text-primary-foreground rounded-md text-sm font-medium transition-colors">
              Think/Plan
            </Link>
          </div>
        </div>
      </header>
      
      {/* Main content */}
      <div className="flex-1 flex flex-col max-w-screen-xl mx-auto w-full px-4 py-8">
        <Card className="w-full max-w-3xl mx-auto shadow-lg border-primary/10">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold mb-6 text-center">Video AI Agents</h2>
            <p className="text-center text-muted-foreground mb-8">
              Specialized AI agents to help create production-quality videos
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <Card className="bg-blue-50/30 hover:bg-blue-100/30 cursor-pointer transition-colors border-blue-200/50">
                <CardContent className="p-4 flex flex-col items-center justify-center">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-3">
                    <PenTool className="h-6 w-6 text-blue-500" />
                  </div>
                  <h3 className="font-medium text-center">Script Agent</h3>
                  <p className="text-xs text-center text-muted-foreground mt-1">Write compelling narratives</p>
                </CardContent>
              </Card>
              
              <Card className="bg-amber-50/30 hover:bg-amber-100/30 cursor-pointer transition-colors border-amber-200/50">
                <CardContent className="p-4 flex flex-col items-center justify-center">
                  <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mb-3">
                    <Camera className="h-6 w-6 text-amber-500" />
                  </div>
                  <h3 className="font-medium text-center">Keyframe Agent</h3>
                  <p className="text-xs text-center text-muted-foreground mt-1">Generate visual scenes</p>
                </CardContent>
              </Card>
              
              <Card className="bg-purple-50/30 hover:bg-purple-100/30 cursor-pointer transition-colors border-purple-200/50">
                <CardContent className="p-4 flex flex-col items-center justify-center">
                  <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-3">
                    <Video className="h-6 w-6 text-purple-500" />
                  </div>
                  <h3 className="font-medium text-center">Video Agent</h3>
                  <p className="text-xs text-center text-muted-foreground mt-1">Create stunning footage</p>
                </CardContent>
              </Card>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="bg-pink-50/30 hover:bg-pink-100/30 cursor-pointer transition-colors border-pink-200/50">
                <CardContent className="p-4 flex flex-col items-center justify-center">
                  <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center mb-3">
                    <Scissors className="h-6 w-6 text-pink-500" />
                  </div>
                  <h3 className="font-medium text-center">Edit Agent</h3>
                  <p className="text-xs text-center text-muted-foreground mt-1">Perfect timing and transitions</p>
                </CardContent>
              </Card>
              
              <Card className="bg-indigo-50/30 hover:bg-indigo-100/30 cursor-pointer transition-colors border-indigo-200/50">
                <CardContent className="p-4 flex flex-col items-center justify-center">
                  <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-3">
                    <Music className="h-6 w-6 text-indigo-500" />
                  </div>
                  <h3 className="font-medium text-center">Audio Agent</h3>
                  <p className="text-xs text-center text-muted-foreground mt-1">Add music and sound effects</p>
                </CardContent>
              </Card>
            </div>
            
            <div className="mt-8 text-center">
              <Link 
                href="/agents/conversation" 
                className="px-6 py-3 rounded-md bg-primary text-primary-foreground font-medium hover:bg-primary/90 transition-colors inline-flex items-center gap-2"
              >
                Start Your Video Project
                <ArrowRight className="h-4 w-4" />
              </Link>
            </div>
            
            <p className="text-xs text-center text-muted-foreground mt-4">
              Powered by Qwen AI technology with internet search capabilities
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 