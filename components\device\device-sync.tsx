'use client';

import { useEffect } from 'react';
import { setCookie } from 'cookies-next';
import { useDeviceContext } from '@/contexts/DeviceContext';

/**
 * 此组件负责在客户端和服务端之间同步设备信息
 * 它会在客户端加载时收集设备信息，并通过 cookie 将其传递给服务端
 */
export function DeviceSync() {
    const { isClient } = useDeviceContext();

    useEffect(() => {
        if (!isClient) return;

        // 获取屏幕宽度
        const width = window.innerWidth;

        // 将宽度存储为 cookie，以便下一次服务端渲染时使用
        setCookie('client-width', width.toString(), {
            maxAge: 60 * 60 * 24 * 7, // 7 天
            path: '/',
            sameSite: 'strict',
        });

        // 添加视口宽度头信息
        // 部分浏览器支持通过请求头自动发送视口宽度信息
        const metaViewport = document.createElement('meta');
        metaViewport.name = 'viewport-width';
        metaViewport.content = width.toString();
        document.head.appendChild(metaViewport);
    }, [isClient]);

    // 这个组件不渲染任何内容，只用于同步设备信息
    return null;
} 