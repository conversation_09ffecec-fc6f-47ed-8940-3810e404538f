/**
 * 模型duration配置工具函数
 * 用于管理不同模型的duration限制
 */

// 模型duration配置类型
export interface ModelDurationConfig {
  [key: string]: string[];
}

// 默认的模型duration配置
export const DEFAULT_MODEL_DURATION_CONFIG: ModelDurationConfig = {
  'fal-ai/veo3': ['8'], // veo3只支持8s
  'fal-ai/veo2': ['5', '6', '7', '8'], // veo2支持5,6,7,8s
  'fal-ai/veo2/image-to-video': ['5', '6', '7', '8'], // veo2/image-to-video支持5,6,7,8s
  'default': ['5', '10'] // 默认支持5s和10s
};

/**
 * 获取指定模型的可用duration选项
 * @param storagePath 模型的storage_path
 * @param config 可选的自定义配置，默认使用DEFAULT_MODEL_DURATION_CONFIG
 * @returns 可用的duration选项数组
 */
export function getModelAvailableDurations(
  storagePath: string | null | undefined,
  config: ModelDurationConfig = DEFAULT_MODEL_DURATION_CONFIG
): string[] {
  if (!storagePath) {
    return config.default || ['5', '10'];
  }
  
  return config[storagePath] || config.default || ['5', '10'];
}

/**
 * 检查指定duration是否在模型支持的范围内
 * @param storagePath 模型的storage_path
 * @param duration 要检查的duration
 * @param config 可选的自定义配置
 * @returns 是否支持该duration
 */
export function isValidDurationForModel(
  storagePath: string | null | undefined,
  duration: string,
  config: ModelDurationConfig = DEFAULT_MODEL_DURATION_CONFIG
): boolean {
  const availableDurations = getModelAvailableDurations(storagePath, config);
  return availableDurations.includes(duration);
}

/**
 * 获取模型的默认duration
 * @param storagePath 模型的storage_path
 * @param config 可选的自定义配置
 * @returns 默认的duration值
 */
export function getDefaultDurationForModel(
  storagePath: string | null | undefined,
  config: ModelDurationConfig = DEFAULT_MODEL_DURATION_CONFIG
): string {
  const availableDurations = getModelAvailableDurations(storagePath, config);
  
  // 对于veo3模型，默认返回8s
  if (storagePath === 'fal-ai/veo3') {
    return '8';
  }
  
  // 对于其他模型，返回第一个可用选项（通常是最短的）
  return availableDurations[0] || '5';
}

/**
 * 检查是否为veo2系列模型
 * @param storagePath 模型的storage_path
 * @returns 是否为veo2模型
 */
export function isVeo2Model(storagePath: string | null | undefined): boolean {
  if (!storagePath) return false;
  return storagePath === 'fal-ai/veo2' || storagePath === 'fal-ai/veo2/image-to-video';
}

/**
 * 检查是否为veo3模型
 * @param storagePath 模型的storage_path
 * @returns 是否为veo3模型
 */
export function isVeo3Model(storagePath: string | null | undefined): boolean {
  return storagePath === 'fal-ai/veo3';
}

/**
 * 检查是否为veo系列模型
 * @param storagePath 模型的storage_path
 * @returns 是否为veo系列模型
 */
export function isVeoModel(storagePath: string | null | undefined): boolean {
  if (!storagePath) return false;
  return storagePath.toLowerCase().includes('veo');
}
