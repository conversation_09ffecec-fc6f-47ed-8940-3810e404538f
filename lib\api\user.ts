import { ApiResponse } from './client';
import { apiClient } from './client';

// 用户个人资料
export interface UserProfile {
    id: string;
    email: string;
    nickname: string;
    avatar_url: string | null;
    bio: string | null;
    website: string | null;
    location: string | null;
    created_at: string;
    updated_at: string;
}

// 更新个人资料请求参数
export interface UpdateProfileParams {
    nickname?: string;
    bio?: string;
    website?: string;
    location?: string;
}

// 用户角色响应
export interface UserRoleResponse {
    role: string | null;
}

// 管理员检查响应
export interface CheckAdminResponse {
    isAdmin: boolean;
}

/**
 * 用户API服务
 */
export const userApi = {
    /**
     * 获取用户个人资料
     * @returns 用户个人资料
     */
    getProfile: async (): Promise<UserProfile> => {
        const response = await apiClient.get<ApiResponse<UserProfile>>(
            '/user/profile',
            {
                requireAuth: true,
            }
        );
        return response.data;
    },

    /**
     * 更新用户个人资料
     * @param params 更新个人资料参数
     * @returns 更新后的用户个人资料
     */
    updateProfile: async (params: UpdateProfileParams): Promise<UserProfile> => {
        const response = await apiClient.post<ApiResponse<UserProfile>>(
            '/user/profile/update',
            params,
            {
                requireAuth: true,
            }
        );
        return response.data;
    },

    /**
     * 获取用户角色
     * @returns 用户角色信息
     */
    getUserRole: async (): Promise<UserRoleResponse> => {
        const response = await apiClient.get<ApiResponse<UserRoleResponse>>(
            '/user/role',
            {
                requireAuth: true,
            }
        );
        console.log(response.data);
        return response.data;
    },

    /**
     * 检查用户是否为管理员
     * @returns 是否为管理员
     */
    checkAdmin: async (): Promise<CheckAdminResponse> => {
        const result = await userApi.getUserRole();
        return { isAdmin: result.role === 'admin' };
    },
}; 