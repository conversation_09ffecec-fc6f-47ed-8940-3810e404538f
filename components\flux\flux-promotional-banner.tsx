"use client"
import { cn } from "@/lib/utils"
import { useRouter } from "next/navigation"

interface FluxPromotionalBannerProps {
    className?: string;
}

export function FluxPromotionalBanner({ className }: FluxPromotionalBannerProps) {
    const router = useRouter()

    // Handle click to navigate to lego page with flux model selection
    const handleFluxRedirect = () => {
        router.push('/lego?model=flux-kontext-pro')
    }

    return (
        <div className={cn("relative overflow-hidden cursor-pointer", className)} onClick={handleFluxRedirect}>
            <div className="relative bg-gradient-to-r from-purple-500/10 via-blue-500/10 to-cyan-500/10 border border-purple-500/20 rounded-lg p-6 backdrop-blur-sm hover:border-purple-500/30 transition-all duration-300 h-24">
                {/* Subtle animated background */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-blue-500/5 to-cyan-500/5 rounded-lg" />
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-500/40 to-transparent" />

                {/* Badge */}
                <div className="absolute top-0 right-0 z-10">
                    <div className="relative">
                        <div className="bg-gradient-to-r from-purple-600 to-indigo-700 px-3 py-1 rounded-bl-lg shadow-lg relative overflow-hidden">
                            <div className="absolute top-0 left-[-100%] w-[200%] h-full bg-gradient-to-r from-transparent via-white/40 to-transparent skew-x-[-15deg] animate-shimmer"></div>
                            <div className="flex items-center justify-center relative">
                                <span className="text-white font-semibold text-sm">Image-to-Image</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="relative z-10 h-full flex items-center">
                    <div className="flex items-center gap-4 w-full">
                        <div className="text-white bg-white/10 p-2.5 rounded-lg backdrop-blur-sm flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div className="flex-1 min-w-0">
                            <h3 className="text-xl font-bold text-purple-300 truncate">FLUX.1 Kontext</h3>
                            <p className="text-sm text-gray-300 truncate">Advanced image-to-image AI model</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
