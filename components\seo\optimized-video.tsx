'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { OptimizedImage } from './optimized-image';

interface OptimizedVideoProps {
    src: string;
    thumbnailSrc?: string;
    className?: string;
    autoPlay?: boolean;
    muted?: boolean;
    loop?: boolean;
    controls?: boolean;
    preload?: 'auto' | 'metadata' | 'none';
    priority?: boolean;
}

/**
 * 优化视频组件 - 支持懒加载和缩略图预览
 * 提高性能和SEO友好度
 */
export function OptimizedVideo({
    src,
    thumbnailSrc,
    className,
    autoPlay = false,
    muted = true,
    loop = false,
    controls = true,
    preload = 'metadata',
    priority = false,
}: OptimizedVideoProps) {
    const videoRef = useRef<HTMLVideoElement>(null);
    const [isVideoLoaded, setIsVideoLoaded] = useState(false);
    const [isInView, setIsInView] = useState(false);
    const [hasStartedPlaying, setHasStartedPlaying] = useState(false);

    // 监听视频元素是否在视口内
    useEffect(() => {
        if (!videoRef.current) return;

        const observer = new IntersectionObserver((entries) => {
            const [entry] = entries;
            setIsInView(entry.isIntersecting);
        }, { threshold: 0.1 });

        observer.observe(videoRef.current);

        return () => {
            if (videoRef.current) {
                observer.unobserve(videoRef.current);
            }
        };
    }, []);

    // 当视频在视口中且未开始播放时，设置视频源
    useEffect(() => {
        if (isInView && videoRef.current && !hasStartedPlaying && !priority) {
            // 延迟加载 - 只有在视口内才加载
            if (!videoRef.current.src) {
                videoRef.current.src = src;
            }
        }
    }, [isInView, hasStartedPlaying, src, priority]);

    // 处理视频加载事件
    const handleLoadedData = () => {
        setIsVideoLoaded(true);
        if (autoPlay && isInView) {
            videoRef.current?.play()
                .then(() => setHasStartedPlaying(true))
                .catch(err => console.error('Video autoplay failed:', err));
        }
    };

    return (
        <div className={cn('relative overflow-hidden', className)}>
            {/* 优先加载模式不显示缩略图 */}
            {thumbnailSrc && !isVideoLoaded && !priority && (
                <div className="absolute inset-0 z-10">
                    <OptimizedImage
                        src={thumbnailSrc}
                        alt="Video thumbnail"
                        fill
                        objectFit="cover"
                        priority={priority}
                    />
                </div>
            )}

            <video
                ref={videoRef}
                className={cn(
                    'w-full h-full object-cover transition-opacity duration-300',
                    isVideoLoaded ? 'opacity-100' : 'opacity-0'
                )}
                autoPlay={autoPlay}
                muted={muted}
                loop={loop}
                controls={controls}
                preload={priority ? 'auto' : preload}
                playsInline
                onLoadedData={handleLoadedData}
                src={priority ? src : undefined} // 高优先级模式直接设置src，否则等待视口触发
            >
                <source type="video/mp4" src={priority ? src : undefined} />
                Your browser does not support the video tag.
            </video>

            {/* 加载指示器 */}
            {!isVideoLoaded && (
                <div className="absolute inset-0 flex items-center justify-center bg-slate-800/20 backdrop-blur-sm">
                    <div className="w-12 h-12 rounded-full border-4 border-slate-300 border-t-blue-500 animate-spin"></div>
                </div>
            )}
        </div>
    );
} 