"use client"

import { useInfiniteModelList } from "@/app/models/queries"
import { ModelCard } from "@/app/models/components/model-card"
import { Loader2 } from "lucide-react"
import { use, useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Model } from "@/types/model"
import { useRouter } from "next/navigation"
import { TabsContent } from "@/components/ui/tabs"
import { ModelTypeTabs } from "@/app/models/components/model-type-tabs"
// 注意：在App Router中，不需要使用Head组件，而是使用metadata

export default function AllModelsPage({
    searchParams,
}: {
    searchParams: Promise<{ [key: string]: any }>
}) {
    const router = useRouter()
    const { type: typeFilter } = use(searchParams);

    // 使用状态来跟踪当前选中的类型
    const [selectedType, setSelectedType] = useState<string | null>(typeFilter)

    // 获取模型数据，每页20个
    const {
        data,
        isLoading,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage
    } = useInfiniteModelList(20)

    // 用于存储所有模型的数组
    const allModels = data?.pages.flatMap(page => page.models) || []

    // 用于存储总模型数量
    const totalModels = data?.pages[0]?.total || 0

    // 根据类型过滤模型
    const filteredModels = selectedType
        ? allModels.filter(model => model.type === selectedType)
        : allModels

    // 当URL参数变化时更新选中的类型
    useEffect(() => {
        setSelectedType(typeFilter)
    }, [typeFilter])

    // 处理类型选择变化
    const handleTypeChange = (type: string) => {
        const newType = type === "all" ? null : type
        setSelectedType(newType)

        // 更新URL参数
        if (newType) {
            router.push(`/models/all?type=${newType}`)
        } else {
            router.push('/models/all')
        }
    }

    // 创建一个ref用于观察底部元素
    const bottomRef = useRef<HTMLDivElement>(null)

    // 使用Intersection Observer API监听底部元素
    useEffect(() => {
        // 如果没有更多页面或正在加载，则不需要设置观察器
        if (!hasNextPage || isLoading || isFetchingNextPage) return

        const observer = new IntersectionObserver(
            (entries) => {
                // 当底部元素可见时，加载下一页
                if (entries[0].isIntersecting && hasNextPage) {
                    fetchNextPage()
                }
            },
            { threshold: 0.5 } // 当50%的元素可见时触发
        )

        // 开始观察底部元素
        if (bottomRef.current) {
            observer.observe(bottomRef.current)
        }

        // 清理函数
        return () => {
            if (bottomRef.current) {
                observer.unobserve(bottomRef.current)
            }
        }
    }, [hasNextPage, isLoading, isFetchingNextPage, fetchNextPage])

    // 注意：在客户端组件中，我们不能使用Next.js的metadata API
    // 但我们可以通过服务端渲染的内容进行SEO优化

    return (
        <main className="container mx-auto px-4 py-8">
            <div className="space-y-2 mb-8">
                <h1 className="text-3xl font-bold tracking-tight">
                    {selectedType
                        ? `${selectedType.charAt(0).toUpperCase() + selectedType.slice(1)} Models`
                        : "All Video Models"
                    }
                </h1>
                <p className="text-muted-foreground">
                    Browse our collection of AI models for video generation
                    {totalModels > 0 && ` (${filteredModels.length} ${selectedType ? `${selectedType} models` : `models`})`}
                </p>
            </div>

            {/* 使用我们的新组件替换原来的Tabs组件 */}
            <div className="mb-8">
                <ModelTypeTabs
                    activeTab={selectedType || "all"}
                    onTabChange={handleTypeChange}
                >
                    <TabsContent value="all" className="mt-0">
                        {/* 这里的内容会在下面的模型网格中显示 */}
                    </TabsContent>
                    <TabsContent value="text-to-video" className="mt-0">
                        {/* 这里的内容会在下面的模型网格中显示 */}
                    </TabsContent>
                    <TabsContent value="image-to-video" className="mt-0">
                        {/* 这里的内容会在下面的模型网格中显示 */}
                    </TabsContent>
                    <TabsContent value="video-to-video" className="mt-0">
                        {/* 这里的内容会在下面的模型网格中显示 */}
                    </TabsContent>
                </ModelTypeTabs>
            </div>

            {/* Models Grid */}
            {isLoading && filteredModels.length === 0 ? (
                <div className="flex justify-center py-12">
                    <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
                </div>
            ) : (
                <>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {filteredModels.map((model: Model) => (
                            <ModelCard key={model.id} model={model} />
                        ))}
                    </div>

                    {/* 底部加载指示器或加载更多按钮 */}
                    <div className="mt-8 flex justify-center" ref={bottomRef}>
                        {isFetchingNextPage ? (
                            <div className="flex items-center gap-2">
                                <Loader2 className="w-5 h-5 animate-spin text-gray-400" />
                                <span className="text-muted-foreground">Loading more models...</span>
                            </div>
                        ) : hasNextPage ? (
                            <Button
                                variant="outline"
                                onClick={() => fetchNextPage()}
                                className="text-muted-foreground"
                            >
                                Load More Models
                            </Button>
                        ) : filteredModels.length > 0 ? (
                            <p className="text-muted-foreground">You've reached the end! All models loaded.</p>
                        ) : (
                            <p className="text-muted-foreground">No models found matching your criteria.</p>
                        )}
                    </div>
                </>
            )}

            {/* Space to account for mobile navigation bar */}
            <div className="h-16 lg:h-0"></div>
        </main>
    )
}