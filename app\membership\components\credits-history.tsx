import React, { useEffect, useState, useRef } from 'react';
import useCreditsStore from '@/store/useCreditsStore';
import { CreditTransaction } from '@/lib/api/credits';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Loader2 } from 'lucide-react';

// Transaction type mapping for display
const transactionTypes: Record<string, string> = {
    membership_monthly: 'Monthly Membership Gift',
    direct_purchase: 'Credits Purchase',
    platform_reward: 'Platform Reward',
    new_user_bonus: 'New User Bonus',
    post_reward: 'Post Reward',
    video_generation_sd: 'Standard Video Generation',
    video_generation_hd: 'HD Video Generation'
};

export function CreditsHistory() {
    const {
        transactions = [],
        totalTransactions,
        isLoadingTransactions,
        fetchTransactions,
        hasLoadedTransactions
    } = useCreditsStore();

    const [page, setPage] = useState(1);
    const limit = 10;

    // 只在组件首次挂载且未加载过数据时获取交易记录
    useEffect(() => {
        console.log('[Debug] CreditsHistory useEffect executed', {
            hasLoadedTransactions,
            isLoadingTransactions
        });

        if (!hasLoadedTransactions && !isLoadingTransactions) {
            fetchTransactions({ page, limit });
        }
    }, [hasLoadedTransactions, isLoadingTransactions, fetchTransactions]);

    // 加载更多交易记录
    const loadMore = () => {
        const nextPage = page + 1;
        setPage(nextPage);
        fetchTransactions({ page: nextPage, limit }, false); // 第二个参数false表示不强制刷新
    };

    // Format date to readable format
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(date);
    };

    // Get friendly name for transaction type
    const getTransactionTypeName = (type: string) => {
        return transactionTypes[type] || type;
    };

    return (
        <Card className="w-full mt-6">
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Transaction History</CardTitle>
                    <CardDescription>Your recent credit transactions</CardDescription>
                </div>
            </CardHeader>

            <CardContent>
                {isLoadingTransactions && (!transactions || transactions.length === 0) ? (
                    <div className="flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-primary-foreground" />
                    </div>
                ) : transactions && transactions.length > 0 ? (
                    <>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Date</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Amount</TableHead>
                                    <TableHead>Status</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {transactions.map((transaction: CreditTransaction) => (
                                    <TableRow key={transaction.id}>
                                        <TableCell className="font-medium">
                                            {formatDate(transaction.created_at)}
                                        </TableCell>
                                        <TableCell>{getTransactionTypeName(transaction.type)}</TableCell>
                                        <TableCell className={transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}>
                                            {transaction.amount > 0 ? `+${transaction.amount}` : transaction.amount}
                                        </TableCell>
                                        <TableCell className="capitalize">{transaction.status}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {totalTransactions && transactions.length < totalTransactions && (
                            <div className="flex justify-center mt-4">
                                <Button
                                    variant="outline"
                                    onClick={loadMore}
                                    disabled={isLoadingTransactions}
                                >
                                    {isLoadingTransactions ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Loading...
                                        </>
                                    ) : (
                                        'Load More'
                                    )}
                                </Button>
                            </div>
                        )}
                    </>
                ) : (
                    <div className="text-center py-8 text-muted-foreground">
                        No transaction history available
                    </div>
                )}
            </CardContent>
        </Card>
    );
} 