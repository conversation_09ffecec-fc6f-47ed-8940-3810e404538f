"use client"

import { useState, useEffect } from "react"
import { Bookmark } from "lucide-react"
import { Button } from "@/components/ui/button"
import { favoriteApi, FavoriteTargetType } from "@/lib/api/favorite"
import { useAuth } from "@/contexts/auth-context"

interface UserFavoriteButtonProps {
    itemId: string
    itemType: FavoriteTargetType
    className?: string
    showText?: boolean
}

export function UserFavoriteButton({ itemId, itemType, className = "", showText = true }: UserFavoriteButtonProps) {
    const { user } = useAuth()
    const [isFavorite, setIsFavorite] = useState(false)
    const [isLoading, setIsLoading] = useState(false)

    // 检查是否已收藏
    const checkFavoriteStatus = async () => {
        if (!user) return

        try {
            const params = {
                item_type: itemType,
                ...(itemType === FavoriteTargetType.POST ? { post_id: itemId } : { model_id: itemId })
            };

            const { favorite } = await favoriteApi.checkFavorite(params)
            setIsFavorite(favorite)
        } catch (err) {
            console.error("检查收藏状态失败", err)
        }
    }

    // 处理收藏/取消收藏
    const handleToggleFavorite = async () => {
        if (!user) return
        if (isLoading) return

        setIsLoading(true)

        try {
            const params = {
                item_type: itemType,
                ...(itemType === FavoriteTargetType.POST ? { post_id: itemId } : { model_id: itemId })
            };

            if (isFavorite) {
                await favoriteApi.removeFavorite(params)
                setIsFavorite(false)
            } else {
                await favoriteApi.addFavorite(params)
                setIsFavorite(true)
            }
        } catch (err) {
            console.error("操作收藏失败", err)
        } finally {
            setIsLoading(false)
        }
    }

    // 初始检查收藏状态
    useEffect(() => {
        if (user && itemId) {
            checkFavoriteStatus()
        }
    }, [user, itemId])

    if (!user) return null

    return (
        <Button
            variant={isFavorite ? "default" : "outline"}
            size="sm"
            className={`${className} ${isFavorite ? "bg-blue-500 hover:bg-blue-600 border-blue-500" : "hover:text-blue-500 hover:border-blue-500"}`}
            onClick={handleToggleFavorite}
            disabled={isLoading}
        >
            <Bookmark className={`h-4 w-4 mr-1 ${isFavorite ? "fill-white" : ""}`} />
            {showText && (isFavorite ? "已收藏" : "收藏")}
        </Button>
    )
} 