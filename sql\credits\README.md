# 积分系统优化说明

## 背景

原有积分系统在计算用户余额时，每次都需要从 `credit_transactions` 表中查询所有交易记录并计算总和，随着交易数量增加，这种方法会导致严重的性能问题。

## 改进方案

1. **独立余额表设计**：创建 `user_credit_balances` 表存储每个用户的当前积分余额，避免重复计算。
2. **数据库触发器**：使用触发器在交易记录创建或更新时自动更新用户余额。
3. **事务处理**：使用存储过程和事务确保数据一致性，防止余额与交易记录不同步。
4. **乐观锁**：通过版本字段实现乐观锁，避免并发操作导致的数据不一致。
5. **自修复机制**：提供管理员接口用于验证和修复可能出现的余额不一致问题。

## 主要文件和功能

### 数据库架构

- `sql/credits/user_credit_balances.sql`：用户余额表定义和触发器
- `sql/credits/credit_procedures.sql`：交易相关存储过程
- `sql/credits/credit_balance_fix.sql`：余额修复存储过程
- `sql/credits/initialize_user_balances.sql`：初始化现有用户余额的脚本

### 业务逻辑

- `src/credits/credits.service.ts`：更新了所有获取、消费和添加积分的方法，使用新的余额表和存储过程
- `src/credits/credits.controller.ts`：添加了管理员用于验证和修复余额的接口

## 部署步骤

1. 执行 `user_credit_balances.sql` 创建余额表和触发器
2. 执行 `credit_procedures.sql` 创建存储过程
3. 执行 `credit_balance_fix.sql` 创建修复存储过程
4. 执行 `initialize_user_balances.sql` 初始化所有用户的余额
5. 部署更新后的后端代码

## 性能对比

| 操作     | 原方案                     | 新方案                     | 性能提升                   |
| -------- | -------------------------- | -------------------------- | -------------------------- |
| 查询余额 | O(n) - 需要扫描所有交易    | O(1) - 直接读取余额表      | 显著提升，尤其是交易量大时 |
| 消费积分 | 需先计算余额再创建消费记录 | 事务内原子操作，确保一致性 | 显著提升，且更安全         |
| 添加积分 | 只记录交易                 | 事务内同时更新余额表       | 查询性能显著提升           |

## 注意事项

1. 部署时需要短暂停机或在低峰期进行，确保数据迁移和初始化完成
2. 初始化后，建议使用管理员接口验证所有用户的余额是否与交易记录一致
3. 系统包含自修复机制，但仍建议定期(如每天)运行验证脚本确保数据一致性
