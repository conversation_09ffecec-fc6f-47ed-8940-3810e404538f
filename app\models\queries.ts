import { useQuery, useInfiniteQuery } from "@tanstack/react-query"
import { modelApi } from "@/lib/api/model"
import { effectApi } from "@/lib/api/effect"
import type { Model } from "@/types/model"
import type { Effect } from "@/types/model"

// 准备模型数据，适配前端组件需要的格式
const prepareModelData = (model: Model): Model => {
    return {
        ...model,
        // 使用默认封面图片替代null值
        cover_img: model.cover_img || '/placeholder.svg?height=400&width=300&text=Model',
        // 将model_type复制到type属性，并转为小写(兼容前端现有代码)
        type: model.model_type.toLowerCase(),
        // 添加一些默认信息
        creator: {
            name: 'Reelmind',
            avatar: '/placeholder.svg?height=32&width=32&text=R'
        },
        // 为新模型添加标签
        tags: ['AI'],
    };
}

// 准备效果数据，适配前端组件需要的格式
const prepareEffectData = (effect: Effect): Effect => {
    return {
        ...effect,
        cover_img: effect.cover_img || '/placeholder.svg?height=400&width=300&text=Effect'
    };
}

// 获取模型列表的查询
export const useModelList = () => {
    return useQuery<{ models: Model[] }>({
        queryKey: ["models"],
        queryFn: async () => {
            const modelData = await modelApi.getModels();

            // 处理后端返回的数据，适配前端组件需要的格式
            const processedModels = modelData.models.map(prepareModelData);

            return {
                models: processedModels
            };
        },
        staleTime: 1000 * 60 * 5, // 5分钟
    })
}

// 获取模型列表的无限滚动查询
export const useInfiniteModelList = (limit: number = 20) => {
    return useInfiniteQuery({
        queryKey: ["infiniteModels", limit],
        queryFn: async ({ pageParam = 1 }) => {
            const modelData = await modelApi.getModels(pageParam, limit);

            // 处理后端返回的数据，适配前端组件需要的格式
            const processedModels = modelData.models.map(prepareModelData);

            return {
                models: processedModels,
                total: modelData.total,
                nextPage: pageParam + 1,
                hasMore: pageParam * limit < modelData.total
            };
        },
        initialPageParam: 1,
        getNextPageParam: (lastPage) => {
            return lastPage.hasMore ? lastPage.nextPage : undefined;
        },
        staleTime: 1000 * 60 * 5, // 5分钟
    });
}

// 获取效果列表的查询hook
export function useEffectList() {
    return useQuery<{ effects: Effect[] }>({
        queryKey: ["effects"],
        queryFn: async () => {
            try {
                const effectData = await effectApi.getEffects();

                // 处理后端返回的数据，适配前端组件需要的格式
                const processedEffects = effectData.map(prepareEffectData);

                return {
                    effects: processedEffects
                };
            } catch (error) {
                console.error("Failed to fetch effects:", error);
                return { effects: [] };
            }
        },
        staleTime: 1000 * 60 * 5, // 5分钟
        // 如果API请求失败，返回空数组而不是抛出错误
        retry: 1,
    });
}