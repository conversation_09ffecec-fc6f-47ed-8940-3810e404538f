"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { X, FileType, Check, Loader2 } from "lucide-react"
import type { Video } from "../types"
import { formatBytes } from "@/lib/utils"
import { useTrainStore } from "@/store/useTrainStore"

interface VideoCardProps {
  video: Video
  isPreparing?: boolean
}

export function VideoCard({ video, isPreparing = false }: VideoCardProps) {
  const { removeVideo, totalSize, updateTotalSize } = useTrainStore()

  const handleRemove = () => {
    // Revoke object URL to avoid memory leaks
    URL.revokeObjectURL(video.preview)

    // Update total size
    const newTotalSize = totalSize - video.file.size
    updateTotalSize(newTotalSize)

    // Remove video from store
    removeVideo(video.id)
  }

  return (
    <Card className="aspect-video relative group overflow-hidden pixel-border bg-black border-[#F5EFFF]/50">
      <CardContent className="p-0 h-full">
        {/* Video thumbnail with overlay */}
        <video src={video.preview} className="h-full w-full object-cover" controls />

        {/* 上传状态指示器 */}
        {(isPreparing || video.url) && (
          <div className="absolute top-2 left-2 rounded-md bg-black/70 border border-[#F5EFFF]/50 p-1 flex items-center space-x-1">
            {isPreparing ? (
              <>
                <Loader2 className="h-3 w-3 text-[#F5EFFF] animate-spin" />
                <span className="text-xs text-[#F5EFFF] font-mono">PREPARING</span>
              </>
            ) : video.url ? (
              <>
                <Check className="h-3 w-3 text-[#F5EFFF]" />
                <span className="text-xs text-[#F5EFFF] font-mono">READY</span>
              </>
            ) : null}
          </div>
        )}

        {/* Cyberpunk overlay with glitch effect on hover */}
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col items-center justify-end p-3">
          <div className="absolute top-0 left-0 right-0 h-[1px] bg-[#F5EFFF]/50"></div>
          <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-[#F5EFFF]/50"></div>
          <div className="absolute right-0 top-0 bottom-0 w-[1px] bg-[#F5EFFF]/50"></div>
          <div className="absolute left-0 top-0 bottom-0 w-[1px] bg-[#F5EFFF]/50"></div>

          {/* Filename with cyberpunk style */}
          <p className="text-xs font-mono truncate w-full text-center text-[#F5EFFF] uppercase tracking-wider">
            {video.file.name.slice(0, 20)}{video.file.name.length > 20 ? '...' : ''}
          </p>

          {/* File size with neon effect */}
          <p className="text-xs text-[#F5EFFF] font-mono bg-black/50 px-2 py-0.5 rounded border border-[#F5EFFF]/30">
            {formatBytes(video.file.size)}
          </p>

          {/* Delete button with cyberpunk styling */}
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-6 w-6 bg-red-900/80 hover:bg-red-700 border border-red-500 shadow-[0_0_5px_rgba(255,0,0,0.5)]"
            onClick={handleRemove}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        {/* Diagonal corner edge */}
        <div className="absolute top-0 right-0 w-12 h-12 overflow-hidden pointer-events-none">
          <div className="absolute top-0 right-0 w-full h-full bg-[#F5EFFF] transform rotate-45 origin-top-right translate-x-1/2 -translate-y-1/2"></div>
          <FileType className="absolute top-1 right-1 h-3 w-3 text-black" />
        </div>
      </CardContent>
    </Card>
  )
}

