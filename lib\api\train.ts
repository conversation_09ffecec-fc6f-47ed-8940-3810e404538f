import { apiClient, ApiResponse } from './client';
import type { BaseModel, TrainingSettings } from '@/app/train/types';

// API响应类型
export interface BaseModelResponse extends ApiResponse<{ base_models: BaseModel[] }> {}
export interface PriceResponse extends ApiResponse<{ credits: number; discount_info?: string }> {}

export interface TrainTask {
    id: string;
    user_id: string;
    status: TrainTaskStatus;
    progress: number;
    videos: string[];
    settings: TrainingSettings;
    model_id?: string;
    priority: number;
    created_at: string;
    started_at?: string;
    completed_at?: string;
    error_message?: string;
}

export enum TrainTaskStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled",
}

export interface TrainTaskResponse extends ApiResponse<TrainTask> {}
export interface TrainTaskListResponse extends ApiResponse<{ tasks: TrainTask[]; total: number }> {}

/**
 * 训练相关API服务
 */
export const trainApi = {
    /**
     * 获取基础模型列表
     */
    getBaseModels() {
        return apiClient.get<BaseModelResponse>('/train/base_models');
    },

    /**
     * 计算训练价格 - 现在只基于视频数量，但保持原接口兼容
     */
    calculatePrice(videos: string[], settings: TrainingSettings) {
        return apiClient.post<PriceResponse>('/train/price', {
            videos,
            settings,
        });
    },

    /**
     * 开始训练
     */
    startTraining(videos: string[], settings: TrainingSettings) {
        return apiClient.post<TrainTaskResponse>('/train/start', {
            videos,
            settings,
        });
    },

    /**
     * 获取用户训练任务列表
     */
    getUserTasks(params?: {
        status?: TrainTaskStatus;
        limit?: number;
        offset?: number;
    }) {
        return apiClient.get<TrainTaskListResponse>('/train/user-tasks', {
            params: params as Record<string, string>,
        });
    },

    /**
     * 获取训练任务详情
     */
    getTaskDetail(taskId: string) {
        return apiClient.get<TrainTaskResponse>(`/train/task/${taskId}`);
    },

    /**
     * 取消训练任务
     */
    cancelTask(taskId: string) {
        return apiClient.post<TrainTaskResponse>(`/train/task/cancel/${taskId}`);
    },
}; 