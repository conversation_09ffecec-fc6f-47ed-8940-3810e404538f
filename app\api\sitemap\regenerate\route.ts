import { NextResponse } from 'next/server';
import { generateAndCacheSitemapIndex, generateAndCacheBlogSitemap } from '@/lib/sitemap-cache-vercel';
import { getBlogPostsCount } from '@/lib/blog';

// 定义API密钥（应该存储在环境变量中）
const API_KEY = process.env.SITEMAP_REGENERATE_API_KEY || 'change-this-to-a-secure-key';

// 处理站点地图重新生成请求
export async function POST(request: Request) {
    try {
        // 验证API密钥
        const { searchParams } = new URL(request.url);
        const apiKey = searchParams.get('key');

        if (apiKey !== API_KEY) {
            return NextResponse.json(
                { error: '未授权访问' },
                { status: 401 }
            );
        }

        // 获取要重新生成的站点地图类型
        const body = await request.json().catch(() => ({}));
        const { type = 'all', id } = body;

        // 根据类型重新生成站点地图
        if (type === 'index' || type === 'all') {
            await generateAndCacheSitemapIndex();
        }

        if (type === 'blog' && typeof id === 'number') {
            // 重新生成特定的博客站点地图
            await generateAndCacheBlogSitemap(id);
        } else if (type === 'all-blogs' || type === 'all') {
            // 重新生成所有博客站点地图
            const totalPosts = await getBlogPostsCount();
            const postsPerSitemap = 40000;
            const sitemapCount = Math.ceil(totalPosts / postsPerSitemap);

            // 在Vercel环境中，我们需要限制并行请求的数量，以避免超时
            // 使用串行方式处理，每次处理一个站点地图
            for (let i = 0; i < sitemapCount; i++) {
                console.log(`重新生成博客站点地图 ${i + 1}/${sitemapCount}...`);
                await generateAndCacheBlogSitemap(i);
            }
        }

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('重新生成站点地图时出错:', error);
        return NextResponse.json(
            { error: '重新生成站点地图时出错' },
            { status: 500 }
        );
    }
}
