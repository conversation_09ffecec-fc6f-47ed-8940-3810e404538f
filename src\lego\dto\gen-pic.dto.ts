import { IsNotEmpty, <PERSON>S<PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsArray } from 'class-validator';

// 定义图片生成请求的参数类
export class GenPicRequestDto {
    @IsString()
    @IsOptional()
    model_id: string;

    @IsString()
    @IsOptional()
    model: string;

    @IsString()
    @IsNotEmpty()
    prompt: string;

    @IsString()
    @IsOptional()
    negative_prompt?: string;

    @IsNumber()
    @IsOptional()
    @Min(0)
    @Max(50)
    guidance_scale?: number;

    @IsNumber()
    @IsOptional()
    @Min(1)
    @Max(100)
    steps?: number;

    @IsString()
    @IsOptional()
    seed?: string;

    @IsString()
    @IsOptional()
    aspect_ratio?: string;

    @IsNumber()
    @IsOptional()
    width?: number;

    @IsNumber()
    @IsOptional()
    height?: number;

    @IsArray()
    @IsOptional()
    refer_img_urls?: string[];

    @IsString()
    @IsOptional()
    image_url?: string;

    @IsArray()
    @IsOptional()
    image_urls?: string[];
}

export class TickProgressDto {
    @IsString()
    @IsNotEmpty()
    task_id: string;

    @IsNumber()
    @IsNotEmpty()
    progress: number;

    @IsString()
    @IsOptional()
    message?: string;
} 