"use client"

import { Clock } from "lucide-react";
import type { VideoTask } from "@/types/video-task";
import { formatTimeAgo } from "@/lib/utils";

// 共享的卡片信息区域组件
interface CardInfoSectionProps {
    video: VideoTask;
    children?: React.ReactNode;
}

/**
 * 卡片信息区域组件 - 显示视频任务的基本信息和操作按钮
 * 被所有卡片类型共享，以保持一致的UI
 */
export const CardInfoSection = ({
    video,
    children
}: CardInfoSectionProps) => {
    return (
        <div className="w-full p-4 flex flex-col justify-between bg-card">
            <div>
                {/* Prompt */}
                <h3 className="text-sm font-medium text-foreground line-clamp-2 mb-2">
                    {video.input_params.prompt.slice(0, 80)}...
                </h3>

                {/* Creation date and duration */}
                <div className="flex items-center text-xs text-muted-foreground">
                    <Clock size={12} className="mr-1.5" />
                    <span>{formatTimeAgo(video.created_at)}</span>
                    <span className="mx-1.5">•</span>
                    <span>{video.input_params.duration}</span>
                </div>
            </div>

            {/* Video metadata tags and action buttons */}
            <div className="flex flex-col space-y-3 mt-3">
                <div className="flex flex-wrap gap-1.5">
                    {video.input_params.model_id && (
                        <div className="px-2 py-0.5 bg-muted/40 rounded text-xs text-muted-foreground">
                            {video.input_params.model_id.split('-')[0] || "Model"}
                        </div>
                    )}
                    {video.input_params.gen_type && (
                        <div className="px-2 py-0.5 bg-muted/40 rounded text-xs text-muted-foreground">
                            {video.input_params.gen_type}
                        </div>
                    )}
                </div>

                {/* 子组件 - 操作按钮 */}
                {children}
            </div>
        </div>
    );
};
