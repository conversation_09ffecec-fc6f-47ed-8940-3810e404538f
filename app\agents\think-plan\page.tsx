"use client"

import Link from "next/link"
import { ArrowRight, Film, Camera, PenTool, Music, RefreshCw, Lightbulb, Globe, Sparkles, ImageIcon, Info, ArrowLeft } from "lucide-react"
import { useState, useEffect, useRef, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { qwenClient } from "@/lib/qwen-client"
import ReactMarkdown from "react-markdown"
import { generateImage, getQueueInfo, getTaskDetail } from "@/app/lego/api"
import { GenerateImageParams } from "@/app/lego/types"
import { Progress } from "@/components/ui/progress"

interface KeyframeTask {
  id: string
  prompt: string
  status: "pending" | "processing" | "completed" | "failed"
  progress: number
  imageUrl: string | null
  taskId?: string
  startedAt?: Date
  elapsedTime?: number
}

// Component that uses useSearchParams - needs to be wrapped in Suspense
function ThinkPlanContent() {
  const searchParams = useSearchParams()
  const [prompt, setPrompt] = useState<string>("")
  const [thinkContent, setThinkContent] = useState<string>("")
  const [keyframePromptsContent, setKeyframePromptsContent] = useState<string>("")
  const [keyframePrompts, setKeyframePrompts] = useState<string[]>([])
  const [keyframeTasks, setKeyframeTasks] = useState<KeyframeTask[]>([])
  const [planContent, setPlanContent] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)
  const [isGeneratingKeyframes, setIsGeneratingKeyframes] = useState(false)
  const [isGeneratingImages, setIsGeneratingImages] = useState(false)
  const [isSearching, setIsSearching] = useState(false)
  const [searchResult, setSearchResult] = useState<string | null>(null)
  const thinkContentRef = useRef<HTMLDivElement>(null)
  const keyframePromptsRef = useRef<HTMLDivElement>(null)
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)

  // Get prompt and sessionId from URL parameters
  useEffect(() => {
    const urlPrompt = searchParams?.get('prompt')
    const sessionId = searchParams?.get('sessionId')
    
    console.log('🔍 URL参数调试:', { 
      urlPrompt, 
      sessionId, 
      searchParams: searchParams?.toString(),
      allParams: Object.fromEntries(searchParams?.entries() || [])
    })
    
    if (sessionId) {
      // Load existing session
      console.log('📁 加载现有会话:', sessionId)
      loadSession(sessionId)
    } else if (urlPrompt) {
      console.log('✏️ 设置新prompt:', urlPrompt)
      setPrompt(urlPrompt)
      // 立即开始分析，不等待其他条件
      setTimeout(() => {
        if (urlPrompt && !thinkContent) {
          console.log('🚀 自动开始分析:', urlPrompt)
          startAnalysis(urlPrompt)
        }
      }, 100)
    } else {
      console.log('⚠️ 没有prompt或sessionId')
      setPrompt("")
    }
  }, [searchParams])

  // Load session from database
  const loadSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/movie-agents/sessions/${sessionId}`)
      if (response.ok) {
        const data = await response.json()
        const session = data.session

        setPrompt(session.prompt)
        setThinkContent(session.think_content || "")
        setPlanContent(session.plan_content || "")
        setKeyframePromptsContent(session.keyframe_prompts_content || "")
        setKeyframePrompts(session.keyframe_prompts || [])
        setKeyframeTasks(session.keyframe_tasks || [])
        setCurrentSessionId(sessionId)
      }
    } catch (error) {
      console.error('Error loading session:', error)
    }
  }

  // Save session to database
  const saveSession = async () => {
    if (!prompt) return null

    try {
      const sessionData = {
        prompt,
        think_content: thinkContent,
        plan_content: planContent,
        keyframe_prompts_content: keyframePromptsContent,
        keyframe_prompts: keyframePrompts,
        keyframe_tasks: keyframeTasks,
        status: 'active'
      }

      if (currentSessionId) {
        // Update existing session
        await fetch(`/api/movie-agents/sessions/${currentSessionId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(sessionData),
        })
        return currentSessionId
      } else {
        // Create new session
        const response = await fetch('/api/movie-agents/sessions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(sessionData),
        })

        if (response.ok) {
          const data = await response.json()
          setCurrentSessionId(data.session.id)
          return data.session.id
        }
      }
    } catch (error) {
      console.error('Error saving session:', error)
    }
    return null
  }

  // Auto-start analysis when prompt changes (only for new sessions)
  useEffect(() => {
    if (prompt && thinkContent === "" && !currentSessionId) {
      console.log('🎯 自动开始分析条件满足:', { prompt, thinkContent: thinkContent === "", currentSessionId: !currentSessionId })
      startAnalysis(prompt)
    }
  }, [prompt, thinkContent, currentSessionId])

  // Save session whenever content changes
  useEffect(() => {
    if (prompt && (thinkContent || planContent || keyframePromptsContent)) {
      const timeoutId = setTimeout(() => {
        saveSession()
      }, 1000) // Debounce saves

      return () => clearTimeout(timeoutId)
    }
  }, [prompt, thinkContent, planContent, keyframePromptsContent, keyframePrompts, keyframeTasks])

  // Poll active tasks for completion
  useEffect(() => {
    const activeTasks = keyframeTasks.filter(task =>
      task.status === "pending" || task.status === "processing"
    )

    if (activeTasks.length > 0) {
      // Start polling
      pollingIntervalRef.current = setInterval(async () => {
        await checkTasksCompletion()
      }, 2000)
    } else {
      // Stop polling if no active tasks
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
      }
    }

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
      }
    }
  }, [keyframeTasks])

  // Check completion status for all active tasks
  const checkTasksCompletion = async () => {
    const activeTasks = keyframeTasks.filter(task =>
      task.status === "pending" || task.status === "processing"
    )

    if (activeTasks.length === 0) return

    const updatedTasks = [...keyframeTasks]
    let hasUpdates = false

    for (const task of activeTasks) {
      if (!task.taskId) continue

      try {
        // Check queue info first
        const queueInfo = await getQueueInfo(task.taskId)
        const taskIndex = updatedTasks.findIndex(t => t.id === task.id)

        if (taskIndex === -1) continue

        if (queueInfo.position > 0) {
          // Task in queue
          if (updatedTasks[taskIndex].status !== "pending") {
            updatedTasks[taskIndex].status = "pending"
            updatedTasks[taskIndex].progress = Math.max(5, Math.min(30, 30 - queueInfo.position * 3))
            hasUpdates = true
          }
        } else if (queueInfo.isProcessing) {
          // Task is processing
          if (updatedTasks[taskIndex].status !== "processing") {
            updatedTasks[taskIndex].status = "processing"
            updatedTasks[taskIndex].startedAt = new Date()
            hasUpdates = true
          }

          // Update elapsed time and progress
          if (updatedTasks[taskIndex].startedAt) {
            const now = new Date()
            const elapsedSeconds = (now.getTime() - updatedTasks[taskIndex].startedAt!.getTime()) / 1000
            updatedTasks[taskIndex].elapsedTime = elapsedSeconds

            const estimatedTime = 60 // 60 seconds
            if (elapsedSeconds > estimatedTime) {
              updatedTasks[taskIndex].progress = 99
            } else {
              updatedTasks[taskIndex].progress = Math.min(99, 30 + (elapsedSeconds / estimatedTime) * 70)
            }
            hasUpdates = true
          }
        } else if (queueInfo.result && queueInfo.result.imageUrl) {
          // Task completed with result
          updatedTasks[taskIndex].status = "completed"
          updatedTasks[taskIndex].progress = 100
          updatedTasks[taskIndex].imageUrl = queueInfo.result.imageUrl
          hasUpdates = true
        } else {
          // Check task detail for completion
          const taskDetail = await getTaskDetail(task.taskId)
          if (taskDetail && taskDetail.status === "completed" && taskDetail.output_result?.image_url) {
            updatedTasks[taskIndex].status = "completed"
            updatedTasks[taskIndex].progress = 100
            updatedTasks[taskIndex].imageUrl = taskDetail.output_result.image_url
            hasUpdates = true
          }
        }
      } catch (error) {
        console.error(`Error checking task ${task.taskId}:`, error)
      }
    }

    if (hasUpdates) {
      setKeyframeTasks(updatedTasks)
    }

    // Check if all tasks are completed
    const stillActive = updatedTasks.filter(task =>
      task.status === "pending" || task.status === "processing"
    )

    if (stillActive.length === 0) {
      setIsGeneratingImages(false)
    }
  }

  // Function to search the internet with Qwen
  const searchInternet = async (userPrompt: string) => {
    if (!userPrompt.trim()) return

    setIsSearching(true)

    try {
      // Feature flag to disable API calls in development
      const DISABLE_API_CALLS = true; // Change to false when API is accessible

      if (DISABLE_API_CALLS) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        setSearchResult(`Based on my research, I found that videos similar to "${userPrompt}" tend to perform well with the following elements: strong opening hook, clear brand messaging, emotional storytelling, and high-quality visuals with consistent color grading.`);
      } else {
        const response = await qwenClient.search(userPrompt)
        setSearchResult(response.result)
      }
    } catch (error) {
      console.error("Error searching with Qwen:", error)
      setSearchResult("I couldn't find specific information for your request. Let's proceed with planning based on best practices.")
    } finally {
      setIsSearching(false)
    }
  }

  // Function to extract prompts from markdown text
  const extractPrompts = (content: string): string[] => {
    // Split content by lines and find numbered items
    const lines = content.split('\n');
    const prompts: string[] = [];
    let currentPrompt = '';

    for (const line of lines) {
      const trimmedLine = line.trim();
      // Check if line starts with a number followed by a period
      const numberMatch = trimmedLine.match(/^\d+\.\s+(.+)/);

      if (numberMatch) {
        // If we have a current prompt, save it
        if (currentPrompt.trim()) {
          prompts.push(currentPrompt.trim());
        }
        // Start new prompt
        currentPrompt = numberMatch[1];
      } else if (currentPrompt && trimmedLine) {
        // Continue current prompt if we have content
        currentPrompt += ' ' + trimmedLine;
      }
    }

    // Don't forget the last prompt
    if (currentPrompt.trim()) {
      prompts.push(currentPrompt.trim());
    }

    return prompts.filter(Boolean);
  }

  // Function to generate keyframes with Lego Pixel - batch submission
  const generateKeyframeImages = async () => {
    if (keyframePrompts.length === 0) return;

    setIsGeneratingImages(true);

    // Feature flag to disable API calls in development
    const DISABLE_API_CALLS = false; // Change to true for development

    // Initialize tasks
    const initialTasks: KeyframeTask[] = keyframePrompts.map((prompt, index) => ({
      id: `keyframe-${Date.now()}-${index}`,
      prompt: prompt,
      status: "pending",
      progress: 0,
      imageUrl: null
    }))

    setKeyframeTasks(initialTasks)

    if (DISABLE_API_CALLS) {
      // Simulate task processing for development
      const simulatedTasks = [...initialTasks]

      for (let i = 0; i < simulatedTasks.length; i++) {
        // Simulate task submission
        await new Promise(resolve => setTimeout(resolve, 200))
        simulatedTasks[i].taskId = `sim-task-${Date.now()}-${i}`
        simulatedTasks[i].status = "processing"
        simulatedTasks[i].startedAt = new Date()
        setKeyframeTasks([...simulatedTasks])

        // Simulate completion after some time
        setTimeout(() => {
          simulatedTasks[i].status = "completed"
          simulatedTasks[i].progress = 100
          simulatedTasks[i].imageUrl = `/placeholder-${(i % 5) + 1}.jpg`
          setKeyframeTasks([...simulatedTasks])

          // Check if all completed
          if (simulatedTasks.every(t => t.status === "completed")) {
            setIsGeneratingImages(false)
          }
        }, 3000 + i * 500) // Stagger completions
      }
    } else {
      // Real API calls - submit all tasks in parallel
      const tasksWithApiCalls = await Promise.allSettled(
        initialTasks.map(async (task, index) => {
          try {
            const params: GenerateImageParams = {
              prompt: task.prompt,
              aspectRatio: "16:9",
              guidance_scale: 7.5,
              steps: 30
            }

            const response = await generateImage(params)

            if (response.success && response.taskId) {
              return {
                ...task,
                taskId: response.taskId,
                status: "pending" as const
              }
            } else {
              return {
                ...task,
                status: "failed" as const
              }
            }
          } catch (error) {
            console.error(`Error submitting task ${index}:`, error)
            return {
              ...task,
              status: "failed" as const
            }
          }
        })
      )

      // Update tasks with API results
      const updatedTasks = tasksWithApiCalls.map((result, index) => {
        if (result.status === "fulfilled") {
          return result.value
        } else {
          return {
            ...initialTasks[index],
            status: "failed" as const
          }
        }
      })

      setKeyframeTasks(updatedTasks)
    }
  }

  // Function to generate keyframe prompts after thinking phase
  const generateKeyframePrompts = async (userPrompt: string, thinkingContent: string) => {
    if (!userPrompt.trim() || !thinkingContent.trim()) return;

    setIsGeneratingKeyframes(true);
    setKeyframePromptsContent("");
    setKeyframePrompts([]);
    setKeyframeTasks([]);

    // Feature flag to disable API calls in development
    const DISABLE_API_CALLS = true; // Change to false when API is accessible

    if (DISABLE_API_CALLS) {
      // Simulate keyframe prompts generation
      const keyframePromptsText = `# Keyframe Prompts for "${userPrompt}"

Here are 5 generalized keyframe ideas based on your prompt:

1. **Opening Scene:** A captivating shot establishing the main subject or theme from "${userPrompt}". Consider the environment and initial mood. (e.g., A wide shot of a serene landscape, or a close-up of a key object.)

2. **Developing the Narrative:** A sequence that builds upon the opening, perhaps introducing a character, action, or a core concept related to "${userPrompt}". (e.g., The character begins a journey, or a product's unique feature is highlighted.)

3. **Mid-Point / Climax:** A visually striking keyframe representing a significant moment or turning point in the story of "${userPrompt}". This could be an emotional peak or a crucial reveal. (e.g., A dramatic confrontation, a moment of discovery, or a stunning visual spectacle.)

4. **Supporting Detail / B-Roll Idea:** A close-up or alternative angle that adds texture and depth to "${userPrompt}". This could focus on smaller details, emotional reactions, or atmospheric elements. (e.g., A hand gesture, a subtle expression, or a detail of the setting.)

5. **Concluding Scene:** A final keyframe that provides a sense of closure or leaves a lasting impression, tying back to the core message of "${userPrompt}". (e.g., The character looking towards the horizon, a final product shot, or a call to action.)`;

      // Simulate streaming by sending chunks
      const chunks = keyframePromptsText.split('\n').filter(line => line.trim());
      for (const chunk of chunks) {
        await new Promise(resolve => setTimeout(resolve, 150));
        setKeyframePromptsContent(prev => prev + chunk + '\n');

        // Auto-scroll to bottom as content streams
        if (keyframePromptsRef.current) {
          keyframePromptsRef.current.scrollTop = keyframePromptsRef.current.scrollHeight;
        }
      }

      // Extract prompts from content
      const extractedPrompts = extractPrompts(keyframePromptsText);
      setKeyframePrompts(extractedPrompts);

      // Auto-generate images after a delay
      setTimeout(() => {
        generateKeyframeImages();
      }, 500);

    } else {
      // Generate keyframe prompts using Qwen
      await qwenClient.streamResponse(
        `Based on the video project request: "${userPrompt}" and this analysis: "${thinkingContent}", 
        create 10 detailed visual keyframe prompts that can be used to generate images for the key scenes in this video.
        Each prompt should be:
        1. Visually descriptive and specific to ${userPrompt}
        2. Include artistic style, lighting, and composition details
        3. Avoid mentioning URLs, references, or citations
        4. Be cohesive and focus on the main subject/theme
        5. Be between 50-150 words in length
        
        Format as a numbered list from 1 to 10, with each prompt being a paragraph.`,
        (chunk) => {
          setKeyframePromptsContent((prev) => prev + chunk);

          // Auto-scroll to bottom as content streams
          if (keyframePromptsRef.current) {
            keyframePromptsRef.current.scrollTop = keyframePromptsRef.current.scrollHeight;
          }
        }
      );

      // Extract prompts after streaming is complete
      const extractedPrompts = extractPrompts(keyframePromptsContent);
      setKeyframePrompts(extractedPrompts);

      // Auto-generate images
      setTimeout(() => {
        generateKeyframeImages();
      }, 500);
    }

    setIsGeneratingKeyframes(false);
  };

  // Function to start streaming response for thinking phase
  const startAnalysis = async (userPrompt: string) => {
    if (!userPrompt.trim()) return

    setIsLoading(true)
    setThinkContent("")
    setKeyframePromptsContent("")
    setKeyframePrompts([])
    setKeyframeTasks([])
    setPlanContent("")

    // First search the internet for relevant information
    await searchInternet(userPrompt)

    // Feature flag to disable API calls in development
    const DISABLE_API_CALLS = true; // Change to false when API is accessible

    if (DISABLE_API_CALLS) {
      // Simulate streaming for thinking phase
      const thinkingContent = `For a video project about "${userPrompt}", we need to consider several key elements:

1. **Target Audience Analysis**
   The video should resonate with the primary audience while still appealing to secondary demographics. Research indicates that viewer retention is highest when content feels personally relevant.

2. **Visual Identity and Branding**
   A consistent color palette and visual language will strengthen brand recognition. For this project, I recommend developing a storyboard that emphasizes your key brand elements while creating a visually engaging narrative.

3. **Narrative Structure**
   The most effective promotional videos follow the classic storytelling arc:
   - Hook (0-3 seconds): Capture attention immediately
   - Problem (3-10 seconds): Identify the challenge or need
   - Solution (10-20 seconds): Present your offering as the answer
   - Evidence (20-40 seconds): Show proof of effectiveness
   - Call to Action (40-45 seconds): Clear direction on next steps

4. **Technical Considerations**
   - Optimal video length: 45-60 seconds for social media, 1-2 minutes for website
   - Aspect ratios: Prepare 16:9 for website, 9:16 for Stories, and 1:1 for feed posts
   - Sound design: Background music with emotional resonance, clear voiceover if needed
   - Text overlays: Minimal, with main points emphasized for viewers watching without sound`;

      // Simulate streaming by sending chunks
      const sentences = thinkingContent.split('. ');
      for (const sentence of sentences) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setThinkContent(prev => prev + sentence + ". ");
      }

      // Auto-generate keyframe prompts after thinking phase
      await generateKeyframePrompts(userPrompt, thinkingContent);

      // Simulate streaming for planning phase after a delay
      setTimeout(async () => {
        const planningContent = `## Production Plan for "${userPrompt}"

### 1. Pre-Production (Week 1-2)
- **Script Development**
  - Create detailed outline (Day 1-2)
  - Write first draft (Day 3-5)
  - Revise and finalize script (Day 6-8)
  - Develop shot list from script (Day 9-10)

- **Visual Design**
  - Create mood boards and style frames (Day 3-6)
  - Develop storyboard based on shot list (Day 7-10)
  - Design key visual elements (Day 8-12)

### 2. Production (Week 3)
- **Keyframe Generation**
  - Generate scene visualizations using AI (Day 13-15)
  - Refine and approve keyframes (Day 16-17)

- **Video Generation**
  - Transform approved keyframes into video sequences (Day 18-20)
  - Create transitions between scenes (Day 19-21)

### 3. Post-Production (Week 4)
- **Audio Enhancement**
  - Select background music (Day 21)
  - Add sound effects (Day 22)
  - Record and integrate voiceover if needed (Day 23)

- **Final Touches**
  - Color correction and grading (Day 22-23)
  - Add text overlays and graphics (Day 24)
  - Final review and adjustments (Day 25)

### 4. Delivery (Week 4)
- Export in multiple formats for different platforms (Day 26)
- Create thumbnails and preview clips (Day 27)
- Prepare distribution strategy (Day 28)

**Timeline Summary:** 4 weeks from concept to delivery
**Budget Estimate:** $2,000-$5,000 depending on complexity`;

        // Simulate streaming by sending chunks
        const lines = planningContent.split('\n');
        for (const line of lines) {
          await new Promise(resolve => setTimeout(resolve, 100));
          setPlanContent(prev => prev + line + '\n');
        }

        setIsLoading(false);
      }, 1000);
    } else {
      let completedThinkContent = "";

      // Then start streaming the thinking phase response
      await qwenClient.streamResponse(
        `Analyze this video project request and provide detailed thinking and planning: "${userPrompt}". 
        Include visual storytelling elements, technical considerations, and creative direction.`,
        (chunk) => {
          completedThinkContent += chunk;
          setThinkContent((prev) => prev + chunk);

          // Auto-scroll to bottom as content streams
          if (thinkContentRef.current) {
            thinkContentRef.current.scrollTop = thinkContentRef.current.scrollHeight;
          }
        }
      );

      // Auto-generate keyframe prompts after thinking is complete
      await generateKeyframePrompts(userPrompt, completedThinkContent);

      // Generate plan content after keyframe prompts are complete
      await qwenClient.streamResponse(
        `Based on the video project request: "${userPrompt}", create a step-by-step production plan. 
        Include script development, visual design, production workflow, and post-production steps.`,
        (chunk) => {
          setPlanContent((prev) => prev + chunk);
        }
      );

      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background/50 to-background flex flex-col">
      {/* Header */}
      <header className="border-b p-4">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Link 
              href="/agents/conversation" 
              className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium transition-all duration-200"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Conversation
            </Link>
            <h1 className="text-xl font-semibold">The World's First AI Agent Director: Nolan</h1>
          </div>
          <Link href="/agents/studio" className="px-4 py-2 bg-primary/10 hover:bg-primary/20 text-primary-foreground rounded-md text-sm font-medium transition-colors">
            Video AI Agents
          </Link>
        </div>
      </header>

      {/* Main content */}
      <div className="flex-1 flex flex-col max-w-screen-xl mx-auto w-full px-4 py-8">
        {/* No prompt warning */}
        {!prompt && (
          <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-center gap-2 text-amber-800">
              <Info className="h-5 w-5" />
              <div>
                <p className="font-medium">No video project prompt provided</p>
                <p className="text-sm text-amber-700 mt-1">
                  Please go to the{" "}
                  <Link href="/agents/conversation" className="underline hover:text-amber-900">
                    conversation page
                  </Link>{" "}
                  to start a new video project.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Debug info in development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-blue-800">
              <p className="font-medium">Debug Info (Development Only)</p>
              <div className="text-sm mt-2 space-y-1">
                <p><strong>Prompt:</strong> {prompt || 'None'}</p>
                <p><strong>Session ID:</strong> {currentSessionId || 'None'}</p>
                <p><strong>URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
                <p><strong>Think Content:</strong> {thinkContent ? 'Present' : 'Empty'}</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Think section */}
            <div className="relative">
              <div className="sticky top-8">
                <div className="bg-blue-50/30 rounded-xl p-6 border border-blue-100 shadow-sm">
                  <div className="flex items-center mb-4 gap-2 justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <div className="h-2.5 w-2.5 rounded-full bg-blue-500"></div>
                      </div>
                      <h2 className="font-semibold text-xl">Think</h2>
                    </div>

                    {/* Refresh button */}
                    <button
                      onClick={() => startAnalysis(prompt)}
                      disabled={isLoading}
                      className="p-1.5 rounded-full hover:bg-blue-100/50 text-blue-500 transition-colors"
                      title="Refresh analysis"
                    >
                      <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    </button>
                  </div>

                  <h3 className="text-lg font-medium mb-4">Video Project Brief</h3>

                  {/* Search result display */}
                  {searchResult && (
                    <div className="mb-4 p-3 bg-white/80 rounded-lg border border-blue-100/50 text-sm">
                      <div className="flex items-center gap-2 mb-2 text-blue-600">
                        <Globe className="h-4 w-4" />
                        <span className="font-medium">Internet Research</span>
                        {isSearching && <RefreshCw className="h-3 w-3 animate-spin ml-1" />}
                      </div>
                      <p className="text-muted-foreground">{searchResult}</p>
                    </div>
                  )}

                  {/* Loading indicator */}
                  {isLoading && !thinkContent && (
                    <div className="py-8 flex flex-col items-center justify-center">
                      <RefreshCw className="h-8 w-8 text-blue-500 animate-spin mb-3" />
                      <p className="text-sm text-muted-foreground">Analyzing your project...</p>
                    </div>
                  )}

                  {/* Streaming content with Markdown */}
                  <div ref={thinkContentRef} className="prose prose-sm max-w-none text-muted-foreground max-h-[400px] overflow-y-auto">
                    {thinkContent ? (
                      <ReactMarkdown>
                        {thinkContent}
                      </ReactMarkdown>
                    ) : !isLoading ? (
                      <div>
                        <p>Creating this video will require a thoughtful approach to visual storytelling, with careful consideration of pacing, composition, and emotional impact.</p>

                        <p className="mt-2">Key elements to consider in this video production:</p>

                        <ul className="mt-2 space-y-1 list-disc list-inside">
                          <li>Opening hook that captures attention within 3 seconds</li>
                          <li>Visual consistency and branded color palette</li>
                          <li>Narrative structure with clear beginning, middle, and end</li>
                          <li>Appropriate music and sound design to enhance mood</li>
                          <li>Optimal video length for target platforms (social: 15-60s, web: 1-2min)</li>
                        </ul>
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>

            {/* Plan section */}
            <div>
              <div className="bg-white rounded-xl p-6 border shadow-sm">
                <div className="flex items-center mb-4 gap-2 justify-between">
                  <div className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-purple-500/20 flex items-center justify-center">
                      <div className="h-2.5 w-2.5 rounded-full bg-purple-500"></div>
                    </div>
                    <h2 className="font-semibold text-xl">Plan</h2>
                  </div>

                  {/* Light bulb icon showing content is AI-generated */}
                  {planContent && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Lightbulb className="h-3.5 w-3.5 text-amber-500" />
                      <span>AI-Generated</span>
                    </div>
                  )}
                </div>

                {/* Loading indicator */}
                {isLoading && !planContent && (
                  <div className="py-8 flex flex-col items-center justify-center">
                    <RefreshCw className="h-8 w-8 text-purple-500 animate-spin mb-3" />
                    <p className="text-sm text-muted-foreground">Creating production plan...</p>
                  </div>
                )}

                {/* Plan content with Markdown */}
                {planContent ? (
                  <div className="prose prose-sm max-w-none text-muted-foreground">
                    <ReactMarkdown>
                      {planContent}
                    </ReactMarkdown>
                  </div>
                ) : !isLoading ? (
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <div className="h-6 w-6 rounded-full bg-background border border-primary flex items-center justify-center">
                          <svg viewBox="0 0 24 24" width="16" height="16" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                            <polyline points="20 6 9 17 4 12"></polyline>
                          </svg>
                        </div>
                        <div className="flex items-center gap-2">
                          <PenTool className="h-4 w-4 text-purple-500" />
                          <h3 className="font-medium">Script Development</h3>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground ml-9">Create a compelling narrative structure and dialogue for your video</p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <div className="h-6 w-6 rounded-full bg-background border border-primary flex items-center justify-center">
                          <svg viewBox="0 0 24 24" width="16" height="16" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                            <polyline points="20 6 9 17 4 12"></polyline>
                          </svg>
                        </div>
                        <div className="flex items-center gap-2">
                          <Camera className="h-4 w-4 text-amber-500" />
                          <h3 className="font-medium">Keyframe Generation</h3>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground ml-9">Create AI-powered keyframes to visualize scenes before production</p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <div className="h-6 w-6 rounded-full bg-background border border-primary flex items-center justify-center">
                          <svg viewBox="0 0 24 24" width="16" height="16" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                            <polyline points="20 6 9 17 4 12"></polyline>
                          </svg>
                        </div>
                        <div className="flex items-center gap-2">
                          <Film className="h-4 w-4 text-blue-500" />
                          <h3 className="font-medium">Video Generation</h3>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground ml-9">Use AI to transform your script and keyframes into video sequences</p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <div className="h-6 w-6 rounded-full bg-background border border-primary flex items-center justify-center">
                          <svg viewBox="0 0 24 24" width="16" height="16" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                            <polyline points="20 6 9 17 4 12"></polyline>
                          </svg>
                        </div>
                        <div className="flex items-center gap-2">
                          <Music className="h-4 w-4 text-indigo-500" />
                          <h3 className="font-medium">Audio Enhancement</h3>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground ml-9">Add music, sound effects, and voice-over to elevate your video</p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <div className="h-6 w-6 rounded-full bg-background border border-primary flex items-center justify-center">
                          <svg viewBox="0 0 24 24" width="16" height="16" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                            <polyline points="20 6 9 17 4 12"></polyline>
                          </svg>
                        </div>
                        <h3 className="font-medium">Final Video Export</h3>
                      </div>
                      <p className="text-sm text-muted-foreground ml-9">Optimize your video for target platforms and export in required formats</p>
                    </div>
                  </div>
                ) : null}

                <div className="mt-8 bg-muted/30 rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium">{prompt || "Create a promotional video"}</p>

                    <Link href="/agents/studio" className="px-3 py-1.5 rounded-md bg-primary text-primary-foreground text-sm font-medium hover:bg-primary/90 transition-colors flex items-center gap-1">
                      Start Creating
                      <ArrowRight className="h-3.5 w-3.5" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Keyframe Prompts section */}
          <div>
            <div className="bg-amber-50/30 rounded-xl p-6 border border-amber-100 shadow-sm">
              <div className="flex items-center mb-4 gap-2 justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-5 w-5 rounded-full bg-amber-500/20 flex items-center justify-center">
                    <div className="h-2.5 w-2.5 rounded-full bg-amber-500"></div>
                  </div>
                  <h2 className="font-semibold text-xl">Keyframes</h2>
                </div>

                {/* Refresh button */}
                {thinkContent && (
                  <button
                    onClick={() => generateKeyframePrompts(prompt, thinkContent)}
                    disabled={isGeneratingKeyframes || isGeneratingImages}
                    className="p-1.5 rounded-full hover:bg-amber-100/50 text-amber-500 transition-colors"
                    title="Regenerate keyframes"
                  >
                    <RefreshCw className={`h-4 w-4 ${isGeneratingKeyframes ? 'animate-spin' : ''}`} />
                  </button>
                )}
              </div>

              {/* Loading indicator */}
              {isGeneratingKeyframes && !keyframePromptsContent && (
                <div className="py-8 flex flex-col items-center justify-center">
                  <RefreshCw className="h-8 w-8 text-amber-500 animate-spin mb-3" />
                  <p className="text-sm text-muted-foreground">Generating keyframe prompts...</p>
                </div>
              )}

              {/* Streaming content with Markdown */}
              {keyframePromptsContent ? (
                <>
                  <div ref={keyframePromptsRef} className="prose prose-sm max-w-none text-muted-foreground max-h-[300px] overflow-y-auto mb-6">
                    <ReactMarkdown>
                      {keyframePromptsContent}
                    </ReactMarkdown>
                  </div>

                  {/* Generated images grid */}
                  <div>
                    <div className="flex items-center mb-3 justify-between">
                      <h3 className="text-lg font-medium">Generated Keyframes</h3>

                      {/* Generate Images button */}
                      {keyframePrompts.length > 0 && !isGeneratingImages && (
                        <button
                          onClick={generateKeyframeImages}
                          className="px-3 py-1.5 rounded-md bg-amber-500 hover:bg-amber-600 text-white text-sm font-medium flex items-center gap-1"
                        >
                          <Sparkles className="h-3.5 w-3.5" />
                          <span>Generate Images</span>
                        </button>
                      )}
                    </div>

                    {/* Batch progress indicator */}
                    {isGeneratingImages && (
                      <div className="mb-4 p-3 bg-white/80 rounded-lg border border-amber-100/50">
                        <div className="flex items-center gap-2 mb-2">
                          <RefreshCw className="h-4 w-4 text-amber-500 animate-spin" />
                          <span className="font-medium text-sm">Generating Images...</span>
                          <span className="text-xs text-muted-foreground ml-auto">
                            {keyframeTasks.filter(t => t.status === "completed").length} / {keyframeTasks.length} completed
                          </span>
                        </div>
                        <div className="space-y-1">
                          {keyframeTasks.map((task, index) => (
                            <div key={task.id} className="flex items-center gap-2 text-xs">
                              <span className="w-4 text-right">{index + 1}.</span>
                              <div className="flex-1">
                                <Progress value={task.progress} className="h-1" />
                              </div>
                              <span className={`w-16 ${task.status === "completed" ? "text-green-600" :
                                task.status === "processing" ? "text-blue-600" :
                                  task.status === "failed" ? "text-red-600" : "text-gray-600"
                                }`}>
                                {task.status === "pending" ? "queued" : task.status}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Images grid */}
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mt-4">
                      {keyframeTasks.map((task, index) => (
                        <div key={task.id} className="relative group overflow-hidden rounded-lg border bg-card shadow-sm">
                          <div className="aspect-video relative bg-muted flex items-center justify-center">
                            {task.imageUrl ? (
                              <img
                                src={task.imageUrl}
                                alt={`Keyframe ${index + 1}`}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="flex flex-col items-center justify-center text-muted-foreground">
                                <ImageIcon className="h-8 w-8 mb-2 opacity-50" />
                                <span className="text-xs">Frame {index + 1}</span>
                                {task.status === "processing" && (
                                  <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                                    <RefreshCw className="h-6 w-6 text-white animate-spin" />
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Overlay with frame number and status */}
                            <div className="absolute top-2 left-2 bg-black/60 text-white text-xs font-medium rounded-full w-5 h-5 flex items-center justify-center">
                              {index + 1}
                            </div>

                            {/* Status indicator */}
                            <div className={`absolute top-2 right-2 w-2 h-2 rounded-full ${task.status === "completed" ? "bg-green-500" :
                              task.status === "processing" ? "bg-blue-500" :
                                task.status === "failed" ? "bg-red-500" : "bg-gray-400"
                              }`} />
                          </div>

                          {/* Prompt preview on hover */}
                          <div className="absolute inset-0 bg-black/70 opacity-0 group-hover:opacity-100 transition-opacity p-3 overflow-auto text-white text-xs">
                            {task.prompt}
                          </div>
                        </div>
                      ))}

                      {/* Placeholder for empty slots */}
                      {keyframeTasks.length === 0 && !isGeneratingKeyframes && (
                        Array.from({ length: 5 }).map((_, index) => (
                          <div key={index} className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                            <div className="flex flex-col items-center justify-center text-muted-foreground">
                              <ImageIcon className="h-8 w-8 mb-2 opacity-30" />
                              <span className="text-xs opacity-50">Frame {index + 1}</span>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </>
              ) : !isGeneratingKeyframes && !isLoading ? (
                <div className="p-8 flex flex-col items-center justify-center text-center">
                  <Camera className="h-10 w-10 text-amber-500/40 mb-4" />
                  <h3 className="font-medium text-base mb-2">Keyframe Visualization</h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    After analyzing your video concept, we'll generate detailed keyframe prompts
                    to visualize the key scenes in your video, which can then be turned into images.
                  </p>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Loading component for Suspense fallback
function ThinkPlanLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-background/50 to-background flex flex-col">
      {/* Header */}
      <header className="border-b p-4">
        <div className="max-w-screen-xl mx-auto flex justify-between items-center">
          <h1 className="text-xl font-semibold">The World's First AI Agent Director: Nolan</h1>
          <Link href="/agents/studio" className="px-4 py-2 bg-primary/10 hover:bg-primary/20 text-primary-foreground rounded-md text-sm font-medium transition-colors">
            Video AI Agents
          </Link>
        </div>
      </header>

      {/* Loading content */}
      <div className="flex-1 flex flex-col max-w-screen-xl mx-auto w-full px-4 py-8">
        <div className="grid grid-cols-1 gap-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Think section loading */}
            <div className="relative">
              <div className="sticky top-8">
                <div className="bg-blue-50/30 rounded-xl p-6 border border-blue-100 shadow-sm">
                  <div className="flex items-center mb-4 gap-2 justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <div className="h-2.5 w-2.5 rounded-full bg-blue-500"></div>
                      </div>
                      <h2 className="font-semibold text-xl">Think</h2>
                    </div>
                  </div>
                  <div className="py-8 flex flex-col items-center justify-center">
                    <RefreshCw className="h-8 w-8 text-blue-500 animate-spin mb-3" />
                    <p className="text-sm text-muted-foreground">Loading...</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Plan section loading */}
            <div>
              <div className="bg-white rounded-xl p-6 border shadow-sm">
                <div className="flex items-center mb-4 gap-2 justify-between">
                  <div className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-purple-500/20 flex items-center justify-center">
                      <div className="h-2.5 w-2.5 rounded-full bg-purple-500"></div>
                    </div>
                    <h2 className="font-semibold text-xl">Plan</h2>
                  </div>
                </div>
                <div className="py-8 flex flex-col items-center justify-center">
                  <RefreshCw className="h-8 w-8 text-purple-500 animate-spin mb-3" />
                  <p className="text-sm text-muted-foreground">Loading...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Main page component with Suspense boundary
export default function ThinkPlanPage() {
  return (
    <Suspense fallback={<ThinkPlanLoading />}>
      <ThinkPlanContent />
    </Suspense>
  )
}