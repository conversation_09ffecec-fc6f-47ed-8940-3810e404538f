import { create } from 'zustand';
import { QueueInfoResponse } from '@/lib/api/queue';

interface QueueInfoState {
    // 队列信息
    queueInfo: Record<string, QueueInfoResponse | null>;
    // 设置队列信息
    setQueueInfo: (taskId: string, info: QueueInfoResponse | null) => void;
    // 获取指定任务的队列信息
    getQueueInfo: (taskId: string) => QueueInfoResponse | null;
    // 清除队列信息
    clearQueueInfo: (taskId: string) => void;
    // 重置所有状态
    reset: () => void;
}

/**
 * 队列信息状态管理
 */
const useQueueInfoStore = create<QueueInfoState>((set, get) => ({
    queueInfo: {},

    setQueueInfo: (taskId, info) => {
        set((state) => ({
            queueInfo: {
                ...state.queueInfo,
                [taskId]: info
            }
        }));
    },

    getQueueInfo: (taskId) => {
        return get().queueInfo[taskId] || null;
    },

    clearQueueInfo: (taskId) => {
        set((state) => {
            const newQueueInfo = { ...state.queueInfo };
            delete newQueueInfo[taskId];
            return { queueInfo: newQueueInfo };
        });
    },

    reset: () => {
        set({ queueInfo: {} });
    }
}));

export default useQueueInfoStore; 