# 反馈系统后端模块

## 概述

本模块实现了完整的用户反馈系统后端功能，包括用户反馈提交、管理员反馈管理、统计分析等功能。

## 模块结构

```
src/feedback/
├── dto/
│   └── feedback.dto.ts          # 数据传输对象定义
├── sql/
│   ├── feedbacks_table.sql      # 数据库表结构
│   └── migration_add_feedbacks.sql # 数据库迁移脚本
├── feedback.controller.ts       # 用户反馈控制器
├── feedback-admin.controller.ts # 管理员反馈控制器
├── feedback.service.ts          # 用户反馈服务
├── feedback-admin.service.ts    # 管理员反馈服务
├── feedback.module.ts           # 反馈模块定义
└── README.md                    # 本文档
```

## 功能特性

### 用户功能
- ✅ 创建反馈（支持多种类型和优先级）
- ✅ 查看自己的反馈列表
- ✅ 查看反馈详情
- ✅ 更新反馈状态（仅限关闭）
- ✅ 删除反馈（仅限待处理或已关闭）

### 管理员功能
- ✅ 查看所有反馈列表
- ✅ 查看反馈详情（包含用户信息）
- ✅ 更新反馈状态
- ✅ 回复反馈
- ✅ 删除反馈
- ✅ 获取反馈统计信息

### 系统功能
- ✅ 完整的权限控制（RLS策略）
- ✅ 数据验证和约束
- ✅ 自动时间戳更新
- ✅ 索引优化
- ✅ API文档（Swagger）

## API接口

### 用户接口

#### 创建反馈
```
POST /feedback
Authorization: Bearer <token>
Content-Type: application/json

{
  "subject": "Video generation failed",
  "message": "When I try to generate a video with the prompt 'sunset', it fails with an error.",
  "type": "bug_report",
  "priority": "high",
  "attachments": ["https://example.com/screenshot.png"],
  "user_agent": "Mozilla/5.0...",
  "page_url": "https://reelmind.ai/create"
}
```

#### 获取用户反馈列表
```
GET /feedback/user?page=1&limit=10&status=pending&type=bug_report
Authorization: Bearer <token>
```

#### 获取反馈详情
```
GET /feedback/:id
Authorization: Bearer <token>
```

#### 更新反馈状态
```
PUT /feedback/:id/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "closed"
}
```

#### 删除反馈
```
DELETE /feedback/:id
Authorization: Bearer <token>
```

### 管理员接口

#### 获取所有反馈列表
```
GET /feedback/admin?page=1&limit=10&status=pending&type=bug_report
Authorization: Bearer <admin_token>
```

#### 管理员获取反馈详情
```
GET /feedback/admin/:id
Authorization: Bearer <admin_token>
```

#### 管理员更新反馈状态
```
PUT /feedback/admin/:id/status
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "status": "in_progress"
}
```

#### 管理员回复反馈
```
PUT /feedback/admin/:id/reply
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "reply": "Thank you for your feedback. We are working on this issue.",
  "status": "resolved"
}
```

#### 获取反馈统计
```
GET /feedback/admin/stats/overview
Authorization: Bearer <admin_token>
```

## 数据模型

### 反馈类型 (FeedbackType)
- `bug_report` - Bug报告
- `feature_request` - 功能请求
- `general_feedback` - 一般反馈
- `technical_support` - 技术支持
- `other` - 其他

### 优先级 (FeedbackPriority)
- `low` - 低
- `medium` - 中
- `high` - 高
- `urgent` - 紧急

### 状态 (FeedbackStatus)
- `pending` - 待处理
- `in_progress` - 处理中
- `resolved` - 已解决
- `closed` - 已关闭

### 数据库表结构
```sql
CREATE TABLE public.feedbacks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'general_feedback',
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    attachments JSONB DEFAULT '[]'::jsonb,
    user_agent TEXT,
    page_url TEXT,
    admin_reply TEXT,
    admin_reply_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);
```

## 权限控制

### 行级安全策略 (RLS)
- 用户只能查看、创建、更新、删除自己的反馈
- 用户只能将反馈状态更新为 `closed`
- 用户只能删除 `pending` 或 `closed` 状态的反馈
- 管理员可以查看、更新、删除所有反馈

### 角色权限
- **普通用户**: 管理自己的反馈
- **管理员**: 管理所有反馈，回复反馈，查看统计
- **超级管理员**: 所有管理员权限

## 部署说明

### 1. 数据库迁移
```bash
# 执行迁移脚本
psql -h <host> -U <user> -d <database> -f src/feedback/sql/migration_add_feedbacks.sql
```

### 2. 环境变量
确保以下环境变量已配置：
- `SUPABASE_URL` - Supabase项目URL
- `SUPABASE_ANON_KEY` - Supabase匿名密钥
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase服务角色密钥

### 3. 模块注册
模块已自动注册到 `app.module.ts` 中。

## 测试

### 单元测试
```bash
npm run test src/feedback
```

### 集成测试
```bash
npm run test:e2e feedback
```

### API测试
使用Postman或其他API测试工具测试接口：
1. 导入Swagger文档
2. 配置认证token
3. 测试各个接口功能

## 监控和日志

### 日志记录
- 所有操作都会记录详细日志
- 错误日志包含堆栈信息
- 使用CustomLogger服务统一管理

### 性能监控
- 数据库查询性能监控
- API响应时间监控
- 错误率监控

## 扩展功能

### 计划中的功能
- [ ] 邮件通知（反馈状态变更）
- [ ] 反馈分类自动化
- [ ] 反馈优先级自动评估
- [ ] 反馈模板系统
- [ ] 批量操作功能
- [ ] 反馈导出功能
- [ ] 反馈分析报告

### 集成建议
- 与邮件服务集成（SendGrid、AWS SES等）
- 与通知系统集成
- 与客服系统集成
- 与分析系统集成

## 故障排除

### 常见问题
1. **权限错误**: 检查RLS策略和用户角色
2. **数据验证失败**: 检查输入数据格式和长度
3. **数据库连接问题**: 检查Supabase配置
4. **认证失败**: 检查JWT token有效性

### 调试技巧
1. 启用详细日志记录
2. 使用数据库查询日志
3. 检查网络连接
4. 验证环境变量配置

## 贡献指南

1. 遵循项目代码规范
2. 添加适当的测试用例
3. 更新相关文档
4. 提交前运行所有测试
