import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { calculateReadingTime } from '@/lib/utils';

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 获取单个博客文章
export async function GET(
    request: NextRequest,
    { params }: { params: { slug: string } }
) {
    try {
        const resolvedParams = await params;
        const slug = resolvedParams.slug;

        if (!slug) {
            return NextResponse.json(
                { error: 'Slug is required' },
                { status: 400 }
            );
        }

        // 根据slug获取博客文章
        const { data, error } = await supabase
            .from('blog_posts')
            .select('*')
            .eq('slug', slug)
            .single();

        if (error) {
            console.error('Error fetching blog post by slug:', error);
            if (error.code === 'PGRST116') {
                return NextResponse.json(
                    { error: 'Post not found' },
                    { status: 404 }
                );
            }
            return NextResponse.json(
                { error: error.message },
                { status: 500 }
            );
        }

        return NextResponse.json(data);
    } catch (error) {
        console.error('Error in GET /api/blog/[slug]:', error);
        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    }
}

// 更新单个博客文章
export async function PUT(
    request: NextRequest,
    { params }: { params: { slug: string } }
) {
    try {
        // 获取认证信息
        const cookieStore = await cookies();
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
        const supabaseAdmin = createClient(supabaseUrl, supabaseKey, {
            auth: {
                persistSession: false,
            }
        });

        // 从cookie中获取会话token
        const token = cookieStore.get('sb-access-token')?.value;

        if (!token) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // 设置会话
        const { data: { user }, error: sessionError } = await supabaseAdmin.auth.getUser(token);

        if (sessionError || !user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // 检查用户是否有管理员权限
        const { data: userData, error: userError } = await supabaseAdmin
            .from('users')
            .select('role')
            .eq('id', user.id)
            .single();

        if (userError || !userData || userData.role !== 'admin') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
        }

        const resolvedParams = await params;
        const slug = resolvedParams.slug;

        if (!slug) {
            return NextResponse.json({ error: 'Missing slug parameter' }, { status: 400 });
        }

        // 获取现有文章
        const { data: existingPost, error: fetchError } = await supabase
            .from('blog_posts')
            .select('id, created_at')
            .eq('slug', slug)
            .single();

        if (fetchError) {
            console.error('Error fetching existing blog post:', fetchError);

            if (fetchError.code === 'PGRST116') {
                return NextResponse.json({ error: 'Post not found' }, { status: 404 });
            }

            return NextResponse.json({ error: fetchError.message }, { status: 500 });
        }

        const body = await request.json();

        // 基本验证
        if (!body.title || !body.content || !body.category) {
            return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
        }

        // 处理标签
        let tags = [];
        if (body.tags && Array.isArray(body.tags)) {
            tags = body.tags;
        }

        // 计算阅读时间
        const readingTime = calculateReadingTime(body.content);

        // 准备要更新的数据
        const post: any = {
            title: body.title,
            content: body.content,
            category: body.category,
            tags,
            excerpt: body.excerpt || null,
            featuredImage: body.featuredImage || null,
            featured: body.featured || false,
            readingTime,
            updatedAt: new Date().toISOString(),
            // 如果文章之前未发布，且现在要发布，更新发布时间
            created_at: existingPost.created_at,
            meta: {
                title: body.metaTitle || body.title,
                description: body.metaDescription || body.excerpt || '',
                keywords: body.metaKeywords || tags.join(', '),
                ogImage: body.ogImage || body.featuredImage || '',
            }
        };

        // 如果提供了新slug，更新它
        if (body.slug && body.slug !== slug) {
            post.slug = body.slug;
        }

        // 更新数据库
        const { data, error } = await supabase
            .from('blog_posts')
            .update(post)
            .eq('id', existingPost.id)
            .select('*')
            .single();

        if (error) {
            console.error('Error updating blog post:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json(data);
    } catch (error) {
        console.error(`Error in PUT /api/blog/${params.slug}:`, error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}

// 删除单个博客文章
export async function DELETE(
    request: NextRequest,
    { params }: { params: { slug: string } }
) {
    try {
        // 获取认证信息
        const cookieStore = await cookies();
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
        const supabaseAdmin = createClient(supabaseUrl, supabaseKey, {
            auth: {
                persistSession: false,
            }
        });

        // 从cookie中获取会话token
        const token = cookieStore.get('sb-access-token')?.value;

        if (!token) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // 设置会话
        const { data: { user }, error: sessionError } = await supabaseAdmin.auth.getUser(token);

        if (sessionError || !user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // 检查用户是否有管理员权限
        const { data: userData, error: userError } = await supabaseAdmin
            .from('users')
            .select('role')
            .eq('id', user.id)
            .single();

        if (userError || !userData || userData.role !== 'admin') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
        }

        const resolvedParams = await params;
        const slug = resolvedParams.slug;

        if (!slug) {
            return NextResponse.json({ error: 'Missing slug parameter' }, { status: 400 });
        }

        // 获取文章ID
        const { data: post, error: fetchError } = await supabase
            .from('blog_posts')
            .select('id')
            .eq('slug', slug)
            .single();

        if (fetchError) {
            console.error('Error fetching blog post:', fetchError);

            if (fetchError.code === 'PGRST116') {
                return NextResponse.json({ error: 'Post not found' }, { status: 404 });
            }

            return NextResponse.json({ error: fetchError.message }, { status: 500 });
        }

        // 从数据库中删除
        const { error } = await supabase
            .from('blog_posts')
            .delete()
            .eq('id', post.id);

        if (error) {
            console.error('Error deleting blog post:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error(`Error in DELETE /api/blog/${params.slug}:`, error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
} 