'use client';

import { useRouter } from 'next/navigation';
import { useBlogPromptStore } from '@/store';

interface PromptTemplateButtonProps {
    prompt: string;
    className?: string;
}

export default function PromptTemplateButton({ prompt, className }: PromptTemplateButtonProps) {
    const router = useRouter();
    const setPrompt = useBlogPromptStore(state => state.setPrompt);
    
    const handleClick = () => {
        // 将prompt存储到store中
        setPrompt(prompt);
        
        // 跳转到lego页面
        router.push('/lego');
    };
    
    return (
        <button 
            onClick={handleClick}
            className={`inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-md hover:from-indigo-700 hover:to-purple-700 transition-all shadow-sm ${className || ''}`}
        >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Use Template
        </button>
    );
}
