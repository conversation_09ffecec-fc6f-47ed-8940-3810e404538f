'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import type { BlogPost } from '@/types/blog';
import MarkdownEditor from './MarkdownEditor';
import Image from 'next/image';

interface BlogFormProps {
    post?: BlogPost;
    mode: 'create' | 'edit';
}

export default function BlogForm({ post, mode }: BlogFormProps) {
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [imageUploadLoading, setImageUploadLoading] = useState(false);
    const [imageUploadError, setImageUploadError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [formData, setFormData] = useState<Partial<BlogPost>>({
        title: '',
        slug: '',
        content: '',
        excerpt: '',
        cover_image: '',
        created_at: new Date().toISOString().split('T')[0],
        meta_title: '',
        meta_description: '',
        meta_keywords: [],
    });

    // 如果是编辑模式，加载已有数据
    useEffect(() => {
        if (mode === 'edit' && post) {
            setFormData({
                ...post,
                created_at: post.created_at ? new Date(post.created_at).toISOString().split('T')[0] : '',
                meta_keywords: post.meta_keywords || [],
            });
        }
    }, [mode, post]);

    // 处理输入变化
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value,
        }));
    };

    // 处理内容编辑器变化
    const handleContentChange = (value: string) => {
        setFormData(prev => ({
            ...prev,
            content: value,
        }));
    };

    // 自动生成slug
    const generateSlug = () => {
        if (formData.title) {
            const slug = formData.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');
            setFormData(prev => ({ ...prev, slug }));
        }
    };

    // 提交表单
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setError(null);

        try {
            const endpoint = mode === 'create' ? '/api/blog' : `/api/blog?id=${post?.id}`;
            const method = mode === 'create' ? 'POST' : 'PUT';

            const response = await fetch(endpoint, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            console.log(response)

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                const errorMessage = errorData.error || 'Failed to save post';
                throw new Error(errorMessage);
            }

            // 保存成功后导航回博客管理页面
            router.push('/admin/blog');
            router.refresh();
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An unknown error occurred');
        } finally {
            setIsLoading(false);
        }
    };

    // 处理文件输入
    const handleFileInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            await uploadImage(file);
        }
    };

    // 处理拖放图片
    const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();

        const file = e.dataTransfer.files?.[0];
        if (file && file.type.startsWith('image/')) {
            await uploadImage(file);
        }
    };

    // 处理拖动事件，防止浏览器默认行为
    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
    };

    // 处理粘贴图片
    const handlePaste = async (e: React.ClipboardEvent<HTMLDivElement>) => {
        const items = e.clipboardData.items;

        for (let i = 0; i < items.length; i++) {
            if (items[i].type.indexOf('image') !== -1) {
                const file = items[i].getAsFile();
                if (file) {
                    await uploadImage(file);
                    break;
                }
            }
        }
    };

    // 上传图片
    const uploadImage = async (file: File) => {
        setImageUploadLoading(true);
        setImageUploadError(null);

        try {
            // 检查文件类型
            if (!file.type.startsWith("image/")) {
                throw new Error("Only image files are allowed");
            }

            // 检查文件大小 (10MB限制)
            const maxSize = 10 * 1024 * 1024;
            if (file.size > maxSize) {
                throw new Error("The image size cannot exceed 10MB");
            }

            // 先将图片显示在UI上 - 本地预览
            const reader = new FileReader();
            reader.onloadend = (event) => {
                if (event.target && event.target.result) {
                    // 暂时使用本地预览
                    setFormData(prev => ({
                        ...prev,
                        cover_image: event.target.result as string
                    }));
                }
            };
            reader.readAsDataURL(file);

            // 获取预签名URL
            const response = await fetch('/api/upload/presign', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filename: file.name,
                    fileType: file.type,
                    fileSize: file.size,
                    bucketName: 'blog-img'
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to get upload URL');
            }

            const { data } = await response.json();

            // 使用预签名URL上传文件到Cloudflare R2
            const uploadResponse = await fetch(data.url, {
                method: 'PUT',
                headers: {
                    'Content-Type': file.type,
                },
                body: file,
            });

            if (!uploadResponse.ok) {
                throw new Error('Upload image failed');
            }

            // 上传成功后，更新图片URL为Cloudflare R2的URL
            setFormData(prev => ({
                ...prev,
                cover_image: data.publicUrl
            }));

        } catch (error) {
            console.error('Error uploading image:', error);
            setImageUploadError(error instanceof Error ? error.message : 'Upload failed');
        } finally {
            setImageUploadLoading(false);
            // 清空文件输入
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    // 触发文件选择器
    const handleBrowseClick = () => {
        fileInputRef.current?.click();
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded text-red-600 dark:bg-red-900/20 dark:border-red-800 dark:text-red-400">
                    <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                        {error}
                    </div>
                </div>
            )}

            <div className="grid grid-cols-1 gap-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Title
                        </label>
                        <input
                            type="text"
                            id="title"
                            name="title"
                            value={formData.title}
                            onChange={handleChange}
                            onBlur={() => !formData.slug && generateSlug()}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800"
                            placeholder="Enter post title"
                            required
                        />
                    </div>

                    <div>
                        <label htmlFor="slug" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Slug
                        </label>
                        <div className="flex">
                            <input
                                type="text"
                                id="slug"
                                name="slug"
                                value={formData.slug}
                                onChange={handleChange}
                                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-l shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800"
                                placeholder="url-slug"
                                required
                            />
                            <button
                                type="button"
                                onClick={generateSlug}
                                className="px-3 py-2 bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-r hover:bg-gray-300 dark:hover:bg-gray-600"
                            >
                                Generate
                            </button>
                        </div>
                    </div>

                    <div>
                        <label htmlFor="created_at" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Publish Date
                        </label>
                        <input
                            type="date"
                            id="created_at"
                            name="created_at"
                            value={formData.created_at}
                            onChange={handleChange}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800"
                            required
                        />
                    </div>
                </div>

                <div>
                    <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Excerpt
                    </label>
                    <textarea
                        id="excerpt"
                        name="excerpt"
                        value={formData.excerpt}
                        onChange={handleChange}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800"
                        placeholder="Brief summary for blog listings"
                        required
                    />
                </div>

                {/* 封面图片上传区域 */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Cover Image
                    </label>
                    <div
                        className="w-full border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-4 transition-colors hover:border-gray-400 dark:hover:border-gray-600"
                        onDrop={handleDrop}
                        onDragOver={handleDragOver}
                        onPaste={handlePaste}
                        tabIndex={0}
                    >
                        <div className="flex flex-col items-center justify-center space-y-4">
                            {formData.cover_image ? (
                                <div className="relative w-full h-48 md:h-60 overflow-hidden rounded bg-gray-100 dark:bg-gray-800">
                                    <Image
                                        src={formData.cover_image}
                                        alt="Cover preview"
                                        fill
                                        className="object-cover"
                                        sizes="(max-width: 768px) 100vw, 50vw"
                                    />
                                    <button
                                        type="button"
                                        className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                                        onClick={() => setFormData(prev => ({ ...prev, cover_image: '' }))}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            ) : (
                                <>
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">Drag and drop an image here, or click to browse</p>
                                    <p className="text-xs text-gray-400 dark:text-gray-500">You can also paste an image from clipboard</p>
                                </>
                            )}

                            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full">
                                <button
                                    type="button"
                                    onClick={handleBrowseClick}
                                    className="px-4 py-2 bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600 flex-1 text-center"
                                    disabled={imageUploadLoading}
                                >
                                    {imageUploadLoading ? 'Uploading...' : 'Browse Files'}
                                </button>
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleFileInputChange}
                                    className="hidden"
                                />

                                <div className="flex-1 relative">
                                    <input
                                        type="text"
                                        id="cover_image"
                                        name="cover_image"
                                        value={formData.cover_image}
                                        onChange={handleChange}
                                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800"
                                        placeholder="Or enter image URL"
                                    />
                                </div>
                            </div>

                            {imageUploadError && (
                                <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                                    {imageUploadError}
                                </p>
                            )}
                        </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Images are automatically uploaded to cloud storage.
                    </p>
                </div>

                <div>
                    <label htmlFor="content" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Content
                    </label>
                    <MarkdownEditor
                        value={formData.content || ''}
                        onChange={handleContentChange}
                        height="800px"
                        placeholder="Write your content here..."
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Supports Markdown. You can paste, drag & drop, or upload images - they will be automatically uploaded to cloud storage.
                    </p>
                </div>

                <details className="border border-gray-200 dark:border-gray-700 rounded p-3">
                    <summary className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                        SEO Settings
                    </summary>
                    <div className="mt-3 space-y-4">
                        <div>
                            <label htmlFor="meta_title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Meta Title
                            </label>
                            <input
                                type="text"
                                id="meta_title"
                                name="meta_title"
                                value={formData.meta_title}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800"
                                placeholder="Leave empty to use post title"
                            />
                        </div>

                        <div>
                            <label htmlFor="meta_description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Meta Description
                            </label>
                            <textarea
                                id="meta_description"
                                name="meta_description"
                                value={formData.meta_description}
                                onChange={handleChange}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800"
                                placeholder="Leave empty to use post excerpt"
                            />
                        </div>
                    </div>
                </details>

                <div className="flex justify-between pt-2">
                    <button
                        type="button"
                        onClick={() => router.push('/admin/blog')}
                        className="px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        disabled={isLoading}
                        className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isLoading ? 'Saving...' : mode === 'create' ? 'Publish Post' : 'Update Post'}
                    </button>
                </div>
            </div>
        </form>
    );
} 