import { IsString, IsOptional, IsBoolean, IsUUID, IsIn } from 'class-validator';

/**
 * 管理员获取所有帖子的DTO
 */
export class AdminGetPostsDto {
    @IsOptional()
    @IsString()
    readonly visibility?: string;

    @IsOptional()
    @IsString()
    readonly nsfw_level?: string;

    @IsOptional()
    readonly limit?: number = 20;

    @IsOptional()
    readonly offset?: number = 0;

    @IsOptional()
    @IsBoolean()
    readonly is_admin?: boolean = false;
}

/**
 * 管理员更新帖子可见性的DTO
 */
export class UpdatePostVisibilityDto {
    @IsString()
    @IsIn(['public', 'private', 'unlisted', 'deleted'])
    readonly visibility: string;
}

/**
 * 管理员更新帖子NSFW级别的DTO
 */
export class UpdatePostNsfwLevelDto {
    @IsString()
    @IsIn(['none', 'mild', 'moderate', 'severe'])
    readonly nsfw_level: string;
} 