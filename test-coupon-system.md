# 优惠券系统测试指南

## 测试环境准备

### 1. 数据库设置
```sql
-- 确保数据库表已创建
\d user_coupons
```

### 2. 测试用户准备
- 创建一个从未购买过会员的新用户
- 创建一个已购买过会员的老用户

## 功能测试用例

### 1. 新用户优惠券领取流程

#### 测试步骤：
1. 使用新用户账号登录
2. 访问网站首页
3. 检查是否显示全局优惠券横幅
4. 点击"立即领取"按钮
5. 验证优惠券是否成功领取
6. 检查会员页面的优惠券状态

#### 预期结果：
- ✅ 显示全局优惠券横幅
- ✅ 成功领取优惠券
- ✅ 优惠券状态为"已激活"
- ✅ 显示24小时倒计时
- ✅ 会员页面显示优惠券信息

### 2. 老用户资格检查

#### 测试步骤：
1. 使用已购买过会员的用户登录
2. 访问网站首页
3. 检查是否显示优惠券横幅
4. 访问会员页面查看优惠券状态

#### 预期结果：
- ❌ 不显示全局优惠券横幅
- ❌ 会员页面显示"暂无可用优惠券"

### 3. 价格计算测试

#### 测试步骤：
1. 使用有活跃优惠券的用户登录
2. 访问会员页面
3. 查看会员计划价格显示
4. 验证折扣计算是否正确

#### 预期结果：
- ✅ 显示原价（划线）
- ✅ 显示折扣价（绿色）
- ✅ 显示"30% OFF"标识
- ✅ 显示折扣详情

### 4. 优惠券过期测试

#### 测试步骤：
1. 手动修改数据库中优惠券的过期时间
```sql
UPDATE user_coupons 
SET expires_at = NOW() - INTERVAL '1 hour'
WHERE user_id = 'test_user_id';
```
2. 刷新页面
3. 检查优惠券状态

#### 预期结果：
- ✅ 优惠券状态变为"已过期"
- ✅ 不再显示折扣价格
- ✅ 横幅消失

### 5. API端点测试

#### 使用curl或Postman测试：

```bash
# 1. 检查优惠券状态
curl -X GET "http://localhost:3000/api/coupon/status" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. 领取优惠券
curl -X POST "http://localhost:3000/api/coupon/claim" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"coupon_type": "first_month_discount"}'

# 3. 检查资格
curl -X GET "http://localhost:3000/api/coupon/check-eligibility" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 4. 计算价格
curl -X POST "http://localhost:3000/api/coupon/calculate-price" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"original_price": 29.99, "coupon_id": "COUPON_ID"}'
```

## 边界情况测试

### 1. 重复领取测试
- 尝试多次领取同一类型优惠券
- 预期：第二次领取失败，显示"已经领取过"

### 2. 并发领取测试
- 同时发送多个领取请求
- 预期：只有一个请求成功

### 3. 无效优惠券ID测试
- 使用不存在的优惠券ID计算价格
- 预期：返回原价，无折扣

### 4. 过期优惠券使用测试
- 尝试使用已过期的优惠券
- 预期：不应用折扣

## 性能测试

### 1. 页面加载性能
- 测试添加优惠券功能后的页面加载时间
- 确保不影响用户体验

### 2. API响应时间
- 测试各个API端点的响应时间
- 目标：< 500ms

### 3. 数据库查询优化
- 检查数据库查询计划
- 确保索引正确使用

## 安全测试

### 1. 权限验证
- 尝试在未登录状态下访问API
- 预期：返回401未授权

### 2. 用户隔离
- 尝试访问其他用户的优惠券
- 预期：无法访问

### 3. SQL注入测试
- 在请求参数中注入SQL代码
- 预期：请求被正确处理，无安全漏洞

## 用户体验测试

### 1. 移动端适配
- 在不同尺寸的移动设备上测试
- 确保UI正常显示和交互

### 2. 暗色模式
- 测试在暗色模式下的显示效果
- 确保颜色对比度合适

### 3. 无障碍访问
- 使用屏幕阅读器测试
- 确保键盘导航正常

## 错误处理测试

### 1. 网络错误
- 断开网络连接后测试
- 预期：显示友好的错误信息

### 2. 服务器错误
- 模拟服务器500错误
- 预期：显示重试选项

### 3. 数据格式错误
- 发送格式错误的请求
- 预期：返回400错误和详细信息

## 测试数据清理

### 测试完成后清理数据：
```sql
-- 删除测试优惠券
DELETE FROM user_coupons WHERE user_id IN ('test_user_1', 'test_user_2');
```

## 自动化测试

### 建议使用的测试框架：
- **前端**: Jest + React Testing Library
- **后端**: Jest + Supertest
- **E2E**: Playwright 或 Cypress

### 示例测试用例：
```typescript
// 前端组件测试
describe('CouponStatusCard', () => {
  it('should display active coupon information', () => {
    // 测试代码
  });
});

// 后端API测试
describe('CouponController', () => {
  it('should claim coupon for eligible user', () => {
    // 测试代码
  });
});
```

## 监控和报警

### 设置监控指标：
- 优惠券领取成功率
- API响应时间
- 错误率
- 用户转化率

### 报警规则：
- API错误率 > 5%
- 响应时间 > 1秒
- 优惠券领取失败率 > 10%
