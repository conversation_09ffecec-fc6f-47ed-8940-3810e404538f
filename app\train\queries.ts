import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import type { BaseModel, TrainingSettings } from "./types"
import { trainApi, TrainTask, TrainTaskStatus } from "@/lib/api/train"

// 获取基础模型列表
export const useBaseModels = () => {
    return useQuery<BaseModel[]>({
        queryKey: ["baseModels"],
        queryFn: async () => {
            const { data } = await trainApi.getBaseModels();
            return data.base_models;
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
    })
}

// 计算训练价格 - 现在只基于视频数量
export const useCalculatePrice = (videos: string[], settings: TrainingSettings, options?: { enabled?: boolean }) => {
    return useQuery<{ credits: number; discount_info?: string }>({
        queryKey: ["price", { videoCount: videos.length }],
        queryFn: async () => {
            if (videos.length === 0) return { credits: 0 };
            // 现在只使用视频ID数组和settings
            const { data } = await trainApi.calculatePrice(videos, settings);
            return data;
        },
        enabled: options?.enabled !== undefined ? options.enabled : videos.length > 0,
        staleTime: 1000 * 30, // 30 seconds
    })
}

// 开始训练
export const useStartTraining = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: async ({ videos, settings }: { videos: string[]; settings: TrainingSettings }) => {
            if (videos.length === 0) {
                throw new Error("Please upload at least one video")
            }

            if (videos.length > 10) {
                throw new Error("Maximum 10 videos allowed")
            }

            if (!settings.trigger_word) {
                throw new Error("Please enter a trigger word")
            }

            const { data } = await trainApi.startTraining(videos, settings);
            return data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["userTasks"] })
        },
    })
}

// 获取用户训练任务列表
export const useUserTasks = (
    status?: TrainTaskStatus,
    limit: number = 10,
    offset: number = 0,
    options?: { enabled?: boolean }
) => {
    return useQuery<{ tasks: TrainTask[]; total: number }>({
        queryKey: ["userTasks", { status, limit, offset }],
        queryFn: async () => {
            const { data } = await trainApi.getUserTasks({ status, limit, offset });
            return data;
        },
        staleTime: 1000 * 30, // 30 seconds,
        ...options
    })
}

// 获取训练任务详情
export const useTaskDetail = (taskId: string) => {
    return useQuery<TrainTask>({
        queryKey: ["taskDetail", taskId],
        queryFn: async () => {
            const { data } = await trainApi.getTaskDetail(taskId);
            return data;
        },
        staleTime: 1000 * 30, // 30 seconds
    })
}

// 取消训练任务
export const useCancelTask = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: async (taskId: string) => {
            const { data } = await trainApi.cancelTask(taskId);
            return data;
        },
        onSuccess: (_, taskId) => {
            queryClient.invalidateQueries({ queryKey: ["taskDetail", taskId] })
            queryClient.invalidateQueries({ queryKey: ["userTasks"] })
        },
    })
}

