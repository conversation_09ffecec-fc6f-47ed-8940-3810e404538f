"use client";

import { Download, Info, Video, AlertTriangle, RefreshCw } from "lucide-react";
import Image from "next/image";
import { downloadImage, getTaskDetail } from "../api";
import { LegoHistoryItem } from "../types";
import React, { useEffect, useRef, useState } from "react";
import { Badge } from "@/components/ui/badge";
import CSSNoise from "./CSSNoise";
import { Progress } from "@/components/ui/progress";
import { useLegoStore } from "../store";
import { useRouter } from "next/navigation";
import { saveReferenceData } from "@/lib/storage/tempDataStorage";

const MIN_PROGRESS = 20;

interface HistoryCardProps {
    item: LegoHistoryItem;
}

export default function HistoryCard({ item }: HistoryCardProps) {
    // 使用状态来存储最新的任务信息
    const [taskItem, setTaskItem] = useState<LegoHistoryItem>(item);
    // 使用ref来存储轮询ID
    const intervalRef = useRef<NodeJS.Timeout | null>(null);
    // 添加下载状态
    const [isDownloading, setIsDownloading] = useState(false);
    // 使用store中的updateHistoryItem方法来更新全局状态
    const {
        updateHistoryItem,
        setUserPrompt,
        setNegativePrompt,
        setSeed,
        setSteps,
        setGuidanceScale,
        setAspectRatio,
        setSelectedModel,
        availableModels,
        addFluxImage,
        clearFluxImages,
        addCard,
        selectedModel,
        themeCards,
        removeCard
    } = useLegoStore();
    // 使用router进行页面导航
    const router = useRouter();

    // 处理下载
    const handleDownload = async (imageUrl: string) => {
        if (isDownloading) return; // 防止重复点击

        setIsDownloading(true);

        try {
            // 生成文件名
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            const filename = `lego-creation-${timestamp}.png`;

            const success = await downloadImage(imageUrl, filename);

            if (success) {
                console.log('Download completed successfully');
            }
        } catch (error) {
            console.error('Download failed:', error);
        } finally {
            setIsDownloading(false);
        }
    };

    // 处理"Make Video"按钮点击 - 将图片作为参考图片带入到create页面
    const handleMakeVideo = (e: React.MouseEvent, imageUrl: string) => {
        e.stopPropagation(); // 阻止事件冒泡

        // 保存图片URL和提示词到本地存储
        saveReferenceData(
            imageUrl,
            taskItem.input_params?.prompt || '',
            {
                // 保存其他可能有用的参数
                seed: taskItem.input_params?.seed,
                steps: taskItem.input_params?.steps,
                width: taskItem.input_params?.width,
                height: taskItem.input_params?.height
            }
        );

        // 导航到create页面，使用一个简单的标记参数
        router.push('/create?ref=lego');
    };

    // 处理Remix按钮点击 - 将历史记录的参数重新填入到control-panel
    const handleRemix = (e: React.MouseEvent) => {
        e.stopPropagation(); // 阻止事件冒泡

        const params = taskItem.input_params;
        if (!params) return;

        console.log('Remix - taskItem:', taskItem);
        console.log('Remix - params:', params);
        console.log('Remix - availableModels:', availableModels);
        console.log('Remix - selectedModel:', selectedModel);

        // 设置基本参数
        setUserPrompt(params.prompt || '');
        setNegativePrompt(params.negative_prompt || '');
        setSeed(params.seed || '-1');
        setSteps(params.steps || 30);
        setGuidanceScale(params.guidance_scale || 3.5);

        // 设置宽高比
        if (params.width && params.height) {
            const ratio = params.width / params.height;
            let aspectRatio: "16:9" | "9:16" | "1:1" = '1:1';

            if (params.width === 576 && params.height === 1024) {
                aspectRatio = '9:16';
            } else if (params.width === 1024 && params.height === 576) {
                aspectRatio = '16:9';
            } else if (ratio > 1.5) {
                aspectRatio = '16:9';
            } else if (ratio < 0.75) {
                aspectRatio = '9:16';
            }

            setAspectRatio(aspectRatio);
        }

        // 匹配并设置模型 - 使用taskItem.model
        const modelName = (taskItem as any).model || (params as any).model;
        if (availableModels.length > 0 && modelName) {
            const matchedModel = availableModels.find(model =>
                model.name === modelName ||
                model.displayName === modelName
            );

            if (matchedModel) {
                console.log('Remix - Found matching model:', matchedModel);
                setSelectedModel(matchedModel);

                // 根据匹配的模型来处理图片
                const isFluxModel = matchedModel.name.toLowerCase().includes('flux');
                handleImageRemix(params, isFluxModel);
            } else {
                console.log('Remix - No matching model found for model name:', modelName);
                // 使用当前选中的模型
                const isFluxModel = selectedModel?.name.toLowerCase().includes('flux') || false;
                handleImageRemix(params, isFluxModel);
            }
        } else {
            // 使用当前选中的模型
            const isFluxModel = selectedModel?.name.toLowerCase().includes('flux') || false;
            handleImageRemix(params, isFluxModel);
        }
    };

    // 处理图片复用的辅助函数
    const handleImageRemix = (params: any, isFluxModel: boolean) => {
        // 收集所有要复用的参考图片（只复用原始参考图片，不包括生成结果）
        const imagesToReuse: string[] = [];

        // 1. 添加历史记录中的参考图片 - 检查多个可能的字段
        if (params.refer_img_urls && params.refer_img_urls.length > 0) {
            imagesToReuse.push(...params.refer_img_urls);
        }

        // 2. 检查单张图片参数
        if (params.image_url) {
            imagesToReuse.push(params.image_url);
        }

        // 3. 检查多张图片参数
        if (params.image_urls && params.image_urls.length > 0) {
            imagesToReuse.push(...params.image_urls);
        }

        // 注意：不再添加生成的结果图片，只复用原始参考图片

        console.log('Remix - Reference images to reuse:', imagesToReuse);
        console.log('Remix - Is FLUX model:', isFluxModel);

        if (imagesToReuse.length > 0) {
            if (isFluxModel) {
                // FLUX模型：清空现有图片并添加到fluxImages
                clearFluxImages();
                imagesToReuse.forEach(imageUrl => {
                    console.log('Remix - Adding FLUX image:', imageUrl);
                    addFluxImage(imageUrl);
                });
            } else {
                // 非FLUX模型：清除现有的图片卡片，然后添加新的图片卡片
                // 首先移除所有现有的图片类型的theme卡片
                themeCards.forEach(card => {
                    if (card.type === 'image') {
                        console.log('Remix - Removing existing image card:', card.id);
                        removeCard('theme', card.id);
                    }
                });

                // 然后添加新的图片卡片
                imagesToReuse.forEach((imageUrl, index) => {
                    const cardId = `theme-remix-${Date.now()}-${index}`;
                    console.log('Remix - Adding theme image card:', cardId, imageUrl);
                    addCard('theme', {
                        id: cardId,
                        type: 'image',
                        content: imageUrl,
                        selected: true
                    });
                });
            }
        } else {
            console.log('Remix - No reference images found to reuse');
            // 如果是FLUX模型，仍然需要清空现有图片
            if (isFluxModel) {
                clearFluxImages();
            }
        }
    };

    // 从API数据中获取图片URL
    const imageUrl = taskItem.output_result?.image_url;

    // 检查图片是否处于生成中状态 - 只有pending和processing状态才算生成中
    const isGenerating = taskItem.status === 'pending' || taskItem.status === 'processing';

    // 定期检查任务状态
    useEffect(() => {
        // 只有当任务处于pending或processing状态时才需要轮询
        if (taskItem.status !== 'pending' && taskItem.status !== 'processing') {
            return;
        }

        // 检查任务状态的函数
        const checkTaskStatus = async () => {
            try {
                // 获取任务详情
                const taskDetail = await getTaskDetail(taskItem.id);
                if (!taskDetail) return;

                // 如果状态发生变化，更新本地状态和全局状态
                if (taskDetail.status !== taskItem.status ||
                    taskDetail.progress !== taskItem.progress) {

                    // 更新本地状态
                    setTaskItem(prev => ({
                        ...prev,
                        status: taskDetail.status,
                        progress: taskDetail.progress || prev.progress,
                        started_at: taskDetail.started_at || prev.started_at,
                        output_result: taskDetail.output_result || prev.output_result
                    }));

                    // 更新全局状态
                    updateHistoryItem(taskItem.id, {
                        status: taskDetail.status,
                        progress: taskDetail.progress,
                        started_at: taskDetail.started_at,
                        output_result: taskDetail.output_result
                    });

                    // 如果任务已完成或失败，停止轮询
                    if (taskDetail.status === 'completed' || taskDetail.status === 'failed') {
                        if (intervalRef.current) {
                            clearInterval(intervalRef.current);
                            intervalRef.current = null;
                        }
                    }
                }
            } catch (error) {
                console.error(`Failed to check task ${taskItem.id}:`, error);
            }
        };

        // 设置轮询间隔 - 根据任务状态动态调整
        const getPollingInterval = () => {
            // 如果任务处于processing状态，每15秒检查一次
            if (taskItem.status === 'processing') return 15000;

            // 如果任务处于pending状态，每10秒检查一次
            return 10000;
        };

        // 设置轮询
        if (!intervalRef.current) {
            // 执行首次检查
            checkTaskStatus();

            // 设置定期轮询
            intervalRef.current = setInterval(checkTaskStatus, getPollingInterval());
        }

        // 清理函数
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        };
    }, [taskItem.id, taskItem.status, updateHistoryItem]);

    // 格式化时间显示 - 确保可以处理字符串格式的时间戳
    const timeString = taskItem.created_at instanceof Date ?
        taskItem.created_at.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :
        new Date(taskItem.created_at as string).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    // 获取状态显示文本
    const getStatusText = () => {
        if (taskItem.status === 'pending') return 'Pending';
        if (taskItem.status === 'processing') return 'Processing';
        if (taskItem.status === 'failed') return 'Failed';
        return taskItem.status === 'completed' ? 'Completed' : 'Generating';
    };

    // 获取当前进度
    const getProgress = () => {
        // 如果任务有明确的进度值，优先使用
        if (taskItem.progress && taskItem.progress > 0) {
            return Math.min(99, taskItem.progress);
        }

        if (taskItem.status === 'completed') return 100;
        if (taskItem.status === 'failed') return 0;
        if (taskItem.status === 'pending') return MIN_PROGRESS; // 等待中显示最小进度

        // 默认估计任务处理时间（秒）
        const estimatedProcessingTime = 60; // 约1分钟

        // 基于时间的进度计算
        if (taskItem.started_at) {
            const startTime = taskItem.started_at instanceof Date
                ? taskItem.started_at
                : new Date(taskItem.started_at);
            const now = new Date();
            const elapsedSeconds = (now.getTime() - startTime.getTime()) / 1000;

            // 如果已经超过了估计时间，显示99%
            if (elapsedSeconds > estimatedProcessingTime) {
                return 99;
            }

            // 在估计时间范围内的进度计算
            const progress = Math.min(99, (elapsedSeconds / estimatedProcessingTime) * 100);
            return Math.max(MIN_PROGRESS, progress);
        }

        // 如果没有开始时间，返回最小进度
        return MIN_PROGRESS;
    };

    // 获取提示词文本
    const promptText = taskItem.input_params?.prompt || '';

    // 获取宽高比信息
    const getAspectRatio = () => {
        // @ts-ignore - 忽略类型检查
        const width = taskItem.input_params?.width;
        // @ts-ignore - 忽略类型检查
        const height = taskItem.input_params?.height;

        if (width && height) {
            if (width === height) return "1:1";
            if (width === 576 && height === 1024) return "9:16";
            if (width === 1024 && height === 576) return "16:9";
            return `${width}x${height}`;
        }
        return null;
    };

    // 获取统一的容器样式 - 所有卡片使用相同尺寸
    const getContainerStyle = () => {
        // 统一使用 4:3 比例的容器，适合展示各种比例的图片
        return "aspect-[4/3]";
    };

    // 获取图片显示模式 - 智能适配不同比例
    const getImageObjectFit = () => {
        // @ts-ignore - 忽略类型检查
        const width = taskItem.input_params?.width;
        // @ts-ignore - 忽略类型检查
        const height = taskItem.input_params?.height;

        if (width && height) {
            const imageRatio = width / height;
            const containerRatio = 4 / 3; // 容器比例 4:3

            // 如果图片比例与容器比例相近（±20%），使用 cover 填满容器
            if (Math.abs(imageRatio - containerRatio) / containerRatio <= 0.2) {
                return "object-cover";
            }

            // 对于宽图（比容器更宽），使用 contain 以显示完整内容
            if (imageRatio > containerRatio * 1.2) {
                return "object-contain";
            }

            // 对于高图（比容器更高），使用 contain 以显示完整内容，会居中显示
            if (imageRatio < containerRatio * 0.8) {
                return "object-contain";
            }
        }

        // 默认使用 contain 以确保图片完整显示
        return "object-contain";
    };

    // 获取步数
    // @ts-ignore - 忽略类型检查
    const steps = taskItem.input_params?.steps || null;

    return (
        <div className="rounded-xl overflow-hidden flex flex-col transform cursor-pointer bg-primary/80">
            <div className={`relative ${getContainerStyle()} bg-muted/20`}>
                {/* 生成中的图片使用简化的噪点效果占位 */}
                {isGenerating ? (
                    <div className="absolute inset-0 bg-gradient-to-br from-slate-800 to-slate-900">
                        {/* 使用轻量级的静态SVG噪点效果替代Canvas动画 */}
                        <CSSNoise />
                        {/* 叠加生成中状态提示 */}
                        <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
                            <p className="text-sm text-white font-medium drop-shadow-lg">
                                {getStatusText()}<span className="animate-pulse">...</span>
                            </p>
                            {/* 添加进度条 - 仅在processing状态下显示 */}
                            {taskItem.status === 'processing' && (
                                <div className="w-3/4 mt-2">
                                    <Progress
                                        value={getProgress()}
                                        className="h-1 bg-white/20"
                                        variant="success"
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                ) : taskItem.status === 'failed' ? (
                    /* 失败状态的简约素雅显示 */
                    <div className="absolute inset-0 bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800/50 dark:to-slate-700/30">
                        {/* 添加极简的纹理背景 */}
                        <div className="absolute inset-0 opacity-5">
                            <svg width="100%" height="100%" className="w-full h-full">
                                <defs>
                                    <pattern id="failed-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
                                        <circle cx="4" cy="4" r="0.5" fill="currentColor" opacity="0.4" />
                                        <circle cx="24" cy="24" r="0.5" fill="currentColor" opacity="0.3" />
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#failed-pattern)" />
                            </svg>
                        </div>

                        {/* 失败状态内容 - 极简设计 */}
                        <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
                            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-slate-200/60 dark:bg-slate-600/40 border border-slate-300/50 dark:border-slate-500/30 mb-3">
                                <AlertTriangle size={16} className="text-slate-500 dark:text-slate-400" />
                            </div>
                            <p className="text-sm font-light text-slate-600 dark:text-slate-300 mb-1">Generation Failed</p>
                            <p className="text-xs text-slate-500 dark:text-slate-400 text-center px-4 leading-relaxed font-light">
                                Unable to complete generation
                            </p>
                        </div>

                        {/* 极简边框 */}
                        <div className="absolute inset-0 rounded-xl border border-slate-200/60 dark:border-slate-600/30"></div>
                    </div>
                ) : (
                    <Image
                        src={imageUrl}
                        alt={promptText}
                        fill
                        className={getImageObjectFit()}
                        sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                        loading="lazy"
                        unoptimized
                    />
                )}
            </div>

            <div className="p-3 space-y-1.5">
                {/* 提示词 - 精简显示 */}
                <div className="group relative">
                    <p className="text-sm line-clamp-1 leading-tight font-medium truncate" title={promptText}>
                        {promptText}
                    </p>
                    {/* Hover tooltip for full prompt */}
                    {promptText.length > 50 && (
                        <div className="absolute bottom-full left-0 mb-2 px-3 py-2 bg-black/90 text-white text-xs rounded-lg shadow-lg z-50 max-w-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                            {promptText}
                            <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/90"></div>
                        </div>
                    )}
                </div>

                {/* 元数据标签 - 精简显示 */}
                <div className="flex flex-wrap gap-1 items-center">
                    {getAspectRatio() && (
                        <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-auto bg-muted/30 flex-shrink-0">
                            {getAspectRatio()}
                        </Badge>
                    )}
                    {/* 显示模型名称 - 精简版 */}
                    {taskItem.storage_path && (
                        <div className="group relative flex-shrink-0">
                            <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-auto bg-muted/30 max-w-20 truncate">
                                <Info size={8} className="mr-1 flex-shrink-0" />
                                <span className="truncate">{taskItem.storage_path}</span>
                            </Badge>
                            {/* Hover tooltip for full model name */}
                            <div className="absolute bottom-full left-0 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded shadow-lg z-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                Model: {taskItem.storage_path}
                                <div className="absolute top-full left-4 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-black/90"></div>
                            </div>
                        </div>
                    )}
                    {/* 状态标签 - 精简版 */}
                    {taskItem.status && (
                        <Badge
                            variant="outline"
                            className={`text-xs px-1.5 py-0.5 h-auto flex-shrink-0 ${taskItem.status === 'completed' ? 'bg-green-900/30 text-green-400' :
                                taskItem.status === 'failed' ? 'bg-red-900/30 text-red-400' :
                                    'bg-blue-900/30 text-blue-400'
                                }`}
                        >
                            {taskItem.status === 'completed' ? '✓' :
                                taskItem.status === 'failed' ? '✗' :
                                    taskItem.status === 'processing' ? '⟳' : '⋯'}
                        </Badge>
                    )}
                </div>

                {/* 底部操作栏 - 精简布局 */}
                <div className="flex justify-between items-center">
                    <span className="text-xs text-muted-foreground truncate flex-shrink-0">
                        {timeString}
                    </span>
                    <div className="flex space-x-1 flex-shrink-0 ml-2">
                        {/* 只有完成状态才显示下载按钮 */}
                        {item.status === 'completed' && (
                            <button
                                onClick={() => handleDownload(imageUrl)}
                                className="p-1 rounded hover:bg-accent transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                title={isDownloading ? "Downloading..." : "Download"}
                                disabled={isGenerating || isDownloading}
                            >
                                {isDownloading ? (
                                    <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin" />
                                ) : (
                                    <Download size={12} />
                                )}
                            </button>
                        )}

                        {/* 失败状态显示重试按钮 */}
                        {item.status === 'failed' && (
                            <button
                                onClick={handleRemix}
                                className="flex items-center px-1.5 py-0.5 text-xs rounded border border-slate-300 dark:border-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-600 dark:text-slate-300 transition-colors"
                                title="Retry - Try again with same parameters"
                            >
                                <RefreshCw size={10} className="mr-0.5" />
                                Retry
                            </button>
                        )}

                        {/* Remix按钮 - 精简版 */}
                        {item.status !== 'failed' && (
                            <button
                                onClick={handleRemix}
                                className={`px-1.5 py-0.5 text-xs rounded transition-colors ${isGenerating
                                    ? 'opacity-50 cursor-not-allowed'
                                    : 'hover:bg-accent cursor-pointer'
                                    }`}
                                title="Remix - Reuse parameters"
                                disabled={isGenerating}
                            >
                                Remix
                            </button>
                        )}

                        {/* Make Video按钮 - 保持原有逻辑 */}
                        {item.status === 'completed' && (
                            <button
                                onClick={(e) => handleMakeVideo(e, imageUrl)}
                                className="flex items-center px-1.5 py-0.5 text-xs rounded bg-blue-600 hover:bg-blue-700 text-white transition-colors"
                                title="Make Video"
                                disabled={isGenerating}
                            >
                                <Video size={10} className="mr-0.5" />
                                Video
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
