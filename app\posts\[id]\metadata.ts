import { Metadata } from "next"
import { PostItemDto } from "@/types/posts"
import { postApi } from "@/lib/api"

// 使用项目中已有的API接口获取帖子详情
async function getPostDetails(id: string): Promise<PostItemDto> {
    try {
        // 使用项目中已有的postApi.getPost方法获取帖子详情
        const { data } = await postApi.getPost(id);
        return data;
    } catch (error) {
        console.error("Error fetching post details:", error);
        // 返回一个基础的默认值
        return {
            id,
            title: '',
            description: "AI generated video",
            video_url: "",
            videos: {
                input_params: {
                    model_id: "",
                    model_name: "",
                    seed: 0,
                    // @ts-ignore
                    ratio: "16:9",
                    steps: 0,
                    prompt: "",

                },
                cover_img: "",
            },
            user_id: "",
            username: "AI Creator",
            user_avatar: "",
            like_count: 0,
            comment_count: 0,
            created_at: new Date().toISOString(),
            user_profiles: {
                nickname: '',
                avatar: '',
                bio: '',
            },
        };
    }
}

// Metadata generation function - Server Component
export async function generateMetadata({
    params
}: {
    params: { id: string }
}): Promise<Metadata> {
    // Use Promise.resolve to ensure parameters are fully resolved
    const { id } = await Promise.resolve(params);

    // 获取实际的帖子数据
    const post = await getPostDetails(id);

    const ogImageUrl = new URL(`/api/og?title=${encodeURIComponent(post.title || `Video #${id}`)}`,
        process.env.NEXT_PUBLIC_APP_URL || "https://reelmind.ai").toString();

    return {
        title: {
            default: post.title || `Video #${id}`,
            template: `%s | ReelMind`,
            absolute: `${post.title || `Video #${id}`} | ReelMind - AI Video Creation Community`,
        },
        description: post.description || "AI generated video showcasing the latest video generation technology.",
        keywords: ["AI", "Text-to-Video", "AIGC"],
        authors: [{ name: post.username }],
        openGraph: {
            title: post.title || `Video #${id}`,
            description: post.description || "AI generated video",
            type: "video.other",
            url: `https://reelmind.ai/posts/${id}`,
            images: [
                {
                    url: post.videos?.cover_img || ogImageUrl,
                    width: 1200,
                    height: 630,
                    alt: post.title || `Video #${id}`,
                }
            ],
            videos: [
                {
                    url: post.video_url,
                    type: "video/mp4",
                }
            ],
            locale: "en_US",
        },
        twitter: {
            card: "summary_large_image",
            title: post.title || `Video #${id}`,
            description: post.description || "AI generated video",
            images: [post.videos?.cover_img || ogImageUrl],
        },
    }
} 