import { create } from 'zustand';
import { ModelFilter } from '@/app/create/hooks/useModelQuery';
import { TABS } from '@/app/create/constants';

interface ModelSelectorState {
    // 模态框状态
    isModalOpen: boolean;
    searchTerm: string;
    page: number;

    // 激活的Tab
    activeTab: string;

    // 过滤器
    filters: ModelFilter;

    // 选中的模型
    selectedModelId: string;

    // 操作方法
    openModal: () => void;
    closeModal: () => void;
    setSearchTerm: (term: string) => void;
    setSelectedModelId: (id: string) => void;
    incrementPage: () => void;
    resetPage: () => void;

    // Tab方法
    setActiveTab: (tab: string) => void;
    syncTabWithFilters: () => void;

    // 过滤器方法
    addTypeFilter: (type: string) => void;
    removeTypeFilter: (type: string) => void;
    addFeatureFilter: (feature: string) => void;
    removeFeatureFilter: (feature: string) => void;
    addSourceFilter: (source: string) => void;
    removeSourceFilter: (source: string) => void;
    resetFilters: () => void;
    setFiltersByTab: (tab: string) => void;

    // 重置状态
    resetState: () => void;
}

const initialState = {
    isModalOpen: false,
    searchTerm: '',
    page: 1,
    activeTab: TABS.IMAGE_TO_VIDEO, // 默认为图像到视频
    selectedModelId: '',
    filters: {
        type: [],
        features: [],
        source: []
    } as ModelFilter,
};

const useModelSelectorStore = create<ModelSelectorState>((set, get) => ({
    ...initialState,

    // 操作方法
    openModal: () => set({ isModalOpen: true }),
    closeModal: () => set({ isModalOpen: false }),
    setSearchTerm: (term) => set({ searchTerm: term }),
    setSelectedModelId: (id) => set({ selectedModelId: id }),
    incrementPage: () => set((state) => ({ page: state.page + 1 })),
    resetPage: () => set({ page: 1 }),

    // Tab方法
    setActiveTab: (tab) => {
        const currentTab = get().activeTab;

        // 只有当标签页真正变化时才执行操作
        if (currentTab !== tab) {
            // 更新activeTab
            set({
                activeTab: tab,
                // 清空当前选中的模型ID，确保用户在新标签页中重新选择模型
                selectedModelId: ''
            });

            // 当Tab变化时，自动更新过滤器
            get().setFiltersByTab(tab);
        }
    },

    syncTabWithFilters: () => {
        const state = get();
        // 如果过滤器中有类型，则将activeTab设置为第一个类型
        if (state.filters.type && state.filters.type.length > 0) {
            set({ activeTab: state.filters.type[0] });
        } else {
            // 否则保持当前activeTab
        }
    },

    // 过滤器方法
    addTypeFilter: (type) => {
        set((state) => ({
            filters: {
                ...state.filters,
                type: [...(state.filters.type || []), type]
            }
        }));
        // 当添加类型过滤器时，同步更新activeTab
        get().syncTabWithFilters();
    },
    removeTypeFilter: (type) => {
        set((state) => ({
            filters: {
                ...state.filters,
                type: (state.filters.type || []).filter(t => t !== type)
            }
        }));
        // 当移除类型过滤器时，同步更新activeTab
        get().syncTabWithFilters();
    },
    addFeatureFilter: (feature) => set((state) => ({
        filters: {
            ...state.filters,
            features: [...(state.filters.features || []), feature]
        }
    })),
    removeFeatureFilter: (feature) => set((state) => ({
        filters: {
            ...state.filters,
            features: (state.filters.features || []).filter(f => f !== feature)
        }
    })),
    addSourceFilter: (source) => set((state) => ({
        filters: {
            ...state.filters,
            source: [...(state.filters.source || []), source]
        }
    })),
    removeSourceFilter: (source) => set((state) => ({
        filters: {
            ...state.filters,
            source: (state.filters.source || []).filter(s => s !== source)
        }
    })),
    resetFilters: () => set({ filters: initialState.filters }),

    // 根据Tab设置过滤器
    setFiltersByTab: (tab) => {
        const { resetFilters, addTypeFilter } = get();

        // 先重置所有过滤器
        resetFilters();

        // 如果不是"all"选项卡，则添加对应的类型过滤器
        if (tab !== "all" && tab !== TABS.EFFECT) {
            addTypeFilter(tab);
        }
    },

    // 重置状态
    resetState: () => set(initialState),
}));

export default useModelSelectorStore;
