import { useState, useRef } from "react"
import { Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"

interface CardRemixButtonProps {
  onClick: (e: React.MouseEvent) => void;
  taskId?: string;
}

export function CardRemixButton({ onClick, taskId }: CardRemixButtonProps) {
  const router = useRouter();

  // 处理Remix按钮点击事件 - 跳转到创建页面并确保模型选择
  const handleRemixClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡，不触发卡片点击

    if (!taskId) {
      // 如果没有taskId，调用传入的onClick处理函数
      onClick(e);
      return;
    }

    // 导航到创建页面并传递task_id
    // 不再重置模型选择状态，让RemixLoader组件处理模型选择
    router.push(`/create?remix=${taskId}`);
  };

  return (
    <motion.button
      onClick={handleRemixClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={cn(
        "group relative flex items-center justify-center",
        "bg-gradient-to-r from-indigo-500/90 to-purple-500/90",
        "text-white font-medium",
        "w-full rounded-full px-4 py-2 gap-2",
        "shadow-sm",
        "transition-all duration-200 ease-out",
        "hover:shadow-md hover:from-indigo-500 hover:to-purple-500",
      )}
    >
      {/* 闪光图标 */}
      <Sparkles size={16} className="text-white/90" />

      {/* 文字 */}
      <span className="font-bold text-sm whitespace-nowrap">
        Remix
      </span>

      {/* 光效 */}
      <div className={cn(
        "absolute inset-0 rounded-full",
        "bg-gradient-to-r from-indigo-600/20 to-purple-600/20",
        "opacity-0 group-hover:opacity-100",
        "transition-opacity duration-300"
      )} />
    </motion.button>
  );
}
