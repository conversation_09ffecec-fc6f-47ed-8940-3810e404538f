import {
    Controller,
    Get,
    Post,
    Body,
    Req,
    Query,
    UseGuards,
    UnauthorizedException,
} from '@nestjs/common';
import { CreditsService } from './credits.service';
import { PurchaseCreditsDto } from './dto/credit-transaction.dto';
import { CreditTransactionQueryDto } from './dto/credit-query.dto';
import { JwtGuard } from '../common/guards/jwt.guard';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { getRealClientIP } from '../common/utils/ip.utils';

@ApiTags('积分')
@Controller('credits')
@UseGuards(JwtGuard)
export class CreditsController {
    constructor(
        private readonly creditsService: CreditsService
    ) {}

    @Get('balance')
    @ApiOperation({ summary: '获取用户积分余额' })
    @ApiResponse({ status: 200, description: '返回用户积分余额' })
    @UseGuards(JwtGuard)
    async getUserBalance(@Req() req) {
        const userId = req.user.id;
        if (!userId) {
            throw new UnauthorizedException('未授权的请求');
        }
        const balance = await this.creditsService.getUserCreditBalance(userId);
        return { balance };
    }

    @Get('transactions')
    @ApiOperation({ summary: '获取用户积分交易记录' })
    @ApiResponse({ status: 200, description: '返回用户积分交易记录列表和分页信息' })
    @UseGuards(JwtGuard)
    async getTransactions(@Req() req, @Query() queryDto: CreditTransactionQueryDto) {
        const userId = req.user.id;
        if (!userId) {
            throw new UnauthorizedException('未授权的请求');
        }
        return this.creditsService.getCreditTransactions(userId, queryDto);
    }

    @Post('purchase')
    @ApiOperation({ summary: '购买积分' })
    @ApiResponse({ status: 201, description: '创建购买积分交易记录，并返回支付URL' })
    @UseGuards(JwtGuard)
    async purchaseCredits(@Req() req, @Body() purchaseDto: PurchaseCreditsDto) {
        const userId = req.user.id;
        if (!userId) {
            throw new UnauthorizedException('未授权的请求');
        }

        // 获取请求中的origin，用于构建支付成功/取消的回调URL
        const origin = req.headers.origin || 'https://reelmind.ai';

        return this.creditsService.purchaseCredits(userId, purchaseDto.amount, origin);
    }

    @Get('claim-status')
    @ApiOperation({ summary: '获取用户积分领取状态' })
    @ApiResponse({ status: 200, description: '返回用户是否可以领取积分以及相关状态' })
    @UseGuards(JwtGuard)
    async getClaimStatus(@Req() req) {
        const userId = req.user.id;
        if (!userId) {
            throw new UnauthorizedException('未授权的请求');
        }
        return this.creditsService.getUserClaimStatus(userId);
    }

    @Post('claim-bonus')
    @ApiOperation({ summary: '手动领取新用户积分' })
    @ApiResponse({ status: 201, description: '领取新用户积分奖励' })
    @UseGuards(JwtGuard)
    async claimNewUserBonus(@Req() req, @Body() claimData: {
        fingerprint: any;
        deviceInfo?: any;
    }) {
        const userId = req.user.id;
        if (!userId) {
            throw new UnauthorizedException('未授权的请求');
        }

        // 获取客户端真实IP地址（考虑Nginx代理）
        const ipAddress = getRealClientIP(req);

        // 获取用户代理
        const userAgent = req.headers['user-agent'] || '';

        return this.creditsService.claimNewUserBonus({
            userId,
            fingerprint: claimData.fingerprint,
            ipAddress,
            userAgent
        });
    }
}