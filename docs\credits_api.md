# credits积分系统接入文档

## 概述

ReelMind平台的积分系统(Credits)用于管理用户在平台内的虚拟积分，这些积分可用于生成AI视频或图片等功能。本文档提供了积分系统的API接口说明，以便前端开发人员进行集成。

## 基础信息

- **基础URL**: `https://api.reelmind.ai/`
- **认证方式**: Bearer Token认证，请在所有API请求头中添加`Authorization: Bearer {token}`
- **响应格式**: 所有API返回JSON格式数据
- **错误处理**: 所有接口在遇到错误时会返回适当的HTTP状态码和错误信息

## 积分兑换比例

- 1 USD = 100 Credits (积分)

## API接口列表

### 1. 获取用户积分余额

获取当前登录用户的积分余额。

- **接口地址**: `GET /credits/balance`
- **请求头**:
  - `Authorization: Bearer {token}` (必填)
- **请求参数**: 无
- **响应示例**:

```json
{
  "balance": 1250
}
```

- **响应说明**:
  - `balance`: 用户当前积分余额

### 2. 获取用户积分交易记录

获取当前登录用户的积分交易历史记录，支持分页和筛选。

- **接口地址**: `GET /credits/transactions`
- **请求头**:
  - `Authorization: Bearer {token}` (必填)
- **请求参数(Query)**:
  - `type`: 交易类型，可选值见[交易类型列表](#交易类型列表)
  - `status`: 交易状态，可选值见[交易状态列表](#交易状态列表)
  - `page`: 页码，默认为1
  - `limit`: 每页记录数，默认为20
  - `fromDate`: 开始日期，格式为ISO日期字符串
  - `toDate`: 结束日期，格式为ISO日期字符串
- **响应示例**:

```json
{
  "data": [
    {
      "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "user_id": "u1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "type": "direct_purchase",
      "amount": 500,
      "description": "购买500积分",
      "reference_id": "p1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "status": "completed",
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z"
    },
    {
      "id": "b1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "user_id": "u1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "type": "video_generation_sd",
      "amount": -50,
      "description": "生成标准视频",
      "reference_id": null,
      "status": "completed",
      "created_at": "2023-01-02T14:30:00Z",
      "updated_at": "2023-01-02T14:30:00Z"
    }
  ],
  "total": 42,
  "page": 1,
  "limit": 20
}
```

- **响应说明**:
  - `data`: 交易记录数组
    - `id`: 交易ID
    - `user_id`: 用户ID
    - `type`: 交易类型
    - `amount`: 交易金额(正数为收入，负数为支出)
    - `description`: 交易描述
    - `reference_id`: 关联ID(如支付ID、订单ID等)
    - `status`: 交易状态
    - `created_at`: 创建时间
    - `updated_at`: 更新时间
  - `total`: 总记录数
  - `page`: 当前页码
  - `limit`: 每页记录数

### 3. 购买积分

创建积分购买订单，并返回支付URL。

- **接口地址**: `POST /credits/purchase`
- **请求头**:
  - `Authorization: Bearer {token}` (必填)
  - `Origin`: 前端域名，用于构建支付回调URL
- **请求体(JSON)**:

```json
{
  "amount": 10.5
}
```

- **请求参数说明**:
  - `amount`: 购买金额(USD)，必填
- **响应示例**:

```json
{
  "checkoutUrl": "https://checkout.stripe.com/xxx",
  "amountUsd": 10.5,
  "creditsAmount": 1050
}
```

- **响应说明**:
  - `checkoutUrl`: Stripe支付页面URL，前端应重定向用户到此URL完成支付
  - `amountUsd`: 购买金额(USD)
  - `creditsAmount`: 积分数量

### 4. 发帖获得奖励积分

用户发布内容后，可通过此API获得奖励积分(每日限额1次)。

- **接口地址**: `POST /credits/grant-post-reward`
- **请求头**:
  - `Authorization: Bearer {token}` (必填)
- **请求体**: 无
- **响应示例(成功)**:

```json
{
  "transaction": {
    "id": "c1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "user_id": "u1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "type": "post_reward",
    "amount": 50,
    "description": "发布帖子奖励50积分",
    "status": "completed",
    "created_at": "2023-01-03T10:15:00Z"
  },
  "newBalance": 1300,
  "rewardAmount": 50
}
```

- **响应示例(已达上限)**:

```json
{
  "status": "limit_exceeded",
  "message": "今日发帖奖励已达上限"
}
```

- **响应说明**:
  - 成功时返回交易详情、新余额和奖励金额
  - 达到每日上限时返回状态和提示信息

## 数据字典

### 交易类型列表

以下是系统中使用的积分交易类型:

| 类型                  | 说明               | 符号 |
| --------------------- | ------------------ | ---- |
| `membership_monthly`  | 会员每月赠送       | +    |
| `direct_purchase`     | 直接购买           | +    |
| `platform_reward`     | 平台奖励           | +    |
| `new_user_bonus`      | 新用户注册奖励     | +    |
| `post_reward`         | 发帖奖励           | +    |
| `video_generation_sd` | 标准视频生成(480P) | -    |
| `video_generation_hd` | 高清视频生成(720P) | -    |

### 交易状态列表

| 状态        | 说明   |
| ----------- | ------ |
| `pending`   | 待处理 |
| `completed` | 已完成 |
| `failed`    | 失败   |
| `refunded`  | 已退款 |

### 积分消耗列表

以下是平台上各项功能的积分消耗:

| 功能               | 消耗积分 | 说明     |
| ------------------ | -------- | -------- |
| 标准视频生成(480P) | 50       | 最长10秒 |
| 高清视频生成(720P) | 80       | 最长10秒 |

## 支付流程说明

积分购买的支付流程如下:

1. 前端调用 `POST /credits/purchase` 创建支付订单
2. 后端返回 `checkoutUrl`，前端将用户重定向到此URL
3. 用户在Stripe页面完成支付
4. Stripe通过Webhook通知后端支付完成
5. 后端处理支付结果，为用户添加积分
6. 用户被重定向回网站的支付成功页面

### 支付回调页面

支付成功/失败后，用户将被重定向到以下URL:

- 支付成功: `{origin}/payment/success?payment_id={payment_id}&type=credits`
- 支付取消: `{origin}/payment/cancel?payment_id={payment_id}&type=credits`

其中 `{origin}` 是前端请求的 Origin 头或默认值 `https://reelmind.ai`。

## 错误处理

所有API在遇到错误时都会返回适当的HTTP状态码和错误信息。常见的错误包括:

- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未授权的请求，通常是token无效或过期
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误

错误响应的格式为:

```json
{
  "statusCode": 400,
  "message": "购买金额必须大于0",
  "error": "Bad Request"
}
```

## 前端集成示例

### 获取用户余额

```javascript
async function getUserBalance() {
  const response = await fetch('https://api.reelmind.ai/credits/balance', {
    headers: {
      Authorization: `Bearer ${userToken}`,
    },
  });

  if (!response.ok) {
    throw new Error('获取积分余额失败');
  }

  const data = await response.json();
  return data.balance;
}
```

### 购买积分

```javascript
async function purchaseCredits(amountUsd) {
  const response = await fetch('https://api.reelmind.ai/credits/purchase', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${userToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ amount: amountUsd }),
  });

  if (!response.ok) {
    throw new Error('创建购买订单失败');
  }

  const data = await response.json();
  // 重定向用户到支付页面
  window.location.href = data.checkoutUrl;
}
```
