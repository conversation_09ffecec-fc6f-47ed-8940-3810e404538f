/* 自定义动画效果 - 精简版 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(0.85);
  }
}

/* 透明度脉冲效果 - 更加优雅的动画 - 增强对比度 */
@keyframes pulseOpacity {
  0%, 100% {
    stroke-opacity: 0.7;
    stroke-width: 2;
  }
  50% {
    stroke-opacity: 0.3;
    stroke-width: 1;
  }
}

/* History Card CSS */
.history-card {
    transition: all 200ms ease;
    position: relative;
}

.history-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.history-card-mobile-optimized {
    overflow: hidden;
    max-height: none !important;
}

/* Mobile optimization: better tap targets */
@media (max-width: 768px) {
    .history-card-actions button {
        min-height: 40px;
        min-width: 40px;
        margin: 0 2px;
    }
    
    .history-card-prompt {
        max-height: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
