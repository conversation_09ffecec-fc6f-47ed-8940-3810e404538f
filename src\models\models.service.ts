import { Injectable, NotFoundException, BadRequestException, Inject } from '@nestjs/common';
import { CustomLogger } from '../common/services/logger.service';
import { CreateModelDto, ModelDto } from './dto/model.dto';
import { VideoDefinition, VideoDuration, VideoRatio } from '../common/constants/video';
import { SUPABASE_CLIENT } from 'src/common/providers/supabase.provider';
import { SupabaseClient } from '@supabase/supabase-js';
import { EffectDto } from './dto/effect.dto';

@Injectable()
export class ModelsService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {}

    /**
     * 获取所有视频模型列表
     */
    async getAllModels(page = 1, limit = 20) {
        try {
            // 计算分页参数
            const startRow = (page - 1) * limit;
            const endRow = startRow + limit - 1;

            // 获取分页后的模型列表
            const { data, error, count } = await this.supabase
                .from('models')
                .select(`*`, { count: 'exact' })
                .order('weight', { ascending: false })
                .order('created_at', { ascending: true }) // 添加第二排序字段确保结果一致性
                .filter('is_public', 'eq', true)
                .range(startRow, endRow);

            if (error) {
                throw new Error(error.message);
            }

            return {
                models: data || [],
                total: count || 0
            };
        } catch (error) {
            this.logger.error('Error getting all models', {
                error: error.message,
                stack: error.stack,
            });
            throw error;
        }
    }

    /**
     * 搜索模型
     * @param searchParams 搜索参数
     */
    async searchModels(searchParams: {
        query?: string;
        model_types?: string[];
        features?: string[];
        page?: number;
        limit?: number;
    }) {
        try {
            const { query, model_types, features, page = 1, limit = 20 } = searchParams;

            // 计算分页参数
            const startRow = (page - 1) * limit;
            const endRow = startRow + limit - 1;

            // 构建查询
            let supabaseQuery = this.supabase
                .from('models')
                .select('*', { count: 'exact' })
                .filter('is_public', 'eq', true);

            // 添加全文搜索条件（如果提供了查询关键词）
            if (query && query.trim()) {
                // 使用Supabase的全文搜索功能
                // 搜索name和description字段
                supabaseQuery = supabaseQuery.or(
                    `name.ilike.%${query}%,description.ilike.%${query}%`
                );
            }

            // 按模型类型过滤
            if (model_types && model_types.length > 0) {
                supabaseQuery = supabaseQuery.in('model_type', model_types);
            }

            // 添加分页和排序
            const { data, error, count } = await supabaseQuery
                .order('id', { ascending: true }) // 添加排序字段确保结果一致性
                .order('weight', { ascending: false })
                .range(startRow, endRow);

            if (error) {
                throw new Error(`搜索模型失败: ${error.message}`);
            }

            return {
                models: data || [],
                total: count || 0
            };
        } catch (error) {
            this.logger.error('Error searching models', {
                error: error.message,
                params: JSON.stringify(searchParams),
                stack: error.stack,
            });
            throw error;
        }
    }

    /**
     * 获取单个模型详情
     */
    async getModelById(model_id: string): Promise<any> {
        try {
            // 从数据库获取模型
            const { data, error } = await this.supabase
                .from('models')
                .select('*')
                .eq('id', model_id)
                .filter('is_public', 'eq', true)
                .single();

            if (error) {
                throw new NotFoundException(`模型未找到: ${model_id}`);
            }

            return data;
        } catch (error) {
            this.logger.error('Error getting model by id', {
                error: error.message,
                model_id,
                stack: error.stack,
            });
            throw error;
        }
    }

    /**
     * 获取所有效果列表
     */
    async getEffects(): Promise<EffectDto[]> {
        const { data } = await this.supabase.from('video_effects').select('*');
        return data;
    }

    /**
     * 检查模型是否已存在
     */
    async checkModelExists(storagePath: string): Promise<boolean> {
        try {
            const { data, error } = await this.supabase
                .from('models')
                .select('id')
                .eq('storage_path', storagePath)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 是 "not found" 错误
                this.logger.error('Error checking model existence', {
                    error: error.message,
                    storage_path: storagePath,
                });
                return false;
            }

            return !!data;
        } catch (error) {
            this.logger.error('Error checking model existence', {
                error: error.message,
                storage_path: storagePath,
                stack: error.stack,
            });
            return false;
        }
    }

    /**
     * 创建新模型
     */
    async createModel(createModelDto: CreateModelDto): Promise<any> {
        try {
            const { data, error } = await this.supabase
                .from('models')
                .insert(createModelDto)
                .select()
                .single();

            if (error) {
                throw new Error(`创建模型失败: ${error.message}`);
            }

            return data;
        } catch (error) {
            this.logger.error('Error creating model', {
                error: error.message,
                data: JSON.stringify(createModelDto),
                stack: error.stack,
            });
            throw error;
        }
    }

    /**
     * 更新模型信息
     */
    async updateModel(modelId: string, updateModelDto: any): Promise<ModelDto> {
        try {
            // 检查模型是否存在
            await this.getModelById(modelId);

            // 准备更新数据
            const updateData: any = {};

            if (updateModelDto.name !== undefined) updateData.name = updateModelDto.name;
            if (updateModelDto.description !== undefined) updateData.description = updateModelDto.description;
            if (updateModelDto.cover_img !== undefined) updateData.cover_img = updateModelDto.cover_img;
            if (updateModelDto.cover_video !== undefined) updateData.cover_video = updateModelDto.cover_video;
            if (updateModelDto.is_public !== undefined) updateData.is_public = updateModelDto.is_public;
            if (updateModelDto.nsfw_level !== undefined) updateData.nsfw_level = updateModelDto.nsfw_level;
            if (updateModelDto.category !== undefined) updateData.category = updateModelDto.category;
            if (updateModelDto.group !== undefined) updateData.group = updateModelDto.group;
            if (updateModelDto.price !== undefined) updateData.price = updateModelDto.price;
            if (updateModelDto.weight !== undefined) updateData.weight = updateModelDto.weight;
            if (updateModelDto.metadata !== undefined) updateData.metadata = updateModelDto.metadata;
            if (updateModelDto.default_config !== undefined) {
                updateData.default_config = this.validateAndNormalizeConfig(updateModelDto.default_config);
            }
            if (updateModelDto.supported_features !== undefined) updateData.supported_features = updateModelDto.supported_features;

            // 添加更新时间
            updateData.updated_at = new Date().toISOString();

            // 执行更新
            const { data, error } = await this.supabase
                .from('models')
                .update(updateData)
                .eq('id', modelId)
                .select()
                .single();

            if (error) {
                throw new Error(`更新模型失败: ${error.message}`);
            }

            return data;
        } catch (error) {
            this.logger.error('Error updating model', {
                error: error.message,
                model_id: modelId,
                update_data: JSON.stringify(updateModelDto),
                stack: error.stack,
            });
            throw error;
        }
    }

    /**
     * 更新模型配置
     */
    async updateModelConfig(modelId: string, configData: any): Promise<ModelDto> {
        try {
            // 获取现有模型
            const existingModel = await this.getModelById(modelId);

            // 合并配置
            const updatedConfig = {
                ...existingModel.default_config,
                ...configData
            };

            // 验证和规范化配置
            const normalizedConfig = this.validateAndNormalizeConfig(updatedConfig);

            // 执行更新
            const { data, error } = await this.supabase
                .from('models')
                .update({
                    default_config: normalizedConfig
                })
                .eq('id', modelId)
                .select()
                .single();

            if (error) {
                throw new NotFoundException(`模型未找到或更新失败: ${modelId}`);
            }

            return data;
        } catch (error) {
            this.logger.error('Error updating model config', {
                error: error.message,
                model_id: modelId,
                config_data: JSON.stringify(configData),
                stack: error.stack,
            });
            throw error;
        }
    }

    /**
     * 删除模型
     */
    async deleteModel(modelId: string): Promise<void> {
        try {
            // 检查模型是否存在
            await this.getModelById(modelId);

            // 检查模型是否已被使用
            const { data: usageData } = await this.supabase
                .from('video_models_usage')
                .select('usage_count')
                .eq('id', modelId)
                .single();

            if (usageData && usageData.usage_count > 0) {
                throw new BadRequestException(`无法删除已使用过的模型，该模型已被使用 ${usageData.usage_count} 次`);
            }

            // 删除模型
            const { error } = await this.supabase
                .from('models')
                .delete()
                .eq('id', modelId);

            if (error) {
                throw new Error(`删除模型失败: ${error.message}`);
            }
        } catch (error) {
            this.logger.error('Error deleting model', {
                error: error.message,
                model_id: modelId,
                stack: error.stack,
            });

            // 重新抛出以保持原始错误
            if (error instanceof NotFoundException || error instanceof BadRequestException) {
                throw error;
            }

            throw new Error(`删除模型失败: ${error.message}`);
        }
    }

    /**
     * 验证并规范化模型配置
     */
    private validateAndNormalizeConfig(config: any): any {
        // 确保数值参数在合理范围内
        const normalizedConfig = {
            min_guidance_scale: Math.min(Math.max(config.min_guidance_scale || 0, 0), 100),
            max_guidance_scale: Math.min(Math.max(config.max_guidance_scale || 20, 1), 100),
            default_guidance_scale: Math.min(Math.max(config.default_guidance_scale || 7, 0), 100),
            min_steps: Math.min(Math.max(config.min_steps || 10, 1), 100),
            max_steps: Math.min(Math.max(config.max_steps || 50, 1), 100),
            default_steps: Math.min(Math.max(config.default_steps || 30, 1), 100),

            // 确保支持的定义、时长和比例是有效枚举值
            supported_definitions: this.validateEnumArray(
                config.supported_definitions || [VideoDefinition.SD, VideoDefinition.HD],
                VideoDefinition
            ),
            supported_durations: config.supported_durations || [VideoDuration.SHORT, VideoDuration.LONG],
            supported_ratios: this.validateEnumArray(
                config.supported_ratios || [VideoRatio.SQUARE, VideoRatio.NARROW, VideoRatio.WIDE],
                VideoRatio
            ),

            // 布尔值设置
            allow_negative_prompt: config.allow_negative_prompt !== undefined ? !!config.allow_negative_prompt : true,
            allow_seed_input: config.allow_seed_input !== undefined ? !!config.allow_seed_input : true,
            allow_reference_image: config.allow_reference_image !== undefined ? !!config.allow_reference_image : false,
        };

        // 确保默认值在最小和最大范围内
        normalizedConfig.default_guidance_scale = Math.min(
            Math.max(normalizedConfig.default_guidance_scale, normalizedConfig.min_guidance_scale),
            normalizedConfig.max_guidance_scale
        );

        normalizedConfig.default_steps = Math.min(
            Math.max(normalizedConfig.default_steps, normalizedConfig.min_steps),
            normalizedConfig.max_steps
        );

        return normalizedConfig;
    }

    /**
     * 验证枚举数组，确保只包含有效的枚举值
     */
    private validateEnumArray(values: any[], enumType: any): any[] {
        if (!Array.isArray(values) || values.length === 0) {
            // 如果未提供或为空，则使用所有可能的枚举值
            return Object.values(enumType);
        }

        // 过滤出有效的枚举值
        const validValues = values.filter(value => Object.values(enumType).includes(value));

        // 如果过滤后为空，则返回所有枚举值
        return validValues.length > 0 ? validValues : Object.values(enumType);
    }
}