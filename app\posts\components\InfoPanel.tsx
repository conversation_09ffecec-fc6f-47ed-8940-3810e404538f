import { PostItemDto } from "@/types/posts"
import { AuthorInfo } from "./AuthorInfo"
import { CommentSection } from "./CommentSection"
import { CommentsSkeleton } from "./VideoDetailSkeleton"
import { ReactNode } from "react"
import { motion } from "framer-motion"
import { GenerationData } from "./GenerationData"

interface InfoPanelProps {
    post: PostItemDto
    animate?: boolean
    children?: ReactNode
}

/**
 * 基础信息面板组件 - 可传入children用于评论区域
 */
export function InfoPanel({
    post,
    animate = false,
    children
}: InfoPanelProps) {
    const wrapperProps = animate
        ? {
            as: motion.aside,
            variants: {
                hidden: { y: 20, opacity: 0 },
                visible: {
                    y: 0,
                    opacity: 1,
                    transition: { type: 'spring', stiffness: 300, damping: 24 }
                }
            }
        }
        : {}

    return (
        <aside
            {...wrapperProps}
            className="w-full lg:w-[350px] xl:w-[480px] rounded-lg overflow-hidden"
        >
            <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700 scrollbar-track-transparent">
                {/* Author Info */}
                <AuthorInfo post={post} />

                {/* Generation Data - 视频生成参数聚合 */}
                <section className="border-gray-100 dark:border-gray-800">
                    <GenerationData post={post} />
                </section>

                {/* Comments Section - 由子组件传入 */}
                <section className="border-gray-100 dark:border-gray-800">
                    {children}
                </section>
            </div>
        </aside>
    )
}

/**
 * 客户端信息面板 - 带有交互功能的评论区
 */
export function ClientInfoPanel({
    post,
    comments,
    loadingComments,
    commentContent,
    submittingComment,
    setCommentContent,
    submitComment,
    animate = false
}: {
    post: PostItemDto
    comments: any[]
    loadingComments: boolean
    commentContent: string
    submittingComment: boolean
    setCommentContent: (content: string) => void
    submitComment: (postId: string, content: string) => void
    animate?: boolean
}) {
    return (
        <InfoPanel post={post} animate={animate}>
            {loadingComments ? (
                <CommentsSkeleton />
            ) : (
                <CommentSection
                    videoId={post.id}
                    comments={comments}
                    loadingComments={loadingComments}
                    commentContent={commentContent}
                    submittingComment={submittingComment}
                    setCommentContent={setCommentContent}
                    submitComment={submitComment}
                />
            )}
        </InfoPanel>
    )
}

/**
 * 服务端信息面板 - 使用CommentSectionWrapper组件
 */
export function ServerInfoPanel({
    post,
    comments
}: {
    post: PostItemDto
    comments: any[]
}) {
    return (
        <InfoPanel post={post}>
            <section id="comments-container">
                {/* 客户端包装组件将在挂载时注入交互功能 */}
                <CommentSectionWrapper
                    videoId={post.id}
                    initialComments={comments}
                />
            </section>
        </InfoPanel>
    )
}

// 导入必须放在这里，因为CommentSectionWrapper依赖于InfoPanel，避免循环依赖
import { CommentSectionWrapper } from "./CommentSectionWrapper" 