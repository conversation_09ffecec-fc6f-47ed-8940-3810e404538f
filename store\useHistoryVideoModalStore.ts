import { create } from 'zustand';

interface HistoryVideoModalState {
    // 当前打开的视频任务ID
    taskId: string | null;
    // 弹窗是否打开
    isOpen: boolean;

    // Actions
    openModal: (taskId: string) => void;
    closeModal: () => void;
}

export const useHistoryVideoModalStore = create<HistoryVideoModalState>((set) => ({
    taskId: null,
    isOpen: false,

    openModal: (taskId: string) => {
        set({ taskId, isOpen: true });
    },

    closeModal: () => {
        set({ isOpen: false });
    }
})); 