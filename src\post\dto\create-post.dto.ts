import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreatePostDto {
    @IsOptional()
    @IsUUID()
    taskId?: string; // gen-video-task 生成的视频ID，兼容旧版本

    @IsOptional()
    @IsString()
    videoUrl?: string; // 用户上传的视频URL

    @IsNotEmpty()
    @IsString()
    title: string; // 帖子标题

    @IsOptional()
    @IsString()
    description?: string; // 帖子描述

    @IsOptional()
    @IsString()
    thumbnailUrl?: string; // 缩略图URL，如果上传视频时有提供
}