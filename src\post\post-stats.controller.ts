import { Controller, Post, Body, Req, UseGuards, Get, Param, ParseUUIDPipe } from '@nestjs/common';
import { PostService } from './post.service';
import { PostStatsEventDto, PostStatsResponseDto } from './dto/post-stats.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { Request } from 'express';

@ApiTags('post-stats')
@Controller('post-stats')
export class PostStatsController {
    constructor(private readonly postService: PostService) {}

    /**
     * 记录帖子统计事件（浏览量或点击量）
     * @param req 请求对象，可能包含用户信息
     * @param statsEventDto 统计事件DTO
     * @returns 操作结果
     */
    @Post()
    @ApiOperation({ summary: '记录帖子统计事件' })
    @ApiResponse({ status: 200, description: '操作成功', type: PostStatsResponseDto })
    async recordPostStats(
        @Req() req: Request,
        @Body() statsEventDto: PostStatsEventDto,
    ): Promise<PostStatsResponseDto> {
        const userId = req.user?.id || null;
        return this.postService.recordPostStats(userId, statsEventDto);
    }

    /**
     * 获取帖子统计数据
     * @param postId 帖子ID
     * @returns 帖子统计数据
     */
    @Get(':post_id')
    @ApiOperation({ summary: '获取帖子统计数据' })
    @ApiParam({ name: 'post_id', description: '帖子ID' })
    @ApiResponse({ status: 200, description: '获取成功' })
    async getPostStats(
        @Param('post_id', ParseUUIDPipe) postId: string,
    ): Promise<{ view_count: number, click_count: number }> {
        return this.postService.getPostStats(postId);
    }
}
