{"name": "reelmind", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-switch": "1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@stripe/react-stripe-js": "^3.3.0", "@stripe/stripe-js": "^5.9.2", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.69.0", "@tanstack/react-virtual": "^3.13.8", "@types/uuid": "^10.0.0", "@uiw/react-md-editor": "^4.0.5", "@uppy/aws-s3": "^4.2.3", "@uppy/core": "^4.4.2", "@uppy/dashboard": "^4.3.2", "@uppy/react": "^4.2.1", "@uppy/tus": "^4.2.2", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "framer-motion": "^12.5.0", "isomorphic-dompurify": "^2.25.0", "lucide-react": "^0.454.0", "markdown-it": "^14.1.0", "next": "^15.2.4", "next-remove-imports": "^1.0.12", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-markdown-editor-lite": "^1.3.4", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.9", "@tailwindcss/typography": "^0.5.16", "@types/markdown-it": "^14.1.2", "@types/node": "^22", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-syntax-highlighter": "^15.5.13", "postcss": "^8.4.31", "tailwindcss": "^4.0.9", "typescript": "^5"}}