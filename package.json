{"name": "reelmind.server", "version": "0.0.1", "description": "Reelmind Server", "author": "<PERSON><PERSON><PERSON>", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build && pnpm sentry:sourcemaps --auth-token $echo $(sed -n 's/^SENTRY_AUTH_TOKEN=//p' .env)", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "NODE_ENV=production node dist/main", "reload": "pm2 reload reelmind.server", "deploy": "npm run build && pm2 start npm --name reelmind.server -- run start:prod", "deploy:i": "pnpm i && npm run build && pm2 start npm --name reelmind.server -- run start:prod", "update": "git pull && pnpm i && npm run build && npm run reload", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org reelmind --project node-nestjs ./dist && sentry-cli sourcemaps upload --org reelmind --project node-nestjs ./dist"}, "dependencies": {"@fal-ai/client": "^1.4.0", "@nestjs/common": "^11.0.11", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.11", "@nestjs/platform-express": "^11.0.11", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.0.6", "@sentry/cli": "^2.42.2", "@sentry/nestjs": "^9.5.0", "@sentry/profiling-node": "^9.5.0", "@supabase/supabase-js": "^2.49.1", "@types/geoip-lite": "^1.4.4", "@types/useragent": "^2.3.4", "body-parser": "^1.20.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "geoip-lite": "^1.4.10", "helmet": "^8.0.0", "nanoid": "^5.1.2", "nest-winston": "^1.10.2", "openai": "^4.95.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "stripe": "^17.7.0", "useragent": "^2.3.0", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.1", "@nestjs/testing": "^11.0.11", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.13.8", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.2", "eslint-plugin-prettier": "^5.2.3", "jest": "^29.7.0", "prettier": "^3.5.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.6", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}