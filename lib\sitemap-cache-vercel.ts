import { createClient } from '@supabase/supabase-js';
import { getBlogSlugsForSitemapPaginated, getBlogPostsCount } from './blog';
import zlib from 'zlib';
import { promisify } from 'util';
import crypto from 'crypto';

// 将回调函数转换为Promise
const gzip = promisify(zlib.gzip);

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 站点地图配置
const BASE_URL = 'https://reelmind.ai';
const POSTS_PER_SITEMAP = 40000;
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24小时，单位：毫秒

// Supabase存储桶名称
const SITEMAP_BUCKET = 'sitemaps';

// 缓存元数据类型
interface SitemapMetadata {
  id: number;
  type: 'index' | 'blog';
  blog_id?: number;
  last_updated: string;
  total_posts?: number;
  etag: string;
}

/**
 * 保存站点地图元数据
 */
async function saveSitemapMetadata(metadata: Omit<SitemapMetadata, 'id'>): Promise<void> {
  try {

    // 查询是否已存在相同类型和blog_id的记录
    const { data, error } = await supabase
      .from('sitemap_metadata')
      .select('id')
      .eq('type', metadata.type)
      .eq('blog_id', metadata.blog_id || 0);

    if (error) {
      console.error('查询站点地图元数据时出错:', error);
      throw error;
    }

    if (data && data.length > 0) {
      // 更新现有记录
      const { error: updateError } = await supabase
        .from('sitemap_metadata')
        .update({
          last_updated: metadata.last_updated,
          total_posts: metadata.total_posts,
          etag: metadata.etag,
        })
        .eq('id', data[0].id);

      if (updateError) {
        console.error('更新站点地图元数据时出错:', updateError);
        throw updateError;
      }
    } else {
      // 插入新记录
      const { error: insertError } = await supabase
        .from('sitemap_metadata')
        .insert([metadata]);

      if (insertError) {
        console.error('插入站点地图元数据时出错:', insertError);
        throw insertError;
      }
    }
  } catch (error) {
    console.error('保存站点地图元数据时出错:', error);
    throw error;
  }
}

/**
 * 获取站点地图元数据
 */
async function getSitemapMetadata(type: 'index' | 'blog', blogId?: number): Promise<SitemapMetadata | null> {
  try {
    const { data, error } = await supabase
      .from('sitemap_metadata')
      .select('*')
      .eq('type', type)
      .eq('blog_id', blogId || 0)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // 没有找到记录
        return null;
      }
      console.error('获取站点地图元数据时出错:', error);
      throw error;
    }

    return data as SitemapMetadata;
  } catch (error) {
    console.error('获取站点地图元数据时出错:', error);
    return null;
  }
}

/**
 * 检查缓存是否有效
 */
async function isCacheValid(type: 'index' | 'blog', blogId?: number): Promise<boolean> {
  try {
    const metadata = await getSitemapMetadata(type, blogId);
    if (!metadata) return false;

    // 检查缓存是否过期
    const lastUpdated = new Date(metadata.last_updated).getTime();
    const now = Date.now();
    if (now - lastUpdated > CACHE_TTL) return false;

    // 对于索引站点地图，检查博客文章总数是否变化
    if (type === 'index' && metadata.total_posts !== undefined) {
      const currentTotalPosts = await getBlogPostsCount();
      if (currentTotalPosts !== metadata.total_posts) return false;
    }

    return true;
  } catch (error) {
    console.error('检查缓存有效性时出错:', error);
    return false;
  }
}

/**
 * 获取站点地图文件路径
 */
function getSitemapPath(type: 'index' | 'blog', blogId?: number): string {
  if (type === 'index') {
    return 'sitemap-index.xml.gz';
  } else {
    return `sitemap-blog-${blogId}.xml.gz`;
  }
}

/**
 * 生成站点地图XML
 */
function generateSitemapXML(entries: Array<{
  url: string;
  lastModified: Date;
  changeFrequency: string;
  priority: number;
}>): string {
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  for (const entry of entries) {
    xml += '  <url>\n';
    xml += `    <loc>${entry.url}</loc>\n`;
    xml += `    <lastmod>${entry.lastModified.toISOString()}</lastmod>\n`;
    xml += `    <changefreq>${entry.changeFrequency}</changefreq>\n`;
    xml += `    <priority>${entry.priority}</priority>\n`;
    xml += '  </url>\n';
  }

  xml += '</urlset>';
  return xml;
}

/**
 * 生成站点地图索引XML
 */
function generateSitemapIndexXML(entries: Array<{
  url: string;
  lastModified: Date;
}>): string {
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  for (const entry of entries) {
    xml += '  <sitemap>\n';
    xml += `    <loc>${entry.url}</loc>\n`;
    xml += `    <lastmod>${entry.lastModified.toISOString()}</lastmod>\n`;
    xml += '  </sitemap>\n';
  }

  xml += '</sitemapindex>';
  return xml;
}

/**
 * 生成ETag
 */
function generateETag(content: Buffer): string {
  return crypto.createHash('md5').update(content.toString('binary')).digest('hex');
}

/**
 * 生成并缓存博客站点地图
 */
export async function generateAndCacheBlogSitemap(id: number): Promise<Buffer> {
  console.log(`生成博客站点地图(ID: ${id})...`);

  // 计算起始索引
  const start = id * POSTS_PER_SITEMAP;

  // 获取当前批次的博客文章
  const currentBatch = await getBlogSlugsForSitemapPaginated(start, POSTS_PER_SITEMAP);

  console.log(`获取到${currentBatch.length}条记录`);

  // 生成站点地图条目
  const sitemapEntries = currentBatch.map(post => ({
    url: `${BASE_URL}/blog/${post.slug}`,
    lastModified: post.created_at ? new Date(post.created_at) : new Date(),
    changeFrequency: 'monthly',
    priority: 0.7,
  }));

  // 生成XML
  const sitemapXML = generateSitemapXML(sitemapEntries);

  // 压缩XML
  const compressedXML = await gzip(sitemapXML);

  // 生成ETag
  const etag = generateETag(compressedXML);

  // 保存到Supabase Storage
  const filePath = getSitemapPath('blog', id);
  const { error } = await supabase.storage
    .from(SITEMAP_BUCKET)
    .upload(filePath, compressedXML, {
      contentType: 'application/xml',
      cacheControl: '86400',
      upsert: true,
    });

  if (error) {
    console.error(`上传博客站点地图(ID: ${id})时出错:`, error);
    throw error;
  }

  // 保存元数据
  await saveSitemapMetadata({
    type: 'blog',
    blog_id: id,
    last_updated: new Date().toISOString(),
    etag,
  });

  return compressedXML;
}

/**
 * 生成并缓存站点地图索引
 */
export async function generateAndCacheSitemapIndex(): Promise<Buffer> {
  console.log('生成站点地图索引...');

  // 获取博客文章总数
  const totalPosts = await getBlogPostsCount();

  // 计算站点地图数量
  const sitemapCount = Math.ceil(totalPosts / POSTS_PER_SITEMAP);

  console.log(`博客文章总数: ${totalPosts}, 站点地图数量: ${sitemapCount}`);

  // 生成博客站点地图引用
  const blogSitemapReferences = Array.from({ length: sitemapCount }, (_, i) => ({
    url: `${BASE_URL}/blog/sitemap/${i}`,
    lastModified: new Date(),
  }));

  // 生成站点地图索引XML
  const mainSitemapUrl = `${BASE_URL}/layout.xml`;
  const sitemapIndexEntries = [
    {
      url: mainSitemapUrl,
      lastModified: new Date(),
    },
    ...blogSitemapReferences,
  ];

  const sitemapIndexXML = generateSitemapIndexXML(sitemapIndexEntries);

  // 压缩XML
  const compressedXML = await gzip(sitemapIndexXML);

  // 生成ETag
  const etag = generateETag(compressedXML);

  // 保存到Supabase Storage
  const filePath = getSitemapPath('index');
  const { error } = await supabase.storage
    .from(SITEMAP_BUCKET)
    .upload(filePath, compressedXML, {
      contentType: 'application/xml',
      cacheControl: '86400',
      upsert: true,
    });

  if (error) {
    console.error('上传站点地图索引时出错:', error);
    throw error;
  }

  // 保存元数据
  await saveSitemapMetadata({
    type: 'index',
    last_updated: new Date().toISOString(),
    total_posts: totalPosts,
    etag,
  });

  return compressedXML;
}

/**
 * 获取缓存的站点地图
 */
export async function getCachedSitemap(id: number): Promise<{ data: Buffer } | null> {
  try {
    // 从Supabase Storage获取文件
    const filePath = getSitemapPath('blog', id);
    const { data, error } = await supabase.storage
      .from(SITEMAP_BUCKET)
      .download(filePath);

    if (error || !data) {
      console.error(`获取缓存的博客站点地图(ID: ${id})时出错:`, error);
      return null;
    }

    // 将Blob转换为Buffer
    const arrayBuffer = await data.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    return { data: buffer };
  } catch (error) {
    console.error(`获取缓存的博客站点地图(ID: ${id})时出错:`, error);
    return null;
  }
}

/**
 * 获取缓存的站点地图索引
 */
export async function getCachedSitemapIndex(): Promise<{ data: Buffer; etag: string } | null> {
  try {
    // 获取元数据
    const metadata = await getSitemapMetadata('index');
    if (!metadata) return null;

    // 从Supabase Storage获取文件
    const filePath = getSitemapPath('index');
    const { data, error } = await supabase.storage
      .from(SITEMAP_BUCKET)
      .download(filePath);

    if (error || !data) {
      console.error('获取缓存的站点地图索引时出错:', error);
      return null;
    }

    // 将Blob转换为Buffer
    const arrayBuffer = await data.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    return { data: buffer, etag: metadata.etag };
  } catch (error) {
    console.error('获取缓存的站点地图索引时出错:', error);
    return null;
  }
}

/**
 * 获取或生成站点地图
 */
export async function getOrGenerateSitemap(id: number): Promise<{ data: Buffer }> {
  // 如果缓存有效，尝试从缓存获取
  const cachedSitemap = await getCachedSitemap(id);
  if (cachedSitemap) {
    console.log(`使用缓存的站点地图(ID: ${id})`);
    return cachedSitemap;
  }

  // 如果缓存无效或不存在，生成新的站点地图
  const compressedXML = await generateAndCacheBlogSitemap(id);

  return { data: compressedXML };
}

/**
 * 获取或生成站点地图索引
 */
export async function getOrGenerateSitemapIndex(): Promise<{ data: Buffer; etag: string }> {
  // 检查缓存是否有效
  const isCacheValidResult = await isCacheValid('index');

  // 如果缓存有效，尝试从缓存获取
  if (isCacheValidResult) {
    const cachedSitemapIndex = await getCachedSitemapIndex();
    if (cachedSitemapIndex) {
      console.log('使用缓存的站点地图索引');
      return cachedSitemapIndex;
    }
  }

  // 如果缓存无效或不存在，生成新的站点地图索引
  const compressedXML = await generateAndCacheSitemapIndex();
  const etag = generateETag(compressedXML);

  return { data: compressedXML, etag };
}

