"use client"

import type React from "react"
import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Mail, EyeOff, Eye, X, AlertCircle } from "lucide-react"
import { Dialog } from "@/components/ui/dialog"
import { useAuth } from "@/contexts/auth-context"
import useLoginDialogStore from "@/store/useLoginDialogStore"
import { validateEmail } from "@/lib/email-validator"

export function LoginDialog() {
    const [email, setEmail] = useState("")
    const [password, setPassword] = useState("")
    const [showPassword, setShowPassword] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [emailError, setEmailError] = useState<string | null>(null)
    const [showConfirmation, setShowConfirmation] = useState(false)
    const [mounted, setMounted] = useState(false)

    const { signIn, signUp, signInWithGoogle, user } = useAuth()
    const {
        isOpen,
        closeLoginDialog,
        pendingAction,
        executePendingAction
    } = useLoginDialogStore()

    useEffect(() => {
        setMounted(true)
    }, [])

    // 当用户登录状态发生变化时，检查是否有待处理操作
    useEffect(() => {
        if (user && pendingAction) {
            executePendingAction();
            closeLoginDialog();
        }
    }, [user, pendingAction, executePendingAction, closeLoginDialog]);

    // 重置表单状态
    const resetForm = () => {
        setEmail("")
        setPassword("")
        setError(null)
        setEmailError(null)
        setShowConfirmation(false)
        setIsLoading(false)
    }

    // 处理邮箱输入变化
    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newEmail = e.target.value;
        setEmail(newEmail);

        // 如果邮箱为空，清除错误
        if (!newEmail) {
            setEmailError(null);
            return;
        }

        // 验证邮箱
        const emailValidation = validateEmail(newEmail);
        if (!emailValidation.valid && emailValidation.suspicious) {
            setEmailError(emailValidation.message || "This email appears to be suspicious");
        } else {
            setEmailError(null);
        }
    }

    // 当对话框关闭时重置表单
    useEffect(() => {
        if (!isOpen) {
            resetForm()
        }
    }, [isOpen])

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setError(null)

        // 验证邮箱是否为可疑的机器人邮箱
        const emailValidation = validateEmail(email);
        if (!emailValidation.valid) {
            setError(emailValidation.message || "Invalid email address");
            return;
        }

        setIsLoading(true)

        try {
            // 先尝试登录
            try {
                await signIn(email, password)
                closeLoginDialog()
                return
            } catch (err) {
                // 如果登录失败，检查错误类型
                if (err instanceof Error) {
                    // 邮箱未验证
                    if (err.message.includes("Email not confirmed")) {
                        setError("Please verify your email before logging in")
                        setIsLoading(false)
                        return
                    }
                    // 如果是无效凭证，尝试注册
                    if (err.message.includes("Invalid login credentials")) {
                        // 再次验证邮箱（防止绕过前端验证）
                        const emailValidation = validateEmail(email);
                        if (!emailValidation.valid) {
                            setError(emailValidation.message || "Invalid email address");
                            setIsLoading(false);
                            return;
                        }

                        const { data: signUpData, error: signUpError } = await signUp(email, password)
                        if (signUpError) throw signUpError

                        // 注册成功，显示确认邮件提示
                        setShowConfirmation(true)
                        setIsLoading(false)
                        return
                    }
                }
                throw err
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : "Operation failed, please try again")
            setIsLoading(false)
        }
    }

    const handleGoogleSignIn = async () => {
        try {
            await signInWithGoogle()
            closeLoginDialog()
        } catch (error) {
            console.error("Google sign in failed:", error)
            setError("Google sign in failed, please try again")
        }
    }

    if (!mounted) return null

    // 显示邮箱确认提示
    if (showConfirmation) {
        return (
            <Dialog open={isOpen} onOpenChange={closeLoginDialog}>
                <div
                    className="relative w-[800px] max-w-[90vw] bg-background rounded-xl overflow-hidden shadow-2xl p-6 text-center"
                    onClick={(e) => e.stopPropagation()}
                >
                    <button onClick={(e) => {
                        e.stopPropagation();  // 阻止事件冒泡
                        closeLoginDialog();
                    }} className="absolute right-4 top-4 rounded-full p-2 hover:bg-muted transition-colors">
                        <X className="h-5 w-5 text-muted-foreground" />
                    </button>

                    <div className="max-w-md mx-auto">
                        <Mail className="h-12 w-12 mx-auto mb-4 text-primary-foreground" />
                        <h2 className="text-2xl font-bold mb-2">Verify your email</h2>
                        <p className="text-muted-foreground mb-4">
                            We've sent a verification email to {email}.
                            Please click the link in the email to complete verification, then return here to log in.
                        </p>
                        <div className="space-y-2">
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();  // 阻止事件冒泡
                                    closeLoginDialog();
                                }}
                                className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                            >
                                I understand
                            </button>
                            <button
                                onClick={resetForm}
                                className="w-full px-4 py-2 border rounded-md hover:bg-muted transition-colors"
                            >
                                Use a different email
                            </button>
                        </div>
                    </div>
                </div>
            </Dialog>
        )
    }

    return (
        <Dialog open={isOpen} onOpenChange={closeLoginDialog}>
            <div
                className="relative w-[800px] max-w-[90vw] bg-background rounded-xl overflow-hidden shadow-2xl transition-all duration-300 ease-out"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex h-[600px]">
                    {/* Left: Login Form */}
                    <div className="w-full lg:w-1/2 bg-foreground/5 p-8 sm:p-10">
                        <div className="h-full flex flex-col">
                            <div className="flex justify-between items-center mb-8">
                                <div>
                                    <h2 className="text-2xl font-bold">Welcome to Reelmind</h2>
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Sign in or create your creator account
                                    </p>
                                </div>
                                <button onClick={(e) => {
                                    e.stopPropagation();  // 阻止事件冒泡
                                    closeLoginDialog();
                                }} className="rounded-full p-2 hover:bg-muted transition-colors">
                                    <X className="h-5 w-5 text-muted-foreground" />
                                </button>
                            </div>

                            {/* Social Login Buttons */}
                            <div>
                                <button
                                    type="button"
                                    onClick={handleGoogleSignIn}
                                    className="w-full flex justify-center items-center gap-2 px-4 py-3 border rounded-lg shadow-xs bg-background text-sm font-medium text-foreground hover:bg-muted transition-colors duration-200"
                                >
                                    <Image src="/google.svg" alt="Google" width={20} height={20} className="w-5 h-5" priority />
                                    <span>Sign in with Google</span>
                                </button>
                            </div>

                            <div className="relative my-6">
                                <div className="absolute inset-0 flex items-center">
                                    <div className="w-full border-t" />
                                </div>
                                <div className="relative flex justify-center text-sm">
                                    <span className="px-2 text-muted-foreground">Or continue with email</span>
                                </div>
                            </div>

                            <form onSubmit={handleSubmit} className="flex flex-col flex-1">
                                {error && (
                                    <div className="rounded-md bg-destructive/10 p-4 mb-4">
                                        <div className="text-sm text-destructive">{error}</div>
                                    </div>
                                )}

                                <div className="space-y-4 flex-1">
                                    <div>
                                        <label htmlFor="email" className="sr-only">
                                            Email
                                        </label>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <Mail className="h-5 w-5 text-muted-foreground" />
                                            </div>
                                            <input
                                                id="email"
                                                name="email"
                                                type="email"
                                                autoComplete="email"
                                                required
                                                value={email}
                                                onChange={handleEmailChange}
                                                className={`block w-full pl-10 pr-3 py-3 border rounded-lg bg-background placeholder-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-primary focus:border-transparent sm:text-sm transition-all duration-200 ${emailError ? 'border-destructive' : ''}`}
                                                placeholder="Email"
                                            />
                                        </div>
                                        {emailError && (
                                            <div className="mt-1 text-sm text-destructive flex items-center gap-1">
                                                <AlertCircle className="h-3 w-3" />
                                                <span>{emailError}</span>
                                            </div>
                                        )}
                                    </div>

                                    <div>
                                        <label htmlFor="password" className="sr-only">
                                            Password
                                        </label>
                                        <div className="relative">
                                            <input
                                                id="password"
                                                name="password"
                                                type={showPassword ? "text" : "password"}
                                                autoComplete="current-password"
                                                required
                                                value={password}
                                                onChange={(e) => setPassword(e.target.value)}
                                                className="block w-full pl-3 pr-10 py-3 border rounded-lg bg-background placeholder-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-primary focus:border-transparent sm:text-sm transition-all duration-200"
                                                placeholder="Password"
                                            />
                                            <button
                                                type="button"
                                                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                                onClick={() => setShowPassword(!showPassword)}
                                            >
                                                {showPassword ? (
                                                    <Eye className="h-5 w-5 text-muted-foreground" />
                                                ) : (
                                                    <EyeOff className="h-5 w-5 text-muted-foreground" />
                                                )}
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-4">
                                    <button
                                        type="submit"
                                        disabled={isLoading}
                                        className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-xs font-medium bg-white text-black disabled:bg-primary/70 disabled:cursor-not-allowed transition-colors duration-200"
                                    >
                                        {isLoading ? "Processing..." : "Continue"}
                                    </button>
                                </div>

                                <div className="mt-4 text-center text-xs text-muted-foreground">
                                    By continuing, you agree to our{" "}
                                    <Link href="/terms" target="_blank" className="hover:text-foreground transition-colors duration-200">
                                        Terms of Service
                                    </Link>{" "}
                                    and{" "}
                                    <Link href="/privacy" target="_blank" className="hover:text-foreground transition-colors duration-200">
                                        Privacy Policy
                                    </Link>
                                    .
                                </div>
                            </form>
                        </div>
                    </div>

                    {/* Right: Brand Section */}
                    <div className="hidden lg:block w-1/2 bg-card p-12 relative overflow-hidden">
                        <div className="relative z-10">
                            <h1 className="text-4xl font-bold mb-6">
                                Reel-Makers Welcome Here.
                            </h1>
                            <p className="text-lg">
                                Unspool Your Mind. Let AI Reel It In.
                                <br />
                                Join our creator community and start creating.
                            </p>
                        </div>
                        {/* Decorative Elements */}
                        <div className="absolute bottom-0 right-0 transform translate-x-1/4 translate-y-1/4">
                            <div className="w-80 h-80 bg-foreground/20 rounded-full" />
                        </div>
                        <div className="absolute top-0 left-0 transform -translate-x-1/4 -translate-y-1/4">
                            <div className="w-64 h-64 bg-foreground/10 rounded-full" />
                        </div>
                    </div>
                </div>
            </div>
        </Dialog>
    )
}

