import { useCallback, useState } from "react";
import { useHistoryVideoModalStore } from "@/store/useHistoryVideoModalStore";
import type { VideoTask } from "@/types/video-task";
import { downloadVideo } from "@/lib/utils/downloadVideo";

/**
 * 处理任务操作的自定义Hook
 */
export function useTaskActions() {
    const { openModal } = useHistoryVideoModalStore();
    const [selectedVideo, setSelectedVideo] = useState<VideoTask | null>(null);
    const [publishDialogOpen, setPublishDialogOpen] = useState(false);
    const [isDownloading, setIsDownloading] = useState(false);

    // 处理视频点击
    const handleVideoClick = useCallback((task: VideoTask) => {
        if (task.status === "completed" && task.output_result?.video_url) {
            openModal(task.id);
        }
    }, [openModal]);

    // 处理remix
    const handleRemix = useCallback((taskId: string) => {
        // 使用window.location而不是router，避免依赖router
        window.location.href = `/create?remix=${taskId}`;
    }, []);

    // 处理下载
    const handleDownload = useCallback(async (video: VideoTask) => {
        if (isDownloading) return; // 防止重复点击
        
        if (video.status === "completed" && video.output_result?.video_url) {
            setIsDownloading(true);
            
            try {
                // 生成文件名
                const prompt = video.input_params?.prompt || "video";
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const filename = `Reelmind-${prompt.slice(0, 10)}-${timestamp}.mp4`;
                
                const success = await downloadVideo(video.output_result.video_url, filename);
                
                if (success) {
                    console.log('Video download completed successfully');
                }
            } catch (error) {
                console.error('Video download failed:', error);
            } finally {
                setIsDownloading(false);
            }
        }
    }, [isDownloading]);

    // 处理发布
    const handlePublish = useCallback((video: VideoTask, e: React.MouseEvent) => {
        e.stopPropagation();
        setSelectedVideo(video);
        setPublishDialogOpen(true);
    }, []);

    // 处理分享
    const handleShare = useCallback((video: VideoTask, e: React.MouseEvent) => {
        e.stopPropagation();
        setSelectedVideo(video);
        setPublishDialogOpen(true);
    }, []);

    return {
        selectedVideo,
        publishDialogOpen,
        setPublishDialogOpen,
        isDownloading,
        handleVideoClick,
        handleRemix,
        handleDownload,
        handlePublish,
        handleShare,
    };
}
