"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Loader2, Zap, CreditCard, Terminal } from "lucide-react"
import { useTrainStore } from "@/store/useTrainStore"
import { useCalculatePrice, useStartTraining } from "../queries"
import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/toast"

export function PriceDisplaySection() {
  const { videos, settings } = useTrainStore()
  const [videoUrls, setVideoUrls] = useState<string[]>([])
  const toast = useToast()
  const [isProcessComplete, setIsProcessComplete] = useState(false)

  // 监听videos变化，立即准备计算价格（只依赖视频数量）
  useEffect(() => {
    // 提取有效的视频URL列表
    const urls = videos.map(v => v.url ?? "").filter(Boolean)
    setVideoUrls(urls)
  }, [videos])

  // 计算价格，不需要等待trigger_word也不需要额外的条件控制
  const { data, isLoading: isPriceLoading } = useCalculatePrice(
    videos.map(v => v.id), // 只需要视频ID即可，因为现在价格只和视频数量有关
    settings,
    { enabled: videos.length > 0 } // 只要有视频就可以计算价格
  )

  const { mutate: startTraining, isPending: isTrainingLoading } = useStartTraining()
  const [isUploading, setIsUploading] = useState(false)
  const price = data?.credits

  // 检查是否可以执行训练
  const canExecuteTraining =
    !isTrainingLoading &&
    !isUploading &&
    videos.length > 0 &&
    !!settings.trigger_word &&
    !isProcessComplete

  const handleStartTraining = async () => {
    // 如果不能执行训练，显示提示
    if (!canExecuteTraining) {
      if (!settings.trigger_word) {
        toast.error("Error", "Please enter a trigger word")
      } else if (videos.length === 0) {
        toast.error("Error", "Please upload at least one video")
      }
      return
    }

    setIsUploading(true)
    console.log("开始训练流程: 准备上传视频...")

    try {
      // 先调用uploadTrainingVideos上传视频
      if (typeof window.uploadTrainingVideos === 'function') {
        console.log("找到上传函数，开始执行上传...")
        const uploadSuccess = await window.uploadTrainingVideos()
        console.log("上传结果:", uploadSuccess)

        if (uploadSuccess) {
          // 从store获取最新视频状态
          const currentVideos = useTrainStore.getState().videos
          const currentUrls = currentVideos.map(v => v.url ?? "").filter(Boolean)
          console.log("上传后的视频URL:", currentUrls)

          if (currentUrls.length === 0) {
            toast.error("Error", "No valid videos were uploaded")
            setIsUploading(false)
            return
          }

          console.log("开始初始化训练任务，URL数量:", currentUrls.length)
          // 执行训练
          startTraining(
            {
              videos: currentUrls,
              settings
            },
            {
              onSuccess: (data) => {
                console.log("训练任务创建成功!", data)
                // 训练开始后，重置上传状态
                setIsUploading(false)
                // 标记流程完成
                setIsProcessComplete(true)
                // 显示成功消息
                toast.success("Success", "Training started successfully")
              },
              onError: (error) => {
                console.error("训练任务创建失败:", error)
                toast.error("Training Failed", error instanceof Error ? error.message : "Failed to start training")
                setIsUploading(false)
                setIsProcessComplete(false)
              }
            }
          )
        } else {
          console.error("上传失败，取消训练")
          toast.error("Upload Failed", "Failed to upload videos")
          setIsUploading(false)
        }
      } else {
        console.error("上传函数不可用!")
        toast.error("Error", "Upload function not available")
        setIsUploading(false)
      }
    } catch (error) {
      console.error("训练流程失败:", error)
      toast.error("Error", error instanceof Error ? error.message : "An unknown error occurred")
      setIsUploading(false)
    }
  }

  // 训练状态文本
  const buttonText = () => {
    if (isProcessComplete) return "TRAINING STARTED"
    if (isTrainingLoading) return "INITIALIZING TRAINING"
    if (isUploading) return "UPLOADING VIDEOS"
    return "EXECUTE TRAINING"
  }

  // 按钮禁用条件
  const isButtonDisabled = isTrainingLoading || isUploading || videos.length === 0 || !settings.trigger_word || isProcessComplete

  return (
    <div className="flex flex-row items-center justify-between gap-4 px-1">
      {/* Cyberpunk price display - 左侧价格显示 */}
      <div className="flex items-center space-x-2 border border-[#F5EFFF]/30 bg-black/30 rounded-lg p-2">
        <CreditCard className="h-4 w-4 text-[#F5EFFF]" />
        <span className="text-[#F5EFFF] font-mono uppercase text-xs tracking-wider">CREDITS:</span>
        {isPriceLoading ? (
          <Loader2 className="h-4 w-4 text-[#F5EFFF] animate-spin ml-1" />
        ) : (
          <div className="font-bold font-mono text-[#F5EFFF] flex items-center">
            <span className="text-shadow-[0_0_5px_#F5EFFF]">
              {price !== null && price !== undefined ? `${price}` : "--"}
            </span>
          </div>
        )}
      </div>

      {/* Hackernoon style button - 右侧按钮 */}
      <div className="flex-1">
        <Button
          className="w-full h-10 neon-button bg-black text-[#F5EFFF] uppercase tracking-widest font-mono"
          onClick={handleStartTraining}
          disabled={isButtonDisabled}
        >
          {isTrainingLoading || isUploading ? (
            <div className="flex items-center justify-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">{buttonText()}</span>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2">
              <Zap className="h-4 w-4" />
              <span className="glitch-effect text-sm">{buttonText()}</span>
            </div>
          )}
        </Button>
      </div>

      {/* Decorative scanlines */}
      <div className="scanlines"></div>
    </div>
  )
}

