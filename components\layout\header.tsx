"use client"

import { UserNav } from "@/components/shared/user-nav"
import { cn } from "@/lib/utils"
import { Search } from "lucide-react"
import { useState } from "react"
import { usePathname } from "next/navigation"
import { Logo } from "./logo"
import { FunctionToggle } from "@/app/sound/components/function-toggle"

export function Header({ className }: { className?: string }) {
  const [showMobileSearch, setShowMobileSearch] = useState(false);
  const pathname = usePathname() || "";

  // 判断是否是create页面
  const isCreatePage = pathname.startsWith("/create");
  // 判断是否是sound页面
  const isSoundPage = pathname.startsWith("/sound");

  return (
    <header className={cn(
      "fixed top-0 left-0 right-0 flex h-[var(--header-height)] items-center bg-background/95 backdrop-blur-sm z-10",
      "px-2 sm:px-4 lg:px-5 py-4 sm:py-6 z-50",
      className
    )}>
      <Logo />

      {/* Function Toggle for Sound Page - Center */}
      {isSoundPage && (
        <div className="flex-1 flex justify-center">
          <FunctionToggle />
        </div>
      )}

      {/* Mobile Search Toggle Button */}
      {!showMobileSearch && !isCreatePage && !isSoundPage && (
        <button
          onClick={() => setShowMobileSearch(true)}
          className="flex lg:hidden items-center justify-center p-2 rounded-full hover:bg-muted transition-colors mr-1"
          aria-label="Open search"
        >
          <Search className="h-5 w-5" />
        </button>
      )}

      {/* Close Search Button - Mobile Only */}
      {showMobileSearch && !isCreatePage && !isSoundPage && (
        <button
          onClick={() => setShowMobileSearch(false)}
          className="absolute right-2 top-1/2 -translate-y-1/2 flex lg:hidden items-center justify-center p-2 rounded-full hover:bg-muted transition-colors z-20"
          aria-label="Close search"
        >
          <span className="text-sm font-medium">Cancel</span>
        </button>
      )}

      {/* User navigation - hide on mobile when search is active */}
      <div className={cn(
        "ml-auto",
        showMobileSearch ? "hidden" : "block"
      )}>
        <UserNav />
      </div>
    </header>
  )
}

