'use client';

import { useState } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { MoreHorizontal, Edit, Trash2, ExternalLink } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import type { Model } from '@/types/model';

interface ModelTableProps {
    models: Model[];
    isLoading: boolean;
    onEdit: (model: Model) => void;
    onDelete: (modelId: string) => void;
}

export function ModelTable({
    models,
    isLoading,
    onEdit,
    onDelete,
}: ModelTableProps) {
    const [sortField, setSortField] = useState<keyof Model>('created_at');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

    // 处理排序
    const handleSort = (field: keyof Model) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    // 排序后的模型列表
    const sortedModels = [...models].sort((a, b) => {
        const aValue = a[sortField];
        const bValue = b[sortField];

        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;

        if (typeof aValue === 'string' && typeof bValue === 'string') {
            return sortDirection === 'asc'
                ? aValue.localeCompare(bValue)
                : bValue.localeCompare(aValue);
        }

        if (typeof aValue === 'number' && typeof bValue === 'number') {
            return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
        }

        return 0;
    });

    // 格式化模型类型显示
    const formatModelType = (type: string) => {
        switch (type) {
            case 'image-to-video':
                return 'I2V';
            case 'text-to-video':
                return 'T2V';
            case 'video-to-video':
                return 'V2V';
            default:
                return type.toUpperCase();
        }
    };

    // 格式化价格显示
    const formatPrice = (price: number | null | undefined) => {
        if (price === null || price === undefined) return '-';
        return price.toString();
    };

    // 格式化权重显示
    const formatWeight = (weight: number | null | undefined) => {
        if (weight === null || weight === undefined) return '-';
        return Math.round(weight).toString();
    };

    if (isLoading) {
        return (
            <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <div className="space-y-2 flex-1">
                            <Skeleton className="h-4 w-[250px]" />
                            <Skeleton className="h-4 w-[200px]" />
                        </div>
                        <Skeleton className="h-8 w-[100px]" />
                        <Skeleton className="h-8 w-[80px]" />
                    </div>
                ))}
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[60px]">Cover</TableHead>
                            <TableHead
                                className="cursor-pointer hover:bg-muted/50"
                                onClick={() => handleSort('name')}
                            >
                                Name
                                {sortField === 'name' && (
                                    <span className="ml-1">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                )}
                            </TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Storage Path</TableHead>
                            <TableHead
                                className="cursor-pointer hover:bg-muted/50"
                                onClick={() => handleSort('price')}
                            >
                                Price
                                {sortField === 'price' && (
                                    <span className="ml-1">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                )}
                            </TableHead>
                            <TableHead
                                className="cursor-pointer hover:bg-muted/50"
                                onClick={() => handleSort('weight')}
                            >
                                Weight
                                {sortField === 'weight' && (
                                    <span className="ml-1">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                )}
                            </TableHead>
                            <TableHead
                                className="cursor-pointer hover:bg-muted/50"
                                onClick={() => handleSort('created_at')}
                            >
                                Created
                                {sortField === 'created_at' && (
                                    <span className="ml-1">
                                        {sortDirection === 'asc' ? '↑' : '↓'}
                                    </span>
                                )}
                            </TableHead>
                            <TableHead className="w-[70px]">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {sortedModels.map((model) => (
                            <TableRow
                                key={model.id}
                                className="cursor-pointer hover:bg-muted/50"
                                onClick={() => onEdit(model)}
                            >
                                <TableCell onClick={(e) => e.stopPropagation()}>
                                    <Avatar className="h-10 w-10">
                                        <AvatarImage
                                            src={model.cover_img || undefined}
                                            alt={model.name}
                                        />
                                        <AvatarFallback>
                                            {model.name.substring(0, 2).toUpperCase()}
                                        </AvatarFallback>
                                    </Avatar>
                                </TableCell>
                                <TableCell>
                                    <div className="font-medium">{model.name}</div>
                                </TableCell>
                                <TableCell>
                                    <div className="text-sm font-medium text-muted-foreground">
                                        {formatModelType(model.model_type)}
                                    </div>
                                </TableCell>
                                <TableCell>
                                    <div className="text-sm font-mono text-muted-foreground">
                                        {model.storage_path || '-'}
                                    </div>
                                </TableCell>
                                <TableCell>
                                    <div className="text-sm font-medium">
                                        {formatPrice(model.price)}
                                    </div>
                                </TableCell>
                                <TableCell>
                                    <div className="text-sm">
                                        {formatWeight(model.weight)}
                                    </div>
                                </TableCell>
                                <TableCell>
                                    <div className="text-sm text-muted-foreground">
                                        {new Date(model.created_at).toLocaleDateString()}
                                    </div>
                                </TableCell>
                                <TableCell onClick={(e) => e.stopPropagation()}>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => onEdit(model)}>
                                                <Edit className="mr-2 h-4 w-4" />
                                                Edit
                                            </DropdownMenuItem>
                                            {model.source === 'fal.ai' && model.storage_path && (
                                                <DropdownMenuItem
                                                    onClick={() => window.open(`https://fal.ai/models/${model.storage_path}`, '_blank')}
                                                >
                                                    <ExternalLink className="mr-2 h-4 w-4" />
                                                    View on fal.ai
                                                </DropdownMenuItem>
                                            )}
                                            <DropdownMenuItem
                                                onClick={() => onDelete(model.id)}
                                                className="text-destructive"
                                            >
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}
