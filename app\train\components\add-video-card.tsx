import type React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Plus, Database } from "lucide-react"
import type { DropzoneRootProps } from "react-dropzone"

interface AddVideoCardProps {
  getRootProps?: <T extends HTMLElement = HTMLElement>() => DropzoneRootProps
  getInputProps?: () => React.InputHTMLAttributes<HTMLInputElement>
  isDragActive?: boolean
  onClick?: () => void
}

export function AddVideoCard({ getRootProps, getInputProps, isDragActive, onClick }: AddVideoCardProps) {
  // 使用拖放属性或点击事件
  const cardProps = getRootProps ? getRootProps() : { onClick };

  return (
    <Card className="aspect-video overflow-hidden pixel-border bg-black/60 border-[#F5EFFF]/50 hover:border-[#F5EFFF] transition-all duration-300 group relative">
      <CardContent className="p-0 h-full w-full relative" {...cardProps}>
        {getInputProps && <input {...getInputProps()} />}

        {/* Cyberpunk background pattern */}
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(#F5EFFF_1px,transparent_1px)] bg-[length:10px_10px]"></div>

        {/* Glowing corners */}
        <div className="absolute top-0 left-0 w-5 h-5 border-t-2 border-l-2 border-[#F5EFFF]"></div>
        <div className="absolute top-0 right-0 w-5 h-5 border-t-2 border-r-2 border-[#F5EFFF]"></div>
        <div className="absolute bottom-0 left-0 w-5 h-5 border-b-2 border-l-2 border-[#F5EFFF]"></div>
        <div className="absolute bottom-0 right-0 w-5 h-5 border-b-2 border-r-2 border-[#F5EFFF]"></div>

        <div className="flex flex-col items-center justify-center h-full group-hover:bg-[#F5EFFF]/10 transition-colors duration-300 z-10">
          <div className="p-3 rounded-full bg-black border border-[#F5EFFF] group-hover:shadow-[0_0_15px_rgba(0,255,0,0.7)] transition-all duration-300 mb-3">
            {isDragActive ? (
              <Database className="h-6 w-6 text-[#F5EFFF] animate-pulse" />
            ) : (
              <Plus className="h-6 w-6 text-[#F5EFFF]" />
            )}
          </div>
          <p className="text-sm text-center text-[#F5EFFF] font-mono uppercase tracking-wider">
            {isDragActive ? (
              <span className="blink-cursor">_INJECT_DATA_HERE_</span>
            ) : (
              <span>ADD_FILES<span className="blink-cursor">_</span></span>
            )}
          </p>
        </div>

        {/* Horizontal scan line effect */}
        <div className="absolute inset-0 overflow-hidden opacity-30 pointer-events-none">
          <div className="w-full h-[1px] bg-[#F5EFFF] animate-[scanline_2s_linear_infinite] shadow-[0_0_5px_#F5EFFF]"></div>
        </div>
      </CardContent>
    </Card>
  )
}

