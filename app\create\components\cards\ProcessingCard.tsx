"use client"

import { useState, useEffect, useRef, useMemo } from "react";
import type { VideoTask } from "@/types/video-task";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { useQuery } from "@tanstack/react-query";
import { generationApi } from "@/lib/api/generation";
import { getStatusText, getStatusColor } from "../../utils/status-utils";
import { CardInfoSection } from "./CardInfoSection";

interface ProcessingCardProps {
    video: VideoTask;
    onStatusChange?: (videoId: string, newStatus: string, updatedVideo?: VideoTask) => void;
}

/**
 * ProcessingCard Component - 显示处理中状态的视频任务
 * 显示进度条，每10秒查询一次状态更新
 * 根据任务创建时间和model.metadata.duration_estimate估算进度
 */
export const ProcessingCard = ({
    video,
    onStatusChange
}: ProcessingCardProps) => {
    // 本地状态
    const [progress, setProgress] = useState(video.progress || 0);
    const [status, setStatus] = useState(video.status);
    const [startedAt, setStartedAt] = useState<Date | null>(
        video.started_at ? new Date(video.started_at) : null
    );
    // 从模型元数据中获取预计处理时间，如果不存在则默认为3分钟
    const [estimatedTime, setEstimatedTime] = useState(3); // 默认3分钟

    // 引用
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    // 使用react-query获取任务详情，每10秒轮询一次
    const { data: taskDetail } = useQuery({
        queryKey: ['processingTaskDetail', video.id],
        queryFn: async () => {
            try {
                const result = await generationApi.getTaskDetail(video.id);
                return result;
            } catch (error) {
                console.error("Error fetching processing task detail:", error);
                return null;
            }
        },
        // 启用查询
        enabled: true,
        // 每10秒查询一次
        refetchInterval: 10000,
        // 当组件失去焦点时继续刷新
        refetchIntervalInBackground: true,
        staleTime: 5000,
    });

    // 计算进度阶段
    const progressPhase = useMemo(() => {
        if (progress < 30) return 'Phase 1';
        if (progress < 60) return 'Phase 2';
        if (progress < 90) return 'Phase 3';
        return 'Final';
    }, [progress]);

    // 计算进度状态描述
    const progressStatus = useMemo(() => {
        if (progress < 30) return 'Initializing process';
        if (progress < 60) return 'Processing content';
        if (progress < 90) return 'Rendering output';
        return 'Finalizing';
    }, [progress]);

    // 处理状态变化和进度计算
    useEffect(() => {
        // 1. 如果没有开始时间，设置开始时间
        if (!startedAt && video.started_at) {
            setStartedAt(new Date(video.started_at));
        } else if (!startedAt) {
            setStartedAt(new Date());
        }

        // 2. 处理来自API的状态更新
        if (taskDetail) {
            // 如果状态发生变化
            if (taskDetail.status !== status) {
                setStatus(taskDetail.status);

                // 如果状态变为completed或failed，通知父组件
                if ((taskDetail.status === "completed" || taskDetail.status === "failed") && onStatusChange) {
                    onStatusChange(video.id, taskDetail.status, taskDetail);
                }
            }

            // 如果API返回了进度，更新进度
            if (taskDetail.progress && taskDetail.progress > progress) {
                setProgress(taskDetail.progress);
            }

            // 如果API返回了预计处理时间，更新预计处理时间
            if (taskDetail.duration_estimate) {
                setEstimatedTime(taskDetail.duration_estimate);
            }
        }

        // 3. 设置定时器计算进度
        if (status === "processing" && startedAt && !intervalRef.current) {
            intervalRef.current = setInterval(() => {
                const now = new Date();
                const elapsed = (now.getTime() - startedAt.getTime()) / 1000;

                // 计算进度
                const estimatedProcessingTime = estimatedTime * 60; // 转换为秒
                let calculatedProgress;

                // 如果已经超过了估计时间，显示99%
                if (elapsed > estimatedProcessingTime) {
                    calculatedProgress = 99;
                } else {
                    // 在估计时间范围内的进度计算
                    calculatedProgress = Math.min(99, (elapsed / estimatedProcessingTime) * 100);
                }

                // 只有当计算的进度大于当前进度时才更新
                if (calculatedProgress > progress) {
                    setProgress(calculatedProgress);
                }
            }, 1000);
        }

        // 4. 如果任务已完成或失败，清除定时器
        if (status !== "processing" && intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }

        // 5. 组件卸载时清除定时器
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        };
    }, [video.id, video.started_at, taskDetail, status, progress, startedAt, estimatedTime, onStatusChange]);

    return (
        <>
            {/* Video preview area */}
            <div className="relative w-full overflow-hidden bg-muted/60 cursor-pointer">
                {/* Status badge */}
                <div
                    className={cn(
                        "absolute top-2 right-2 z-10 px-2.5 py-1 rounded-full text-xs font-medium",
                        "transition-opacity duration-300",
                        getStatusColor(status)
                    )}
                >
                    {getStatusText(status)}
                </div>

                {/* Processing animation and skeleton */}
                <div className="aspect-video">
                    <Skeleton className="w-full h-full" />
                    {/* Display reference image if available */}
                    {video.input_params.refer_img_url && (
                        <div className="absolute inset-0 flex justify-center">
                            <img
                                src={video.input_params.refer_img_url}
                                alt="Reference image"
                                className="max-h-full max-w-full object-contain opacity-60"
                            />
                        </div>
                    )}
                </div>
                <div className="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-b from-background/70 to-background/40 backdrop-blur-sm">
                    {/* 增强动态效果的进度指示器 */}
                    <div className="relative w-16 h-16 flex items-center justify-center">
                        {/* 背景圆环 */}
                        <div className="absolute inset-0 rounded-full border border-primary-foreground/10"></div>

                        {/* 进度指示器 - 使用渐变色弧线 */}
                        <svg className="absolute inset-0 w-full h-full -rotate-90" viewBox="0 0 100 100">
                            <defs>
                                <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" stopColor="white" stopOpacity="1" />
                                    <stop offset="100%" stopColor="white" stopOpacity="0.8" />
                                </linearGradient>

                                {/* 脉冲渐变 - 使用白色 */}
                                <linearGradient id="pulseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stopColor="white" stopOpacity="0.8" />
                                    <stop offset="50%" stopColor="white" stopOpacity="0.3" />
                                    <stop offset="100%" stopColor="white" stopOpacity="0.8" />
                                </linearGradient>

                                {/* 添加发光效果 */}
                                <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
                                    <feGaussianBlur stdDeviation="2" result="blur" />
                                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                                </filter>
                            </defs>

                            {/* 背景轨道 - 极大增强对比度 */}
                            <circle
                                cx="50"
                                cy="50"
                                r="46"
                                fill="none"
                                stroke="rgba(50, 50, 50, 0.5)"
                                strokeWidth="5"
                                strokeDasharray="1,3"
                            />

                            {/* 辅助动态效果 - 渐变光晕 */}
                            <circle
                                cx="50"
                                cy="50"
                                r="46"
                                fill="none"
                                stroke="url(#pulseGradient)"
                                strokeWidth="2"
                                strokeOpacity="0.7"
                                style={{
                                    animation: "pulseOpacity 3s ease-in-out infinite"
                                }}
                            />

                            {/* 主进度圆环 - 极大增强可见度 */}
                            <circle
                                cx="50"
                                cy="50"
                                r="46"
                                fill="none"
                                stroke="url(#progressGradient)"
                                strokeWidth="6"
                                strokeDasharray={`${Math.min(289, (progress * 289) / 100)} 289`}
                                strokeLinecap="round"
                                filter="url(#glow)"
                                style={{
                                    filter: "drop-shadow(0 0 3px white)"
                                }}
                            />

                            {/* 进度圆环边缘高亮 */}
                            <circle
                                cx="50"
                                cy="50"
                                r="46"
                                fill="none"
                                stroke="white"
                                strokeWidth="1"
                                strokeDasharray={`1 ${Math.min(289, (progress * 289) / 100)}`}
                                strokeDashoffset="0"
                                strokeLinecap="round"
                            />
                        </svg>

                        {/* 中心文本 - 不显示具体百分比 */}
                        <div className="z-10 text-xs font-medium text-primary-foreground/80">
                            {progressPhase}
                        </div>
                    </div>

                    {/* 进度状态文本 - 使用更专业的描述 */}
                    <div className="mt-4 font-medium text-primary-foreground">
                        {progressStatus}
                    </div>

                    {/* 动态图标指示器 - 增大尺寸 */}
                    <div className="mt-3 flex justify-center">
                        <div className="flex space-x-2">
                            {[...Array(3)].map((_, i) => (
                                <div
                                    key={i}
                                    className="w-2 h-2 rounded-full bg-primary-foreground/60"
                                    style={{
                                        animation: "pulse 1.5s ease-in-out infinite",
                                        animationDelay: `${i * 0.2}s`
                                    }}
                                ></div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Video info overlay - only show info, no buttons */}
                <div className={cn(
                    "absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent",
                    "transition-opacity duration-300 flex items-end p-3",
                    "hover:opacity-100 opacity-0"
                )}>
                    <div className="text-xs text-white/90 font-medium">
                        {video.input_params.ratio} · {video.input_params.definition || "SD"}
                    </div>
                </div>
            </div>

            {/* Information area */}
            <CardInfoSection video={video} />
        </>
    );
};
