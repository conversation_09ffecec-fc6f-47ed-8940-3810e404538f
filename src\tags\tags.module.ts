import { Module } from '@nestjs/common';
import { TagsController } from './tags.controller';
import { TagsService } from './tags.service';
import { CustomLogger } from 'src/common/services/logger.service';
import { ClientInfoService } from 'src/common/services/client-info.service';
import { GenerationModule } from 'src/generation/generation.module';

@Module({
    imports: [GenerationModule],
    controllers: [TagsController],
    providers: [TagsService, ClientInfoService, CustomLogger],
    exports: [TagsService],
})
export class TagsModule {} 