import {
    Controller,
    Get,
    Post,
    Body,
    Query,
    Param,
    UseGuards,
    Req,
    ParseUUIDPipe,
    ParseIntPipe,
    DefaultValuePipe,
} from '@nestjs/common';
import { TrainService } from './train.service';
import { JwtGuard } from '../common/guards/jwt.guard';
import {
    StartTrainRequestDto,
    TrainPriceRequestDto,
    FinishTrainTaskRequestDto,
    UpdateTrainTaskRequestDto,
} from './dto/train.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('模型训练')
@Controller('train')
export class TrainController {
    constructor(private readonly trainService: TrainService) {}

    /**
     * 获取基础模型列表
     */
    @Get('base_models')
    @ApiOperation({ summary: '获取基础模型列表' })
    @ApiResponse({ status: 200, description: '可用于训练的基础模型列表' })
    async getBaseModels() {
        return this.trainService.getBaseModels();
    }

    /**
     * 计算训练价格
     */
    @Post('price')
    @UseGuards(JwtGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: '计算训练价格' })
    @ApiResponse({ status: 200, description: '训练所需积分' })
    async calculateTrainPrice(
        @Req() req,
        @Body() trainPriceDto: TrainPriceRequestDto,
    ) {
        const userId = req.user.id;
        return this.trainService.calculateTrainPrice(userId, trainPriceDto);
    }

    /**
     * 开始训练
     */
    @Post('start')
    @UseGuards(JwtGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: '开始模型训练' })
    @ApiResponse({ status: 201, description: '创建训练任务成功' })
    async startTraining(
        @Req() req,
        @Body() startTrainDto: StartTrainRequestDto,
    ) {
        const userId = req.user.id;
        return this.trainService.startTraining(userId, startTrainDto);
    }

    /**
     * 获取用户训练任务列表
     */
    @Get('user-tasks')
    @UseGuards(JwtGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: '获取用户训练任务列表' })
    @ApiResponse({ status: 200, description: '用户的训练任务列表' })
    async getUserTrainTasks(
        @Req() req,
        @Query('status') status?: string,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit?: number,
        @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset?: number,
    ) {
        const userId = req.user.id;
        return this.trainService.getUserTrainTasks(userId, status, limit, offset);
    }

    /**
     * 获取训练任务详情
     */
    @Get('task/:taskId')
    @UseGuards(JwtGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: '获取训练任务详情' })
    @ApiResponse({ status: 200, description: '训练任务详情' })
    async getTrainTaskById(
        @Req() req,
        @Param('taskId', ParseUUIDPipe) taskId: string,
    ) {
        const userId = req.user.id;
        return this.trainService.getTrainTaskById(userId, taskId);
    }

    /**
     * 取消训练任务
     */
    @Post('task/cancel/:taskId')
    @UseGuards(JwtGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: '取消训练任务' })
    @ApiResponse({ status: 200, description: '取消训练任务成功' })
    async cancelTrainTask(
        @Req() req,
        @Param('taskId', ParseUUIDPipe) taskId: string,
    ) {
        const userId = req.user.id;
        return this.trainService.cancelTrainTask(userId, taskId);
    }

    /**
     * 获取下一个待处理的训练任务（训练服务调用）
     */
    @Post('pop_task')
    @ApiOperation({ summary: '获取下一个待处理的训练任务' })
    @ApiResponse({ status: 200, description: '下一个待处理任务' })
    async popTrainTask() {
        return this.trainService.popTrainTask();
    }

    /**
     * 完成训练任务（训练服务调用）
     */
    @Post('finish')
    @ApiOperation({ summary: '完成训练任务' })
    @ApiResponse({ status: 200, description: '完成训练任务成功' })
    async finishTrainTask(
        @Body() finishDto: FinishTrainTaskRequestDto,
    ) {
        return this.trainService.finishTrainTask(finishDto);
    }

    /**
     * 更新训练任务状态（训练服务调用）
     */
    @Post('update_task')
    @ApiOperation({ summary: '更新训练任务状态' })
    @ApiResponse({ status: 200, description: '更新训练任务成功' })
    async updateTrainTask(
        @Body() updateDto: UpdateTrainTaskRequestDto,
    ) {
        return this.trainService.updateTrainTask(updateDto);
    }
} 