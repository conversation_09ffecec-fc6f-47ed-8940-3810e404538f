-- 创建用户积分余额表
CREATE TABLE IF NOT EXISTS user_credit_balances (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    credits INT NOT NULL DEFAULT 0,
    version INT NOT NULL DEFAULT 1, -- 用于乐观锁控制
    last_transaction_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_credit_balances_credits ON user_credit_balances(credits);

-- 为 user_credit_balances 表添加 RLS 策略
ALTER TABLE user_credit_balances ENABLE ROW LEVEL SECURITY;

-- 创建允许用户查看自己的积分余额的策略
CREATE POLICY user_read_own_credit_balance ON user_credit_balances
    FOR SELECT
    USING (auth.uid() = user_id);

-- 创建允许服务角色执行所有操作的策略
CREATE POLICY service_role_all_credit_balances ON user_credit_balances
    FOR ALL
    USING (auth.role() = 'service_role')
    WITH CHECK (auth.role() = 'service_role');

-- 创建允许管理员执行所有操作的策略
CREATE POLICY admin_all_credit_balances ON user_credit_balances
    FOR ALL
    USING (auth.role() = 'admin')
    WITH CHECK (auth.role() = 'admin');

-- 创建一个触发器函数，用于在交易记录创建或更新时自动更新余额
CREATE OR REPLACE FUNCTION update_user_credit_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- 只处理已完成的交易
    IF NEW.status = 'completed' THEN
        -- 尝试更新现有用户余额
        UPDATE user_credit_balances
        SET 
            credits = credits + NEW.amount,
            version = version + 1,
            last_transaction_id = NEW.id,
            updated_at = NOW()
        WHERE user_id = NEW.user_id;
        
        -- 如果没有找到记录，则创建一个新记录
        IF NOT FOUND THEN
            INSERT INTO user_credit_balances (user_id, credits, last_transaction_id)
            VALUES (NEW.user_id, NEW.amount, NEW.id);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建一个触发器，在交易表插入记录时触发
CREATE TRIGGER after_credit_transaction_insert
AFTER INSERT ON credit_transactions
FOR EACH ROW
EXECUTE FUNCTION update_user_credit_balance();

-- 创建一个触发器，在交易表更新记录状态为已完成时触发
CREATE TRIGGER after_credit_transaction_update
AFTER UPDATE ON credit_transactions
FOR EACH ROW
WHEN (OLD.status != 'completed' AND NEW.status = 'completed')
EXECUTE FUNCTION update_user_credit_balance(); 