import {
    Controller,
    Get,
    Post,
    Put,
    Body,
    Param,
    Query,
    BadRequestException,
    ParseUUIDPipe,
    Delete,
    UseGuards,
    Request
} from '@nestjs/common';
import { ModelsService } from './models.service';
import {
    CreateModelDto,
    UpdateModelDto,
    ModelDto,
    ModelListResponseDto,
} from './dto/model.dto';
import { Roles } from 'src/common/decorators/roles.decorator';
import { RolesGuard } from 'src/common/guards/roles.guard';

@Controller('admin/models')
@UseGuards(RolesGuard)
@Roles('admin')
export class ModelsAdminController {
    constructor(private readonly modelsService: ModelsService) {}

    /**
     * 管理员获取所有视频模型
     * @param page 页码
     * @param limit 每页数量
     */
    @Get()
    async getAllModels(
        @Query('page') page?: number,
        @Query('limit') limit?: number,
    ): Promise<ModelListResponseDto> {
        return this.modelsService.getAllModels(
            page ? parseInt(page.toString(), 10) : 1,
            limit ? parseInt(limit.toString(), 10) : 10
        );
    }

    /**
     * 检查模型是否已存在
     * @param storage_path 存储路径
     */
    @Get('check-exists')
    async checkModelExists(
        @Query('storage_path') storagePath: string,
    ): Promise<{ exists: boolean }> {
        if (!storagePath) {
            throw new BadRequestException('storage_path参数不能为空');
        }

        const exists = await this.modelsService.checkModelExists(storagePath);
        return { exists };
    }

    /**
     * 管理员获取单个模型详情
     * @param modelId 模型ID
     */
    @Get(':modelId')
    async getModelById(
        @Param('modelId', ParseUUIDPipe) modelId: string,
    ): Promise<ModelDto> {
        return this.modelsService.getModelById(modelId);
    }

    /**
     * 管理员创建新模型
     * @param createModelDto 创建模型参数
     */
    @Post()
    async createModel(
        @Body() createModelDto: CreateModelDto,
        @Request() req
    ): Promise<ModelDto> {
        // 使用请求中的用户ID
        const userId = req.user.id;
        // 确保数据合法性
        if (!createModelDto.name || !createModelDto.source || !createModelDto.model_type || !createModelDto.storage_path) {
            throw new BadRequestException('缺少必要的模型信息');
        }

        return this.modelsService.createModel({
            ...createModelDto,
            user_id: userId
        });
    }

    /**
     * 管理员更新模型
     * @param modelId 模型ID
     * @param updateModelDto 更新模型参数
     */
    @Put(':modelId')
    async updateModel(
        @Param('modelId', ParseUUIDPipe) modelId: string,
        @Body() updateModelDto: UpdateModelDto,
    ): Promise<ModelDto> {
        return this.modelsService.updateModel(modelId, updateModelDto);
    }

    /**
     * 管理员删除模型
     * @param modelId 模型ID
     */
    @Delete(':modelId')
    async deleteModel(
        @Param('modelId', ParseUUIDPipe) modelId: string,
    ): Promise<{ success: boolean, message: string }> {
        await this.modelsService.deleteModel(modelId);
        return { success: true, message: '模型删除成功' };
    }
} 