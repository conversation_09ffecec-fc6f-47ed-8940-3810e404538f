import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CreditsService } from '../credits/credits.service';
import { CreditTransactionType } from '../credits/constant';

@Injectable()
export class MembershipCreditsScheduler {
    private readonly logger = new Logger(MembershipCreditsScheduler.name);
    
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        @Inject(forwardRef(() => CreditsService))
        private readonly creditsService: CreditsService,
    ) {}
    
    /**
     * 每天凌晨1点检查会员状态并刷新积分
     * 这个任务会检查所有活跃会员，并为符合条件的会员发放月度积分
     */
    @Cron(CronExpression.EVERY_DAY_AT_1AM)
    async refreshMembershipCredits() {
        this.logger.log('开始执行会员积分刷新任务');
        
        try {
            // 获取当前日期
            const now = new Date();
            const currentDay = now.getDate();
            
            // 如果不是每月1号，则不执行月度刷新
            if (currentDay !== 1) {
                this.logger.log('今天不是月初，不执行月度积分刷新');
                return;
            }
            
            // 获取所有活跃会员
            const { data: activeMembers, error } = await this.supabase
                .from('user_memberships')
                .select('user_id, plan_id')
                .eq('is_active', true)
                .gte('expires_at', now.toISOString());
                
            if (error) {
                this.logger.error(`获取活跃会员失败: ${error.message}`, error);
                return;
            }
            
            this.logger.log(`找到 ${activeMembers.length} 个活跃会员需要刷新积分`);
            
            // 为每个会员发放月度积分
            let successCount = 0;
            let failCount = 0;
            
            for (const member of activeMembers) {
                try {
                    // 检查本月是否已经发放过积分
                    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
                    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                    
                    const { data: existingGrants, error: checkError } = await this.supabase
                        .from('credit_transactions')
                        .select('id')
                        .eq('user_id', member.user_id)
                        .eq('type', CreditTransactionType.MEMBERSHIP_MONTHLY)
                        .gte('created_at', firstDayOfMonth.toISOString())
                        .lte('created_at', lastDayOfMonth.toISOString())
                        .limit(1);
                        
                    if (checkError) {
                        this.logger.error(`检查用户 ${member.user_id} 积分发放记录失败: ${checkError.message}`);
                        failCount++;
                        continue;
                    }
                    
                    // 如果已经发放过，则跳过
                    if (existingGrants && existingGrants.length > 0) {
                        this.logger.log(`用户 ${member.user_id} 本月已经获得过会员月度积分，跳过`);
                        continue;
                    }
                    
                    // 发放月度积分
                    const result = await this.creditsService.grantMembershipMonthlyCredits(
                        member.user_id,
                        member.plan_id
                    );
                    
                    if (result && result.success) {
                        successCount++;
                        this.logger.log(`成功为用户 ${member.user_id} 发放月度积分 ${result.amount} 点`);
                    } else {
                        failCount++;
                        this.logger.warn(`为用户 ${member.user_id} 发放月度积分失败: ${result?.message || '未知原因'}`);
                    }
                } catch (memberError) {
                    failCount++;
                    this.logger.error(`处理用户 ${member.user_id} 月度积分时出错: ${memberError.message}`, memberError);
                }
            }
            
            this.logger.log(`会员月度积分刷新完成，成功: ${successCount}，失败: ${failCount}`);
        } catch (error) {
            this.logger.error(`执行会员积分刷新任务失败: ${error.message}`, error);
        }
    }
    
    /**
     * 每天凌晨2点检查会员过期情况
     * 这个任务会检查所有即将过期的会员，并更新其状态
     */
    @Cron(CronExpression.EVERY_DAY_AT_2AM)
    async checkExpiredMemberships() {
        this.logger.log('开始检查过期会员');
        
        try {
            const now = new Date();
            
            // 查找已过期但仍标记为活跃的会员
            const { data: expiredMembers, error } = await this.supabase
                .from('user_memberships')
                .select('id, user_id, plan_id, expires_at')
                .eq('is_active', true)
                .lt('expires_at', now.toISOString());
                
            if (error) {
                this.logger.error(`查询过期会员失败: ${error.message}`, error);
                return;
            }
            
            this.logger.log(`找到 ${expiredMembers.length} 个已过期会员需要更新状态`);
            
            // 更新过期会员状态
            for (const member of expiredMembers) {
                try {
                    const { error: updateError } = await this.supabase
                        .from('user_memberships')
                        .update({
                            is_active: false,
                            status: 'expired',
                            updated_at: now.toISOString()
                        })
                        .eq('id', member.id);
                        
                    if (updateError) {
                        this.logger.error(`更新过期会员 ${member.id} 状态失败: ${updateError.message}`);
                    } else {
                        this.logger.log(`已将用户 ${member.user_id} 的会员状态更新为过期`);
                    }
                } catch (memberError) {
                    this.logger.error(`处理过期会员 ${member.id} 时出错: ${memberError.message}`, memberError);
                }
            }
            
            this.logger.log('过期会员检查完成');
        } catch (error) {
            this.logger.error(`检查过期会员任务失败: ${error.message}`, error);
        }
    }
}
