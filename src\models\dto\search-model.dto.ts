import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON>rray, <PERSON><PERSON><PERSON>ber, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class SearchModelDto {
    @IsString()
    @IsOptional()
    query?: string;

    @IsArray()
    @IsOptional()
    @Type(() => String)
    model_types?: string[];

    @IsArray()
    @IsOptional()
    @Type(() => String)
    features?: string[];

    @IsNumber()
    @IsOptional()
    @Min(1)
    @Type(() => Number)
    page?: number;

    @IsNumber()
    @IsOptional()
    @Min(1)
    @Type(() => Number)
    limit?: number;
}

export class SearchModelResponseDto {
    models: any[];
    total: number;
}
