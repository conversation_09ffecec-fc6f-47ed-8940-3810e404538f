"use client";

type JsonLdProps = {
    data: Record<string, any>;
};

/**
 * JsonLd组件 - 用于向页面添加结构化数据标记
 * @see https://developers.google.com/search/docs/advanced/structured-data/intro-structured-data
 * 
 * 使用示例:
 * <JsonLd 
 *   data={{
 *     "@context": "https://schema.org",
 *     "@type": "Article",
 *     "headline": "文章标题",
 *     "author": {
 *       "@type": "Person",
 *       "name": "作者名"
 *     }
 *   }} 
 * />
 */
export function JsonLd({ data }: JsonLdProps) {
    return (
        <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
        />
    );
} 