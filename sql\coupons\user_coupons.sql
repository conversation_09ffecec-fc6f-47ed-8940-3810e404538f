-- 创建用户优惠券表
CREATE TABLE IF NOT EXISTS public.user_coupons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    coupon_type VARCHAR(50) NOT NULL DEFAULT 'first_month_discount', -- 优惠券类型
    discount_percentage INTEGER NOT NULL DEFAULT 30, -- 折扣百分比 (30 = 7折)
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- 状态: active, used, expired
    claimed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(), -- 领取时间
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '24 hours'), -- 过期时间
    used_at TIMESTAMP WITH TIME ZONE NULL, -- 使用时间
    payment_id UUID NULL, -- 关联的支付记录ID
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- 约束
    CONSTRAINT user_coupons_user_id_coupon_type_unique UNIQUE(user_id, coupon_type),
    CONSTRAINT user_coupons_status_check CHECK (status IN ('active', 'used', 'expired')),
    CONSTRAINT user_coupons_discount_check CHECK (discount_percentage > 0 AND discount_percentage <= 100)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_coupons_user_id ON user_coupons(user_id);
CREATE INDEX IF NOT EXISTS idx_user_coupons_status ON user_coupons(status);
CREATE INDEX IF NOT EXISTS idx_user_coupons_expires_at ON user_coupons(expires_at);

-- 为 user_coupons 表添加 RLS 策略
ALTER TABLE user_coupons ENABLE ROW LEVEL SECURITY;

-- 用户只能查看和操作自己的优惠券
CREATE POLICY "Users can view their own coupons" ON user_coupons
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own coupons" ON user_coupons
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own coupons" ON user_coupons
    FOR UPDATE USING (auth.uid() = user_id);

-- 创建自动更新过期优惠券状态的函数
CREATE OR REPLACE FUNCTION update_expired_coupons()
RETURNS void AS $$
BEGIN
    UPDATE user_coupons 
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'active' AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务（需要pg_cron扩展）
-- SELECT cron.schedule('update-expired-coupons', '*/5 * * * *', 'SELECT update_expired_coupons();');
