import { Suspense } from "react";
import { ControlPanel } from "./components/ControlPanel";
import { HistoryPanel } from "./components/history-pannel";
import RemixTaskLoader from "./components/RemixLoader";
import MobileNavigator from "./components/MobileNavigator";
import ResponsiveLayout from "./components/ResponsiveLayout";

export default function VideoGenerationPage() {
    return (
        <>
            <Suspense fallback={null}>
                <RemixTaskLoader />
            </Suspense>
            
            <ResponsiveLayout 
                mobileView={
                    <Suspense fallback={<div className="p-4">Loading mobile view...</div>}>
                        <MobileNavigator />
                    </Suspense>
                }
                desktopView={
                    <div className="flex h-full overflow-hidden">
                        {/* 左侧控制面板 - 包裹在Suspense中以处理useSearchParams */}
                        <Suspense fallback={<div className="w-1/2 p-4">Loading control panel...</div>}>
                            <ControlPanel />
                        </Suspense>

                        {/* 历史面板占据空间，限制为最多50%宽度 */}
                        <HistoryPanel />
                    </div>
                }
            />
        </>
    )
}

