"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { postApi } from "@/lib/api"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/toast"
import type { VideoTask } from "@/types/video-task"
import type { ChangeEvent } from "react"
import { Share, Image, PenLine } from "lucide-react"

interface PublishDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    task: VideoTask
    onSuccess?: () => void
}

export function PublishDialog({ open, onOpenChange, task, onSuccess }: PublishDialogProps) {
    const [title, setTitle] = useState("")
    const [description, setDescription] = useState("")
    const [isPublishing, setIsPublishing] = useState(false)
    const [publishProgress, setPublishProgress] = useState(0)
    const toast = useToast()

    // Reset form when dialog opens
    useEffect(() => {
        if (open) {
            // Extract a title from the prompt as initial value
            if (task.input_params.prompt) {
                // Limit to first 30 characters
                setTitle(task.input_params.prompt.slice(0, 30))
            }
        }
    }, [open, task]);

    // Simulate progress during publishing
    useEffect(() => {
        if (isPublishing) {
            const interval = setInterval(() => {
                setPublishProgress(prev => {
                    if (prev >= 90) {
                        clearInterval(interval)
                        return prev
                    }
                    return prev + 10
                })
            }, 300)

            return () => clearInterval(interval)
        } else {
            setPublishProgress(0)
        }
    }, [isPublishing])

    // Handle publishing to community
    const handlePublish = async () => {
        if (!title.trim()) {
            toast.error("Publication Failed", "Please enter a title for your video");
            return;
        }

        try {
            setIsPublishing(true);

            // Show processing toast
            toast.info("Processing", "Preparing to publish your video...");

            // Prepare publish parameters
            const publishParams = {
                taskId: task.id,
                title: title.trim(),
                description: description.trim()
            };

            // Send request
            const response = await postApi.createPost(publishParams);

            // Complete progress
            setPublishProgress(100);

            // Show success toast
            toast.success(
                "Successfully Published",
                "Your video has been shared to the community"
            );

            // Reset form and close dialog after delay
            setTimeout(() => {
                setTitle("");
                setDescription("");
                setIsPublishing(false);

                // Close dialog
                onOpenChange(false);

                // Call success callback
                onSuccess?.();
            }, 1000);

        } catch (error) {
            console.error("Publish failed:", error);
            toast.error("Publication Failed", "Please check your network connection or try again later");
            setIsPublishing(false);
        }
    }

    // Render publishing state
    const renderPublishingState = () => (
        <div className="flex flex-col items-center justify-center py-6">
            <div className="w-16 h-16 flex items-center justify-center mb-4">
                <div className="absolute w-16 h-16 border-4 border-primary/20 rounded-full"></div>
                <div className="absolute w-16 h-16 border-4 border-transparent border-t-primary rounded-full animate-spin"></div>
                <Share className="h-6 w-6 text-primary-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">Publishing Your Video</h3>
            <p className="text-sm text-muted-foreground mb-6">Please wait while we process your video...</p>

            {/* Progress bar */}
            <div className="w-full max-w-xs mb-2">
                <Progress
                    value={publishProgress}
                    variant={publishProgress === 100 ? "success" : "primary"}
                />
            </div>
            <p className="text-xs text-muted-foreground">
                {publishProgress < 100
                    ? `Publishing ${publishProgress}%`
                    : "Complete! Redirecting..."}
            </p>
        </div>
    );

    return (
        <Dialog open={open} onOpenChange={(newOpen) => !isPublishing && onOpenChange(newOpen)}>
            <DialogContent className="sm:max-w-[500px] overflow-hidden">
                <DialogHeader>
                    <DialogTitle className="flex items-center text-xl">
                        <Share className="mr-2 h-5 w-5 text-primary-foreground" />
                        Share to Community
                    </DialogTitle>
                    <DialogDescription>
                        Share your amazing video with the community to get likes and comments
                    </DialogDescription>
                </DialogHeader>

                {/* Publishing state or form content */}
                {isPublishing ? (
                    renderPublishingState()
                ) : (
                    <div className="space-y-4 py-4">
                        {/* Preview */}
                        <div className="aspect-video bg-muted rounded-lg overflow-hidden">
                            {task.output_result?.video_url ? (
                                <video
                                    src={task.output_result.video_url}
                                    className="w-full h-full object-cover"
                                    controls
                                    autoPlay
                                    loop
                                    muted
                                />
                            ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                    <p className="text-muted-foreground">Video preview unavailable</p>
                                </div>
                            )}
                        </div>

                        {/* Title */}
                        <div className="space-y-2">
                            <Label htmlFor="title" className="text-sm font-medium flex items-center">
                                <PenLine className="mr-2 h-4 w-4" />
                                Video Title <span className="text-red-500 ml-1">*</span>
                            </Label>
                            <Input
                                id="title"
                                value={title}
                                onChange={(e: ChangeEvent<HTMLInputElement>) => setTitle(e.target.value)}
                                placeholder="Give your creation an engaging title"
                                className="transition-all focus:ring-2 focus:ring-primary/50"
                                disabled={isPublishing}
                                maxLength={100}
                            />
                            <p className="text-xs text-muted-foreground">
                                A good title helps attract more viewers ({title.length}/100)
                            </p>
                        </div>

                        {/* Description */}
                        <div className="space-y-2">
                            <Label htmlFor="description" className="text-sm font-medium flex items-center">
                                <PenLine className="mr-2 h-4 w-4" />
                                Description
                            </Label>
                            <Textarea
                                id="description"
                                value={description}
                                onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setDescription(e.target.value)}
                                placeholder="Add some description to help others understand your creation (optional)"
                                rows={4}
                                className="resize-none transition-all focus:ring-2 focus:ring-primary/50"
                                disabled={isPublishing}
                                maxLength={200}
                            />
                            <p className="text-xs text-muted-foreground">
                                Briefly describe your video content ({description.length}/200)
                            </p>
                        </div>

                        {/* Buttons */}
                        <div className="flex justify-between pt-4">
                            <Button
                                variant="outline"
                                onClick={() => onOpenChange(false)}
                                disabled={isPublishing}
                                className="transition-all"
                            >
                                Cancel
                            </Button>

                            <Button
                                onClick={handlePublish}
                                disabled={isPublishing || !title.trim()}
                                className="gap-2 transition-all hover:shadow-md"
                            >
                                <Share className="h-4 w-4" />
                                Publish to Community
                            </Button>
                        </div>
                    </div>
                )}
            </DialogContent>
        </Dialog>
    )
} 