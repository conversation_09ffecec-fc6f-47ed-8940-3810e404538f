/*
部署说明:
1. 将此SQL函数部署到Supabase项目中
2. 通过Supabase SQL编辑器执行此脚本
3. 或者通过命令行执行: psql -h [host] -d [database] -U [user] -f create_post_tag_function.sql
4. 成功部署后，系统会自动使用高性能的RPC调用

性能说明:
- 这个函数将多次数据库查询合并为单次操作
- 直接使用tag_id查询，减少一次标签名称查找
- 预编译执行计划减少了查询分析时间
- 适合处理大量数据的高并发场景
- 支持异常处理和边界条件
*/

-- 创建按标签ID获取帖子的函数
CREATE OR REPLACE FUNCTION get_posts_by_tag_id(
    p_tag_id UUID,
    p_limit INT DEFAULT 10,
    p_offset INT DEFAULT 0
) 
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    -- 验证标签ID是否存在
    IF NOT EXISTS (SELECT 1 FROM tags WHERE id = p_tag_id) THEN
        RETURN json_build_object(
            'posts', '[]'::json,
            'total', 0
        );
    END IF;
    
    -- 获取符合条件的帖子及总数
    WITH tag_videos AS (
        SELECT video_id
        FROM video_tags
        WHERE tag_id = p_tag_id
    ),
    filtered_posts AS (
        SELECT
            p.*,
            COUNT(*) OVER() AS total_count,
            row_to_json(v.*) AS videos,
            row_to_json(up.*) AS user_profiles  
        FROM
            posts p
        JOIN 
            tag_videos tv ON p.video_id = tv.video_id
        LEFT JOIN
            videos v ON p.video_id = v.id
        LEFT JOIN
            user_profiles up ON p.user_id = up.user_id
        WHERE
            p.visibility = 'public'
        ORDER BY
            p.created_at DESC
        LIMIT p_limit OFFSET p_offset
    )
    SELECT 
        json_build_object(
            'posts', COALESCE(json_agg(p.*), '[]'::json),
            'total', CASE WHEN COUNT(*) > 0 THEN MAX(p.total_count) ELSE 0 END
        ) INTO result
    FROM 
        filtered_posts p;

    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 为向后兼容性保留基于tag_name的函数
CREATE OR REPLACE FUNCTION get_posts_by_tag_name(
    p_tag_name TEXT,
    p_limit INT DEFAULT 10,
    p_offset INT DEFAULT 0
) 
RETURNS JSON AS $$
DECLARE
    v_tag_id UUID;
BEGIN
    -- 获取标签ID
    SELECT id INTO v_tag_id FROM tags WHERE name = p_tag_name;
    
    -- 如果标签不存在，返回空结果
    IF v_tag_id IS NULL THEN
        RETURN json_build_object(
            'posts', '[]'::json,
            'total', 0
        );
    END IF;
    
    -- 调用基于ID的函数
    RETURN get_posts_by_tag_id(v_tag_id, p_limit, p_offset);
END;
$$ LANGUAGE plpgsql;

-- 创建所需的索引来优化函数性能
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
CREATE INDEX IF NOT EXISTS idx_video_tags_tag_id ON video_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_video_tags_video_id ON video_tags(video_id);
CREATE INDEX IF NOT EXISTS idx_posts_video_id ON posts(video_id);
CREATE INDEX IF NOT EXISTS idx_posts_visibility_created_at ON posts(visibility, created_at DESC);

-- 授予权限给api角色调用此函数
GRANT EXECUTE ON FUNCTION get_posts_by_tag_id(UUID, INT, INT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_posts_by_tag_id(UUID, INT, INT) TO anon;
GRANT EXECUTE ON FUNCTION get_posts_by_tag_name(TEXT, INT, INT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_posts_by_tag_name(TEXT, INT, INT) TO anon; 