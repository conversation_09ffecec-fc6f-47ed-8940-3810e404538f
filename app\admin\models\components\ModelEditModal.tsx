'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { adminModelApi } from '@/lib/api/admin-model';
import { toast } from 'sonner';
import type { Model } from '@/types/model';

const modelEditSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    description: z.string().optional(),
    price: z.number().int().min(0, 'Price must be a non-negative integer').optional(),
    weight: z.number().int().min(0, 'Weight must be a non-negative integer').optional(),
    cover_img: z.string().url('Must be a valid URL').optional().or(z.literal('')),
    cover_video: z.string().url('Must be a valid URL').optional().or(z.literal('')),
    is_public: z.boolean(),
    nsfw_level: z.number().min(0).max(5),
    category: z.string().optional(),
    group: z.string().optional(),
});

type ModelEditFormData = z.infer<typeof modelEditSchema>;

interface ModelEditModalProps {
    model: Model | null;
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
}

export function ModelEditModal({ model, isOpen, onClose, onSuccess }: ModelEditModalProps) {
    const [isLoading, setIsLoading] = useState(false);

    const form = useForm<ModelEditFormData>({
        resolver: zodResolver(modelEditSchema),
        defaultValues: {
            name: '',
            description: '',
            price: 0,
            weight: 1,
            cover_img: '',
            cover_video: '',
            is_public: true,
            nsfw_level: 1,
            category: '',
            group: '',
        },
    });

    // 当模型数据变化时更新表单
    useEffect(() => {
        if (model) {
            form.reset({
                name: model.name,
                description: model.description || '',
                price: (model as any).price || 0,
                weight: (model as any).weight || 1,
                cover_img: model.cover_img || '',
                cover_video: model.cover_video || '',
                is_public: model.is_public,
                nsfw_level: model.nsfw_level,
                category: (model as any).category || '',
                group: (model as any).group || '',
            });
        }
    }, [model, form]);

    const onSubmit = async (data: ModelEditFormData) => {
        if (!model) return;

        setIsLoading(true);
        try {
            await adminModelApi.updateModel(model.id, {
                ...data,
                cover_img: data.cover_img || undefined,
                cover_video: data.cover_video || undefined,
            });

            onSuccess();
        } catch (error) {
            console.error('Failed to update model:', error);
            toast.error('Failed to update model');
        } finally {
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        form.reset();
        onClose();
    };

    if (!model) return null;

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>Edit Model: {model.name}</DialogTitle>
                </DialogHeader>

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        {/* Basic Information */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Basic Information</h3>

                            <div className="space-y-2">
                                <FormLabel>Model Info</FormLabel>
                                <div className="flex flex-wrap gap-2">
                                    <Badge variant="outline">
                                        Type: {model.model_type}
                                    </Badge>
                                    <Badge variant="outline">
                                        Source: {model.source}
                                    </Badge>
                                    <Badge variant="outline">
                                        Storage: {model.storage_path}
                                    </Badge>
                                </div>
                            </div>

                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Name</FormLabel>
                                        <FormControl>
                                            <Input {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Description</FormLabel>
                                        <FormControl>
                                            <Textarea {...field} rows={3} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="category"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Category</FormLabel>
                                            <FormControl>
                                                <Input {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        {/* Media */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Media</h3>

                            <FormField
                                control={form.control}
                                name="cover_img"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Cover Image URL</FormLabel>
                                        <FormControl>
                                            <Input {...field} placeholder="https://example.com/image.jpg" />
                                        </FormControl>
                                        <FormMessage />
                                        {field.value && (
                                            <div className="mt-2">
                                                <img
                                                    src={field.value}
                                                    alt="Cover preview"
                                                    className="w-32 h-32 object-cover rounded-md border"
                                                    onError={(e) => {
                                                        e.currentTarget.style.display = 'none';
                                                    }}
                                                />
                                            </div>
                                        )}
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="cover_video"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Cover Video URL</FormLabel>
                                        <FormControl>
                                            <Input {...field} placeholder="https://example.com/video.mp4" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        {/* Settings */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Settings</h3>

                            <div className="grid grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="price"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Price (Credits)</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    min="0"
                                                    step="1"
                                                    {...field}
                                                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="weight"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Weight</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    min="0"
                                                    step="1"
                                                    {...field}
                                                    onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <FormField
                                control={form.control}
                                name="nsfw_level"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>NSFW Level</FormLabel>
                                        <Select
                                            value={field.value.toString()}
                                            onValueChange={(value) => field.onChange(parseInt(value))}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem value="0">0 - Safe</SelectItem>
                                                <SelectItem value="1">1 - Low</SelectItem>
                                                <SelectItem value="2">2 - Medium</SelectItem>
                                                <SelectItem value="3">3 - High</SelectItem>
                                                <SelectItem value="4">4 - Explicit</SelectItem>
                                                <SelectItem value="5">5 - Extreme</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="is_public"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Public Model</FormLabel>
                                            <div className="text-sm text-muted-foreground">
                                                Make this model available to all users
                                            </div>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={handleClose}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isLoading}>
                                {isLoading ? 'Saving...' : 'Save Changes'}
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog >
    );
}
