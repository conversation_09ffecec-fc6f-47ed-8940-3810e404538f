import { ArrowLeft } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { useRouter } from "next/navigation";

interface TopBarProps {
    isPublishing: boolean;
    handlePublish: () => void;
}

export const TopBar = ({ isPublishing, handlePublish }: TopBarProps) => {
    const router = useRouter();

    return (
        <div className="flex justify-between items-center py-4 border-b border-border">
            <button onClick={() => router.back()} className="p-2 hover:bg-muted rounded-full">
                <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="space-x-2">
                <Button onClick={handlePublish} disabled={isPublishing} className="px-12 font-bold text-md">
                    {isPublishing ? "发布中..." : "发布"}
                </Button>
            </div>
        </div>
    );
}; 