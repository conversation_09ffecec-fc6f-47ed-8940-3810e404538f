import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';

// Cloudflare R2 配置
const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID || '';
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID || '';
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY || '';
const R2_BLOG_BUCKET_NAME = process.env.R2_BLOG_BUCKET_NAME || 'blog_img';
const R2_BLOG_PUBLIC_URL = process.env.R2_BLOG_PUBLIC_URL || 'https://blog-img.reelmind.ai';

// 设置 R2 客户端
const s3Client = new S3Client({
    region: 'auto',
    endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    credentials: {
        accessKeyId: R2_ACCESS_KEY_ID,
        secretAccessKey: R2_SECRET_ACCESS_KEY,
    },
});

export async function POST(request: NextRequest) {
    try {
        const formData = await request.formData();
        const file = formData.get('file') as File;

        if (!file) {
            return NextResponse.json({ error: '没有找到文件' }, { status: 400 });
        }

        // 确认是图片
        if (!file.type.startsWith('image/')) {
            return NextResponse.json({ error: '请上传图片文件' }, { status: 400 });
        }

        // 将文件转换为 ArrayBuffer
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // 生成唯一文件名
        const fileExt = file.name.split('.').pop();
        const fileName = `${uuidv4()}.${fileExt}`;
        const filePath = `blog/${fileName}`;

        // 上传到 Cloudflare R2
        try {
            console.log(`开始上传到 ${R2_BLOG_BUCKET_NAME} 桶...`);

            // 创建上传命令
            const command = new PutObjectCommand({
                Bucket: R2_BLOG_BUCKET_NAME,
                Key: filePath,
                Body: buffer,
                ContentType: file.type,
                ACL: 'public-read',
            });

            // 执行上传
            await s3Client.send(command);

            // 构建公共URL
            const publicUrl = `${R2_BLOG_PUBLIC_URL}/${filePath}`;
            console.log(`上传成功，文件URL: ${publicUrl}`);

            return NextResponse.json({ url: publicUrl });
        } catch (r2Error) {
            console.error('上传到Cloudflare R2失败:', r2Error);
            return NextResponse.json({ error: '上传图片失败' }, { status: 500 });
        }
    } catch (error) {
        console.error('处理上传请求时出错:', error);
        return NextResponse.json({ error: '服务器错误' }, { status: 500 });
    }
} 