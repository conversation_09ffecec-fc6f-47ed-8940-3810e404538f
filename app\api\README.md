# API 设计文档

本项目使用 Next.js 的 App Router 架构设计 API 端点，完全遵循 Next.js 13+ 的最新范式。

## 目录结构

```
app/api/
├── upload/                 # 上传相关API
│   ├── presign/            # 预签名URL API
│   │   └── route.ts        # 生成预签名URL的路由
│   └── tools.ts            # 上传相关通用工具函数
└── [其他API目录]/          # 其他功能模块API
```

## API 端点

### 视频上传 API

**端点**: `/api/upload/presign`
**方法**: `POST`
**功能**: 生成 Cloudflare R2 存储的预签名 URL，用于直接上传视频文件
**参数**:

- `filename`: 文件名
- `fileType`: 文件 MIME 类型
- `fileSize`: 文件大小(bytes)

**响应示例**:

```json
{
  "success": true,
  "data": {
    "url": "https://example.r2.cloudflarestorage.com/...",
    "key": "user/timestamp-id.mp4",
    "fields": {},
    "publicUrl": "https://public-url.com/user/timestamp-id.mp4",
    "expires": "2023-07-01T12:00:00.000Z"
  }
}
```

**错误响应**:

```json
{
  "error": "错误类型",
  "message": "用户友好的错误消息"
}
```

## 身份验证

- 使用 Supabase Auth 进行身份验证，API 会自动从 Cookie 中获取会话信息
- 所有 API 请求都需要用户身份认证，无例外

## 设计原则

1. **路由隔离**: 每个 API 端点都在自己的目录中定义
2. **命名导出**: 使用 HTTP 方法名称(POST, GET 等)作为导出函数
3. **共享工具**: 公共工具函数放在共享文件中
4. **类型安全**: 使用 TypeScript 确保类型安全
5. **错误处理**: 统一的错误响应格式
6. **结构化响应**: API 返回统一的结构，包含 success 标志和 data/error 字段

## 安全考虑

1. **强制身份验证**：所有上传请求必须经过身份验证，无任何例外，包括开发环境
2. **用户隔离**：每个用户的文件存储在独立的路径下，基于用户 ID
3. **文件名安全**：文件名使用 UUID 和时间戳生成，避免冲突和预测
4. **类型验证**：严格验证文件类型，只允许视频文件
5. **大小限制**：验证文件大小，防止滥用存储资源
6. **有时效签名**：预签名 URL 有时间限制，增强安全性
7. **请求验证**：使用 zod 库进行请求验证，确保数据完整性
