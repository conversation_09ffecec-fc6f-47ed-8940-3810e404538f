"use client"

import { useState, useEffect, ReactNode, createContext, useContext } from "react"
import { createPortal } from "react-dom"
import { CheckCircle, AlertCircle, XCircle, Info, X } from "lucide-react"
import { cn } from "@/lib/utils"

// Toast类型和配置
export type ToastType = "success" | "error" | "warning" | "info"

export interface ToastProps {
    id: string
    type: ToastType
    title: string
    message?: string
    duration?: number
    onClose: (id: string) => void
}

// Toast组件上下文
interface ToastContextType {
    toasts: ToastProps[]
    addToast: (toast: Omit<ToastProps, "id" | "onClose">) => void
    removeToast: (id: string) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

// 单个Toast组件
function ToastItem({ id, type, title, message, duration = 5000, onClose }: ToastProps) {
    // 自动关闭计时器
    useEffect(() => {
        const timer = setTimeout(() => {
            onClose(id)
        }, duration)

        return () => clearTimeout(timer)
    }, [id, duration, onClose])

    // 根据类型获取图标
    const getIcon = () => {
        switch (type) {
            case "success":
                return <CheckCircle className="h-5 w-5 text-green-500" />
            case "error":
                return <XCircle className="h-5 w-5 text-red-500" />
            case "warning":
                return <AlertCircle className="h-5 w-5 text-amber-500" />
            case "info":
                return <Info className="h-5 w-5 text-blue-500" />
        }
    }

    // 根据类型获取背景色
    const getBgColor = () => {
        switch (type) {
            case "success":
                return "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
            case "error":
                return "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
            case "warning":
                return "bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800"
            case "info":
                return "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"
        }
    }

    return (
        <div
            className={cn(
                "flex items-start w-full max-w-sm p-4 mb-3 rounded-lg shadow-lg border",
                "animate-in slide-in-from-right duration-300",
                "backdrop-blur-sm bg-background/95",
                getBgColor()
            )}
        >
            <div className="flex-shrink-0 mt-0.5">
                {getIcon()}
            </div>
            <div className="ml-3 flex-1">
                <p className="text-sm font-medium">{title}</p>
                {message && <p className="mt-1 text-sm text-muted-foreground">{message}</p>}
            </div>
            <button
                type="button"
                className="ml-2 inline-flex items-center justify-center rounded-md p-1 text-muted-foreground hover:text-foreground focus:outline-none"
                onClick={() => onClose(id)}
            >
                <X className="h-4 w-4" />
            </button>
        </div>
    )
}

// Toast容器组件
function ToastContainer() {
    const context = useContext(ToastContext)

    if (!context) {
        throw new Error("useToast must be used within a ToastProvider")
    }

    const { toasts, removeToast } = context
    const [isMounted, setIsMounted] = useState(false)

    useEffect(() => {
        setIsMounted(true)
        return () => setIsMounted(false)
    }, [])

    if (!isMounted) return null

    return createPortal(
        <div className="fixed top-4 right-4 z-50 flex flex-col items-end">
            {toasts.map((toast) => (
                <ToastItem key={toast.id} {...toast} onClose={removeToast} />
            ))}
        </div>,
        document.body
    )
}

// Toast提供者组件
export function ToastProvider({ children }: { children: ReactNode }) {
    const [toasts, setToasts] = useState<ToastProps[]>([])

    const addToast = (toast: Omit<ToastProps, "id" | "onClose">) => {
        const id = Math.random().toString(36).substring(2, 9)
        setToasts((prev) => [...prev, { ...toast, id, onClose: removeToast }])
    }

    const removeToast = (id: string) => {
        setToasts((prev) => prev.filter((toast) => toast.id !== id))
    }

    return (
        <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
            {children}
            <ToastContainer />
        </ToastContext.Provider>
    )
}

// 使用Toast的Hook
export function useToast() {
    const context = useContext(ToastContext)

    if (!context) {
        throw new Error("useToast must be used within a ToastProvider")
    }

    return {
        toast: context.addToast,
        success: (title: string, message?: string, duration?: number) =>
            context.addToast({ type: "success", title, message, duration }),
        error: (title: string, message?: string, duration?: number) =>
            context.addToast({ type: "error", title, message, duration }),
        warning: (title: string, message?: string, duration?: number) =>
            context.addToast({ type: "warning", title, message, duration }),
        info: (title: string, message?: string, duration?: number) =>
            context.addToast({ type: "info", title, message, duration }),
    }
} 