import React from 'react';
import Link from 'next/link';
import { Metadata } from 'next';
import { getBlogPosts, getBlogPostsCount } from '../../../lib/blog';
import { formatDate } from '../../../lib/utils';
import DeletePostButton from './components/DeletePostButton';

export const metadata: Metadata = {
    title: 'Blog Management - Reelmind Admin',
    description: 'Manage your blog posts',
};

export default async function BlogAdminPage({
    searchParams,
}: {
    searchParams: { page?: string },
}) {
    // searchParams 是异步对象，需要先获取其属性值
    const params = await Promise.resolve(searchParams);
    const pageParam = params.page || '1';
    const page = parseInt(pageParam);
    const { posts } = await getBlogPosts(page, 20);
    const total = await getBlogPostsCount();
    const hasMore = total > (page * 20);

    // 计算分页信息
    const totalPages = Math.ceil(total / 20);
    const showPagination = totalPages > 1;

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-8">
                <h1 className="text-2xl font-bold">Blog Management</h1>
                <Link
                    href="/admin/blog/new"
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                >
                    New Post
                </Link>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Title
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Published Date
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                            {posts.map((post) => (
                                <tr key={post.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                                            {post.title}
                                        </div>
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            {post.slug}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {formatDate(post.created_at)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div className="flex space-x-2">
                                            <Link
                                                href={`/admin/blog/edit/${post.id}`}
                                                className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                                            >
                                                Edit
                                            </Link>
                                            <Link
                                                href={`/blog/${post.slug}`}
                                                target="_blank"
                                                className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                            >
                                                View
                                            </Link>
                                            <DeletePostButton postId={post.id} postTitle={post.title} />
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Pagination */}
            {showPagination && (
                <div className="flex justify-center mt-8">
                    <nav className="flex items-center gap-1">
                        {page > 1 && (
                            <Link
                                href={{ pathname: '/admin/blog', query: { page: page - 1 } }}
                                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
                            >
                                Previous
                            </Link>
                        )}

                        {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                            // Calculate page numbers to show
                            let pageNum;
                            if (totalPages <= 5) {
                                pageNum = i + 1;
                            } else if (page <= 3) {
                                pageNum = i + 1;
                            } else if (page >= totalPages - 2) {
                                pageNum = totalPages - 4 + i;
                            } else {
                                pageNum = page - 2 + i;
                            }

                            // If this page is current page, style it differently
                            const isCurrentPage = pageNum === page;

                            return (
                                <Link
                                    key={i}
                                    href={{ pathname: '/admin/blog', query: { page: pageNum } }}
                                    className={`px-4 py-2 text-sm font-medium rounded-md ${isCurrentPage
                                        ? 'bg-primary-600 text-white'
                                        : 'text-gray-700 dark:text-gray-200 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
                                        }`}
                                >
                                    {pageNum}
                                </Link>
                            );
                        })}

                        {hasMore && (
                            <Link
                                href={{ pathname: '/admin/blog', query: { page: page + 1 } }}
                                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
                            >
                                Next
                            </Link>
                        )}
                    </nav>
                </div>
            )}
        </div>
    );
} 