'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { ContentCard } from '@/components/content/content-card';
import type { PostItem } from '@/types/posts';
import { motion } from 'framer-motion';

// 加载状态的骨架屏组件
const TopFeedSkeleton = () => {
    return (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
            {Array(4).fill(0).map((_, i) => (
                <div
                    key={i}
                    className="aspect-video bg-gray-200 dark:bg-gray-800 rounded-lg animate-pulse"
                />
            ))}
        </div>
    );
};

// 用于获取随机视频的客户端组件
const RandomVideosFeed = () => {
    const [posts, setPosts] = useState<PostItem[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchRandomPosts = async () => {
            try {
                setIsLoading(true);
                const response = await fetch('/api/posts/random?limit=4');
                const data = await response.json();

                if (data.posts) {
                    setPosts(data.posts);
                }
            } catch (error) {
                console.error('Failed to fetch random posts:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchRandomPosts();
    }, []);

    if (isLoading) {
        return <TopFeedSkeleton />;
    }

    return (
        <div className="space-y-4">
            <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 min-[0px]:max-md:flex min-[0px]:max-md:overflow-x-auto min-[0px]:max-md:snap-x min-[0px]:max-md:snap-mandatory min-[0px]:max-md:pb-4">
                {posts.map((post) => (
                    <div key={post.id} className="h-full min-[0px]:max-md:min-w-[75%] min-[0px]:max-md:snap-start min-[0px]:max-md:scroll-ml-4">
                        <ContentCard item={post} />
                    </div>
                ))}
            </div>
        </div>
    );
};

// 查看更多按钮组件
const ViewMoreButton = () => {
    return (
        <div className="ml-auto flex items-center justify-between">
            <Link href="/" passHref className='w-full'>
                <motion.div
                    className="w-full inline-flex items-center justify-center gap-2 px-5 py-2.5 mt-4 text-sm font-medium rounded-full bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700 transition-all duration-300"
                    whileHover={{
                        scale: 1.01,
                    }}
                    whileTap={{ scale: 0.98 }}
                >
                    <span className="font-medium">Explore more fun</span>
                    <ArrowRight size={16} />
                </motion.div>
            </Link>
        </div>
    );
};

// 主组件
const TopFeed = () => {
    return (
        <motion.div
            className="mb-8 p-2 mx-9"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
        >
            <RandomVideosFeed />
            <div className="md:block hidden">
                <ViewMoreButton />
            </div>
        </motion.div>
    );
};

export default TopFeed;
