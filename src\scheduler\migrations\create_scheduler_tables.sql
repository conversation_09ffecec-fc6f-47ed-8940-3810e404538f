-- 创建调度器锁表
CREATE TABLE IF NOT EXISTS scheduler_locks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lock_key TEXT NOT NULL UNIQUE,
    locked_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_scheduler_locks_lock_key ON scheduler_locks (lock_key);
CREATE INDEX IF NOT EXISTS idx_scheduler_locks_expires_at ON scheduler_locks (expires_at);

-- 注意：会员积分发放失败记录改用全局错误日志表 error_audit_logs

-- 权限设置
ALTER TABLE scheduler_locks ENABLE ROW LEVEL SECURITY;

-- 创建管理员策略
CREATE POLICY "Admin can manage scheduler_locks" ON scheduler_locks
    FOR ALL USING (
        auth.jwt() ? (is_admin_from_jwt())
    );
    
-- 服务器角色策略 (允许服务账户访问)
CREATE POLICY "Service role can manage scheduler_locks" ON scheduler_locks
    FOR ALL USING (
        (auth.role() = 'service_role')
    ); 