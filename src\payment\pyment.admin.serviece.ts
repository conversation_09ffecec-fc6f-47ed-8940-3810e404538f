import { Injectable, OnModuleInit, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { CustomLogger } from '../common/services/logger.service';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { Inject } from '@nestjs/common';

@Injectable()
export class PaymentAdminService implements OnModuleInit {
    private stripe: Stripe;

    constructor(
        private readonly configService: ConfigService,
        private readonly logger: CustomLogger,
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient
    ) {
        this.logger.setContext(PaymentAdminService.name);
        const apiVersion = this.configService.get<string>('STRIPE_API_VERSION');
        const secretKey = this.configService.get<string>('STRIPE_SECRET_KEY');
        if (!apiVersion) {
            throw new Error('STRIPE_API_VERSION 未配置');
        }
        if (!secretKey) {
            throw new Error('STRIPE_SECRET_KEY 未配置');
        }
        this.stripe = new Stripe(secretKey, {
            apiVersion: apiVersion as any
        });
    }

    /**
     * 初始化时，同步每个会员计划到Stripe
     */
    async onModuleInit() {
        try {
            await this.syncMembershipPlansToStripe();
            this.logger.log('已同步会员计划到Stripe');
        } catch (error) {
            this.logger.error('同步会员计划到Stripe失败', error);
        }
    }

    /**
     * 同步会员计划到Stripe，为每个计划创建产品和价格
     */
    async syncMembershipPlansToStripe() {
        try {
            // 获取所有活跃的会员计划
            const { data: plans, error } = await this.supabase
                .from('membership_plans')
                .select('*')
                .eq('is_active', true)
                .neq('level', 0); // 排除免费计划

            if (error) throw error;

            // 为每个计划创建或更新Stripe产品和价格
            for (const plan of plans) {
                // 创建月度价格
                const monthlyPriceId = await this.createOrRetrievePrice(
                    plan.plan_name,
                    Math.round(plan.monthly_price * 100), // 转换为美分
                    'month'
                );

                // 创建年度价格
                const yearlyPriceId = await this.createOrRetrievePrice(
                    plan.plan_name,
                    Math.round(plan.yearly_price * 100), // 转换为美分
                    'year'
                );

                // 更新会员计划表中的价格ID字段
                const { error: updateError } = await this.supabase
                    .from('membership_plans')
                    .update({
                        stripe_price_id_month: monthlyPriceId,
                        stripe_price_id_year: yearlyPriceId
                    })
                    .eq('id', plan.id);

                if (updateError) {
                    this.logger.error(`更新会员计划Stripe价格ID失败: ${plan.id}`, updateError);
                }
            }
        } catch (error) {
            this.logger.error('同步会员计划到Stripe失败', error);
            throw error;
        }
    }

    /**
     * 创建或获取Stripe价格对象
     * @param planName 计划名称
     * @param amount 金额（美分）
     * @param interval 订阅周期 ('month' 或 'year')
     * @returns Stripe价格对象ID
     */
    async createOrRetrievePrice(
        planName: string,
        amount: number,
        interval: 'month' | 'year'
    ): Promise<string> {
        try {
            // 先查找是否已存在对应的产品
            const products = await this.stripe.products.list({
                active: true,
                limit: 100,
            });

            const existingProduct = products.data.find(
                product => product.name === `Reelmind ${planName}`
            );

            let productId: string;

            // 如果产品不存在，创建新产品
            if (!existingProduct) {
                const product = await this.stripe.products.create({
                    name: `Reelmind ${planName}`,
                    description: `Reelmind ${planName} 会员订阅`,
                });
                productId = product.id;
            } else {
                productId = existingProduct.id;
            }

            // 查找是否已存在对应的价格
            const prices = await this.stripe.prices.list({
                product: productId,
                active: true,
                limit: 100,
            });

            const existingPrice = prices.data.find(
                price =>
                    price.unit_amount === amount &&
                    price.recurring?.interval === interval
            );

            // 如果价格已存在，直接返回
            if (existingPrice) {
                return existingPrice.id;
            }

            // 创建新的价格
            const price = await this.stripe.prices.create({
                product: productId,
                unit_amount: amount,
                currency: 'usd',
                recurring: {
                    interval: interval,
                },
                nickname: `Reelmind ${planName} ${interval}`,
            });

            return price.id;
        } catch (error) {
            this.logger.error(`创建Stripe价格失败: ${error.message}`, error);
            throw new BadRequestException('创建订阅价格失败');
        }
    }
}
