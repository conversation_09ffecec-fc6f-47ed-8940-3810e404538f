# Post模块接口说明文档

## 简介

本文档详细说明了灵感社区的Post模块接口，面向前端Next.js开发人员。Post模块支持用户发布视频、点赞、评论等功能，包括使用生成的视频和用户上传的视频。

## 认证说明

大部分接口需要用户认证，认证通过JWT方式实现：

- 需要认证的接口：在请求头中添加 `Authorization: Bearer <token>`
- 部分查询接口支持未登录访问，但返回的数据会受限

## 接口列表

### 1. 创建帖子

**接口描述**：发布一条新的帖子，可以使用生成的视频任务或上传新视频

**请求方法**：POST

**请求URL**：`/posts`

**认证要求**：是

**请求体参数**：

```typescript
{
  taskId?: string;        // 可选，已生成的视频任务ID
  videoUrl?: string;      // 可选，视频URL（上传的视频）
  title: string;          // 必填，帖子标题
  description?: string;   // 可选，帖子描述
  thumbnailUrl?: string;  // 可选，缩略图URL
}
```

**说明**：

- `taskId` 和 `videoUrl` 至少提供一个
- 如果提供 `taskId`，系统会验证该任务是否属于当前用户，并使用任务生成的视频
- 如果任务有缩略图，会自动使用任务的缩略图

**返回参数**：

```typescript
{
  id: string;             // 帖子ID
  title: string;          // 标题
  description?: string;   // 描述
  videoUrl: string;       // 视频URL
  thumbnailUrl?: string;  // 缩略图URL
  userId: string;         // 发布者ID
  username: string;       // 发布者用户名
  userAvatar?: string;    // 发布者头像
  likeCount: number;      // 点赞数
  commentCount: number;   // 评论数
  createdAt: Date;        // 创建时间
  isLiked: boolean;       // 当前用户是否点赞
  taskId?: string;        // 关联的任务ID
}
```

**示例请求**：

```javascript
const createPost = async (post) => {
  const response = await fetch('/posts', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      title: '我的第一个视频',
      description: '这是我生成的很酷的视频',
      taskId: '550e8400-e29b-41d4-a716-446655440000',
    }),
  });
  return await response.json();
};
```

### 2. 获取帖子Feed流

**接口描述**：获取公开帖子的Feed流，按创建时间倒序排列

**请求方法**：POST

**请求URL**：`/posts/feed`

**认证要求**：否（可选，有认证时会返回当前用户点赞状态）

**请求体参数**：

```typescript
{
  limit?: number;         // 可选，每页数量，默认10
  offset?: number;        // 可选，分页偏移量，默认0
  // 可能包含其他过滤条件
}
```

**返回参数**：

```typescript
{
  posts: [
    {
      id: string;             // 帖子ID
      title: string;          // 标题
      description?: string;   // 描述
      videoUrl: string;       // 视频URL
      thumbnailUrl?: string;  // 缩略图URL
      userId: string;         // 发布者ID
      username: string;       // 发布者用户名
      userAvatar?: string;    // 发布者头像
      likeCount: number;      // 点赞数
      commentCount: number;   // 评论数
      createdAt: Date;        // 创建时间
      isLiked: boolean;       // 当前用户是否点赞
      taskId?: string;        // 关联的任务ID
    },
    // ...更多帖子
  ],
  total: number               // 总帖子数
}
```

**示例请求**：

```javascript
const getPostFeed = async (limit = 10, offset = 0) => {
  const response = await fetch(`/posts/feed`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`, // 可选
    },
    body: JSON.stringify({
      limit,
      offset,
    }),
  });
  return await response.json();
};
```

### 3. 获取用户帖子列表

**接口描述**：获取指定用户发布的帖子列表

**请求方法**：GET

**请求URL**：`/posts/user/:userId`

**认证要求**：是

**路径参数**：

- `userId`：必填，用户ID

**查询参数**：

- `limit`：可选，每页数量，默认10
- `offset`：可选，分页偏移量，默认0

**说明**：

- 如果查看的是当前登录用户的帖子，会返回所有帖子（包括私密帖子）
- 如果查看其他用户的帖子，只返回公开帖子

**返回参数**：

```typescript
{
  posts: [
    {
      // 与Feed流相同的帖子结构
    },
    // ...更多帖子
  ],
  total: number               // 总帖子数
}
```

**示例请求**：

```javascript
const getUserPosts = async (userId, limit = 10, offset = 0) => {
  const response = await fetch(
    `/posts/user/${userId}?limit=${limit}&offset=${offset}`,
    {
      headers: {
        Authorization: `Bearer ${token}`, // 必须
      },
    },
  );
  return await response.json();
};
```

### 4. 获取帖子详情

**接口描述**：获取单个帖子的详细信息

**请求方法**：GET

**请求URL**：`/posts/:post_id`

**认证要求**：否（可选，有认证时会返回当前用户点赞状态）

**路径参数**：

- `post_id`：必填，帖子ID

**说明**：

- 如果帖子是私密的，只有帖子作者可以查看

**返回参数**：

```typescript
{
  id: string;             // 帖子ID
  title: string;          // 标题
  description?: string;   // 描述
  videoUrl: string;       // 视频URL
  thumbnailUrl?: string;  // 缩略图URL
  userId: string;         // 发布者ID
  username: string;       // 发布者用户名
  userAvatar?: string;    // 发布者头像
  likeCount: number;      // 点赞数
  commentCount: number;   // 评论数
  createdAt: Date;        // 创建时间
  isLiked: boolean;       // 当前用户是否点赞
  taskId?: string;        // 关联的任务ID
}
```

**示例请求**：

```javascript
const getPostDetail = async (postId) => {
  const response = await fetch(`/posts/${postId}`, {
    headers: {
      Authorization: `Bearer ${token}`, // 可选
    },
  });
  return await response.json();
};
```

### 5. 点赞/取消点赞帖子

**接口描述**：对帖子进行点赞或取消点赞

**请求方法**：POST

**请求URL**：`/posts/like`

**认证要求**：是

**请求体参数**：

```typescript
{
  post_id: string; // 必填，帖子ID
  is_like: boolean; // 必填，true表示点赞，false表示取消点赞
}
```

**说明**：

- 用户只能对同一帖子点赞一次
- 如果已经点赞，再次点赞不会报错
- 如果未点赞，取消点赞不会报错

**返回参数**：

```typescript
{
  // 帖子详情，与获取帖子详情接口返回相同
  // isLiked 值根据操作结果更新
}
```

**示例请求**：

```javascript
// 点赞帖子
const likePost = async (postId) => {
  const response = await fetch(`/posts/like`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      post_id: postId,
      is_like: true,
    }),
  });
  return await response.json();
};

// 取消点赞
const unlikePost = async (postId) => {
  const response = await fetch(`/posts/like`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      post_id: postId,
      is_like: false,
    }),
  });
  return await response.json();
};
```

### 6. 创建评论

**接口描述**：对帖子或模型发表评论，也可以回复其他评论

**请求方法**：POST

**请求URL**：`/posts/comment`

**认证要求**：是

**请求体参数**：

```typescript
{
  content: string;         // 必填，评论内容
  parentId?: string;       // 可选，父评论ID（如果是回复评论）
  targetType: 'post' | 'model'; // 必填，评论目标类型
  targetId: string;        // 必填，评论目标ID（帖子ID或模型ID）
}
```

**返回参数**：

```typescript
{
  id: string;              // 评论ID
  content: string;         // 评论内容
  userId: string;          // 评论者ID
  username: string;        // 评论者用户名
  userAvatar?: string;     // 评论者头像
  parentId?: string;       // 父评论ID
  targetType: string;      // 目标类型
  targetId: string;        // 目标ID
  createdAt: Date;         // 创建时间
}
```

**示例请求**：

```javascript
const createComment = async (comment) => {
  const response = await fetch('/posts/comment', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      content: '很棒的视频！',
      targetType: 'post',
      targetId: '550e8400-e29b-41d4-a716-446655440000',
    }),
  });
  return await response.json();
};
```

### 7. 获取帖子评论

**接口描述**：获取帖子的评论列表，支持分页和获取回复

**请求方法**：GET

**请求URL**：`/posts/:post_id/comments`

**认证要求**：否

**路径参数**：

- `post_id`：必填，帖子ID

**查询参数**：

- `parentId`：可选，父评论ID，获取特定评论的回复
- `limit`：可选，每页数量，默认10
- `offset`：可选，分页偏移量，默认0

**说明**：

- 如果不提供`parentId`，返回顶级评论
- 如果提供`parentId`，返回指定评论的回复

**返回参数**：

```typescript
{
  comments: [
    {
      id: string;              // 评论ID
      content: string;         // 评论内容
      userId: string;          // 评论者ID
      username: string;        // 评论者用户名
      userAvatar?: string;     // 评论者头像
      parentId?: string;       // 父评论ID
      targetType: string;      // 目标类型
      targetId: string;        // 目标ID
      createdAt: Date;         // 创建时间
      childrenCount?: number;  // 回复数量（只有顶级评论有此字段）
    },
    // ...更多评论
  ],
  total: number                // 总评论数
}
```

**示例请求**：

```javascript
// 获取帖子的顶级评论
const getPostComments = async (postId, limit = 10, offset = 0) => {
  const response = await fetch(
    `/posts/${postId}/comments?limit=${limit}&offset=${offset}`,
  );
  return await response.json();
};

// 获取评论的回复
const getCommentReplies = async (postId, parentId, limit = 10, offset = 0) => {
  const response = await fetch(
    `/posts/${postId}/comments?parentId=${parentId}&limit=${limit}&offset=${offset}`,
  );
  return await response.json();
};
```

### 8. 获取模型评论

**接口描述**：获取模型的评论列表，支持分页和获取回复

**请求方法**：GET

**请求URL**：`/posts/model/:model_id/comments`

**认证要求**：否

**路径参数**：

- `model_id`：必填，模型ID

**查询参数**：

- `parentId`：可选，父评论ID，获取特定评论的回复
- `limit`：可选，每页数量，默认10
- `offset`：可选，分页偏移量，默认0

**说明**：

- 功能与获取帖子评论类似，但目标是模型而非帖子

**返回参数**：

```typescript
{
  comments: [
    {
      // 与帖子评论结构相同
    },
    // ...更多评论
  ],
  total: number                // 总评论数
}
```

**示例请求**：

```javascript
// 获取模型的顶级评论
const getModelComments = async (modelId, limit = 10, offset = 0) => {
  const response = await fetch(
    `/posts/model/${modelId}/comments?limit=${limit}&offset=${offset}`,
  );
  return await response.json();
};
```

## 异常处理

所有接口在发生错误时会返回标准HTTP错误码和详细错误信息：

```json
{
  "statusCode": 400, // HTTP状态码
  "message": "错误信息", // 错误描述
  "error": "错误类型" // 错误类型
}
```

常见错误码：

- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证或认证无效
- `403 Forbidden`: 无权操作
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误

## 实现注意事项

1. **文件上传**:

   - 视频上传不在此接口中处理，需先通过文件上传接口获取URL后再创建帖子
   - 前端需确保视频格式和大小符合要求

2. **认证处理**:

   - 使用JWT认证，确保在需要认证的接口中包含有效的token
   - token通常从登录响应中获取并存储在localStorage或Cookie中

3. **优化建议**:
   - 使用SWR或React Query进行数据获取和缓存
   - 实现无限滚动以优化Feed流体验
   - 使用乐观UI更新提高点赞/取消点赞的响应速度

## 示例Next.js接口封装

```typescript
// api/posts.ts
import axios from 'axios';

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
});

// 添加请求拦截器添加token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const postApi = {
  // 创建帖子
  createPost: (postData) => api.post('/posts', postData),

  // 获取Feed流
  getFeed: (limit = 10, offset = 0) =>
    api.post('/posts/feed', { limit, offset }),

  // 获取用户帖子
  getUserPosts: (userId, limit = 10, offset = 0) =>
    api.get(`/posts/user/${userId}?limit=${limit}&offset=${offset}`),

  // 获取帖子详情
  getPost: (postId) => api.get(`/posts/${postId}`),

  // 点赞帖子
  likePost: (postId) =>
    api.post('/posts/like', { post_id: postId, is_like: true }),

  // 取消点赞
  unlikePost: (postId) =>
    api.post('/posts/like', { post_id: postId, is_like: false }),

  // 创建评论
  createComment: (commentData) => api.post('/posts/comment', commentData),

  // 获取帖子评论
  getPostComments: (postId, params = {}) =>
    api.get(`/posts/${postId}/comments`, { params }),

  // 获取评论回复
  getCommentReplies: (postId, parentId, limit = 10, offset = 0) =>
    api.get(
      `/posts/${postId}/comments?parentId=${parentId}&limit=${limit}&offset=${offset}`,
    ),

  // 获取模型评论
  getModelComments: (modelId, params = {}) =>
    api.get(`/posts/model/${modelId}/comments`, { params }),
};
```
