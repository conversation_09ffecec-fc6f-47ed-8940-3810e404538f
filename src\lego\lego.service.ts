import { Injectable, Inject, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { GenPicRequestDto, TickProgressDto } from './dto/gen-pic.dto';
import { PicTaskDto, FinishTaskDto, PicTaskResponseDto, PopTaskDto } from './dto/pic-task.dto';
import { PicResolution, PicTaskStatus } from '../common/constants/pic';
import { CustomLogger } from '../common/services/logger.service';
import { MembershipService } from '../membership/membership.service';
import { CreditsService } from '../credits/credits.service';
import { CreditTransactionType } from '../credits/constant';
import { v4 as uuidv4 } from 'uuid';
import { ErrorAuditService, ErrorSeverity } from '../common/services/error-audit.service';
import { QueueInfoResponseDto } from './dto/queue-info.dto';
import { EnhancePromptRequestDto, EnhancePromptResponseDto } from './dto/enhance-prompt.dto';
import { ConfigService } from '@nestjs/config';
import { OpenAI } from 'openai';
import { IFalGenPicWebhookBody, SystemPromptPic } from './constants';
import { FalAiService } from '../generation/services/fal-ai.service';

@Injectable()
export class LegoService {
    private readonly deepseekApiKey: string;
    private readonly deepseekBaseUrl: string;
    private readonly openaiClient: OpenAI;

    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
        private readonly membershipService: MembershipService,
        private readonly creditsService: CreditsService,
        private readonly errorAuditService: ErrorAuditService,
        private readonly configService: ConfigService,
        private readonly falAiService: FalAiService,
    ) {
        this.logger.setContext(LegoService.name);
        this.deepseekApiKey = this.configService.get<string>('DEEPSEEK_API_KEY');
        this.deepseekBaseUrl = 'https://api.deepseek.com';
        this.openaiClient = new OpenAI({
            apiKey: this.deepseekApiKey,
            baseURL: this.deepseekBaseUrl,
        });
    }

    /**
     * 计算图片生成所需的积分
     * @param resolution 图片分辨率
     * @returns 所需积分数量
     */
    private calculateRequiredCredits(resolution: PicResolution): number {
        return 0; // free
    }

    /**
     * 消费用户积分用于图片生成
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param resolution 图片分辨率
     * @returns 消费结果
     */
    private async consumeCreditsForPicGeneration(
        userId: string,
        taskId: string,
        resolution: PicResolution = PicResolution.LOW
    ): Promise<any> {
        try {
            const creditsAmount = this.calculateRequiredCredits(resolution);
            const transactionType = CreditTransactionType.PIC_GENERATION;

            const description = `generate ${resolution} picture`;

            // 使用taskId作为paymentId（幂等性键）确保不会重复扣费
            const result = await this.creditsService.consumeCredits({
                userId,
                type: transactionType,
                amount: creditsAmount,
                description,
                paymentId: taskId
            });

            this.logger.log(`用户${userId}成功消费${creditsAmount}积分用于图片生成，任务ID：${taskId}`);
            return result;
        } catch (error) {
            this.logger.error(`消费积分失败: ${error.message}`, error.stack);

            // 记录到错误审计系统
            await this.errorAuditService.logCreditError(
                userId,
                `消费积分失败: ${error.message}`,
                error,
                {
                    taskId,
                    resolution,
                    operation: 'consumeCredits'
                },
                ErrorSeverity.HIGH
            );

            throw error;
        }
    }

    /**
     * 退还用户积分（生成失败或取消时）
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param resolution 图片分辨率
     * @param reason 退款原因
     * @returns 退款结果
     */
    private async refundCreditsForPicGeneration(
        userId: string,
        taskId: string,
        resolution: PicResolution = PicResolution.LOW,
        reason: string = 'picture generation failed'
    ): Promise<any> {
        try {
            const creditsAmount = this.calculateRequiredCredits(resolution);

            // 创建一个退款交易记录
            const refundId = uuidv4(); // 生成唯一的退款ID
            const result = await this.creditsService.createCreditTransaction({
                userId,
                type: CreditTransactionType.REFUND, // 使用退款类型进行退款
                amount: creditsAmount,
                description: 'refund for picture generation',
                paymentId: refundId,
                referenceId: taskId // 关联到原始任务ID
            });

            this.logger.log(`用户${userId}成功退还${creditsAmount}积分，任务ID：${taskId}，原因：${reason}`);
            return result;
        } catch (error) {
            this.logger.error(`退还积分失败: ${error.message}`, error.stack);

            // 记录到错误审计系统 - 这是一个严重错误，因为用户可能无法获得退款
            const errorId = await this.errorAuditService.logRefundError(
                userId,
                taskId,
                `退还积分失败: ${error.message}`,
                error,
                {
                    resolution,
                    reason,
                    creditsAmount: this.calculateRequiredCredits(resolution),
                    operation: 'refundCredits'
                },
                ErrorSeverity.CRITICAL // 退款失败是严重问题
            );

            this.logger.warn(`已记录退款失败到错误审计系统，错误ID: ${errorId}，请运营人员尽快处理`);

            throw error;
        }
    }

    /**
     * 创建图片生成任务
     * @param userId 用户ID
     * @param genPicDto 图片生成参数
     * @returns 创建的任务信息
     */
    async createPicGenerationTask(
        userId: string,
        genPicDto: GenPicRequestDto,
        priority?: number,
    ): Promise<PicTaskResponseDto> {
        try {
            // 获取模型名称，默认为UNO
            const model = genPicDto.model || 'UNO';

            // 检查FLUX模型使用限制（仅对非会员用户）
            if (this.isFluxModel(model)) {
                const canUseFlux = await this.checkFluxModelUsageLimit(userId);
                if (!canUseFlux) {
                    throw new ForbiddenException('Non-member users can only use FLUX models twice. Please upgrade to membership for unlimited access.');
                }
            }

            // 生成任务ID
            const taskId = uuidv4();

            // 先扣除积分（如果需要）
            const creditsAmount = this.calculateRequiredCreditsForModel(model);

            if (creditsAmount > 0) {
                try {
                    await this.creditsService.consumeCredits({
                        userId,
                        type: CreditTransactionType.PIC_GENERATION,
                        amount: creditsAmount,
                        description: `generate picture using ${model} model`,
                        paymentId: taskId
                    });
                } catch (creditError) {
                    // 特别处理积分不足的错误
                    if (creditError instanceof BadRequestException &&
                        creditError.message.includes('积分余额不足')) {
                        throw new ForbiddenException('Insufficient credits');
                    }
                    throw creditError;
                }
            }

            // 获取用户会员优先级
            const genPriority = priority || await this.membershipService.getUserMembershipPriority(userId);

            // 如果是FLUX系列模型，调用fal.ai
            if (model.startsWith('FLUX')) {
                try {
                    const falAiResponse = await this.falAiService.generateImage(model, genPicDto);

                    // 创建图片生成任务记录
                    const { data, error } = await this.supabase
                        .from('pic_gen_tasks')
                        .insert({
                            id: taskId,
                            user_id: userId,
                            status: PicTaskStatus.PROCESSING, // 直接设为处理中
                            progress: 0,
                            input_params: genPicDto,
                            model: model,
                            created_at: new Date(),
                            started_at: new Date(),
                            priority: genPriority,
                            request_id: falAiResponse.request_id,
                        })
                        .select()
                        .single();

                    if (error) {
                        // 任务创建失败，退还积分
                        if (creditsAmount > 0) {
                            await this.refundCreditsForPicGeneration(
                                userId,
                                taskId,
                                PicResolution.LOW,
                                '创建任务失败，自动退款'
                            );
                        }
                        throw new BadRequestException(`创建图片生成任务失败: ${error.message}`);
                    }

                    return {
                        task_id: data.id,
                        task: data,
                        status: PicTaskStatus.PENDING,
                        estimated_wait_time: 60, // 默认估计等待时间（秒）
                    };
                } catch (falAiError) {
                    // fal.ai调用失败，退还积分
                    if (creditsAmount > 0) {
                        await this.refundCreditsForPicGeneration(
                            userId,
                            taskId,
                            PicResolution.LOW,
                            'GEN Image:fal.ai调用失败，自动退款'
                        );
                    }
                    throw new BadRequestException(`调用外部服务失败: ${falAiError.message}`);
                }
            } else {
                // UNO模型，创建普通任务
                const { data, error } = await this.supabase
                    .from('pic_gen_tasks')
                    .insert({
                        id: taskId,
                        user_id: userId,
                        status: PicTaskStatus.PENDING,
                        progress: 0,
                        input_params: genPicDto,
                        model: model,
                        created_at: new Date(),
                        priority: genPriority,
                    })
                    .select()
                    .single();

                if (error) {
                    throw new BadRequestException(`创建图片生成任务失败: ${error.message}`);
                }

                return {
                    task_id: data.id,
                    task: data,
                    status: PicTaskStatus.PENDING,
                    estimated_wait_time: 60, // 默认估计等待时间（秒）
                };
            }
        } catch (error) {
            if (error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error; // 已经处理的错误，直接抛出
            }
            this.logger.error(`创建图片生成任务失败: ${error.message}`, error.stack);
            throw new BadRequestException('创建图片生成任务失败，请稍后重试');
        }
    }

    /**
     * 获取下一个待处理任务
     * @param options 任务获取选项
     * @param options.has_refer_img 是否需要有参考图片的任务
     * @param options.is_user_task 是否获取用户任务，true表示获取用户任务(priority >= 0)，false表示获取系统任务(priority < 0)
     * @returns 待处理的任务，如果没有则返回null
     */
    async popNextPendingTask({
        has_refer_img = false,
        is_user_task = true
    }: PopTaskDto): Promise<PicTaskDto | null> {
        try {
            // 事务获取并更新任务状态
            const { data: result, error } = await this.supabase.rpc('get_next_pending_pic_task', {
                has_refer_img,
                is_user_task
            });

            if (error) {
                this.logger.error('获取待处理任务失败', error);
                throw new BadRequestException('获取待处理任务失败');
            }

            const data = result[0];

            if (!data) {
                return null;
            }

            return data as PicTaskDto;
        } catch (error) {
            this.logger.error(`获取待处理任务时发生错误: ${error.message}`, error.stack);
            return null;
        }
    }

    /**
     * 完成图片生成任务
     * @param finishTaskDto 任务完成信息
     * @returns 更新后的任务
     */
    async finishPicGenerationTask(finishTaskDto: FinishTaskDto): Promise<PicTaskDto> {
        const { task_id, output_result, storage_path, status, error_log } = finishTaskDto;

        try {
            // 获取当前任务信息
            const { data: existingTask, error: fetchError } = await this.supabase
                .from('pic_gen_tasks')
                .select('*')
                .eq('id', task_id)
                .single();

            if (fetchError || !existingTask) {
                throw new NotFoundException(`任务不存在: ${task_id}`);
            }

            // 检查任务是否已经处于终态（已完成、已失败或已取消）
            const finalStatuses = [PicTaskStatus.COMPLETED, PicTaskStatus.FAILED, PicTaskStatus.CANCELLED];
            if (finalStatuses.includes(existingTask.status as PicTaskStatus)) {
                this.logger.warn(`尝试更新已处于终态的任务: ${task_id}, 当前状态: ${existingTask.status}`);
                return existingTask as PicTaskDto;
            }

            // 更新任务状态
            const updateData: any = {
                status,
                progress: status === PicTaskStatus.COMPLETED ? 100 : existingTask.progress,
                updated_at: new Date(),
                completed_at: new Date(),
                storage_path,
                output_result
            };

            if (error_log) {
                updateData.error_log = error_log;
            }

            const { data: updatedTask, error: updateError } = await this.supabase
                .from('pic_gen_tasks')
                .update(updateData)
                .eq('id', task_id)
                .select()
                .single();

            if (updateError) {
                throw new BadRequestException(`更新任务状态失败: ${updateError.message}`);
            }

            // 如果任务失败，则退还积分
            if (status === PicTaskStatus.FAILED) {
                try {
                    const userId = existingTask.user_id;
                    const resolution = existingTask.input_params.resolution || PicResolution.LOW;
                    await this.refundCreditsForPicGeneration(
                        userId,
                        task_id,
                        resolution,
                        '图片生成失败，自动退款'
                    );
                } catch (refundError) {
                    // 记录退款失败，但不影响任务完成流程
                    await this.errorAuditService.logRefundError(
                        existingTask.user_id,
                        task_id,
                        `图片生成失败后退款失败: ${refundError.message}`,
                        refundError,
                        {
                            resolution: existingTask.input_params.resolution,
                            operation: 'finishPicGenerationTask'
                        },
                        ErrorSeverity.CRITICAL
                    );
                }
            }

            return updatedTask;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`完成图片生成任务失败: ${error.message}`, error.stack);
            throw new BadRequestException('完成图片生成任务失败，请稍后重试');
        }
    }

    /**
     * 获取用户的图片生成任务列表
     * @param userId 用户ID
     * @param status 可选的状态过滤
     * @param limit 分页限制
     * @param offset 分页偏移
     * @returns 任务列表和总数
     */
    async getUserTasks(
        userId: string,
        status?: PicTaskStatus,
        limit: number = 10,
        offset: number = 0
    ): Promise<{ tasks: PicTaskDto[]; total: number }> {
        try {
            // 构建查询
            let query = this.supabase
                .from('pic_gen_tasks')
                .select('*', { count: 'exact' })
                .eq('user_id', userId);

            // 如果提供了状态，添加状态过滤
            if (status) {
                query = query.eq('status', status);
            }

            // 添加分页
            query = query.order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            const { data, error, count } = await query;

            if (error) {
                throw new BadRequestException(`获取任务列表失败: ${error.message}`);
            }

            return {
                tasks: data || [],
                total: count || 0
            };
        } catch (error) {
            this.logger.error(`获取用户任务列表失败: ${error.message}`, error.stack);
            throw new BadRequestException('获取任务列表失败，请稍后重试');
        }
    }

    /**
     * 获取任务详情
     * @param taskId 任务ID
     * @returns 任务详情
     */
    async getTaskById(taskId: string): Promise<PicTaskDto> {
        try {
            const { data, error } = await this.supabase
                .from('pic_gen_tasks')
                .select('*')
                .eq('id', taskId)
                .single();

            if (error || !data) {
                throw new NotFoundException(`任务不存在: ${taskId}`);
            }

            return data;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error(`获取任务详情失败: ${error.message}`, error.stack);
            throw new BadRequestException('获取任务详情失败，请稍后重试');
        }
    }

    /**
     * 取消任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @returns 取消后的任务
     */
    async cancelTask(taskId: string, userId: string): Promise<PicTaskDto> {
        try {
            // 获取任务详情
            const { data: existingTask, error: fetchError } = await this.supabase
                .from('pic_gen_tasks')
                .select('*')
                .eq('id', taskId)
                .single();

            if (fetchError || !existingTask) {
                throw new NotFoundException(`任务不存在: ${taskId}`);
            }

            // 检查权限
            if (existingTask.user_id !== userId) {
                throw new ForbiddenException('无权操作此任务');
            }

            // 检查任务状态
            if (
                existingTask.status === PicTaskStatus.COMPLETED ||
                existingTask.status === PicTaskStatus.FAILED ||
                existingTask.status === PicTaskStatus.CANCELLED
            ) {
                throw new BadRequestException(`任务已${existingTask.status}，无法取消`);
            }

            // 更新任务状态
            const { data: updatedTask, error: updateError } = await this.supabase
                .from('pic_gen_tasks')
                .update({
                    status: PicTaskStatus.CANCELLED,
                    updated_at: new Date(),
                    completed_at: new Date()
                })
                .eq('id', taskId)
                .select()
                .single();

            if (updateError) {
                throw new BadRequestException(`取消任务失败: ${updateError.message}`);
            }

            // 退还积分
            try {
                const resolution = existingTask.input_params.resolution || PicResolution.LOW;
                await this.refundCreditsForPicGeneration(
                    userId,
                    taskId,
                    resolution,
                    '用户取消任务，自动退款'
                );
            } catch (refundError) {
                // 记录退款失败，但不影响取消流程
                await this.errorAuditService.logRefundError(
                    userId,
                    taskId,
                    `用户取消任务后退款失败: ${refundError.message}`,
                    refundError,
                    {
                        resolution: existingTask.input_params.resolution,
                        operation: 'cancelTask'
                    },
                    ErrorSeverity.CRITICAL
                );
            }

            return updatedTask;
        } catch (error) {
            if (
                error instanceof NotFoundException ||
                error instanceof ForbiddenException ||
                error instanceof BadRequestException
            ) {
                throw error;
            }
            this.logger.error(`取消任务失败: ${error.message}`, error.stack);
            throw new BadRequestException('取消任务失败，请稍后重试');
        }
    }

    /**
     * 获取任务队列信息
     * @param taskId 任务ID
     * @returns 队列信息
     */
    async getQueueInfo(taskId: string): Promise<QueueInfoResponseDto> {
        try {
            // 获取指定任务信息
            const { data: task, error } = await this.supabase
                .from('pic_gen_tasks')
                .select('*')
                .eq('id', taskId)
                .single();

            if (error || !task) {
                throw new NotFoundException(`任务不存在: ${taskId}`);
            }

            // 如果任务已经不在等待队列中，返回相应信息
            if (task.status !== PicTaskStatus.PENDING) {
                return {
                    position: 0,
                    total_tasks: 0,
                    estimated_wait_time: 0,
                    task_id: taskId,
                    message: `任务状态为${task.status}，不在等待队列中`
                };
            }

            // 获取当前任务在队列中的位置
            const { data: queueTasks, error: queueError } = await this.supabase
                .from('pic_gen_tasks')
                .select('id, priority, created_at')
                .eq('status', PicTaskStatus.PENDING)
                .order('priority', { ascending: false })
                .order('created_at', { ascending: true });

            if (queueError) {
                throw new BadRequestException(`获取队列信息失败: ${queueError.message}`);
            }

            // 查找当前任务在队列中的位置
            const position = queueTasks.findIndex(t => t.id === taskId) + 1;
            const totalTasks = queueTasks.length;

            // 估算等待时间（每个任务估计60秒）
            const estimatedWaitTime = position * 60;

            return {
                position,
                total_tasks: totalTasks,
                estimated_wait_time: estimatedWaitTime,
                task_id: taskId,
                message: position > 0
                    ? `排队中，前面还有${position - 1}个任务`
                    : '任务即将处理'
            };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`获取队列信息失败: ${error.message}`, error.stack);
            throw new BadRequestException('获取队列信息失败，请稍后重试');
        }
    }

    /**
     * 更新任务进度
     * @param body 进度更新参数
     * @returns 更新结果
     */
    async tickProgress(body: TickProgressDto): Promise<boolean> {
        const { task_id, progress, message } = body;

        try {
            const { error } = await this.supabase
                .from('pic_gen_tasks')
                .update({
                    progress,
                    error_message: message,
                    updated_at: new Date()
                })
                .eq('id', task_id)
                .eq('status', PicTaskStatus.PROCESSING); // 只更新处理中的任务

            return !error;
        } catch (error) {
            this.logger.error(`更新任务进度失败: ${error.message}`, error.stack);
            return false;
        }
    }

    /**
     * 增强提示词
     * @param enhancePromptDto 原始提示词
     * @returns 增强后的提示词
     */
    async enhancePrompt(enhancePromptDto: EnhancePromptRequestDto): Promise<EnhancePromptResponseDto> {
        try {
            const { prompt } = enhancePromptDto;

            const completion = await this.openaiClient.chat.completions.create({
                model: "deepseek-chat",
                messages: [
                    { role: "system", content: SystemPromptPic },
                    { role: "user", content: prompt }
                ],
                stream: false
            });

            const enhancedPrompt = completion.choices[0].message.content.trim();

            return {
                enhanced_prompt: enhancedPrompt
            };
        } catch (error) {
            this.logger.error(`增强提示词失败: ${error.message}`, error.stack);
            throw new BadRequestException('增强提示词失败，请稍后重试');
        }
    }

    /**
     * 获取图片生成可用的模型列表
     * @returns 模型列表
     */
    async getPicModels() {
        return {
            models: [
                {
                    name: 'UNO',
                    displayName: 'UNO',
                    description: 'Default image generation model',
                    price: 0,
                    type: 'internal',
                },
                {
                    name: 'FLUX Kontext Pro',
                    displayName: 'FLUX Kontext Pro',
                    description: 'Professional FLUX model with balanced performance',
                    price: 4,
                    type: 'external',
                    image_limit: 1,
                },
                {
                    name: 'FLUX Kontext Max',
                    displayName: 'FLUX Kontext Max',
                    description: 'High-quality FLUX model with maximum capabilities',
                    price: 8,
                    type: 'external',
                    image_limit: 2,
                },
            ]
        };
    }

    /**
     * 计算图片生成所需的积分（支持不同模型）
     * @param model 模型名称
     * @returns 所需积分数量
     */
    private calculateRequiredCreditsForModel(model: string): number {
        switch (model) {
            case 'FLUX Kontext Max':
                return 8;
            case 'FLUX Kontext Pro':
                return 4;
            case 'UNO':
            default:
                return 0; // UNO模型免费
        }
    }

    /**
     * 处理fal.ai图片生成webhook回调
     * @param body webhook请求体
     */
    async handleFalAiPicWebhook(body: IFalGenPicWebhookBody): Promise<{ status: string }> {
        try {
            this.logger.log(`收到fal.ai图片生成webhook: ${JSON.stringify(body)}`);

            const { status, request_id, payload, error } = body;

            if (!request_id) {
                this.logger.error('Webhook缺少request_id');
                return { status: 'error' };
            }

            // 根据request_id查找对应的任务
            const { data: task, error: findError } = await this.supabase
                .from('pic_gen_tasks')
                .select('*')
                .eq('request_id', request_id)
                .single();

            if (findError || !task) {
                this.logger.error(`未找到对应的图片生成任务: ${request_id}`);
                return { status: 'error' };
            }

            // 根据webhook状态更新任务
            if (status === 'OK' && payload && payload.images) {
                // 任务完成
                const { images } = payload;
                // 暂不支持多图任务
                if (images && images.length > 0) {
                    const updateData = {
                        status: PicTaskStatus.COMPLETED,
                        progress: 100,
                        output_result: {
                            format: payload.images[0].content_type,
                            image_url: payload.images[0].url,
                            width: payload.images[0].width,
                            height: payload.images[0].height,
                            prompt: payload.prompt,
                            seed: payload.seed,
                            has_nsfw_concepts: payload.has_nsfw_concepts
                        },
                        completed_at: new Date(),
                        updated_at: new Date()
                    };

                    const { error: updateError } = await this.supabase
                        .from('pic_gen_tasks')
                        .update(updateData)
                        .eq('id', task.id);

                    if (updateError) {
                        this.logger.error(`更新任务状态失败: ${updateError.message}`);
                        return { status: 'error' };
                    }

                    this.logger.log(`图片生成任务完成: ${task.id}`);
                } else {
                    this.logger.error('Webhook返回的结果中没有图片');
                    return { status: 'error' };
                }
            } else if (status === 'ERROR' || error) {
                // 任务失败
                const errorDetail = error || 'Unknown error from fal.ai';

                const updateData = {
                    status: PicTaskStatus.FAILED,
                    error_log: {
                        request_id,
                        error: errorDetail,
                        payload_error: body.payload_error || null
                    },
                    completed_at: new Date(),
                    updated_at: new Date()
                };

                const { error: updateError } = await this.supabase
                    .from('pic_gen_tasks')
                    .update(updateData)
                    .eq('id', task.id);

                if (updateError) {
                    this.logger.error(`更新任务状态失败: ${updateError.message}`);
                    return { status: 'error' };
                }

                // 任务失败，退还积分
                try {
                    const model = task.model || 'UNO';
                    const creditsAmount = this.calculateRequiredCreditsForModel(model);
                    if (creditsAmount > 0) {
                        await this.refundCreditsForPicGeneration(
                            task.user_id,
                            task.id,
                            PicResolution.LOW,
                            'fal.ai图片生成失败，自动退款'
                        );
                    }
                } catch (refundError) {
                    this.logger.error(`退还积分失败: ${refundError.message}`);
                }

                this.logger.log(`图片生成任务失败: ${task.id}`);
            } else {
                // 其他状态或未知状态，记录日志但不做处理
                this.logger.warn(`收到未知状态的webhook: ${status}, body: ${JSON.stringify(body)}`);
            }

            return { status: 'success' };
        } catch (error) {
            this.logger.error(`处理fal.ai图片生成webhook失败: ${error.message}`, error.stack);
            return { status: 'error' };
        }
    }

    /**
     * 检查是否为FLUX模型
     * @param model 模型名称
     * @returns 是否为FLUX模型
     */
    private isFluxModel(model: string): boolean {
        return model.toLowerCase().includes('flux');
    }

    /**
     * 检查用户是否可以使用FLUX模型
     * @param userId 用户ID
     * @returns 是否可以使用FLUX模型
     */
    private async checkFluxModelUsageLimit(userId: string): Promise<boolean> {
        try {
            // 首先检查用户是否为会员
            const membership = await this.membershipService.getUserMembership(userId);

            // 如果是活跃会员，则无限制
            if (membership && membership.is_active) {
                return true;
            }

            // 非会员用户，检查FLUX模型使用次数
            const { data: fluxTasks, error } = await this.supabase
                .from('pic_gen_tasks')
                .select('id')
                .eq('user_id', userId)
                .ilike('model', '%flux%')
                .in('status', ['completed', 'processing', 'pending']); // 只计算成功或正在处理的任务

            if (error) {
                this.logger.error(`查询FLUX模型使用次数失败: ${error.message}`, error);
                // 查询失败时允许使用，避免误杀正常用户
                return true;
            }

            const usageCount = fluxTasks?.length || 0;

            // 非会员用户最多使用2次
            return usageCount < 2;
        } catch (error) {
            this.logger.error(`检查FLUX模型使用限制失败: ${error.message}`, error.stack);
            // 异常时允许使用，避免误杀正常用户
            return true;
        }
    }

    /**
     * 获取用户FLUX模型使用限制信息
     * @param userId 用户ID
     * @returns FLUX使用限制信息
     */
    async getFluxUsageLimitInfo(userId: string): Promise<{ canUse: boolean; usageCount: number; limit: number }> {
        try {
            // 首先检查用户是否为会员
            const membership = await this.membershipService.getUserMembership(userId);

            // 如果是活跃会员，则无限制
            if (membership && membership.is_active) {
                return {
                    canUse: true,
                    usageCount: 0,
                    limit: -1 // -1 表示无限制
                };
            }

            // 非会员用户，查询FLUX模型使用次数
            const { data: fluxTasks, error } = await this.supabase
                .from('pic_gen_tasks')
                .select('id')
                .eq('user_id', userId)
                .ilike('model', '%flux%')
                .in('status', ['completed', 'processing', 'pending']); // 只计算成功或正在处理的任务

            if (error) {
                this.logger.error(`查询FLUX模型使用次数失败: ${error.message}`, error);
                // 查询失败时返回保守值
                return {
                    canUse: true,
                    usageCount: 0,
                    limit: 2
                };
            }

            const usageCount = fluxTasks?.length || 0;
            const limit = 2;

            return {
                canUse: usageCount < limit,
                usageCount,
                limit
            };
        } catch (error) {
            this.logger.error(`获取FLUX模型使用限制信息失败: ${error.message}`, error.stack);
            // 异常时返回保守值
            return {
                canUse: true,
                usageCount: 0,
                limit: 2
            };
        }
    }
}