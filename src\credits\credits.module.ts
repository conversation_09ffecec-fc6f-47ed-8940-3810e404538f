import { forwardRef, Module } from '@nestjs/common';
import { CreditsController } from './credits.controller';
import { CreditsService } from './credits.service';
import { AntiAbuseService } from './anti-abuse.service';
import { SupabaseModule } from '../common/providers/supabase.module';
import { PaymentModule } from '../payment/payment.module';
import { MembershipModule } from '../membership/membership.module';
import { CustomLogger } from '../common/services/logger.service';

// 导入策略
import {
    CreditStrategyFactory,
    MembershipInitialStrategy,
    MembershipMonthlyStrategy
} from './strategies';

@Module({
    imports: [
        SupabaseModule,
        forwardRef(() => PaymentModule),
        forwardRef(() => MembershipModule),
    ],
    controllers: [CreditsController],
    providers: [
        // 核心服务
        CreditsService,
        AntiAbuseService,
        CustomLogger,

        // 策略工厂和策略
        CreditStrategyFactory,
        MembershipInitialStrategy,
        MembershipMonthlyStrategy
    ],
    exports: [CreditsService],
})
export class CreditsModule {
    constructor(
        private readonly strategyFactory: CreditStrategyFactory,
        private readonly membershipInitialStrategy: MembershipInitialStrategy,
        private readonly membershipMonthlyStrategy: MembershipMonthlyStrategy
    ) {
        // 注册策略
        this.strategyFactory.registerStrategy(this.membershipInitialStrategy);
        this.strategyFactory.registerStrategy(this.membershipMonthlyStrategy);
    }
}