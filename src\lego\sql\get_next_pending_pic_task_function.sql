-- 创建获取下一个待处理图片生成任务的存储过程
CREATE OR REPLACE FUNCTION public.get_next_pending_pic_task(
    has_refer_img BOOLEAN DEFAULT FALSE,
    is_user_task BOOLEAN DEFAULT TRUE
)
RETURNS SETOF public.pic_gen_tasks
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_task_id UUID;
    v_task public.pic_gen_tasks;
BEGIN
    -- 开始一个事务
    BEGIN
        -- 获取下一个待处理任务
        SELECT id INTO v_task_id
        FROM public.pic_gen_tasks
        WHERE status = 'pending'
        AND (NOT has_refer_img OR (input_params->>'reference_image') IS NOT NULL)
        AND (
            (is_user_task = TRUE AND priority >= 0) OR
            (is_user_task = FALSE AND priority < 0)
        )
        ORDER BY priority DESC, created_at ASC
        LIMIT 1
        FOR UPDATE SKIP LOCKED;

        -- 如果没有找到任务则返回空
        IF v_task_id IS NULL THEN
            RETURN;
        END IF;

        -- 更新任务状态为处理中
        UPDATE public.pic_gen_tasks
        SET status = 'processing',
            started_at = NOW(),
            last_activity_at = NOW(),
            updated_at = NOW()
        WHERE id = v_task_id
        RETURNING * INTO v_task;

        -- 返回更新后的任务
        RETURN NEXT v_task;

        -- 提交事务
        RETURN;
    EXCEPTION
        WHEN OTHERS THEN
            -- 事务出错时回滚
            RAISE;
    END;
END;
$$;