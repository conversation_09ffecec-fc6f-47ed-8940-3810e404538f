import { create } from 'zustand';
import { AspectRatio, CardData, CardType, LegoHistoryItem, LegoQueueInfo, SectionType, LegoModel } from './types';

export interface LegoState {
    // 基本信息状态
    userPrompt: string;
    negativePrompt: string;

    // 卡片数据 - 使用数组存储多个卡片
    themeCards: CardData[];
    sceneCards: CardData[];
    styleCards: CardData[];
    isUploading: boolean;

    // UI状态
    isSidebarCollapsed: boolean;

    // 高级设置
    stylePreset: string;
    seed: string;
    steps: number;
    guidanceScale: number;
    aspectRatio: AspectRatio;

    // 模型选择
    selectedModel: LegoModel | null;
    availableModels: LegoModel[];
    isLoadingModels: boolean;

    // FLUX模型专用图片
    fluxImages: string[];
    isFluxImageUploading: boolean;

    // 生成状态
    isGenerating: boolean;
    generationHistory: LegoHistoryItem[];
    queueInfo: LegoQueueInfo | null;
    error: string | undefined;

    // 分页状态
    currentPage: number;
    totalItems: number;
    itemsPerPage: number;

    // 卡片操作
    addCard: (section: SectionType, card: CardData) => void;
    updateCard: (section: SectionType, id: string, updates: Partial<CardData>) => void;
    removeCard: (section: SectionType, id: string) => void;
    toggleCardSelection: (section: SectionType, id: string) => void;
    setIsUploading: (isUploading: boolean) => void;

    // 获取选中的卡片内容
    getSelectedContent: (section: SectionType, type?: CardType) => string[];

    // 确保每个部分至少有一个卡片
    ensureCardsExist: () => void;

    // 基本信息操作
    setUserPrompt: (prompt: string) => void;
    setNegativePrompt: (prompt: string) => void;

    // UI状态操作
    setIsSidebarCollapsed: (isCollapsed: boolean) => void;

    // 高级设置操作
    setStylePreset: (preset: string) => void;
    setSeed: (seed: string) => void;
    setSteps: (steps: number) => void;
    setGuidanceScale: (scale: number) => void;
    setAspectRatio: (ratio: AspectRatio) => void;

    // 模型选择操作
    setSelectedModel: (model: LegoModel | null) => void;
    setAvailableModels: (models: LegoModel[]) => void;
    setIsLoadingModels: (loading: boolean) => void;
    selectModelByName: (modelName: string) => void;

    // FLUX图片操作
    addFluxImage: (imageUrl: string) => void;
    removeFluxImage: (index: number) => void;
    clearFluxImages: () => void;
    setIsFluxImageUploading: (uploading: boolean) => void;

    // 生成状态操作
    setIsGenerating: (isGenerating: boolean) => void;
    addToHistory: (item: LegoHistoryItem) => void;
    removeFromHistory: (id: string) => void;
    clearHistory: () => void;
    setHistory: (history: LegoHistoryItem[], total: number) => void;
    updateHistoryItem: (taskId: string, updates: Partial<LegoHistoryItem>) => void;
    setQueueInfo: (info: LegoQueueInfo | null) => void;
    setError: (error: string | undefined) => void;

    // 分页操作
    setPage: (page: number) => void;

    // 重置表单
    resetForm: () => void;

    // 获取所有选中的引用图片
    getReferItems: () => string[];

    // 获取所有选中的文本内容，并按照指定格式拼接
    getTextualContent: () => string;
}

export const useLegoStore = create<LegoState>((set, get) => ({
    // 基本信息状态
    userPrompt: '',
    negativePrompt: '',

    // 卡片数据 - 使用数组存储多个卡片，每种类型初始化一个空卡片
    themeCards: [{ id: `theme-${Date.now()}`, type: "empty", content: "", selected: true }],
    sceneCards: [{ id: `scene-${Date.now()}`, type: "empty", content: "", selected: true }],
    styleCards: [{ id: `style-${Date.now()}`, type: "empty", content: "", selected: true }],
    isUploading: false,

    // UI状态
    isSidebarCollapsed: true,

    // 高级设置
    stylePreset: 'realistic',
    seed: '-1',
    steps: 30,
    guidanceScale: 3.5,
    aspectRatio: '9:16',

    // 模型选择
    selectedModel: null,
    availableModels: [],
    isLoadingModels: false,

    // FLUX模型专用图片
    fluxImages: [],
    isFluxImageUploading: false,

    // 生成状态
    isGenerating: false,
    generationHistory: [],
    queueInfo: null,
    error: undefined,

    // 分页状态
    currentPage: 1,
    totalItems: 0,
    itemsPerPage: 20,

    // 卡片操作
    addCard: (section, card) => set((state) => {
        const sectionKey = `${section}Cards` as const;

        // 如果是scene或style，需要取消其他卡片的选择
        let updatedCards = [...state[sectionKey]];

        if (section !== "theme" && card.selected) {
            updatedCards = updatedCards.map(c => ({
                ...c,
                selected: false
            }));
        }

        return {
            [sectionKey]: [...updatedCards, card]
        };
    }),

    updateCard: (section, id, updates) => set((state) => {
        const sectionKey = `${section}Cards` as const;
        const cards = state[sectionKey];
        const cardIndex = cards.findIndex(card => card.id === id);

        if (cardIndex === -1) return state;

        const updatedCards = [...cards];
        updatedCards[cardIndex] = {
            ...updatedCards[cardIndex],
            ...updates
        };

        // 如果是scene或style，且卡片被选中，需要取消其他卡片的选择
        if (section !== "theme" && updates.selected) {
            updatedCards.forEach((card, index) => {
                if (index !== cardIndex) {
                    updatedCards[index] = { ...card, selected: false };
                }
            });
        }

        return {
            [sectionKey]: updatedCards
        };
    }),

    removeCard: (section, id) => set((state) => {
        const sectionKey = `${section}Cards` as const;
        const cards = state[sectionKey];

        // 如果只有一个卡片，不允许删除
        if (cards.length <= 1) return state;

        const cardToDelete = cards.find(card => card.id === id);
        if (!cardToDelete) return state;

        const updatedCards = cards.filter(card => card.id !== id);

        // 如果删除的是选中的卡片，且是scene或style，需要选中另一个卡片
        if (cardToDelete.selected && section !== "theme") {
            const firstNonEmptyCard = updatedCards.find(card => card.type !== "empty");

            if (firstNonEmptyCard) {
                updatedCards.forEach(card => {
                    if (card.id === firstNonEmptyCard.id) {
                        card.selected = true;
                    }
                });
            } else if (updatedCards.length > 0) {
                updatedCards[0].selected = true;
            }
        }

        return {
            [sectionKey]: updatedCards
        };
    }),

    toggleCardSelection: (section, id) => set((state) => {
        const sectionKey = `${section}Cards` as const;
        const cards = state[sectionKey];
        const cardToToggle = cards.find(card => card.id === id);

        if (!cardToToggle || cardToToggle.type === "empty") return state;

        // 如果卡片已经被选中，且是scene或style，不允许取消选择
        if (cardToToggle.selected && section !== "theme") {
            const selectedCount = cards.filter(card => card.selected && card.type !== "empty").length;
            if (selectedCount <= 1) return state; // 不允许取消唯一的选中卡片
        }

        let updatedCards;

        // 对于scene和style，只能选择一个卡片
        if (section !== "theme") {
            updatedCards = cards.map(card => ({
                ...card,
                selected: card.id === id
            }));
        } else {
            // 对于theme，可以多选
            updatedCards = cards.map(card =>
                card.id === id ? { ...card, selected: !card.selected } : card
            );
        }

        return {
            [sectionKey]: updatedCards
        };
    }),

    // 获取选中的卡片内容
    getSelectedContent: (section, type) => {
        const state = get();
        const sectionKey = `${section}Cards` as const;
        const cards = state[sectionKey];

        // 获取所有选中的卡片
        const selectedCards = cards.filter(card => card.selected && card.type !== "empty");

        // 如果指定了类型，则只返回该类型的卡片内容
        if (type) {
            return selectedCards
                .filter(card => card.type === type)
                .map(card => card.content);
        }

        // 否则返回所有选中卡片的内容
        return selectedCards.map(card => card.content);
    },

    // 获取所有选中的引用图片（只返回图片URL，不包含文本）
    getReferItems: () => {
        const state = get();

        // 添加所有选中的图片
        const themeImages = state.getSelectedContent("theme", "image");
        const sceneImages = state.getSelectedContent("scene", "image");
        const styleImages = state.getSelectedContent("style", "image");

        // 只返回图片URL
        return [
            ...themeImages.filter(url => url.startsWith('http')),
            ...sceneImages.filter(url => url.startsWith('http')),
            ...styleImages.filter(url => url.startsWith('http'))
        ];
    },

    // 获取所有选中的文本内容，并按照指定格式拼接
    getTextualContent: () => {
        const state = get();

        // 获取所有选中的文本
        const themeTexts = state.getSelectedContent("theme", "text");
        const sceneTexts = state.getSelectedContent("scene", "text");
        const styleTexts = state.getSelectedContent("style", "text");

        // 过滤掉空文本
        const filteredThemeTexts = themeTexts.filter(text => text.trim() !== "");
        const filteredSceneTexts = sceneTexts.filter(text => text.trim() !== "");
        const filteredStyleTexts = styleTexts.filter(text => text.trim() !== "");

        // 构建拼接文本
        let result = "";

        if (filteredThemeTexts.length > 0) {
            result += `Theme:${filteredThemeTexts.join(",")}`;
        }

        if (filteredSceneTexts.length > 0) {
            if (result) result += ",";
            result += `Scene:${filteredSceneTexts.join(",")}`;
        }

        if (filteredStyleTexts.length > 0) {
            if (result) result += ",";
            result += `Style:${filteredStyleTexts.join(".")}`;
        }

        return result;
    },

    // 基本信息操作
    setUserPrompt: (prompt) => set({ userPrompt: prompt }),
    setNegativePrompt: (negativePrompt) => set({ negativePrompt }),
    setIsUploading: (isUploading) => set({ isUploading }),

    // UI状态操作
    setIsSidebarCollapsed: (isSidebarCollapsed) => set({ isSidebarCollapsed }),

    // 高级设置操作
    setStylePreset: (stylePreset) => set({ stylePreset }),
    setSeed: (seed) => set({ seed }),
    setSteps: (steps) => set({ steps }),
    setGuidanceScale: (guidanceScale) => set({ guidanceScale }),
    setAspectRatio: (aspectRatio) => set({ aspectRatio }),

    // 生成状态操作
    setIsGenerating: (isGenerating) => set({ isGenerating }),

    addToHistory: (item) => set((state) => ({
        generationHistory: [item, ...state.generationHistory],
        totalItems: state.totalItems + 1
    })),

    removeFromHistory: (id) => set((state) => ({
        generationHistory: state.generationHistory.filter(item => item.id !== id),
        totalItems: state.totalItems - 1
    })),

    clearHistory: () => set({ generationHistory: [], totalItems: 0 }),

    setHistory: (history, total) => set({
        generationHistory: history,
        totalItems: total
    }),

    updateHistoryItem: (taskId, updates) => set((state) => ({
        generationHistory: state.generationHistory.map(item =>
            item.id === taskId ? { ...item, ...updates } : item
        )
    })),

    // 确保每个部分至少有一个卡片
    ensureCardsExist: () => set((state) => {
        const generateId = (section: SectionType) => `${section}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        const updates: Partial<LegoState> = {};

        // 检查主题卡片
        if (!state.themeCards || state.themeCards.length === 0) {
            updates.themeCards = [{ id: generateId('theme'), type: "empty", content: "", selected: true }];
        }

        // 检查场景卡片
        if (!state.sceneCards || state.sceneCards.length === 0) {
            updates.sceneCards = [{ id: generateId('scene'), type: "empty", content: "", selected: true }];
        }

        // 检查样式卡片
        if (!state.styleCards || state.styleCards.length === 0) {
            updates.styleCards = [{ id: generateId('style'), type: "empty", content: "", selected: true }];
        }

        return updates;
    }),

    // 模型选择操作实现
    setSelectedModel: (model) => set({ selectedModel: model }),
    setAvailableModels: (models) => set({ availableModels: models }),
    setIsLoadingModels: (loading) => set({ isLoadingModels: loading }),
    selectModelByName: (modelName) => {
        const state = get();
        const searchName = modelName.toLowerCase();

        // 首先尝试精确匹配
        let targetModel = state.availableModels.find(model =>
            model.name.toLowerCase() === searchName ||
            model.displayName?.toLowerCase() === searchName
        );

        // 如果没有精确匹配，尝试包含匹配
        if (!targetModel) {
            targetModel = state.availableModels.find(model =>
                model.name.toLowerCase().includes(searchName) ||
                model.displayName?.toLowerCase().includes(searchName)
            );
        }

        // 特殊处理：flux-kontext-pro 应该匹配 FLUX Kontext Pro
        if (!targetModel && searchName.includes('flux-kontext')) {
            const fluxType = searchName.includes('pro') ? 'pro' :
                searchName.includes('max') ? 'max' : '';
            if (fluxType) {
                targetModel = state.availableModels.find(model =>
                    model.name.toLowerCase().includes('flux kontext') &&
                    model.name.toLowerCase().includes(fluxType)
                );
            }
        }

        if (targetModel) {
            console.log(`模型选择成功: ${modelName} -> ${targetModel.name}`);
            set({ selectedModel: targetModel });
        } else {
            console.warn(`未找到匹配的模型: ${modelName}`);
        }
    },

    // FLUX图片操作实现
    addFluxImage: (imageUrl) => set((state) => {
        // 最多只能有2张图片
        if (state.fluxImages.length >= 2) {
            return state;
        }
        return {
            fluxImages: [...state.fluxImages, imageUrl]
        };
    }),
    removeFluxImage: (index) => set((state) => ({
        fluxImages: state.fluxImages.filter((_, i) => i !== index)
    })),
    clearFluxImages: () => set({ fluxImages: [] }),
    setIsFluxImageUploading: (uploading) => set({ isFluxImageUploading: uploading }),

    // setCurrentTaskId已移除
    setQueueInfo: (queueInfo) => set({ queueInfo }),
    setError: (error) => set({ error }),
    setPage: (currentPage) => set({ currentPage }),

    resetForm: () => set({
        userPrompt: '',
        negativePrompt: '',
        themeCards: [{ id: `theme-${Date.now()}`, type: "empty", content: "", selected: true }],
        sceneCards: [{ id: `scene-${Date.now()}`, type: "empty", content: "", selected: true }],
        styleCards: [{ id: `style-${Date.now()}`, type: "empty", content: "", selected: true }],
        fluxImages: [],
        seed: Math.floor(Math.random() * 1000000).toString(),
        error: undefined
    })
}));