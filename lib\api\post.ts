import { PostItem, PostItemDto } from '@/types/posts';
import { apiClient, ApiResponse } from './client';

export interface PostCreateParams {
    taskId?: string;       // 可选，已生成的视频任务ID
    videoUrl?: string;     // 可选，视频URL（上传的视频）
    title: string;         // 必填，帖子标题
    description?: string;  // 可选，帖子描述
    thumbnailUrl?: string; // 可选，缩略图URL
}

export interface PostsResponse {
    posts: PostItemDto[];
    total: number;
}

export interface CommentCreateParams {
    content: string;              // 必填，评论内容
    parentId?: string;            // 可选，父评论ID（如果是回复评论）
    targetType: 'post' | 'model'; // 必填，评论目标类型
    targetId: string;             // 必填，评论目标ID（帖子ID或模型ID）
}

export interface CommentsResponse {
    comments: Comment[];
    total: number;
}

// 适配器函数：将后端API返回的Post转换为前端使用的ContentItem
export const postToContentItem = (post: PostItemDto): PostItem => {
    return {
        id: post.id,
        title: post.title,
        description: post.description,
        videoUrl: post.video_url,
        thumbnailUrl: post.videos?.cover_img,
        userId: post.user_id,
        username: post.username,
        userAvatar: post.user_avatar,
        likeCount: post.like_count,
        commentCount: post.comment_count,
        createdAt: new Date(post.created_at).toISOString(),
        taskId: post.task_id,
        inputParams: post.videos?.input_params,
        coverImg: post.videos?.cover_img
    };
};

export const postApi = {
    // 创建帖子
    createPost: async (params: PostCreateParams): Promise<any> => {
        return apiClient.post<any>('/posts', params);
    },

    // 获取Feed流
    getFeed: async (params: { limit: number, offset: number, tag_id?: string }): Promise<ApiResponse<PostsResponse>> => {
        return apiClient.post<ApiResponse<PostsResponse>>(`/posts/feed`, params, { requireAuth: false });
    },

    // 获取用户帖子
    getUserPosts: async (userId: string, limit: number = 10, offset: number = 0): Promise<ApiResponse<PostsResponse>> => {
        return apiClient.get<ApiResponse<PostsResponse>>(`/posts/user/${userId}?limit=${limit}&offset=${offset}`);
    },

    // 获取帖子详情
    getPost: async (post_id: string): Promise<ApiResponse<PostItemDto>> => {
        return apiClient.get<ApiResponse<PostItemDto>>(`/posts/${post_id}`, { requireAuth: false });
    },

    // 点赞帖子
    likePost: async (post_id: string): Promise<ApiResponse<PostItem>> => {
        return apiClient.post<ApiResponse<PostItem>>(`/posts/like`, { post_id, is_like: true });
    },

    // 取消点赞
    unlikePost: async (post_id: string): Promise<ApiResponse<PostItem>> => {
        return apiClient.post<ApiResponse<PostItem>>(`/posts/like`, { post_id, is_like: false });
    },

    // 创建评论
    createComment: async (params: CommentCreateParams): Promise<ApiResponse<Comment>> => {
        return apiClient.post<ApiResponse<Comment>>('/posts/comment', params);
    },

    // 获取帖子评论
    getPostComments: async (
        post_id: string,
        parentId?: string,
        limit: number = 10,
        offset: number = 0
    ): Promise<ApiResponse<CommentsResponse>> => {
        const query = parentId
            ? `?parentId=${parentId}&limit=${limit}&offset=${offset}`
            : `?limit=${limit}&offset=${offset}`;
        return apiClient.get<ApiResponse<CommentsResponse>>(`/posts/${post_id}/comments${query}`, { requireAuth: false });
    },

    // 获取模型评论
    getModelComments: async (
        modelId: string,
        parentId?: string,
        limit: number = 10,
        offset: number = 0
    ): Promise<ApiResponse<CommentsResponse>> => {
        const query = parentId
            ? `?parentId=${parentId}&limit=${limit}&offset=${offset}`
            : `?limit=${limit}&offset=${offset}`;
        return apiClient.get<ApiResponse<CommentsResponse>>(`/posts/model/${modelId}/comments${query}`, { requireAuth: false });
    },
}; 