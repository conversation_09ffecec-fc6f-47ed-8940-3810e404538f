"use client"

import { motion } from "framer-motion"

// 简单的骨架屏组件
const Skeleton = ({
    className,
    ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
    return (
        <div
            className={`animate-pulse rounded-md bg-slate-200 dark:bg-slate-700/50 ${className}`}
            {...props}
        />
    )
}

// 视频区域骨架屏
export function VideoAreaSkeleton() {
    return (
        <motion.div
            className="flex-1 flex flex-col bg-gradient-to-b from-black/80 to-black/95 dark:from-black/90 dark:to-black backdrop-blur-xl rounded-xl border border-slate-200/10 dark:border-white/10 overflow-hidden shadow-[0_0_25px_rgba(59,130,246,0.1)] dark:shadow-[0_0_25px_rgba(59,130,246,0.15)] h-full"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.25 }}
        >
            {/* 顶部操作栏骨架屏 */}
            <div className="flex items-center justify-between px-6 py-4 border-b border-slate-200/10 dark:border-white/10">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex items-center gap-3">
                    <Skeleton className="h-10 w-24 rounded-full" />
                    <Skeleton className="h-10 w-20 rounded-full" />
                    <Skeleton className="h-10 w-20 rounded-full" />
                </div>
            </div>

            {/* 视频播放区骨架屏 */}
            <div className="flex-1 overflow-hidden flex justify-center items-center p-1 relative">
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent pointer-events-none z-0"></div>
                <div className="w-full h-full relative z-10 rounded-xl overflow-hidden flex items-center justify-center">
                    <div className="w-full h-full bg-black/40 flex items-center justify-center">
                        <div className="w-20 h-20 rounded-full bg-black/60 flex items-center justify-center">
                            <svg
                                className="w-12 h-12 text-white/30"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                                />
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    )
}

// 作者信息骨架屏
export function AuthorInfoSkeleton() {
    return (
        <div className="p-6 border-b border-slate-200/50 dark:border-white/10 backdrop-blur-sm">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Skeleton className="h-14 w-14 rounded-full" />
                    <div className="space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                    </div>
                </div>
                <Skeleton className="h-10 w-24 rounded-full" />
            </div>
        </div>
    )
}

// 视频详情骨架屏
export function VideoDetailsSkeleton() {
    return (
        <div className="p-6 border-b border-slate-200/50 dark:border-white/10">
            <Skeleton className="h-6 w-3/4 mb-3" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-5/6" />
        </div>
    )
}

// 参数骨架屏
export function PromptSkeleton() {
    return (
        <>
            <div className="p-6 border-b border-slate-200/50 dark:border-white/10">
                <div className="flex items-center gap-2 mb-4">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-5 w-24" />
                </div>
                <Skeleton className="h-24 w-full rounded-lg" />
            </div>

            <div className="p-6 border-b border-slate-200/50 dark:border-white/10">
                <div className="flex items-center gap-2 mb-4">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-5 w-36" />
                </div>
                <Skeleton className="h-16 w-full rounded-lg" />
            </div>

            <div className="p-6 border-b border-slate-200/50 dark:border-white/10">
                <div className="flex items-center gap-2 mb-4">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-5 w-48" />
                </div>
                <div className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {[...Array(4)].map((_, i) => (
                            <div key={i} className="flex flex-col gap-1">
                                <Skeleton className="h-4 w-16 mb-1" />
                                <Skeleton className="h-5 w-full" />
                            </div>
                        ))}
                    </div>
                    <Skeleton className="h-24 w-full rounded-lg" />
                </div>
            </div>
        </>
    )
}

// 评论骨架屏
export function CommentsSkeleton() {
    return (
        <div className="p-6">
            <div className="flex items-center gap-2 mb-5">
                <Skeleton className="h-5 w-5 rounded-full" />
                <Skeleton className="h-5 w-32" />
            </div>
            <div className="mb-8">
                <div className="flex gap-3 items-start">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <Skeleton className="flex-1 h-24 rounded-xl bg-gray-800/50" />
                </div>
            </div>
            <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex gap-3 p-4 rounded-lg bg-gray-900/40 backdrop-blur-sm">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div className="flex-1 space-y-2">
                            <div className="flex items-center gap-2">
                                <Skeleton className="h-4 w-24 bg-gray-800/70" />
                                <Skeleton className="h-3 w-16 bg-gray-800/50" />
                            </div>
                            <Skeleton className="h-12 w-full rounded-lg bg-gray-800/50" />
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}

// Generation Data 骨架屏
export function GenerationDataSkeleton() {
    return (
        <div className="p-6">
            <div className="flex items-center justify-between mb-4 pb-3 border-b border-gray-100 dark:border-gray-800">
                <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-5 rounded" />
                    <Skeleton className="h-5 w-28" />
                </div>
                <Skeleton className="h-4 w-20" />
            </div>

            {/* Action buttons */}
            <div className="flex gap-3 mb-5">
                <Skeleton className="h-10 w-2/3 rounded-md" />
                <Skeleton className="h-10 w-1/3 rounded-md" />
            </div>

            {/* Resources */}
            <div className="mb-5">
                <Skeleton className="h-4 w-24 mb-3" />
                <div className="space-y-2">
                    <div className="flex justify-between">
                        <Skeleton className="h-4 w-36" />
                        <Skeleton className="h-4 w-20" />
                    </div>
                    <div className="flex justify-between">
                        <Skeleton className="h-4 w-28" />
                        <div className="flex gap-2">
                            <Skeleton className="h-4 w-12" />
                            <Skeleton className="h-4 w-8" />
                        </div>
                    </div>
                </div>
            </div>

            {/* Prompt */}
            <div className="mb-5">
                <div className="flex justify-between mb-2">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-20" />
                </div>
                <Skeleton className="h-20 w-full rounded-md" />
            </div>

            {/* Negative prompt */}
            <div className="mb-5">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-16 w-full rounded-md" />
            </div>

            {/* Other metadata */}
            <div>
                <Skeleton className="h-4 w-28 mb-3" />
                <div className="grid grid-cols-2 gap-2">
                    <Skeleton className="h-8 w-full rounded-md" />
                    <Skeleton className="h-8 w-full rounded-md" />
                    <Skeleton className="h-8 w-full rounded-md" />
                    <Skeleton className="h-8 w-full rounded-md" />
                </div>
            </div>
        </div>
    )
}

// 右侧信息面板骨架屏
export function InfoPanelSkeleton() {
    return (
        <motion.div
            className="w-full lg:w-[350px] xl:w-[400px] bg-white dark:bg-gray-900 rounded-lg border border-gray-100 dark:border-gray-800 overflow-hidden shadow-sm h-full"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.25, delay: 0.1 }}
        >
            <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700 scrollbar-track-transparent">
                <AuthorInfoSkeleton />
                <GenerationDataSkeleton />
                <VideoDetailsSkeleton />
                <PromptSkeleton />
                <CommentsSkeleton />
            </div>
        </motion.div>
    )
}

// 完整骨架屏 - 支持分块加载
export function VideoDetailSkeleton() {
    return (
        <div className="h-full w-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-950 text-slate-900 dark:text-white relative overflow-hidden">
            {/* 背景装饰元素 */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/5 dark:bg-blue-500/10 rounded-full filter blur-3xl"></div>
                <div className="absolute bottom-1/4 right-1/3 w-80 h-80 bg-purple-500/5 dark:bg-purple-500/10 rounded-full filter blur-3xl"></div>
                <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-indigo-500/5 dark:bg-indigo-500/10 rounded-full filter blur-3xl"></div>
            </div>

            <motion.div
                className="w-full h-full px-2 sm:px-4 py-2 sm:py-4 relative z-10"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
            >
                <div className="flex flex-col lg:flex-row gap-2 sm:gap-4 h-full rounded-xl overflow-hidden">
                    {/* 影院式视频区域骨架屏 - 左侧 */}
                    <VideoAreaSkeleton />

                    {/* 右侧面板骨架屏 */}
                    <InfoPanelSkeleton />
                </div>
            </motion.div>
        </div>
    )
}