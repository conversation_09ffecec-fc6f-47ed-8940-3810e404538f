"use client"

import React, { ReactNode, useRef } from 'react';
import { Suspense } from "react";
import { TabSelector } from "../tab-selector";
import { ModelSelector } from "../model-selector";
import { EffectSelector } from "../effect-selector";
import { TabParamsHandler } from "../TabParamsHandler";
import { useControlPanel } from './ControlPanelContext';
import { ImageIcon, Video, RefreshCcw, Wand2 } from "lucide-react";
import { TABS } from '../../constants';

interface ModelTabContentProps {
  extraControls?: ReactNode;
}

export const ModelTabContent: React.FC<ModelTabContentProps> = ({ extraControls }) => {
  const {
    activeTab,
    setActiveTab,
    selectedModelId,
    setSelectedModelId,
    effects,
    selectedEffectName,
    handleEffectSelect,
    isLoadingEffects,
    models
  } = useControlPanel();

  // 根据模型类型过滤模型 - 使用useRef避免不必要的重新计算
  const filteredModelsRef = useRef<any[]>([]);
  const prevActiveTabRef = useRef<string>(activeTab);

  // 只有当models或activeTab变化时才重新计算
  if (prevActiveTabRef.current !== activeTab || !filteredModelsRef.current.length) {
    prevActiveTabRef.current = activeTab;

    // 如果是效果选项卡，不需要过滤
    if (activeTab === TABS.EFFECT) {
      filteredModelsRef.current = models;
    } else {
      // 根据选中的选项卡过滤模型
      filteredModelsRef.current = models.filter(model => {
        // 获取模型类型，优先使用type字段，如果没有则使用model_type字段并转为小写
        const modelType = (model.type || model.model_type?.toLowerCase() || '');
        return modelType === activeTab;
      });
    }
  }

  return (
    <div className="space-y-1 md:space-y-2">
      {/* URL 参数处理组件 */}
      <Suspense fallback={<div>Loading...</div>}>
        <TabParamsHandler onTabChange={setActiveTab} />
      </Suspense>

      {/* Tab选择器 - positioned on the left with history button on right for mobile */}
      <div className="flex justify-between items-center px-2">
        <div className="flex-grow mb-3 model-tab-selector bg-background/50 rounded-lg p-1">
      <TabSelector
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        tabs={[
              {
                id: TABS.IMAGE_TO_VIDEO,
                label: "Image to Video",
                shortLabel: "I2V",
                icon: <ImageIcon className="h-3.5 w-3.5" />
              },
              {
                id: TABS.TEXT_TO_VIDEO,
                label: "Text to Video",
                shortLabel: "T2V",
                icon: <Video className="h-3.5 w-3.5" />
              },
              {
                id: TABS.VIDEO_TO_VIDEO,
                label: "Video to Video",
                shortLabel: "V2V",
                icon: <RefreshCcw className="h-3.5 w-3.5" />
              },
              {
                id: TABS.EFFECT,
                label: "Effect",
                shortLabel: "FX",
                icon: <Wand2 className="h-3.5 w-3.5" />
              }
        ]}
            className="grid grid-cols-4 gap-1.5 w-full md:inline-flex md:space-x-1.5"
          />
        </div>

        {/* Extra controls (e.g. History button) for mobile view */}
        {extraControls && <div className="ml-2">{extraControls}</div>}
      </div>

      {/* 根据激活的Tab显示不同内容 */}
      <div className="pt-0">
        {activeTab === TABS.EFFECT ? (
        /* 效果选择器组件 */
        <EffectSelector
          effects={effects}
          selectedEffect={selectedEffectName}
          setSelectedEffect={handleEffectSelect}
          isLoading={isLoadingEffects}
        />
        ) : (
          /* 模型选择器组件 - 根据选中的选项卡过滤模型 */
          <ModelSelector
            selectedModelId={selectedModelId}
            setSelectedModelId={setSelectedModelId}
            filteredModels={filteredModelsRef.current}
            modelType={activeTab}
        />
      )}
      </div>
    </div>
  );
};
