import { Controller, Post, Body, Get, UseGuards, Query } from '@nestjs/common';
import { UserService } from './user.service';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { DeleteAccountDto, FavoriteItemDto, UpdateNsfwSettingDto, UpdateProfileDto, UserLinkDto, CheckFavoriteDto } from './dto/user.dto';
import { JwtGuard } from '../common/guards/jwt.guard';

@Controller('user')
export class UserController {
    constructor(
        private readonly userService: UserService,
    ) {}

    @Get('role')
    @UseGuards(JwtGuard)
    async getRole(@CurrentUser() user: any) {
        return this.userService.getRole(user.id);
    }

    /**
     * 获取用户个人资料
     */
    @Get('profile')
    @UseGuards(JwtGuard)
    async getUserProfile(@CurrentUser() user: any) {
        return this.userService.getUserProfile(user.id);
    }

    /**
     * 更新用户个人资料
     */
    @Post('profile/update')
    @UseGuards(JwtGuard)
    async updateProfile(@CurrentUser() user: any, @Body() updateProfileDto: UpdateProfileDto) {
        return this.userService.updateProfile(user.id, updateProfileDto);
    }

    /**
     * 更新用户头像
     */
    @Post('profile/avatar')
    @UseGuards(JwtGuard)
    async updateAvatar(@CurrentUser() user: any, @Body('avatarUrl') avatarUrl: string) {
        return this.userService.updateProfile(user.id, { avatar: avatarUrl });
    }

    /**
     * 更新用户链接
     */
    @Post('profile/links')
    @UseGuards(JwtGuard)
    async updateLinks(@CurrentUser() user: any, @Body('links') links: UserLinkDto[]) {
        return this.userService.updateProfile(user.id, { links });
    }

    /**
     * 更新NSFW设置
     */
    @Post('settings/nsfw')
    @UseGuards(JwtGuard)
    async updateNsfwSetting(@CurrentUser() user: any, @Body() updateNsfwSettingDto: UpdateNsfwSettingDto) {
        return this.userService.updateNsfwSetting(user.id, updateNsfwSettingDto);
    }

    /**
     * 注销账户
     */
    @Post('account/delete')
    @UseGuards(JwtGuard)
    async deleteAccount(@CurrentUser() user: any, @Body() deleteAccountDto: DeleteAccountDto) {
        return this.userService.deleteAccount(user.id, deleteAccountDto);
    }

    /**
     * 获取用户作品列表
     */
    @Get('creations')
    @UseGuards(JwtGuard)
    async getCreations(@CurrentUser() user: any) {
        return this.userService.getCreations(user.id);
    }

    /**
     * 获取用户模型列表
     */
    @Get('models')
    @UseGuards(JwtGuard)
    async getUserModels(@CurrentUser() user: any) {
        return this.userService.getUserModels(user.id);
    }

    /**
     * 获取用户收藏列表
     */
    @Get('favorites')
    @UseGuards(JwtGuard)
    async getFavorites(@CurrentUser() user: any, @Query('item_type') item_type: string) {
        return this.userService.getFavorites(user.id, item_type);
    }

    /**
     * 添加收藏
     */
    @Post('favorites/add')
    @UseGuards(JwtGuard)
    async addFavorite(@CurrentUser() user: any, @Body() favoriteItemDto: FavoriteItemDto) {
        return this.userService.addFavorite(user.id, favoriteItemDto);
    }

    /**
     * 取消收藏
     */
    @Post('favorites/remove')
    @UseGuards(JwtGuard)
    async removeFavorite(@CurrentUser() user: any, @Body() favoriteItemDto: FavoriteItemDto) {
        return this.userService.removeFavorite(user.id, favoriteItemDto);
    }

    /**
     * 检查是否已收藏
     */
    @Post('favorites/check')
    @UseGuards(JwtGuard)
    async checkFavorite(@CurrentUser() user: any, @Body() checkFavoriteDto: CheckFavoriteDto) {
        return this.userService.checkFavorite(user.id, checkFavoriteDto);
    }
}