import { Controller, Get, Post, Patch, Param, Body, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { PostService } from './post.service';
import { AdminGetPostsDto, UpdatePostVisibilityDto, UpdatePostNsfwLevelDto } from './dto/admin.dto';
import { Roles } from '../common/decorators/roles.decorator';
import { RolesGuard } from 'src/common/guards/roles.guard';

@Controller('posts/admin')
@UseGuards(RolesGuard)
@Roles('admin')
export class PostAdminController {
    constructor(private readonly postService: PostService) {}

    /**
     * 管理员获取所有帖子
     */
    @Post()
    async getAllPosts(@Body() adminGetPostsDto: AdminGetPostsDto) {
        return this.postService.getAdminPosts(adminGetPostsDto);
    }

    /**
     * 管理员获取帖子详情
     */
    @Get(':postId')
    async getPostDetails(@Param('postId', ParseUUIDPipe) postId: string) {
        return this.postService.getAdminPostDetails(postId);
    }

    /**
     * 管理员更新帖子可见性
     */
    @Patch(':postId/visibility')
    async updateVisibility(
        @Param('postId', ParseUUIDPipe) postId: string,
        @Body() updateVisibilityDto: UpdatePostVisibilityDto
    ) {
        return this.postService.updatePostVisibility(postId, updateVisibilityDto.visibility);
    }

    /**
     * 管理员更新帖子NSFW级别
     */
    @Patch(':postId/nsfw-level')
    async updateNsfwLevel(
        @Param('postId', ParseUUIDPipe) postId: string,
        @Body() updateNsfwLevelDto: UpdatePostNsfwLevelDto
    ) {
        return this.postService.updatePostNsfwLevel(postId, updateNsfwLevelDto.nsfw_level);
    }
} 