"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { CheckCircle, ArrowRight } from "lucide-react"
import useMembershipStore from "@/store/useMembershipStore"
import useCreditsStore from "@/store/useCreditsStore"
import { useQueryClient } from "@tanstack/react-query"

// 客户端组件，处理实际的支付成功逻辑
function PaymentSuccessContent() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const queryClient = useQueryClient()
    const [isProcessing, setIsProcessing] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [success, setSuccess] = useState(false)
    const [retryCount, setRetryCount] = useState(0)
    const [membershipDetails, setMembershipDetails] = useState<{
        levelName: string;
        expiresAt: string | null;
    } | null>(null)
    const [creditsDetails, setCreditsDetails] = useState<{
        amount: number | null;
    } | null>(null)

    const { userMembership, fetchUserMembership } = useMembershipStore()
    const { balance, fetchBalance } = useCreditsStore()

    // 从URL参数中获取支付类型
    const paymentType = searchParams?.get("type") || "membership"
    const paymentId = searchParams?.get("payment_id") || null

    useEffect(() => {
        const checkPaymentStatus = async () => {
            try {
                setIsProcessing(true)

                if (paymentType === "credits") {
                    // 积分购买成功逻辑
                    await fetchBalance()

                    if (balance !== null) {
                        // 积分余额已更新，表示购买成功
                        // 使 react-query 的积分余额缓存失效，确保其他组件显示最新余额
                        queryClient.invalidateQueries({ queryKey: ["credits", "balance"] });

                        setSuccess(true)
                        setCreditsDetails({
                            amount: balance
                        })
                    } else if (retryCount < 3) {
                        // 如果积分余额尚未更新且重试次数小于3，延迟2秒后重试
                        setTimeout(() => {
                            setRetryCount(prevCount => prevCount + 1)
                        }, 2000)
                        return
                    } else {
                        // 重试3次后仍未更新，显示错误
                        throw new Error('Processing...');
                    }
                } else {
                    // 会员购买成功逻辑
                    await fetchUserMembership();

                    // 检查会员是否已生效
                    if (userMembership && userMembership.is_active) {
                        // 会员已生效
                        setSuccess(true)
                        setMembershipDetails({
                            levelName: userMembership.plan_name,
                            expiresAt: userMembership.expires_at
                        })
                    } else if (retryCount < 3) {
                        // 如果会员尚未生效且重试次数小于3，延迟2秒后重试
                        setTimeout(() => {
                            setRetryCount(prevCount => prevCount + 1)
                        }, 2000)
                        return
                    } else {
                        // 重试3次后仍未生效，显示错误
                        throw new Error("Processing...");
                    }
                }
            } catch (err) {
                console.error("Payment processing failed", err)
                if (err instanceof Error) {
                    setError(err.message)
                } else {
                    setError("Payment processing failed, please contact customer support")
                }
            } finally {
                setIsProcessing(false)
            }
        }

        checkPaymentStatus()
    }, [retryCount, paymentType, paymentId])

    // 渲染积分购买成功内容
    const renderCreditsSuccess = () => (
        <>
            <div className="flex justify-center mb-6">
                <CheckCircle className="h-16 w-16 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold mb-4">Credit purchase successful!</h1>
            <p className="text-muted-foreground mb-4">
                Your credits have been successfully recharged, now you can use them to generate more videos.
            </p>
            <p className="text-sm text-primary-foreground mb-8">
                Current balance: {creditsDetails?.amount || "Loading..."}
            </p>
            <div className="space-y-3">
                <button
                    onClick={() => router.push("/credits")}
                    className="inline-flex items-center bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-md transition-colors w-full justify-center"
                >
                    View credit details
                    <ArrowRight className="ml-2 h-4 w-4" />
                </button>
                <button
                    onClick={() => router.push("/create")}
                    className="inline-flex items-center bg-muted hover:bg-muted/80 text-muted-foreground px-4 py-2 rounded-md transition-colors w-full justify-center"
                >
                    Generate video immediately
                </button>
            </div>
        </>
    )

    // 渲染会员购买成功内容
    const renderMembershipSuccess = () => (
        <>
            <div className="flex justify-center mb-6">
                <CheckCircle className="h-16 w-16 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold mb-4">Membership purchase successful!</h1>
            <p className="text-muted-foreground mb-4">
                Your {membershipDetails?.levelName || "membership"} has been successfully activated, now you can enjoy all membership privileges.
            </p>
            {membershipDetails?.expiresAt && (
                <p className="text-sm text-primary-foreground mb-8">
                    Expires on: {new Date(membershipDetails.expiresAt).toLocaleDateString()}
                </p>
            )}
            <button
                onClick={() => router.push("/membership")}
                className="inline-flex items-center bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-md transition-colors"
            >
                Return to membership center
                <ArrowRight className="ml-2 h-4 w-4" />
            </button>
        </>
    )

    return (
        <div className="bg-card rounded-xl border border-primary/30 shadow-lg p-8 max-w-md w-full text-center">
            {isProcessing ? (
                <div className="py-8">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-4 text-muted-foreground">
                        {retryCount > 0
                            ? `Checking ${paymentType === "credits" ? "credits" : "membership"} status (attempt ${retryCount})...`
                            : `Processing your ${paymentType === "credits" ? "credit purchase" : "membership activation"}...`}
                    </p>
                </div>
            ) : success ? (
                paymentType === "credits" ? renderCreditsSuccess() : renderMembershipSuccess()
            ) : (
                LoadingState()
            )}
        </div>
    )
}

// 加载中状态显示
function LoadingState() {
    return (
        <div className="bg-card rounded-xl border border-primary/30 shadow-lg p-8 max-w-md w-full text-center">
            <div className="py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-muted-foreground">Loading...</p>
            </div>
        </div>
    )
}

// 主页面组件
export default function PaymentSuccessPage() {
    return (
        <div className="min-h-screen bg-background text-foreground flex flex-col">
            <main className="flex-1 flex items-center justify-center p-6">
                <Suspense fallback={<LoadingState />}>
                    <PaymentSuccessContent />
                </Suspense>
            </main>
            <footer className="text-center py-4 text-muted-foreground text-sm border-t border-border">
                © {new Date().getFullYear()} ReelMind. All rights reserved.
            </footer>
        </div>
    )
} 