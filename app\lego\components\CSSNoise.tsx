"use client";

import React, { useEffect, useRef } from "react";

interface CSSNoiseProps {
    opacity?: number;
    animated?: boolean;
    intensity?: 'light' | 'medium' | 'heavy';
    type?: 'grain' | 'static' | 'film';
}

export default function CSSNoise({
    opacity = 0.6,
    animated = true,
    intensity = 'medium',
    type = 'grain'
}: CSSNoiseProps) {
    const noiseRef = useRef<HTMLDivElement>(null);

    // 对于复杂动画，可以使用JS增强效果
    useEffect(() => {
        if (animated && type === 'static' && noiseRef.current) {
            const interval = setInterval(() => {
                if (noiseRef.current) {
                    noiseRef.current.style.transform = `translate(${Math.random() * 2}px, ${Math.random() * 2}px)`;
                }
            }, 50);

            return () => clearInterval(interval);
        }
    }, [animated, type]);

    // 基于强度的不透明度调整
    const getIntensityOpacity = () => {
        switch (intensity) {
            case 'light': return 0.3 * opacity;
            case 'heavy': return 1.2 * opacity;
            default: return opacity;
        }
    };

    // 基于类型选择不同的噪点效果
    const renderNoiseEffect = () => {
        switch (type) {
            case 'static':
                return (
                    <div
                        ref={noiseRef}
                        className={`absolute inset-0 ${animated ? 'transition-transform duration-50' : ''}`}
                        style={{
                            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
                            backgroundSize: '150px 150px',
                            opacity: getIntensityOpacity(),
                            mixBlendMode: 'overlay',
                            pointerEvents: 'none'
                        }}
                    />
                );

            case 'film':
                return (
                    <>
                        {/* 胶片颗粒效果 */}
                        <div
                            className="absolute inset-0"
                            style={{
                                backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='filmGrain'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.55' numOctaves='5' stitchTiles='stitch'/%3E%3CfeColorMatrix type='saturate' values='0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23filmGrain)'/%3E%3C/svg%3E")`,
                                backgroundSize: '200% 200%',
                                opacity: getIntensityOpacity() * 0.7,
                                mixBlendMode: 'multiply',
                                pointerEvents: 'none'
                            }}
                        />

                        {/* 划痕效果 */}
                        {animated && (
                            <div
                                className="absolute inset-0 overflow-hidden"
                                style={{ pointerEvents: 'none' }}
                            >
                                <div
                                    className="absolute inset-0 animate-film-scratches"
                                    style={{
                                        backgroundImage: 'linear-gradient(90deg, transparent 98%, rgba(255,255,255,0.2) 98%, rgba(255,255,255,0.2) 98.1%, transparent 98.1%), linear-gradient(0deg, transparent 99.5%, rgba(255,255,255,0.3) 99.5%, rgba(255,255,255,0.3) 99.6%, transparent 99.6%)',
                                        backgroundSize: '300px 100%, 100% 300px',
                                        opacity: getIntensityOpacity() * 0.5,
                                        mixBlendMode: 'screen',
                                    }}
                                />
                            </div>
                        )}

                        {/* 胶片闪烁效果 */}
                        {animated && (
                            <div
                                className="absolute inset-0 animate-film-flicker"
                                style={{
                                    backgroundColor: 'rgba(0,0,0,0.03)',
                                    opacity: getIntensityOpacity() * 0.5,
                                    mixBlendMode: 'multiply',
                                    pointerEvents: 'none'
                                }}
                            />
                        )}
                    </>
                );

            case 'grain':
            default:
                return (
                    <>
                        {/* 基础噪点层 */}
                        <div
                            className="absolute inset-0"
                            style={{
                                backgroundImage: 'url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAEeElEQVR4nO2dS67cMAxEfbPZ/5azeRlkgByYEmVJpKqKDzci2/LjqCTb+vn9/f0H+Dj+vV0A2AchYghCxBCEiCEIEUMQIoYgRAxBiBiCEDEEIWIIQsQQhIghCBFDECKGIEQMQYgYghAxBCFiCELEEISIIQgRQxAihiBEDEGIGIIQMQQhYghCxPj36IumafroOVU/60Z/amZfa/TCs/vZPefMc0di5MVnC0IuyBjtkRXSTEhc5bmjAvj5+cme07OP1ndWCtsLShYLrFZBlb9H9tkrpRmJM4XTWx+ZkFVi5NnKXqmzDitO9p7R5x0OWTOde3V7zRTmdkgpxcgHOk6tF7NCFkLK6jMQspfJ3Q5ZtWK4GUpmh8bzQkj5bK2QNft6uhP9yHNnz7+yz1YobBVOx+lViJoQ5RDx9BaxQjZ7fzVktQqy1rdqyOrto5VwWiGkrPWtFrJ6vaqxQpILwrBC2KGpFVKy94oha2Z/JRfgcPiqkLJSSEkPWa3P3U5Za2EsW1BaHb5qKVpBSKuQR+dUXHdohayLPmqFrKflfbpCeu9Xrd5aXnlnd1dIRQghpbBdhJTCT/Z0hdyrZWHsQU9XyN3aevpH/4hLzs+rr5HEbp9vWKnQs785E7JG92cG04uZ+JwZ96jk/Lt8QzxS0BXBOdqWa/WC0JIQNSLEJ6lqQuD9EIQgBAZBCEIQggMIQQhCEIIACEEIQhCCAAhBCEIQggAIQQhCEHIRXiFunxCEIOSO2/I4i1XO4RkH+Uy+8Vz973tdFfMXYnfbWN2fWTDq8BUdFUXQlHGIRXCsVBw1IeVOc9tYHQQ1V8hTFbKyo6Lz7+a7YNYPx1iF7AlRMOIshyBklr0h67YQh2MQghCEIAQBEIKQJUIce8hTPdZJkIfTt1X+u1zO74I4Cqz9iFCG27ePK2TslDVSyNlQtnPX+vD9kOy0NdqWK9s2O33t7McqZK9C3EJcFTL9AiGEkJnrLBHiqpDZc5SFOJ81MsQrxClEPWSNhK1WYc+GrJGwlYW4QtbZeZZDSO8c3pZ3yc9hOStk9L7eHQ+5W8vCWP7/1ysDO89Ofh6yMg5BCByEEISU80aHrNlnzIaK1bC1W0jrvt7r7F5rppBHhax2jc2UNdvbT4as0R7auu6qkFKePxqydk5ZLSE9oa33jKutbN0lxBHWds+pnLJWKmQ0ZD0txPEzkFZhPyXEtYc4w1Z2jcohq9dv+wlY7xqOKes0niFtdo2KkK3bsnYKcfSSmZBVuj4tZJWuMztkld9dOlKIs7Bmw9ZsyLpDSGufswLLQtTDVmu/tG1h7GkhnuecLaRV29t9tmTPVBbium2srJDyM2aGTVVDVnmtHiE9//rU6jMr+mw7ZLVElO9V6bFPDVuqZKEqJSGl3Zz/WgH1+yHOQhz9rApyf46y5F9hVQpRjB1y+Lcs+BxBiBiCEDEEIWIIQsQQhIghCBFDECKGIEQMQYgYghAxBCFiCELEEISIIQgRQxAihiBEDEGIGIIQMQQhYghCxBCEiCEIEUMQIoYgRAxBiBiCEDH+AzBjuIH5QoSaAAAAAElFTkSuQmCC")',
                                backgroundSize: '100px 100px',
                                opacity: getIntensityOpacity() * 0.8,
                                mixBlendMode: 'multiply',
                                pointerEvents: 'none'
                            }}
                        />

                        {/* 细纹理层 */}
                        <div
                            className="absolute inset-0"
                            style={{
                                backgroundImage: 'radial-gradient(ellipse at center, rgba(255,255,255,0.2) 0%, rgba(0,0,0,0) 70%), repeating-linear-gradient(0deg, rgba(0,0,0,0.1), rgba(0,0,0,0.2) 1px, transparent 1px, transparent 4px)',
                                backgroundSize: '200% 200%, 4px 4px',
                                mixBlendMode: 'overlay',
                                filter: 'contrast(170%) brightness(100%)',
                                opacity: getIntensityOpacity() * 0.6,
                                pointerEvents: 'none'
                            }}
                        />

                        {/* 随机噪点动画层 */}
                        {animated && (
                            <div
                                className="absolute inset-0 animate-noise-shift"
                                style={{
                                    background: 'url("data:image/svg+xml,%3Csvg viewBox=\'0 0 512 512\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'noiseFilter\'%3E%3CfeTurbulence type=\'fractalNoise\' baseFrequency=\'0.75\' numOctaves=\'3\' stitchTiles=\'stitch\'/%3E%3C/filter%3E%3Crect width=\'100%25\' height=\'100%25\' filter=\'url(%23noiseFilter)\'/%3E%3C/svg%3E")',
                                    backgroundSize: '256px 256px',
                                    opacity: getIntensityOpacity() * 0.4,
                                    mixBlendMode: 'soft-light',
                                    pointerEvents: 'none'
                                }}
                            />
                        )}
                    </>
                );
        }
    };

    return (
        <div
            className="absolute inset-0 w-full h-full"
            style={{
                position: 'relative',
                overflow: 'hidden',
                pointerEvents: 'none'
            }}
        >
            <div className="absolute inset-0 bg-black/20" style={{ opacity: opacity / 2 }} />
            {renderNoiseEffect()}
        </div>
    );
} 