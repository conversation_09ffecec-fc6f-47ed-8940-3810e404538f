"use client";

import { QueryClient, QueryClientProvider, QueryCache } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { useState, type ReactNode } from "react";
import useAuthStore from "@/store/useAuthStore";

interface QueryProviderProps {
    children: ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
    // 获取 signOut 函数，注意：这里不能直接调用 hook，所以在 Provider 组件内部获取
    // 但是 QueryCache 的 onError 不能是异步的，直接调用 signOut 可能有问题
    // 更稳妥的方式是在这里获取 signOut，然后在 onError 中调用，
    // 或者 onError 触发一个全局事件/状态，由其他地方监听并执行 signOut
    // 这里我们先尝试直接调用，如果不行再调整
    const { signOut } = useAuthStore.getState(); // 直接从 store state 获取

    const [queryClient] = useState(
        () =>
            new QueryClient({
                queryCache: new QueryCache({
                    onError: (error: any, query) => {
                        // 检查错误是否为 401 未授权
                        // 注意：错误对象的结构可能需要根据实际 API 返回调整
                        if (error?.response?.status === 401 || error?.message?.includes('401')) {
                            console.error(`[QueryProvider] Caught 401 error for query ${query.queryKey}:`, error);
                            // 检查是否是因为 token 过期，避免在登录请求等本身就可能401的地方登出
                            // 修复 Linter 错误：比较 queryKey 的第一个元素
                            if (query.queryKey[0] !== 'loginQueryKey') { // 假设登录请求的 key 第一个元素是 'loginQueryKey'
                                console.log('[QueryProvider] Token likely expired or invalid, attempting sign out.');
                                // 执行登出操作
                                signOut().catch(signOutError => {
                                    console.error('[QueryProvider] Error during sign out after 401:', signOutError);
                                });
                            }
                        }
                    },
                }),
                defaultOptions: {
                    queries: {
                        staleTime: 60 * 1000, // 1 minute
                        refetchOnWindowFocus: false,
                        // 也可以在这里设置 onError，但 QueryCache 的 onError 更全局
                    },
                },
            })
    );

    return (
        <QueryClientProvider client={queryClient}>
            {children}
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
} 