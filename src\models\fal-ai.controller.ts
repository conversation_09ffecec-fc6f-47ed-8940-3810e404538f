import {
    Controller,
    Get,
    Query,
    Logger,
    HttpException,
    HttpStatus,
    UseGuards,
} from '@nestjs/common';
import { Roles } from 'src/common/decorators/roles.decorator';
import { RolesGuard } from 'src/common/guards/roles.guard';

export interface FalAiModel {
    id: string;
    title: string;
    category: string;
    shortDescription: string;
    thumbnailUrl: string;
    thumbnailAnimatedUrl?: string;
    modelUrl: string;
    licenseType: string;
    creditsRequired: number;
    group?: {
        key: string;
        label: string;
    };
    tags: string[];
    pricingInfoOverride?: string;
    durationEstimate?: number;
    pinned: boolean;
    highlighted: boolean;
    deprecated: boolean;
    kind: string;
    date: string;
    machineType?: string;
    examples: string[];
    description: string;
    streamUrl: string;
    minimumUnits?: number;
    authSkippable: boolean;
    unlisted: boolean;
    resultComparison: boolean;
    hidePricing: boolean;
    private: boolean;
    removed: boolean;
    adminOnly: boolean;
    trainingEndpoints: any[];
}

@Controller('admin/fal-ai')
@UseGuards(RolesGuard)
@Roles('admin')
export class FalAiController {
    private readonly logger = new Logger(FalAiController.name);

    /**
     * 获取fal.ai模型列表（管理员专用）
     * 作为中转接口，绕过浏览器跨域限制
     */
    @Get('models')
    async getFalAiModels(
        @Query('categories') categories?: string,
        @Query('tags') tags?: string,
        @Query('type') type?: string,
        @Query('deprecated') deprecated?: string,
        @Query('pendingEnterprise') pendingEnterprise?: string,
        @Query('keywords') keywords?: string,
        @Query('sort') sort?: string,
    ): Promise<FalAiModel[]> {
        try {
            // 构建查询参数
            const params = new URLSearchParams({
                categories: categories || '',
                tags: tags || '',
                type: type || '',
                deprecated: deprecated || 'false',
                pendingEnterprise: pendingEnterprise || 'false',
                keywords: keywords || 'video',
                sort: sort || 'relevant',
            });

            const url = `https://fal.ai/api/models?${params.toString()}`;
            
            this.logger.log(`正在获取fal.ai模型列表: ${url}`);

            // 使用fetch获取数据
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'ReelMind-Admin/1.0',
                },
                // 设置超时时间
                signal: AbortSignal.timeout(30000), // 30秒超时
            });

            if (!response.ok) {
                throw new HttpException(
                    `fal.ai API请求失败: ${response.status} ${response.statusText}`,
                    HttpStatus.BAD_GATEWAY
                );
            }

            const models: FalAiModel[] = await response.json();
            
            // 过滤出视频相关的模型
            const videoModels = models.filter((model) =>
                model.category === 'video-to-video' ||
                model.category === 'image-to-video' ||
                model.category === 'text-to-video'
            );

            this.logger.log(`成功获取${models.length}个模型，其中${videoModels.length}个视频模型`);

            return videoModels;

        } catch (error) {
            this.logger.error('获取fal.ai模型列表失败', {
                error: error.message,
                stack: error.stack,
            });

            if (error instanceof HttpException) {
                throw error;
            }

            // 处理网络错误或超时
            if (error.name === 'AbortError') {
                throw new HttpException(
                    'fal.ai API请求超时',
                    HttpStatus.REQUEST_TIMEOUT
                );
            }

            throw new HttpException(
                '获取fal.ai模型列表失败，请稍后重试',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 获取单个fal.ai模型详情
     */
    @Get('models/:modelId')
    async getFalAiModelById(
        @Query('modelId') modelId: string,
    ): Promise<FalAiModel> {
        try {
            if (!modelId) {
                throw new HttpException(
                    '模型ID不能为空',
                    HttpStatus.BAD_REQUEST
                );
            }

            // 先获取所有模型，然后找到指定的模型
            // 这是因为fal.ai没有提供单个模型的API端点
            const allModels = await this.getFalAiModels();
            const model = allModels.find(m => m.id === modelId);

            if (!model) {
                throw new HttpException(
                    `未找到模型: ${modelId}`,
                    HttpStatus.NOT_FOUND
                );
            }

            return model;

        } catch (error) {
            this.logger.error('获取fal.ai模型详情失败', {
                modelId,
                error: error.message,
                stack: error.stack,
            });

            if (error instanceof HttpException) {
                throw error;
            }

            throw new HttpException(
                '获取模型详情失败，请稍后重试',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 健康检查 - 测试fal.ai API连接
     */
    @Get('health')
    async healthCheck(): Promise<{ status: string; message: string; timestamp: string }> {
        try {
            const response = await fetch('https://fal.ai/api/models?limit=1', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'ReelMind-Admin/1.0',
                },
                signal: AbortSignal.timeout(10000), // 10秒超时
            });

            if (response.ok) {
                return {
                    status: 'healthy',
                    message: 'fal.ai API连接正常',
                    timestamp: new Date().toISOString(),
                };
            } else {
                return {
                    status: 'unhealthy',
                    message: `fal.ai API响应异常: ${response.status}`,
                    timestamp: new Date().toISOString(),
                };
            }

        } catch (error) {
            this.logger.error('fal.ai API健康检查失败', error);
            
            return {
                status: 'unhealthy',
                message: `fal.ai API连接失败: ${error.message}`,
                timestamp: new Date().toISOString(),
            };
        }
    }
}
