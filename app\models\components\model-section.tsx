import { ModelCard } from "./model-card"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight } from "lucide-react"
import type { Model } from "@/types/model"

interface ModelSectionProps {
    models?: Model[]
}

export function ModelSection({ models = [] }: ModelSectionProps) {
    const isLoading = false; // 服务端组件不需要loading状态

    return (
        <section className="space-y-6">
            {/* Section Header */}
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold tracking-tight">Models</h2>
                <Link href="/models/all">
                    <Button variant="ghost" className="gap-1">
                        View All <ArrowRight className="h-4 w-4" />
                    </Button>
                </Link>
            </div>

            {/* Models Grid */}
            {isLoading ? (
                <div className="flex justify-center py-8">
                    <div className="w-8 h-8 animate-spin text-gray-400" />
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
                    {models?.map((model) => (
                        <ModelCard key={model.id} model={model} />
                    ))}
                </div>
            )}
        </section>
    )
} 