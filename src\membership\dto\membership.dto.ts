import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, Min, IsBoolean, IsObject, IsArray } from 'class-validator';
import { MembershipLevel, BillingCycle } from '../constant';
import { InterestKey, InterestType } from '../membership-interest.service';

/**
 * 创建会员订阅请求DTO
 */
export class CreateMembershipDto {
    @IsUUID()
    @IsNotEmpty()
    plan_id: string;  // 会员计划ID

    @IsString()
    @IsNotEmpty()
    subscription_id: string;  // 订阅ID

    @IsString()
    @IsOptional()
    payment_id?: string;  // 支付ID
}

/**
 * 取消会员订阅请求DTO
 */
export class CancelMembershipDto {
    @IsString()
    @IsNotEmpty()
    subscription_id: string;  // Stripe订阅ID
}

/**
 * 会员信息响应DTO
 */
export class MembershipResponseDto {
    @IsUUID()
    id: string;

    @IsUUID()
    user_id: string;

    @IsUUID()
    @IsOptional()
    plan_id?: string;

    @IsString()
    plan_name: string;

    @IsEnum(MembershipLevel)
    level: MembershipLevel;

    level_name: string;

    is_active: boolean;

    started_at: Date;

    current_period_start: Date;

    current_period_end: Date;

    days_left: number;

    @IsString()
    @IsOptional()
    subscription_id?: string;

    @IsString()
    @IsOptional()
    subscription_status?: string;  // 订阅状态

    @IsString()
    @IsOptional()
    customer_id?: string;  // Stripe客户ID

    @IsBoolean()
    @IsOptional()
    cancel_at_period_end?: boolean;  // 是否在当前周期结束后取消
}

/**
 * 会员订阅详情DTO
 */
export class MembershipSubscriptionDto {
    @IsString()
    subscription_id: string;  // Stripe订阅ID

    @IsUUID()
    plan_id: string;

    @IsEnum(BillingCycle)
    billing_cycle: BillingCycle;  // 订阅周期 (month/year)

    @IsString()
    @IsOptional()
    customer_id?: string;  // Stripe客户ID

    current_period_start: Date;

    current_period_end: Date;

    @IsBoolean()
    cancel_at_period_end: boolean;  // 是否在当前周期结束后取消

    paymentId: string;
}

/**
 * 会员续费请求DTO
 */
export class RenewMembershipDto {
    @IsUUID()
    @IsNotEmpty()
    plan_id: string;  // 会员计划ID

    @IsNumber()
    @IsNotEmpty()
    @Min(1)
    months: number;  // 续费的月数

    @IsString()
    @IsOptional()
    payment_id?: string;  // 支付标识
}

/**
 * 更新会员订阅信息DTO
 */
export class UpdateMembershipSubscriptionDto {
    @IsUUID()
    @IsNotEmpty()
    plan_id: string;  // 会员计划ID

    @IsString()
    @IsNotEmpty()
    subscription_id: string;  // 订阅ID

    @IsString()
    @IsOptional()
    payment_id?: string;  // 支付标识
}

/**
 * 会员价格响应DTO
 */
export class MembershipPriceDto {
    @IsEnum(MembershipLevel)
    level: MembershipLevel;

    level_name: string;

    @IsNumber()
    monthly_price: number;  // 月付价格

    @IsNumber()
    yearly_price: number;   // 年付价格（按月计算）

    @IsBoolean()
    is_popular: boolean;    // 是否为推荐选项

    features: string[];     // 会员特权列表
}

/**
 * 会员权益请求DTO
 */
export class MembershipInterestDto {
    @IsUUID()
    @IsNotEmpty()
    plan_id: string;

    @IsEnum(InterestType)
    @IsNotEmpty()
    interest_type: InterestType;

    @IsEnum(InterestKey)
    @IsNotEmpty()
    interest_key: InterestKey;

    @IsNotEmpty()
    interest_value: any;

    @IsString()
    @IsOptional()
    description?: string;
}

/**
 * 会员权益响应DTO
 */
export class MembershipInterestResponseDto {
    @IsUUID()
    id: string;

    @IsUUID()
    plan_id: string;

    @IsString()
    interest_type: string;

    @IsString()
    interest_key: string;

    interest_value: any;

    @IsString()
    @IsOptional()
    description?: string;

    @IsBoolean()
    is_active: boolean;
}

/**
 * 会员权益查询DTO
 */
export class MembershipInterestQueryDto {
    @IsUUID()
    @IsOptional()
    plan_id?: string;

    @IsEnum(InterestType)
    @IsOptional()
    interest_type?: InterestType;

    @IsEnum(InterestKey)
    @IsOptional()
    interest_key?: InterestKey;

    @IsBoolean()
    @IsOptional()
    is_active?: boolean;
} 