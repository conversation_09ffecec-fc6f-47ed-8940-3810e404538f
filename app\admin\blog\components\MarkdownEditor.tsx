'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import MarkdownIt from 'markdown-it';
import 'react-markdown-editor-lite/lib/index.css';

// 动态导入编辑器组件，避免SSR问题
const MdEditor = dynamic(() => import('react-markdown-editor-lite'), {
    ssr: false,
});

// 初始化Markdown解析器
const mdParser = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
});

interface MarkdownEditorProps {
    value: string;
    onChange: (value: string) => void;
    height?: string;
    placeholder?: string;
}

export default function MarkdownEditor({ value, onChange, height = '400px', placeholder = 'Enter Markdown content...' }: MarkdownEditorProps) {
    // 使用一个本地状态来处理编辑器的值
    const [content, setContent] = useState(value || '');
    const [isUploading, setIsUploading] = useState(false);
    const [uploadStatus, setUploadStatus] = useState('');

    // 当外部value变化时更新内部状态
    useEffect(() => {
        setContent(value || '');
    }, [value]);

    // 处理编辑器内容变化
    const handleEditorChange = ({ text }: { text: string; html: string }) => {
        setContent(text);
        onChange(text);
    };

    // 处理图片上传
    const handleImageUpload = async (file: File): Promise<string> => {
        try {
            setIsUploading(true);
            setUploadStatus('Preparing to upload...');

            // 检查文件类型
            if (!file.type.startsWith("image/")) {
                throw new Error("Only image files are allowed");
            }

            // 检查文件大小 (10MB限制)
            const maxSize = 10 * 1024 * 1024;
            if (file.size > maxSize) {
                throw new Error("The image size cannot exceed 10MB");
            }

            // 获取预签名URL
            setUploadStatus('Getting upload permission...');
            const response = await fetch('/api/upload/presign', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filename: file.name,
                    fileType: file.type,
                    fileSize: file.size,
                    bucketName: 'blog-img'
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to get upload URL');
            }

            const { data } = await response.json();

            // 使用预签名URL上传文件到Cloudflare R2
            setUploadStatus('Uploading to cloud storage...');
            const uploadResponse = await fetch(data.url, {
                method: 'PUT',
                headers: {
                    'Content-Type': file.type,
                },
                body: file,
            });

            if (!uploadResponse.ok) {
                throw new Error('Upload image failed');
            }

            setUploadStatus('Upload complete!');
            console.log('Image upload successful:', data.publicUrl);

            // 返回Cloudflare R2的公共URL
            return data.publicUrl;
        } catch (error) {
            console.error('Error uploading image:', error);
            setUploadStatus('Upload failed');
            throw error;
        } finally {
            // 延迟清除上传状态，让用户能看到完成消息
            setTimeout(() => {
                setIsUploading(false);
                setUploadStatus('');
            }, 2000);
        }
    };

    return (
        <div className="markdown-editor">
            <style jsx global>{`
                /* 自定义编辑器样式 */
                .rc-md-editor {
                    height: ${height} !important;
                    border: 1px solid #e5e7eb !important;
                    border-radius: 0.5rem !important;
                }
                
                .dark .rc-md-editor {
                    border-color: #374151 !important;
                }
                
                .rc-md-editor .rc-md-navigation {
                    background-color: #f8f9fa !important;
                    border-bottom: 1px solid #e5e7eb !important;
                    padding: 6px !important;
                }
                
                .dark .rc-md-editor .rc-md-navigation {
                    background-color: #1f2937 !important;
                    border-bottom-color: #374151 !important;
                }
                
                .rc-md-editor .editor-container .sec-md .input {
                    background-color: #ffffff !important;
                    color: #111827 !important;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
                    font-size: 14px !important;
                    line-height: 1.6 !important;
                    padding: 12px !important;
                }
                
                .dark .rc-md-editor .editor-container .sec-md .input {
                    background-color: #111827 !important;
                    color: #e5e7eb !important;
                    border-right: 1px solid #374151 !important;
                }
                
                .rc-md-editor .editor-container .sec-html .html-wrap {
                    background-color: #f9fafb !important;
                    color: #111827 !important;
                    padding: 12px !important;
                    font-size: 14px !important;
                    line-height: 1.6 !important;
                }
                
                .dark .rc-md-editor .editor-container .sec-html .html-wrap {
                    background-color: #1a202c !important;
                    color: #e5e7eb !important;
                }
                
                /* 预览样式 */
                .rc-md-editor .editor-container .sec-html .html-wrap img {
                    max-width: 100%;
                }
                
                .rc-md-editor .editor-container .sec-html .html-wrap pre {
                    background-color: #f3f4f6;
                    padding: 1em;
                    border-radius: 0.25em;
                    overflow: auto;
                }
                
                .dark .rc-md-editor .editor-container .sec-html .html-wrap pre {
                    background-color: #374151;
                }

                /* Upload status message */
                .custom-upload-status {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    padding: 10px 15px;
                    background-color: #1a202c;
                    color: #ffffff;
                    border-radius: 4px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                    z-index: 1000;
                    animation: fade-in 0.3s ease-in-out;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                @keyframes fade-in {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
            `}</style>

            <MdEditor
                value={content}
                style={{ height }}
                renderHTML={(text) => mdParser.render(text)}
                onChange={handleEditorChange}
                placeholder={placeholder}
                htmlClass="markdown-preview"
                view={{ menu: true, md: true, html: true }}
                canView={{ menu: true, md: true, html: true, both: false, fullScreen: true, hideMenu: true }}
                imageAccept=".jpg,.jpeg,.png,.gif,.webp"
                onImageUpload={handleImageUpload}
                config={{
                    imageUrl: (uploadedUrl: string) => uploadedUrl, // Return directly without modifications
                }}
            />

            {isUploading && (
                <div className="custom-upload-status">
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {uploadStatus}
                </div>
            )}
        </div>
    );
} 