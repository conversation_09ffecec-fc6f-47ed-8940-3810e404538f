// Qwen AI API client integration

const DASHSCOPE_API_KEY = "sk-6e3d85e3b9ca4d93bc0fbbf0c35807aa";
const QWEN_API_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1";

/**
 * A client for interacting with the Qwen API using native fetch
 */
export const qwenClient = {
  /**
   * Search for information using Qwen API with internet search capability
   * @param prompt User prompt to search for
   * @returns Promise with search results
   */
  search: async (prompt: string): Promise<{ result: string }> => {
    try {
      const response = await fetch(`${QWEN_API_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${DASHSCOPE_API_KEY}`
        },
        body: JSON.stringify({
          model: "qwen-plus",
          messages: [
            {
              role: "system",
              content: "You are a helpful assistant with internet search capability. Search the internet to provide accurate and up-to-date information for the user's query."
            },
            { role: "user", content: prompt }
          ]
        })
      });
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      const data = await response.json();
      
      return {
        result: data.choices[0].message.content || "No results found."
      };
    } catch (error) {
      console.error('Error calling Qwen API:', error);
      
      // Return a friendly fallback response
      return {
        result: "I'll help you create this video project based on your requirements. Let's get started with the planning process."
      };
    }
  },

  /**
   * Stream a response from Qwen API with real-time output
   * @param prompt User prompt to process
   * @param onChunk Callback function for each chunk of streamed response
   * @returns Promise that resolves when streaming is complete
   */
  streamResponse: async (prompt: string, onChunk: (chunk: string) => void): Promise<string> => {
    try {
      const response = await fetch(`${QWEN_API_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${DASHSCOPE_API_KEY}`
        },
        body: JSON.stringify({
          model: "qwen-plus",
          messages: [
            {
              role: "system",
              content: "You are a creative AI assistant specialized in video production. Analyze the user's video project request and provide detailed planning, creative suggestions, and execution advice."
            },
            { role: "user", content: prompt }
          ],
          stream: true
        })
      });
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      // Process the streaming response
      const reader = response.body?.getReader();
      
      if (!reader) {
        throw new Error("Response body is not readable");
      }
      
      const decoder = new TextDecoder();
      let fullContent = "";
      
      // Read the stream
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }
        
        // Decode the chunk
        const chunk = decoder.decode(value, { stream: true });
        
        // Split the chunk by lines
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ') && !line.includes('[DONE]')) {
            try {
              const data = JSON.parse(line.substring(6));
              if (data.choices && data.choices.length > 0) {
                const content = data.choices[0].delta.content || "";
                if (content) {
                  fullContent += content;
                  onChunk(content);
                }
              }
            } catch (e) {
              console.error('Error parsing streaming response:', e);
            }
          }
        }
      }
      
      return fullContent;
    } catch (error) {
      console.error('Error streaming from Qwen API:', error);
      const errorMessage = "I encountered an issue while processing your request. Let's try a simpler approach to your video project.";
      onChunk(errorMessage);
      return errorMessage;
    }
  }
}; 