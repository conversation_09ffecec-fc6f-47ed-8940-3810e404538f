"use client"

import { ReactNode } from "react"
import { VideoModal } from "../app/posts/components/VideoModal"
import { useVideoModalStore } from "@/store/useVideoModalStore"

interface VideoModalProviderProps {
    children: ReactNode
}

export function VideoModalProvider({ children }: VideoModalProviderProps) {
    const { videoId, isOpen, closeModal } = useVideoModalStore()

    return (
        <>
            {children}
            <VideoModal
                videoId={videoId}
                isOpen={isOpen}
                onClose={closeModal}
            />
        </>
    )
} 