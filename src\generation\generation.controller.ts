import { Controller, Post, Get, Body, Param, Query, Req, UseGuards, HttpCode, ParseUUIDPipe, BadRequestException } from '@nestjs/common';
import { GenerationService } from './generation.service';
import { GenVideoRequestDto, TickProgressDto } from './dto/gen-video.dto';
import { VideoTaskDto, FinishTaskDto, VideoTaskResponseDto, PopTaskDto } from './dto/video-task.dto';
import { JwtGuard } from '../common/guards/jwt.guard';
import { Request } from 'express';
import { CancelTaskDto } from './dto/cancel.dto';
import { VideoTaskStatus } from 'src/common/constants/video';
import { QueueInfoResponseDto } from './dto/queue-info.dto';
import { EnhancePromptRequestDto, EnhancePromptResponseDto } from './dto/enhance-prompt.dto';
import { FalAiWebhookRequestDto, FalAiWebhookResponseDto } from './dto/fal-ai-webhook.dto';
import { TaskPriceRequestDto, TaskPriceResponseDto } from './dto/task-price.dto';

@Controller('generation')
export class GenerationController {
    constructor(private readonly generationService: GenerationService) {}

    /**
     * 创建视频生成任务
     * @param req 请求对象，包含用户信息
     * @param genVideoDto 视频生成参数
     */
    @Post('gen-video')
    @UseGuards(JwtGuard)
    async createVideoGenerationTask(
        @Req() req: Request,
        @Body() genVideoDto: GenVideoRequestDto,
    ): Promise<VideoTaskResponseDto> {
        const userId = req.user.id;
        if (!userId) {
            throw new BadRequestException('无效的用户信息');
        }

        return this.generationService.createVideoGenerationTask(userId, genVideoDto);
    }

    /**
     * 增强视频生成提示词
     * @param enhancePromptDto 原始提示词
     */
    @Post('enhance-prompt/video')
    @UseGuards(JwtGuard)
    async enhancePrompt(
        @Body() enhancePromptDto: EnhancePromptRequestDto,
    ): Promise<EnhancePromptResponseDto> {
        return this.generationService.enhancePrompt(enhancePromptDto);
    }

    /**
     * 获取用户的视频生成任务列表
     * @param req 请求对象，包含用户信息
     * @param status 可选的状态过滤
     * @param limit 分页限制
     * @param offset 分页偏移
     */
    @Get('user-tasks')
    @UseGuards(JwtGuard)
    async getUserTasks(
        @Req() req: Request,
        @Query('status') status?: VideoTaskStatus,
        @Query('limit') limit?: number,
        @Query('offset') offset?: number,
    ): Promise<{ tasks: VideoTaskDto[]; total: number }> {
        const userId = req.user.id;
        return this.generationService.getUserTasks(
            userId,
            status,
            limit ? parseInt(limit.toString(), 10) : undefined,
            offset ? parseInt(offset.toString(), 10) : undefined
        );
    }

    /**
     * 获取任务的队列信息
     * @param taskId 任务ID
     */
    @Get('task/queue-info/:taskId')
    @UseGuards(JwtGuard)
    async getTaskQueueInfo(
        @Param('taskId', ParseUUIDPipe) taskId: string,
    ): Promise<QueueInfoResponseDto> {
        return this.generationService.getQueueInfo(taskId);
    }

    /**
     * 获取任务详情
     * @param taskId 任务ID
     */
    @Get('task/:taskId')
    @UseGuards(JwtGuard)
    async getTaskById(
        @Param('taskId', ParseUUIDPipe) taskId: string,
    ): Promise<VideoTaskDto> {
        return this.generationService.getTaskById(taskId);
    }

    /**
     * 取消任务
     * @param req 请求对象，包含用户信息
     * @param taskId 任务ID
     */
    @Post('task/cancel')
    @UseGuards(JwtGuard)
    async cancelTask(
        @Req() req: Request,
        @Body() cancelTaskDto: CancelTaskDto,
    ): Promise<VideoTaskDto> {
        const userId = req.user.id;
        const { task_id } = cancelTaskDto;
        return this.generationService.cancelTask(task_id, userId);
    }

    /**
     * 处理fal.ai的webhook回调
     * @param webhookData webhook数据
     */
    @Post('webhook/fal-ai')
    async handleFalAiWebhook(
        @Body() webhookData: FalAiWebhookRequestDto
    ): Promise<FalAiWebhookResponseDto> {
        return this.generationService.handleFalWebhook(webhookData);
    }

    /**
     * 获取任务价格
     * @param taskPriceRequestDto 任务价格请求
     * @returns 任务价格信息
     */
    @Post('task/price')
    @UseGuards(JwtGuard)
    async getTaskPrice(
        @Body() taskPriceRequestDto: TaskPriceRequestDto
    ): Promise<TaskPriceResponseDto> {
        return this.generationService.getTaskPrice(taskPriceRequestDto);
    }

    // 以下接口由Python视频生成服务调用，需要单独的API密钥验证

    /**
     * 获取下一个待处理任务
     */
    @Post('gen_video_task/pop')
    @HttpCode(200)
    async popNextPendingTask(
        @Body() body: PopTaskDto,
    ): Promise<VideoTaskDto | null> {
        return this.generationService.popNextPendingTask(body);
    }

    /**
     * 完成视频生成任务
     * @param finishTaskDto 任务完成信息
     */
    @Post('gen_video_task/finish')
    @HttpCode(200)
    async finishVideoGenerationTask(
        @Body() finishTaskDto: FinishTaskDto,
    ): Promise<VideoTaskDto> {
        return this.generationService.finishVideoGenerationTask(finishTaskDto);
    }

    @Post('gen_video_task/tick-progress')
    @HttpCode(200)
    async tickProgress(
        @Body() body: TickProgressDto,
    ): Promise<boolean> {
        return this.generationService.tickProgress(body);
    }
}