import { Injectable } from '@nestjs/common';

/**
 * 邮箱验证服务
 * 用于检测和拦截可疑的机器人邮箱
 */
@Injectable()
export class EmailValidatorService {
  /**
   * 检测是否为可疑的机器人邮箱
   * 特征：
   * 1. 使用Gmail
   * 2. @符号前的邮箱地址有多个.符号隔开的字母组成
   * 
   * @param email 要检查的邮箱地址
   * @returns 如果是可疑邮箱返回true，否则返回false
   */
  isSuspiciousEmail(email: string): boolean {
    if (!email) return false;
    
    // 检查是否为Gmail
    if (!email.toLowerCase().endsWith('@gmail.com')) {
      return false;
    }
    
    // 获取@符号前的部分
    const localPart = email.split('@')[0];
    
    // 检查是否包含多个点号
    const dots = localPart.split('.').length - 1;
    if (dots < 2) {
      return false;
    }
    
    // 检查点号之间是否都是短字母组合（可能的机器人特征）
    const segments = localPart.split('.');
    const suspiciousPattern = segments.some(segment => segment.length <= 2);
    
    return suspiciousPattern;
  }

  /**
   * 获取邮箱验证结果
   * @param email 要检查的邮箱地址
   * @returns 验证结果对象
   */
  validateEmail(email: string): { 
    valid: boolean; 
    suspicious: boolean;
    message?: string;
  } {
    // 基本邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValidFormat = emailRegex.test(email);
    
    if (!isValidFormat) {
      return {
        valid: false,
        suspicious: false,
        message: '请输入有效的邮箱地址'
      };
    }
    
    // 检查是否为可疑的机器人邮箱
    const isSuspicious = this.isSuspiciousEmail(email);
    
    if (isSuspicious) {
      return {
        valid: false,
        suspicious: true,
        message: '此邮箱地址被识别为可能的自动注册账户，请使用其他邮箱'
      };
    }
    
    return {
      valid: true,
      suspicious: false
    };
  }
}
