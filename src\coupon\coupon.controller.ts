import { Controller, Get, Post, Body, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CouponService } from './coupon.service';
import {
    ClaimCouponDto,
    CouponResponseDto,
    CouponStatusResponseDto,
    CalculatePriceDto,
    PriceCalculationResponseDto,
} from './dto/coupon.dto';
import { JwtGuard } from 'src/common/guards/jwt.guard';

@ApiTags('优惠券')
@Controller('coupon')
@ApiBearerAuth()
export class CouponController {
    constructor(private readonly couponService: CouponService) {}

    @Get('status')
    @UseGuards(JwtGuard)
    @ApiOperation({ summary: '获取用户优惠券状态' })
    @ApiResponse({ status: 200, description: '成功获取优惠券状态', type: CouponStatusResponseDto })
    async getCouponStatus(@Req() req): Promise<CouponStatusResponseDto> {
        const userId = req.user?.id;

        return this.couponService.getCouponStatus(userId);
    }

    @Post('claim')
    @UseGuards(JwtGuard)
    @ApiOperation({ summary: '领取优惠券' })
    @ApiResponse({ status: 201, description: '成功领取优惠券' })
    async claimCoupon(@Req() req, @Body() claimCouponDto: ClaimCouponDto) {
        const userId = req.user?.id;

        return this.couponService.claimCoupon(userId, claimCouponDto);
    }

    @Get('user-coupons')
    @UseGuards(JwtGuard)
    @ApiOperation({ summary: '获取用户的优惠券列表' })
    @ApiResponse({ status: 200, description: '成功获取优惠券列表', type: [CouponResponseDto] })
    async getUserCoupons(@Req() req): Promise<CouponResponseDto[]> {
        const userId = req.user?.id;

        return this.couponService.getUserCoupons(userId);
    }

    @Post('calculate-price')
    @UseGuards(JwtGuard)
    @ApiOperation({ summary: '计算应用优惠券后的价格' })
    @ApiResponse({ status: 200, description: '成功计算价格', type: PriceCalculationResponseDto })
    async calculatePrice(@Req() req, @Body() calculatePriceDto: CalculatePriceDto): Promise<PriceCalculationResponseDto> {
        const userId = req.user?.id;
        return this.couponService.calculatePrice(userId, calculatePriceDto);
    }

    @Get('check-eligibility')
    @UseGuards(JwtGuard)
    async checkFirstMonthDiscountEligibility(@Req() req): Promise<{ is_eligible: boolean; message?: string }> {
        const userId = req.user?.id;
        const isEligible = await this.couponService.checkFirstMonthDiscountEligibility(userId);

        return {
            is_eligible: isEligible,
            message: isEligible ? '您可以领取首月7折优惠券' : '您已经购买过会员，不符合首月折扣条件'
        };
    }
}
