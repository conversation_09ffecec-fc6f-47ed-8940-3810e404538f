"use client"

import { PostItemDto } from "@/types/posts"
import { useEffect } from "react"
import { motion } from "framer-motion"
import { VideoJsonLd } from "./VideoJsonLd"
import { ClientVideoContainer } from "./VideoContainer"
import { ClientInfoPanel } from "./InfoPanel"

interface VideoDetailLayoutProps {
    post: PostItemDto
    comments: any[]
    loadingComments: boolean
    commentContent: string
    submittingComment: boolean
    setCommentContent: (content: string) => void
    submitComment: (postId: string, content: string) => void
    checkingFavorite?: boolean
    isModal?: boolean
}

export function VideoDetailLayout({
    post,
    comments,
    loadingComments,
    commentContent,
    submittingComment,
    setCommentContent,
    submitComment,
    checkingFavorite = false,
    isModal = false
}: VideoDetailLayoutProps) {
    // 添加滚动到顶部效果
    useEffect(() => {
        if (!isModal) {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            })
        }
    }, [isModal])

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                delayChildren: 0.1,
                staggerChildren: 0.1
            }
        }
    }

    return (
        <div className="w-full h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-950 text-slate-900 dark:text-white relative overflow-hidden">
            {/* 结构化数据 */}
            <VideoJsonLd post={post} />

            <motion.div
                className="w-full h-[calc(100vh-12px)] px-4 pt-4 relative z-10 overflow-auto"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
            >
                <div className="flex flex-col lg:flex-row gap-2 h-auto sm:h-full rounded-xl overflow-auto sm:overflow-hidden">
                    {/* 视频容器 */}
                    <ClientVideoContainer
                        post={post}
                        isModal={isModal}
                        checkingFavorite={checkingFavorite}
                    />

                    {/* 信息面板 */}
                    <ClientInfoPanel
                        post={post}
                        comments={comments}
                        loadingComments={loadingComments}
                        commentContent={commentContent}
                        submittingComment={submittingComment}
                        setCommentContent={setCommentContent}
                        submitComment={submitComment}
                        animate={true}
                    />
                </div>
            </motion.div>
        </div>
    )
} 