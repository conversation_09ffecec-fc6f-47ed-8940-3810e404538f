import { Button } from "@/components/ui/button";
import { ImageIcon, Video, RefreshCcw } from "lucide-react";
import { cn } from "@/lib/utils";
import { TABS } from "@/app/create/constants";

interface ModelTypeFilterProps {
    activeType: string;
    onTypeChange: (type: string) => void;
    className?: string;
}

const filterOptions = [
    {
        id: TABS.IMAGE_TO_VIDEO,
        label: "Image to Video",
        shortLabel: "I2V",
        icon: <ImageIcon className="h-3.5 w-3.5" />
    },
    {
        id: TABS.TEXT_TO_VIDEO,
        label: "Text to Video",
        shortLabel: "T2V",
        icon: <Video className="h-3.5 w-3.5" />
    },
    {
        id: TABS.VIDEO_TO_VIDEO,
        label: "Video to Video",
        shortLabel: "V2V",
        icon: <RefreshCcw className="h-3.5 w-3.5" />
    }
];

export function ModelTypeFilter({
    activeType,
    onTypeChange,
    className
}: ModelTypeFilterProps) {
    return (
        <div className={cn("flex flex-col space-y-4", className)}>
            <div className="flex items-center justify-between">
                <h4 className="text-sm font-semibold text-foreground/90 flex items-center space-x-2">
                    <div className="w-1 h-4 bg-blue-500 rounded-full" />
                    <span>Model Type</span>
                </h4>
                {activeType && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onTypeChange('')}
                        className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground transition-colors"
                    >
                        Clear
                    </Button>
                )}
            </div>

            <div className="grid grid-cols-3 gap-3">
                {filterOptions.map((option, index) => (
                    <Button
                        key={option.id}
                        variant="ghost"
                        size="sm"
                        onClick={() => onTypeChange(option.id)}
                        className={cn(
                            "group relative flex flex-col items-center justify-center h-20 p-3 text-xs",
                            "border-2 rounded-xl transition-all duration-300 ease-out",
                            "hover:scale-[1.02] hover:shadow-md",
                            // 非选中状态
                            activeType !== option.id && [
                                "border-border/30 bg-card/20 backdrop-blur-sm",
                                "hover:bg-card/40 hover:border-border/50",
                                "text-muted-foreground hover:text-foreground"
                            ],
                            // 选中状态 - 使用明显的蓝色主题
                            activeType === option.id && [
                                "border-blue-500 bg-blue-500/10 text-blue-600",
                                "shadow-lg shadow-blue-500/20",
                                "hover:bg-blue-500/15 hover:border-blue-600",
                                "dark:text-blue-400 dark:border-blue-400 dark:bg-blue-400/10",
                                "dark:shadow-blue-400/20 dark:hover:bg-blue-400/15"
                            ]
                        )}
                        style={{
                            animationDelay: `${index * 100}ms`,
                            animation: 'fadeInUp 0.5s ease-out forwards'
                        }}
                    >
                        {/* 选中状态指示器 */}
                        {activeType === option.id && (
                            <div className="absolute top-2 right-2 w-2 h-2 bg-blue-500 rounded-full animate-pulse dark:bg-blue-400" />
                        )}

                        {/* 图标 */}
                        <div className={cn(
                            "mb-2 p-2 rounded-lg transition-all duration-200",
                            "group-hover:scale-110",
                            activeType === option.id
                                ? "bg-blue-500/20 text-blue-600 dark:bg-blue-400/20 dark:text-blue-400"
                                : "bg-muted/50 text-muted-foreground group-hover:bg-muted/70 group-hover:text-foreground"
                        )}>
                            {option.icon}
                        </div>

                        {/* 标签 */}
                        <span className={cn(
                            "hidden sm:block text-center leading-tight font-medium",
                            "transition-colors duration-200"
                        )}>
                            {option.label}
                        </span>
                        <span className={cn(
                            "sm:hidden text-center leading-tight font-medium",
                            "transition-colors duration-200"
                        )}>
                            {option.shortLabel}
                        </span>

                        {/* 选中状态的额外视觉效果 */}
                        {activeType === option.id && (
                            <div className="absolute inset-0 rounded-xl bg-gradient-to-t from-blue-500/10 to-transparent pointer-events-none" />
                        )}

                        {/* 悬停效果 */}
                        <div className="absolute inset-0 rounded-xl bg-gradient-to-t from-foreground/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                    </Button>
                ))}
            </div>
        </div>
    );
}
