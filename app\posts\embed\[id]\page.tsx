import { Metadata } from "next";
import { postApi } from "@/lib/api/post";
import { getVideoRatio } from "../../utils/videoUtils";
import { JsonLd } from "@/components/seo/JsonLd";

// 为嵌入页面生成元数据
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
    // 确保先await params
    const resolvedParams = await Promise.resolve(params);
    const videoId = resolvedParams.id;

    try {
        const { data: post } = await postApi.getPost(videoId);

        return {
            title: `${post.title || 'Video'} | ReelMind Embed`,
            openGraph: {
                title: post.title,
                description: post.description || "Embedded video from ReelMind",
                type: "video.other",
                url: `https://reelmind.ai/posts/embed/${videoId}`,
                images: post.videos?.cover_img ? [post.videos?.cover_img] : [],
                videos: post.video_url ? [{ url: post.video_url }] : undefined,
            },
            robots: {
                index: true,
                follow: true,
            },
        };
    } catch (error) {
        console.error("Failed to fetch video for embed:", error);
        return {
            title: "Video Embed | ReelMind",
            description: "Embedded video from ReelMind",
        };
    }
}

export default async function EmbedPage({ params }: { params: { id: string } }) {
    // 确保先await params
    const resolvedParams = await Promise.resolve(params);
    const videoId = resolvedParams.id;

    try {
        const { data: post } = await postApi.getPost(videoId);

        // 嵌入页面的JSON-LD结构化数据
        const videoJsonLd = {
            "@context": "https://schema.org",
            "@type": "VideoObject",
            "name": post.title || `AI Generated Video #${post.id}`,
            "description": post.description || "AI generated video from ReelMind",
            "thumbnailUrl": post.videos?.cover_img || post.video_url,
            "contentUrl": post.video_url,
            "embedUrl": `https://reelmind.ai/posts/embed/${videoId}`,
            "uploadDate": post.created_at,
            "author": {
                "@type": "Person",
                "name": post.username
            }
        };

        return (
            <div className="w-full h-screen bg-black flex items-center justify-center overflow-hidden">
                {/* SEO增强 */}
                <JsonLd data={videoJsonLd} />

                {/* 视频元素的静态容器 */}
                <div className="w-full h-full max-w-5xl mx-auto relative">
                    {/* 直接使用视频元素，而不是ServerVideoPlayer */}
                    <div className={getVideoRatio(post) === "16:9"
                        ? "w-full h-full flex items-center justify-center"
                        : getVideoRatio(post) === "9:16"
                            ? "h-full w-auto flex items-center justify-center"
                            : "w-full h-full flex items-center justify-center"}>

                        {/* 视频预览图 */}
                        {post.videos?.cover_img && (
                            <div className="absolute inset-0 z-0">
                                <img
                                    src={post.videos?.cover_img}
                                    alt={post.title || "Video preview"}
                                    className="w-full h-full object-cover"
                                    loading="eager"
                                />
                            </div>
                        )}

                        {/* 视频元素 */}
                        <div className="relative z-10 w-full h-full">
                            <video
                                id="embed-video-player"
                                src={post.video_url}
                                className="w-full h-full object-contain"
                                playsInline
                                muted
                                preload="metadata"
                                poster={post.videos?.cover_img || ''}
                                controls={true}
                                controlsList="nodownload"
                                aria-label={post.title || `Video by ${post.username}`}
                                loop
                            />
                        </div>
                    </div>

                    {/* 添加水印和品牌信息 */}
                    <div className="absolute bottom-4 right-4 bg-black/40 backdrop-blur-sm px-3 py-1.5 rounded-lg text-white text-xs flex items-center">
                        <span>Powered by</span>
                        <span className="font-bold ml-1">ReelMind.ai</span>
                    </div>
                </div>

                {/* 简化的客户端脚本 */}
                <script
                    dangerouslySetInnerHTML={{
                        __html: `
                            document.addEventListener('DOMContentLoaded', function() {
                                const video = document.getElementById('embed-video-player');
                                if (video) {
                                    // 为移动设备添加额外点击处理
                                    video.addEventListener('click', function(e) {
                                        if (e.target === video) {
                                            if (video.paused) video.play();
                                            else video.pause();
                                        }
                                    });
                                    
                                    // 尝试自动播放
                                    video.muted = true;
                                    setTimeout(() => {
                                        video.play().catch(function(e) {
                                            console.log('Autoplay prevented:', e);
                                        });
                                    }, 1000);
                                }
                            });
                        `
                    }}
                />
            </div>
        );
    } catch (error) {
        console.error("Failed to load video for embed:", error);
        return (
            <div className="w-full h-screen bg-gray-900 flex items-center justify-center text-white">
                <div className="text-center">
                    <h1 className="text-xl mb-2">Video not found</h1>
                    <p className="text-gray-400">The requested video could not be loaded</p>
                </div>
            </div>
        );
    }
} 