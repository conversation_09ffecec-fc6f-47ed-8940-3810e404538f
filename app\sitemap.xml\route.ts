import { NextResponse } from 'next/server';
import { getBlogPostsCount } from '@/lib/blog';

const postsPerSitemap = 40000;

// 生成sitemap索引XML
function generateSitemapIndexXML(entries: Array<{
    url: string;
    lastModified: Date;
}>): string {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

    for (const entry of entries) {
        xml += '  <sitemap>\n';
        xml += `    <loc>${entry.url}</loc>\n`;
        xml += `    <lastmod>${entry.lastModified.toISOString()}</lastmod>\n`;
        xml += '  </sitemap>\n';
    }

    xml += '</sitemapindex>';
    return xml;
}

// 处理sitemap请求
export async function GET() {
    try {
        // 网站基本URL
        const baseUrl = 'https://reelmind.ai';

        const totalPosts = await getBlogPostsCount();

        // 添加博客子sitemap的引用 - 使用动态计算的方式
        // 使用专门的计数函数获取博客文章总数
        const numberOfBlogSitemaps = Math.ceil(totalPosts / postsPerSitemap);

        // 记录博客文章总数和子sitemap数量
        console.log(`博客文章总数: ${totalPosts}, 子sitemap数量: ${numberOfBlogSitemaps}`);

        // 生成博客子sitemap的引用 - 使用sitemap标签格式
        const blogSitemapReferences = Array.from({ length: numberOfBlogSitemaps }, (_, i) => ({
            url: `${baseUrl}/blog/sitemap/${i}`,
            lastModified: new Date(),
        }));

        // 生成sitemap索引XML，包含主站点地图和博客子sitemap
        const mainSitemapUrl = `${baseUrl}/layout.xml`;
        const sitemapIndexEntries = [
            {
                url: mainSitemapUrl,
                lastModified: new Date(),
            },
            ...blogSitemapReferences,
        ];

        const sitemapIndexXML = generateSitemapIndexXML(sitemapIndexEntries);

        // 返回XML响应
        return new NextResponse(sitemapIndexXML, {
            headers: {
                'Content-Type': 'application/xml',
                'Content-Length': Buffer.byteLength(sitemapIndexXML).toString(),
            },
        });
    } catch (error) {
        console.error(`生成Sitemap索引时出错:`, error);
        return NextResponse.json(
            { error: '生成Sitemap时出错' },
            { status: 500 }
        );
    }
}
