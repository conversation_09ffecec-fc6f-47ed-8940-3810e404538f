import {
    IsString,
    IsNotEmpty,
    IsEnum,
    Length,
    Matches,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform as TransformDecorator } from 'class-transformer';

// 反馈类型枚举 - 简化为3种
export enum FeedbackType {
    BUG = 'bug',
    SUGGESTION = 'suggestion',
    OTHER = 'other'
}

// Security validation patterns
const NO_SCRIPT_PATTERN = /^(?!.*<script|.*javascript:|.*vbscript:|.*on\w+\s*=).*$/i;

// 创建反馈DTO - 增强安全验证
export class CreateFeedbackDto {
    @ApiProperty({
        description: '反馈类型',
        enum: FeedbackType,
        example: FeedbackType.BUG
    })
    @IsEnum(FeedbackType, { message: 'Invalid feedback type' })
    @IsNotEmpty({ message: 'Feedback type is required' })
    type: FeedbackType;

    @ApiProperty({
        description: '反馈主题',
        example: 'Video generation failed',
        minLength: 3,
        maxLength: 200
    })
    @IsString({ message: 'Subject must be a string' })
    @IsNotEmpty({ message: 'Subject is required' })
    @Length(3, 200, { message: 'Subject must be between 3 and 200 characters' })
    @Matches(NO_SCRIPT_PATTERN, { message: 'Subject contains invalid content' })
    @TransformDecorator(({ value }) => typeof value === 'string' ? value.trim() : value)
    subject: string;

    @ApiProperty({
        description: '反馈内容',
        example: 'When I try to generate a video, it fails with an error message.',
        minLength: 5,
        maxLength: 2000
    })
    @IsString({ message: 'Message must be a string' })
    @IsNotEmpty({ message: 'Message is required' })
    @Length(5, 2000, { message: 'Message must be between 5 and 2000 characters' })
    @Matches(NO_SCRIPT_PATTERN, { message: 'Message contains invalid content' })
    @TransformDecorator(({ value }) => typeof value === 'string' ? value.trim() : value)
    message: string;
}

// 反馈响应DTO - 超级简化
export class FeedbackResponseDto {
    @ApiProperty({ description: '反馈ID' })
    id: string;

    @ApiProperty({ description: '反馈类型', enum: FeedbackType })
    type: FeedbackType;

    @ApiProperty({ description: '反馈主题' })
    subject: string;

    @ApiProperty({ description: '反馈内容' })
    message: string;

    @ApiProperty({ description: '创建时间' })
    created_at: string;
}
