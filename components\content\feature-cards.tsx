import Link from "next/link"
import { cn } from "@/lib/utils"

// 特性卡片的属性接口
interface FeatureCardProps {
    title: string
    description: string
    icon: React.ReactNode
    href: string
    bgClass?: string
    isFree?: boolean
    modelCount?: number
    features?: string[]
    highlights?: string[]
}

// 单个特性卡片组件 - 强化版本
export function FeatureCard({
    title,
    description,
    icon,
    href,
    bgClass = "bg-gray-900",
    isFree = false,
    modelCount,
    features,
    highlights,
}: FeatureCardProps) {
    return (
        <Link href={href} className="block group">
            <div className={cn(
                "rounded-lg relative overflow-hidden transition-all duration-500 group-hover:translate-y-[-4px] group-hover:shadow-xl border border-gray-700/50 group-hover:border-gray-600 h-24",
                bgClass
            )}>
                {/* 背景渐变效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/30 z-0"></div>

                {/* 悬停光效效果 */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-10 bg-white transition-all duration-500 z-1"></div>

                {/* 动态光波效果 */}
                <div className="absolute inset-0 overflow-hidden">
                    <div
                        className="absolute top-0 left-[-100%] w-[50%] h-full bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-[-20deg] group-hover:animate-shimmer opacity-0 group-hover:opacity-100"
                    ></div>
                </div>

                {/* 模型数量徽章或FREE徽章 */}
                    <div className="absolute top-0 right-0 z-10">
                        <div className="relative">
                            {/* 徽章背景 */}
                        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-3 py-1 rounded-bl-lg shadow-lg relative overflow-hidden">
                            {/* 闪光效果 */}
                                <div
                                    className="absolute top-0 left-[-100%] w-[200%] h-full bg-gradient-to-r from-transparent via-white/40 to-transparent skew-x-[-15deg] animate-shimmer"
                                ></div>

                                {/* 徽章文字 */}
                                <div className="flex items-center justify-center relative">
                                {modelCount ? (
                                    <>
                                        <span className="text-white font-semibold text-sm mr-1">{modelCount}+</span>
                                        <span className="text-white/90 text-xs">Models</span>
                                    </>
                                ) : isFree ? (
                                    <span className="text-white font-semibold text-sm">FREE</span>
                                ) : null}
                            </div>
                        </div>
                    </div>
                </div>

                {/* 卡片内容 */}
                <div className="p-6 flex items-center relative z-2 h-full">
                    {/* 内容 */}
                    <div className="flex items-center gap-4 w-full">
                        <div className="text-white bg-white/10 p-2.5 rounded-lg backdrop-blur-sm flex-shrink-0">
                            {icon}
                        </div>
                        <div className="flex-1 min-w-0">
                            <h3 className="text-xl font-medium text-white group-hover:text-white/95 transition-colors truncate">{title}</h3>
                            <p className="text-sm text-gray-300 group-hover:text-white transition-colors truncate">{description}</p>
                        </div>
                    </div>
                </div>

                {/* 卡片底部边缘发光效果 */}
                <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
        </Link>
    )
}

// 特性卡片区域组件 - 优化为首屏关键内容
export function FeatureCards() {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
            {/* 优化为首屏关键内容 */}
            <FeatureCard
                title="Create AI Videos"
                description="Generate professional videos with cutting-edge AI models"
                icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2-2v8a2 2 0 002 2z" />
                    </svg>
                }
                href="/create"
                bgClass="bg-gradient-to-br from-gray-800 to-gray-900"
                modelCount={100}
            />
            <FeatureCard
                title="Create Key Frame Image"
                description="Design stunning visuals with advanced AI image generation"
                icon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                }
                href="/lego"
                bgClass="bg-gradient-to-br from-slate-800 to-slate-900"
                isFree={true}
            />
        </div>
    )
}