-- 创建帖子统计表
CREATE TABLE IF NOT EXISTS public.post_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID NOT NULL,
  view_count INTEGER NOT NULL DEFAULT 0,
  click_count INTEGER NOT NULL DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  CONSTRAINT fk_post
    FOREIGN KEY(post_id)
    REFERENCES public.posts(id)
    ON DELETE CASCADE
);

-- 创建唯一索引确保每个帖子只有一条统计记录
CREATE UNIQUE INDEX IF NOT EXISTS post_stats_post_id_idx ON public.post_stats (post_id);

-- 创建帖子统计日志表，用于记录详细的统计事件
CREATE TABLE IF NOT EXISTS public.post_stats_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID NOT NULL,
  user_id UUID,  -- 可以为空，表示匿名用户
  event_type TEXT NOT NULL, -- 'view' 或 'click'
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  client_info JSONB NOT NULL DEFAULT '{}'::jsonb, -- 存储客户端信息，如设备类型、浏览器等
  
  CONSTRAINT fk_post
    FOREIGN KEY(post_id)
    REFERENCES public.posts(id)
    ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS post_stats_logs_post_id_idx ON public.post_stats_logs (post_id);
CREATE INDEX IF NOT EXISTS post_stats_logs_user_id_idx ON public.post_stats_logs (user_id);
CREATE INDEX IF NOT EXISTS post_stats_logs_event_type_idx ON public.post_stats_logs (event_type);
CREATE INDEX IF NOT EXISTS post_stats_logs_created_at_idx ON public.post_stats_logs (created_at);

-- 创建更新帖子统计的函数
CREATE OR REPLACE FUNCTION update_post_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- 根据事件类型更新统计数据
  IF NEW.event_type = 'view' THEN
    INSERT INTO public.post_stats (post_id, view_count, click_count)
    VALUES (NEW.post_id, 1, 0)
    ON CONFLICT (post_id) 
    DO UPDATE SET 
      view_count = post_stats.view_count + 1,
      last_updated = now();
  ELSIF NEW.event_type = 'click' THEN
    INSERT INTO public.post_stats (post_id, view_count, click_count)
    VALUES (NEW.post_id, 0, 1)
    ON CONFLICT (post_id) 
    DO UPDATE SET 
      click_count = post_stats.click_count + 1,
      last_updated = now();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器，当插入新的统计日志时自动更新统计数据
CREATE TRIGGER trigger_update_post_stats
AFTER INSERT ON public.post_stats_logs
FOR EACH ROW
EXECUTE FUNCTION update_post_stats();
