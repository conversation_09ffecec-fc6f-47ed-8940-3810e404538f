"use client";

import { useEffect, useState, useRef } from "react";
import { useLegoStore } from "../store";
import { getQueueInfo, getTaskDetail } from "../api";
import { Progress } from "@/components/ui/progress";

export default function TaskProgressTracker() {
    const { currentTaskId, setCurrentTaskId, setQueueInfo, updateHistoryItem, setIsGenerating } = useLegoStore();
    const [progress, setProgress] = useState(0);
    const [status, setStatus] = useState<"pending" | "processing" | "completed">("pending");
    const [startedAt, setStartedAt] = useState<Date | null>(null);
    const [elapsedTime, setElapsedTime] = useState(0);

    // 使用ref记录上一次处理的任务ID，以便检测任务变化
    const prevTaskIdRef = useRef<string | null>(null);

    // 估计处理时间（秒）
    const estimatedProcessingTime = 60; // 约1分钟

    // 使用ref来存储轮询ID，避免在状态更新时重置轮询
    const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
    // 使用ref来存储开始时间，避免在状态更新时丢失
    const startedTimeRef = useRef<Date | null>(null);

    useEffect(() => {
        // 当任务ID发生变化时，重置所有状态
        if (currentTaskId !== prevTaskIdRef.current) {
            console.log("任务ID变化，重置状态:", prevTaskIdRef.current, "->", currentTaskId);
            setProgress(0);
            setStatus("pending");
            setStartedAt(null);
            setElapsedTime(0);
            prevTaskIdRef.current = currentTaskId;

            // 清除之前的轮询
            if (intervalIdRef.current) {
                clearInterval(intervalIdRef.current);
                intervalIdRef.current = null;
            }

            // 重置开始时间
            startedTimeRef.current = null;
        }

        if (!currentTaskId) {
            return;
        }

        let startedTime: Date | null = null;

        // 检查队列状态
        const checkQueueStatus = async () => {
            // 增加保护，如果 currentTaskId 为 null，则不执行任何操作
            if (!currentTaskId) return;

            try {
                // 获取队列信息
                const queueInfo = await getQueueInfo(currentTaskId);
                setQueueInfo(queueInfo);

                // 基于API返回的 queueInfo 更新状态，而不是依赖本地旧的 status state
                if (queueInfo.position > 0) {
                    // 任务在队列中等待
                    setStatus("pending");
                    setProgress(Math.max(5, Math.min(30, 30 - queueInfo.position * 3)));
                    updateHistoryItem(currentTaskId, { status: "pending" });
                } else if (queueInfo.isProcessing) {
                    // 任务正在处理中
                    setStatus("processing");
                    updateHistoryItem(currentTaskId, { status: "processing" });

                    // 尝试获取或设置 startedAt 时间
                    if (!startedAt) {
                        try {
                            const taskDetail = await getTaskDetail(currentTaskId);
                            if (taskDetail && taskDetail.started_at) {
                                const startTime = new Date(taskDetail.started_at);
                                setStartedAt(startTime);
                                startedTime = startTime; // 更新局部变量以供首次进度计算
                                const now = new Date();
                                setElapsedTime((now.getTime() - startTime.getTime()) / 1000);
                            } else {
                                const now = new Date();
                                setStartedAt(now);
                                startedTime = now;
                                setElapsedTime(0);
                                // 立即更新历史记录中的 started_at
                                updateHistoryItem(currentTaskId, { started_at: now });
                            }
                        } catch (error) {
                            console.error("Failed to get task detail for started_at:", error);
                            const now = new Date();
                            setStartedAt(now);
                            startedTime = now;
                            setElapsedTime(0);
                            updateHistoryItem(currentTaskId, { started_at: now });
                        }
                    } else {
                        // 如果 startedAt 已存在，仅更新 elapsedTime
                        const now = new Date();
                        setElapsedTime((now.getTime() - startedAt.getTime()) / 1000);
                    }

                    // 计算并设置进度
                    calculateProgress(startedTime || startedAt); // 传递确定的开始时间

                    // 如果超过了估计时间，定期检查完成状态
                    if (elapsedTime > estimatedProcessingTime && progress >= 99) {
                        checkCompletionStatus();
                    }
                } else if (queueInfo.result && queueInfo.result.imageUrl) {
                    // 任务已完成，有结果
                    handleTaskCompletion(queueInfo.result.imageUrl);
                } else {
                    // 任务可能已完成但 checkQueueStatus 没拿到结果，或处于未知状态，尝试获取详情
                    checkCompletionStatus();
                }
            } catch (error) {
                console.error("Failed to check queue status:", error);
                // 错误处理：可以考虑设置一个错误状态或重试
            }
        };

        // 计算并设置进度
        const calculateProgress = (startTime: Date | null) => {
            if (startTime) {
                const now = new Date();
                const elapsedSeconds = (now.getTime() - startTime.getTime()) / 1000;

                let calculatedProgress;

                // 如果已经超过了估计时间，显示99%
                if (elapsedSeconds > estimatedProcessingTime) {
                    calculatedProgress = 99;
                } else {
                    // 在估计时间范围内的进度计算
                    // 队列中已经占了0-30%的进度，处理时占剩下的70%
                    calculatedProgress = Math.min(99, 30 + (elapsedSeconds / estimatedProcessingTime) * 70);
                }
                setProgress(calculatedProgress);
            } else if (status === 'processing') {
                // 如果是 processing 状态但没有 startTime，使用递增进度作为后备
                setProgress(prev => Math.min(99, prev + 0.5));
            } // 如果是 pending 状态，进度已在 checkQueueStatus 中设置
        };

        // 检查任务是否已完成
        const checkCompletionStatus = async () => {
            try {
                const taskDetail = await getTaskDetail(currentTaskId);
                if (taskDetail && taskDetail.status === "completed" && taskDetail.output_result?.image_url) {
                    handleTaskCompletion(taskDetail.output_result.image_url);
                }
            } catch (error) {
                console.error("Failed to check completion status:", error);
            }
        };

        // 处理任务完成
        const handleTaskCompletion = (imageUrl: string) => {
            console.log("任务完成，清理资源:", currentTaskId);
            setStatus("completed");
            setProgress(100);
            updateHistoryItem(currentTaskId, {
                status: "completed",
                output_result: {
                    image_url: imageUrl,
                    // 添加其他必要的输出结果字段
                    seed: 0,
                    width: 0,
                    height: 0,
                    format: "png",
                    resolution: "",
                    file_size_mb: 0
                }
            });

            // 停止轮询
            if (intervalIdRef.current) {
                clearInterval(intervalIdRef.current);
                intervalIdRef.current = null;
            }

            // 重置生成状态
            setIsGenerating(false);

            // 直接清除当前任务ID，不使用setTimeout
            setCurrentTaskId(null);
        };

        // 初始检查 - 立即执行一次
        checkQueueStatus();

        // 设置轮询间隔
        // 根据状态和超时情况调整轮询间隔:
        const getPollingInterval = () => {
            if (status === "pending") return 3000; // 队列中，每3秒
            if (elapsedTime > estimatedProcessingTime) return 5000; // 超时任务，每5秒
            return 2000; // 普通处理，每2秒
        };

        // 只有在没有设置轮询时才设置新的轮询
        if (!intervalIdRef.current) {
            // 立即开始轮询，确保pending状态的任务也能定时检查
            intervalIdRef.current = setInterval(() => {
                // 无论是pending还是processing状态，都定期检查队列状态
                if (status === "pending") {
                    // 对于pending状态的任务，定期检查队列状态
                    checkQueueStatus();
                } else if (status === "processing") {
                    // 对于处理中的任务，更新时间和进度
                    calculateProgress(startedAt);

                    // 定期检查状态
                    if (elapsedTime > estimatedProcessingTime && progress >= 99) {
                        // 对于超时任务，定期检查完成状态
                        checkCompletionStatus();
                    } else {
                        checkQueueStatus();
                    }
                }
            }, getPollingInterval());
        }

        return () => {
            // 清除轮询
            if (intervalIdRef.current) {
                clearInterval(intervalIdRef.current);
                intervalIdRef.current = null;
            }

            // 组件卸载时，如果任务仍在进行中，确保重置状态
            if (status !== "completed" && currentTaskId) {
                setIsGenerating(false);
            }
        };
        // 只依赖于currentTaskId和必要的函数，避免因状态变化导致轮询被重置
    }, [currentTaskId, setQueueInfo, updateHistoryItem, setIsGenerating]);

    // 如果没有当前任务，不显示任何内容
    if (!currentTaskId || status === "completed") {
        return null;
    }

    // 获取进度显示文本
    const getStatusText = () => {
        if (elapsedTime > estimatedProcessingTime) {
            return "Taking longer than expected...";
        }
        return status === "pending"
            ? "Waiting in queue..."
            : "Generating image...";
    };

    return (
        <div className="fixed bottom-24 right-4 bg-[#1a1a1a] border border-[#333] rounded-lg shadow-lg p-3 w-72 z-20">
            <div className="flex justify-between mb-2">
                <h3 className="text-sm font-medium text-white/90">
                    {getStatusText()}
                </h3>
                <span className="text-xs text-white/70">{Math.round(progress)}%</span>
            </div>
            <Progress
                value={progress}
                className="h-1.5 bg-[#333]"
                variant="success"
            />
            <p className="text-xs text-white/50 mt-2">
                {status === "pending"
                    ? "Your request is in queue. Please wait..."
                    : elapsedTime > estimatedProcessingTime
                        ? `Still working on it (${Math.round(elapsedTime / 60)} min)...`
                        : "We're creating your image. This usually takes about a minute..."}
            </p>
        </div>
    );
}