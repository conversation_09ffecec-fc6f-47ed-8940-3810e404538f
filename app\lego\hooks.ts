import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { generateImage, getUserHistory, getQueueInfo, getTaskDetail, getLegoModels, getFluxUsageLimit } from './api';
import { GenerateImageParams, LegoHistoryItem } from './types';
import { useLegoStore } from './store';
import { useState, useCallback } from 'react';
import { creditsApi } from '@/lib/api/credits';

// 查询键声明
const QUERY_KEYS = {
    HISTORY: 'lego-history',
    QUEUE_INFO: 'lego-queue-info',
    MODELS: 'lego-models',
    FLUX_USAGE_LIMIT: 'lego-flux-usage-limit'
};

/**
 * 获取用户历史记录的钩子（分页模式）
 */
export function useLegoHistory() {
    const { currentPage, itemsPerPage, isGenerating } = useLegoStore();

    return useQuery({
        queryKey: [QUERY_KEYS.HISTORY, 'paginated', currentPage, itemsPerPage],
        queryFn: async () => {
            // 将page转换为offset: page 1 = offset 0, page 2 = offset 20, etc.
            const offset = (currentPage - 1) * itemsPerPage;
            console.log(`Paginated load: page ${currentPage}, offset ${offset} with ${itemsPerPage} items per page`);
            const { tasks, total } = await getUserHistory(itemsPerPage, offset);

            // 处理任务状态
            const processedTasks = tasks.map(task => {
                // 如果后端已提供状态则使用，否则基于其他字段推断
                if (!task.status) {
                    if (!task.output_result?.image_url) {
                        task.status = 'pending';
                    } else if (task.progress < 100) {
                        task.status = 'processing';
                    } else {
                        task.status = 'completed';
                    }
                }
                return task;
            });

            return { tasks: processedTasks, total };
        },
        // 当有图片正在生成时，可以稍微缩短缓存时间，以便在导航回来时更快看到更新，但不再需要强制轮询
        staleTime: isGenerating ? 1000 * 30 : 1000 * 60 * 5, // 生成中30秒缓存，否则5分钟缓存
        refetchOnWindowFocus: false, // 禁用窗口焦点时自动刷新，减少重复请求
        networkMode: 'online',
    });
}

/**
 * 获取lego模型列表的钩子
 */
export function useLegoModels() {
    const { setAvailableModels, setIsLoadingModels } = useLegoStore();

    return useQuery({
        queryKey: [QUERY_KEYS.MODELS],
        queryFn: async () => {
            setIsLoadingModels(true);
            try {
                const { models } = await getLegoModels();

                setAvailableModels(models);
                return models;
            } finally {
                setIsLoadingModels(false);
            }
        },
        staleTime: 1000 * 60 * 5, // 5分钟
        refetchOnWindowFocus: false,
    });
}

/**
 * 生成图片的钩子
 */
export function useGenerateImage() {
    const queryClient = useQueryClient();
    const {
        setIsGenerating,
        setError,
        addToHistory,
        userPrompt,
        negativePrompt,
        stylePreset,
        seed,
        steps,
        guidanceScale,
        aspectRatio,
        selectedModel,
        fluxImages,
        getReferItems,
        getTextualContent
    } = useLegoStore();

    return useMutation({
        mutationFn: async () => {
            setIsGenerating(true);
            setError(undefined);

            // 检查是否选择了模型
            if (!selectedModel) {
                throw new Error('Please select a model first');
            }

            // 检查积分余额（如果模型收费）
            if (selectedModel.price > 0) {
                try {
                    const balanceResponse = await creditsApi.getUserBalance();
                    const currentBalance = balanceResponse.data.balance;

                    if (currentBalance < selectedModel.price) {
                        throw new Error(`Insufficient credits. You need ${selectedModel.price} credits but only have ${currentBalance}.`);
                    }
                } catch (error) {
                    if (error instanceof Error && error.message.includes('Insufficient credits')) {
                        throw error;
                    }
                    throw new Error('Failed to check credit balance. Please try again.');
                }
            }

            // 获取文本内容
            const textualContent = getTextualContent();

            // 检查是否为FLUX模型
            const isFluxModel = selectedModel.name.toLowerCase().includes('flux');

            // 根据模型类型获取图片
            let referItems: string[];
            if (isFluxModel) {
                // FLUX模型使用专用的图片
                referItems = fluxImages;
                if (referItems.length > 2) {
                    throw new Error('FLUX models support maximum 2 images');
                }
            } else {
                // 非FLUX模型使用sidebar的图片
                referItems = getReferItems();
            }

            // 打印日志，帮助调试
            console.log('Final collected referItems:', referItems);
            console.log('Textual content:', textualContent);
            console.log('Selected model:', selectedModel);

            // 拼接prompt
            const finalPrompt = textualContent ? `${textualContent}, ${userPrompt}` : userPrompt;

            // 准备API请求参数
            const params: GenerateImageParams = {
                prompt: finalPrompt, // 使用拼接后的prompt
                negative_prompt: negativePrompt,
                stylePreset,
                seed,
                steps,
                guidance_scale: guidanceScale,
                aspectRatio,
                refer_img_urls: referItems, // 现在只包含图片URL
                model: selectedModel.name // 添加模型参数
            };

            // 调用API生成图片
            const response = await generateImage(params);

            // 如果API调用成功并返回了任务ID和任务数据
            if (response.success && response.taskId && response.task) {
                // 使用后端返回的完整任务数据创建任务项
                const taskItem: LegoHistoryItem = {
                    id: response.task.id,
                    user_id: response.task.user_id,
                    progress: response.task.progress,
                    prompt: response.task.input_params.prompt,
                    input_params: response.task.input_params,
                    output_result: response.task.output_result || {
                        seed: 0,
                        width: response.task.input_params.width || 512,
                        format: 'png',
                        height: response.task.input_params.height || 512,
                        image_url: '/placeholder.svg',
                        resolution: `${response.task.input_params.width || 512}x${response.task.input_params.height || 512}`,
                        file_size_mb: 0
                    },
                    storage_path: response.task.storage_path || '',
                    created_at: new Date(response.task.created_at),
                    started_at: response.task.started_at ? new Date(response.task.started_at) : new Date(),
                    completed_at: response.task.completed_at ? new Date(response.task.completed_at) : undefined,
                    status: response.task.status as 'pending' | 'processing' | 'completed' | 'failed'
                };

                // 添加到历史记录
                addToHistory(taskItem);

                return { response, taskId: response.taskId };
            } else if (response.error) {
                // 如果API调用失败，直接返回错误
                return { response, taskId: null };
            } else {
                // 如果API调用成功但没有返回任务ID（异常情况）
                console.error('API call succeeded but no task_id was returned:', response);
                return { response, taskId: null };
            }
        },
        onSuccess: (data) => {
            const { response, taskId } = data;

            if (response.success) {
                // 如果API调用成功
                if (taskId) {
                    // 如果返回了任务ID，表示任务已创建并正在处理中
                    // 任务已经添加到历史记录中，可以立即重置生成状态
                    // 这样用户可以继续提交新任务
                    setIsGenerating(false);
                } else {
                    // 异常情况：API调用成功但没有返回任务ID
                    setIsGenerating(false);
                }

                // 使历史记录查询失效
                queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.HISTORY] });

                // 立即获取最新数据
                queryClient.refetchQueries({ queryKey: [QUERY_KEYS.HISTORY] });
            } else if (response.error) {
                // 如果API调用失败
                setError(response.error);
                setIsGenerating(false);
            }
        },
        onError: (error: Error) => {
            setError(error.message);
            // 在错误时，确保停止生成状态
            setIsGenerating(false);
        },
        onSettled: () => {
            // 任务已经提交，无论成功失败都设置 isGenerating 为 false
            // 这样用户可以继续提交新任务
            setIsGenerating(false);
        }
    });
}

/**
 * 监控队列状态的钩子
 */
/**
 * 分批加载历史记录的钩子
 * @param isAuthenticated 用户是否已登录
 */
export function useBatchLegoHistory(isAuthenticated: boolean = true) {
    const { itemsPerPage, isGenerating, setHistory } = useLegoStore();
    const [hasMore, setHasMore] = useState(true);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [totalItems, setTotalItems] = useState(0);
    const [allLoadedItems, setAllLoadedItems] = useState<LegoHistoryItem[]>([]);

    // 添加一个唯一标识符来避免重复查询
    const queryId = `batch-history-${Date.now()}`;

    // 初始加载
    const { isLoading, isError, refetch } = useQuery({
        queryKey: [QUERY_KEYS.HISTORY, 'batch', 0, itemsPerPage, isAuthenticated],
        queryFn: async () => {
            console.log(`[${queryId}] Initial load: offset 0 with ${itemsPerPage} items per page`);
            const { tasks, total } = await getUserHistory(itemsPerPage, 0);
            console.log(`[${queryId}] Initial load: received ${tasks.length} items, total: ${total}`);

            // 处理任务状态
            const processedTasks = tasks.map(task => {
                // 如果后端已提供状态则使用，否则基于其他字段推断
                if (!task.status) {
                    if (!task.output_result?.image_url) {
                        task.status = 'pending';
                    } else if (task.progress < 100) {
                        task.status = 'processing';
                    } else {
                        task.status = 'completed';
                    }
                }
                return {
                    ...task,
                    created_at: new Date(task.timestamp || Date.now())
                };
            });

            setTotalItems(total);
            setAllLoadedItems(processedTasks);

            // 检查是否还有更多数据可加载 - 修复逻辑
            const shouldLoadMore = processedTasks.length >= itemsPerPage && processedTasks.length < total;
            console.log(`[${queryId}] Initial load: should load more: ${shouldLoadMore}, loaded ${processedTasks.length}/${total}`);
            setHasMore(shouldLoadMore);

            // 初始化时设置历史记录
            setHistory(processedTasks, total);

            return { tasks: processedTasks, total };
        },
        staleTime: isGenerating ? 1000 * 30 : 1000 * 60 * 5,
        gcTime: 1000 * 60 * 10, // 10分钟垃圾回收时间
        refetchOnWindowFocus: false, // 禁用窗口焦点时自动刷新
        refetchOnMount: false, // 禁用挂载时自动刷新，依赖缓存
        refetchOnReconnect: false, // 禁用重连时自动刷新
        // 只有在用户已登录时才启用查询
        enabled: isAuthenticated,
        // 添加网络模式配置，避免重复请求
        networkMode: 'online',
        retry: 1, // 减少重试次数
        retryDelay: 1000, // 重试延迟
    });

    // 加载更多数据
    const loadMore = useCallback(async () => {
        if (isLoadingMore || !hasMore || !isAuthenticated) {
            console.log("Skipping loadMore: isLoadingMore=", isLoadingMore, "hasMore=", hasMore, "isAuthenticated=", isAuthenticated);
            return;
        }

        try {
            setIsLoadingMore(true);
            const nextOffset = allLoadedItems.length;
            console.log(`Loading more: offset ${nextOffset} with ${itemsPerPage} items per page`);

            const { tasks, total } = await getUserHistory(itemsPerPage, nextOffset);
            console.log(`Loaded ${tasks.length} items from offset ${nextOffset}, total: ${total}`);

            if (tasks.length === 0) {
                console.log("No more items to load");
                setHasMore(false);
                setIsLoadingMore(false);
                return;
            }

            // 处理任务状态
            const processedTasks = tasks.map(task => {
                // 如果后端已提供状态则使用，否则基于其他字段推断
                if (!task.status) {
                    if (!task.output_result?.image_url) {
                        task.status = 'pending';
                    } else if (task.progress < 100) {
                        task.status = 'processing';
                    } else {
                        task.status = 'completed';
                    }
                }
                return {
                    ...task,
                    created_at: new Date(task.timestamp || Date.now())
                };
            });

            // 更新总数
            setTotalItems(total);

            // 去重并追加到历史记录
            const existingIds = new Set(allLoadedItems.map(item => item.id));
            const newItems = processedTasks.filter(item => !existingIds.has(item.id));

            console.log(`Found ${newItems.length} new items after filtering duplicates`);

            if (newItems.length > 0) {
                const updatedItems = [...allLoadedItems, ...newItems];
                setAllLoadedItems(updatedItems);
                setHistory(updatedItems, total);
                console.log(`Updated history with ${updatedItems.length} total items`);

                // 检查是否还有更多数据可加载 - 修复逻辑
                const shouldLoadMore = updatedItems.length < total && processedTasks.length >= itemsPerPage;
                console.log(`After loading offset ${nextOffset}: loaded ${updatedItems.length}/${total}, should load more: ${shouldLoadMore}`);
                setHasMore(shouldLoadMore);
            } else {
                console.log("No new items to add after filtering");
                // 如果没有新项目，检查是否真的没有更多数据了
                const currentLoadedCount = allLoadedItems.length;
                const shouldLoadMore = currentLoadedCount < total;
                console.log(`No new items: loaded ${currentLoadedCount}/${total}, should load more: ${shouldLoadMore}`);
                setHasMore(shouldLoadMore);
            }
        } catch (error) {
            console.error('Failed to load more history:', error);
            // 出错时不改变hasMore状态，允许用户重试
        } finally {
            setIsLoadingMore(false);
        }
    }, [hasMore, isLoadingMore, itemsPerPage, allLoadedItems, setHistory, isAuthenticated]);

    return {
        isLoading,
        isLoadingMore,
        isError,
        hasMore,
        loadMore,
        refetch,
        totalItems,
        loadedCount: allLoadedItems.length
    };
}

export function useQueueInfo(taskId: string | null) {
    const { setQueueInfo, updateHistoryItem, setIsGenerating } = useLegoStore();
    const queryClient = useQueryClient();
    const [isComplete, setIsComplete] = useState(false);

    return useQuery({
        queryKey: [QUERY_KEYS.QUEUE_INFO, taskId],
        queryFn: async () => {
            if (!taskId) return null;

            const info = await getQueueInfo(taskId);
            setQueueInfo(info);

            // 更新历史记录中的状态
            if (info.isProcessing) {
                // 获取任务详情以获取完整信息，包括started_at
                try {
                    const taskDetail = await getTaskDetail(taskId);
                    if (taskDetail) {
                        updateHistoryItem(taskId, {
                            status: 'processing',
                            progress: taskDetail.progress || 0,
                            started_at: taskDetail.started_at || new Date()
                        });
                    } else {
                        // 没有详情就用简单更新
                        updateHistoryItem(taskId, {
                            status: 'processing',
                            started_at: new Date()
                        });
                    }
                } catch (error) {
                    console.error('Failed to get task detail:', error);
                    // 错误时仍然更新基本状态
                    updateHistoryItem(taskId, {
                        status: 'processing',
                        started_at: new Date()
                    });
                }
            }

            // 检查任务是否已完成并有结果
            if (info && info.isProcessing === false && info.result && !isComplete) {
                // 创建完整的output_result对象
                const updatedOutput = {
                    seed: 0,
                    width: 512,
                    format: 'png',
                    height: 512,
                    image_url: info.result.imageUrl || '/placeholder.svg',
                    resolution: '512x512',
                    file_size_mb: 0
                };

                // 获取任务详情以获取完整信息
                try {
                    const taskDetail = await getTaskDetail(taskId);
                    if (taskDetail) {
                        updateHistoryItem(taskId, {
                            output_result: updatedOutput,
                            status: 'completed',
                            progress: 100,
                            completed_at: new Date(),
                            started_at: taskDetail.started_at
                        });
                    } else {
                        // 没有详情就用简单更新
                        updateHistoryItem(taskId, {
                            output_result: updatedOutput,
                            status: 'completed',
                            progress: 100,
                            completed_at: new Date()
                        });
                    }
                } catch (error) {
                    console.error('Failed to get task detail:', error);
                    // 错误时仍然更新基本状态
                    updateHistoryItem(taskId, {
                        output_result: updatedOutput,
                        status: 'completed',
                        progress: 100,
                        completed_at: new Date()
                    });
                }

                // 标记任务为已完成，避免重复更新
                setIsComplete(true);

                // 确保重置生成状态
                setIsGenerating(false);

                // 使历史记录查询失效，强制刷新历史列表
                queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.HISTORY] });

                // 立即获取最新数据
                queryClient.refetchQueries({ queryKey: [QUERY_KEYS.HISTORY] });
            }

            return info;
        },
        enabled: !!taskId && !isComplete,
        refetchInterval: isComplete ? false : 2000, // 完成后停止轮询，未完成时每2秒轮询一次
        staleTime: 1000, // 1秒后过期，确保获取最新状态
        refetchOnWindowFocus: true, // 窗口获得焦点时刷新
    });
}

/**
 * 获取用户FLUX模型使用限制信息的钩子
 */
export function useFluxUsageLimit() {
    return useQuery({
        queryKey: [QUERY_KEYS.FLUX_USAGE_LIMIT],
        queryFn: getFluxUsageLimit,
        staleTime: 1000 * 10, // 10秒缓存，保持数据相对新鲜
        refetchOnWindowFocus: true, // 窗口获得焦点时刷新
        refetchInterval: 1000 * 60, // 每分钟自动刷新一次
    });
}