/**
 * Stripe优惠券配置
 * 管理系统中使用的Stripe优惠券ID映射，支持测试和生产环境
 */

import { EnvironmentConfig } from '../../common/config/environment.config';

/**
 * 生产环境Stripe优惠券配置
 */
export const STRIPE_COUPON_CONFIG_PRODUCTION = {
    // 月度订阅90%优惠券
    MONTHLY_DISCOUNT: 'PJLCvjEI',
    // PRO年度订阅首月免费优惠券
    PRO_YEARLY_FIRST_MONTH_FREE: 'fBhj5ODp',
    // MAX年度订阅首月免费优惠券
    MAX_YEARLY_FIRST_MONTH_FREE: 'hDZOd3g3',
} as const;

/**
 * 测试环境Stripe优惠券配置
 */
export const STRIPE_COUPON_CONFIG_TEST = {
    // 月度订阅90%优惠券（测试环境）
    MONTHLY_DISCOUNT: 'uYY1w0UZ',
    // PRO年度订阅首月免费优惠券（测试环境）
    PRO_YEARLY_FIRST_MONTH_FREE: 'MzKARHaU',
    // MAX年度订阅首月免费优惠券（测试环境）
    MAX_YEARLY_FIRST_MONTH_FREE: '8QBHsvbz',
} as const;

/**
 * 根据环境获取优惠券配置
 * @param isTestMode 是否为测试模式
 * @returns 对应环境的优惠券配置
 */
export function getStripeCouponConfig(isTestMode: boolean) {
    return isTestMode ? STRIPE_COUPON_CONFIG_TEST : STRIPE_COUPON_CONFIG_PRODUCTION;
}

/**
 * 根据订阅类型和会员计划选择合适的Stripe优惠券ID
 * @param billingCycle 计费周期 ('monthly' | 'yearly')
 * @param planName 会员计划名称 ('PRO' | 'MAX')
 * @param environmentConfig 环境配置服务实例
 * @returns Stripe优惠券ID
 */
export function getStripeCouponIdForSubscription(
    billingCycle: string,
    planName: string,
    environmentConfig: EnvironmentConfig
): string | null {
    const testMode = environmentConfig.isStripeTestMode();
    const config = getStripeCouponConfig(testMode);

    // 月度订阅统一使用90%优惠券
    if (billingCycle === 'monthly') {
        return config.MONTHLY_DISCOUNT;
    }

    // 年度订阅根据会员等级选择首月免费优惠券
    if (billingCycle === 'yearly') {
        const upperPlanName = planName.toUpperCase();
        if (upperPlanName.includes('PRO')) {
            return config.PRO_YEARLY_FIRST_MONTH_FREE;
        } else if (upperPlanName.includes('MAX')) {
            return config.MAX_YEARLY_FIRST_MONTH_FREE;
        }
    }

    // 默认返回月度折扣优惠券
    return config.MONTHLY_DISCOUNT;
}
