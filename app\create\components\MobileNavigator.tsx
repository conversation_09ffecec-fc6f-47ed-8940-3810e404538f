"use client"

import React, { useState, useEffect } from 'react';
import { ControlPanel } from './ControlPanel';
import { HistoryPanel } from './history-pannel';
import { History } from 'lucide-react';
import { Button } from "@/components/ui/button";

export default function MobileNavigator() {
  const [showHistory, setShowHistory] = useState<boolean>(false);
  const [screenWidth, setScreenWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 1024);
  const isMobile = screenWidth < 768;
  
  // Monitor screen width changes
  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };
    
    // Set initial width
    handleResize();
    
    // Add event listener for window resize
    window.addEventListener('resize', handleResize);
    
    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // If not mobile, don't render this component
  if (!isMobile) {
    return null;
  }
  
  return (
    <div className="flex flex-col h-full">
      {/* Main Content Area */}
      {showHistory ? (
        <div className="relative h-full">
          {/* Back button to return from history */}
          <div className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm px-4 py-2">
            <Button 
              variant="outline" 
              onClick={() => setShowHistory(false)}
              className="w-full flex items-center justify-center"
        >
              ← Back to Create
            </Button>
          </div>
          
          <div className="h-[calc(100vh-110px)] overflow-auto px-4">
            <HistoryPanel />
          </div>
        </div>
      ) : (
        <div className="h-full">
          {/* Custom History button at the top, above the Enhance button position */}
          <div className="px-4 py-3 z-20">
            <Button
              onClick={() => setShowHistory(true)}
              variant="outline"
              className="justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4 flex items-center gap-2 w-full"
            >
              <History className="h-4 w-4" />
              <span>History</span>
            </Button>
            </div>
          
          <div className="h-[calc(100vh-70px)] overflow-auto pb-24 px-2">
            {/* The ControlPanel component without the History button */}
            <ControlPanel />
            </div>
      </div>
      )}
    </div>
  );
} 