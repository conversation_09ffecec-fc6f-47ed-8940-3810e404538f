"use client";

import { ComponentType } from 'react';
import { useAuth } from "@/contexts/auth-context";
import useLoginDialogStore from "@/store/useLoginDialogStore";

// 保护需要验证的操作的高阶组件
export function withAuth<P extends object>(
    Component: ComponentType<P>,
    actionType: string = 'generic_action'
) {
    return function WithAuthComponent(props: P & { onSuccess?: () => void }) {
        const { user } = useAuth();
        const { openLoginDialog } = useLoginDialogStore();
        const { onSuccess, ...restProps } = props;

        const handleAction = () => {
            if (!user) {
                // 用户未登录，打开登录弹窗并保存待执行操作
                openLoginDialog({
                    pendingAction: {
                        type: actionType,
                        payload: restProps,
                        onSuccess
                    }
                });
                return;
            }

            // 用户已登录，直接渲染原始组件
            return <Component {...restProps as P} />;
        };

        return handleAction();
    };
}

// 保护需要验证的函数的高阶函数 - 新实现，使用自定义钩子
export function useAuthProtectedCallback(
    callback: (...args: any[]) => void,
    actionType: string = 'generic_function'
) {
    const { user } = useAuth();
    const { openLoginDialog } = useLoginDialogStore();

    // 返回一个函数，捕获了user和openLoginDialog的值
    return (...args: any[]) => {
        if (!user) {
            // 用户未登录，打开登录弹窗并保存待执行操作
            openLoginDialog({
                pendingAction: {
                    type: actionType,
                    payload: args,
                    onSuccess: () => callback(...args)
                }
            });
            return;
        }

        // 用户已登录，直接执行回调
        return callback(...args);
    };
}

// 旧版withAuthCheck函数 - 已废弃，保留以向后兼容，但使用上面的useAuthProtectedCallback实现
export function withAuthCheck(
    callback: (...args: any[]) => void,
    actionType: string = 'generic_function'
) {
    // 警告：这个函数仅作为兼容层保留，建议使用useAuthProtectedCallback代替
    console.warn('withAuthCheck is deprecated, use useAuthProtectedCallback instead');

    // 返回一个函数，它将在调用时获取上下文（但不使用钩子）
    return (...args: any[]) => {
        // 获取当前上下文中的user和store，而不是在这里使用钩子
        // 注意：这个实现依赖于外部应用程序结构，不是理想的解决方案
        // 尝试从全局存储获取必要的信息
        const globalStore = (window as any).__NEXT_DATA__?.props?.pageProps?.user;

        // 如果无法获取用户，默认打开登录对话框
        if (!globalStore) {
            // 使用全局方法触发登录对话框
            if (typeof window !== 'undefined') {
                // 直接访问全局登录对话框状态管理器
                const loginStore = (window as any).__ZUSTAND_STORES__?.loginDialogStore;
                if (loginStore?.getState) {
                    loginStore.getState().openLoginDialog({
                        pendingAction: {
                            type: actionType,
                            payload: args,
                            onSuccess: () => callback(...args)
                        }
                    });
                } else {
                    // 回退方案：重定向到登录页面
                    window.location.href = '/login';
                }
            }
            return;
        }

        // 如果可以获取到用户，直接执行回调
        return callback(...args);
    };
}

// 自定义Hook：检查用户是否已认证，如果没有则显示登录弹窗
export function useAuthCheck() {
    const { user } = useAuth();
    const { openLoginDialog } = useLoginDialogStore();

    const checkAuth = (
        callback: (...args: any[]) => void,
        actionType: string = 'generic_hook_action'
    ) => {
        return (...args: any[]) => {
            if (!user) {
                openLoginDialog({
                    pendingAction: {
                        type: actionType,
                        payload: args,
                        onSuccess: () => callback(...args)
                    }
                });
                return false;
            }
            return callback(...args);
        };
    };

    return { isAuthenticated: !!user, checkAuth };
} 