CREATE OR REPLACE FUNCTION add_credits(
    p_user_id UUID,
    p_amount INT,
    p_type VARCHAR(50),
    p_description TEXT DEFAULT '',
    p_payment_id UUID DEFAULT NULL,
    p_status VARCHAR(20) DEFAULT 'completed'
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_balance INT;
    v_new_balance INT;
    v_transaction_id UUID;
    v_transaction JSONB;
BEGIN
    -- 确保金额为正数
    IF p_amount <= 0 THEN
        RAISE EXCEPTION '添加积分金额必须为正数';
    END IF;
    
    -- 幂等性检查：检查是否已经存在相同payment_id的交易记录
    IF p_payment_id IS NOT NULL THEN
        -- 直接尝试获取已存在交易及其详情
        SELECT 
            jsonb_build_object(
                'id', id,
                'user_id', user_id,
                'type', type,
                'amount', amount,
                'description', description,
                'status', status,
                'payment_id', payment_id,
                'created_at', created_at
            ) INTO v_transaction
        FROM credit_transactions
        WHERE payment_id = p_payment_id;
        
        -- 如果已存在记录，返回当前信息
        IF v_transaction IS NOT NULL THEN
            -- 直接获取当前余额
            SELECT credits INTO v_current_balance 
            FROM user_credit_balances 
            WHERE user_id = p_user_id;
            
            -- 返回已存在的交易信息
            RETURN jsonb_build_object(
                'status', 'already_exists',
                'message', '该交易记录已存在，避免重复处理',
                'transaction', v_transaction,
                'balance', COALESCE(v_current_balance, 0)
            );
        END IF;
    END IF;
    
    -- 使用事务确保原子性操作
    BEGIN
        -- 首先插入交易记录
        INSERT INTO credit_transactions (
            user_id,
            type,
            amount,
            description,
            payment_id,
            status,
            created_at
        ) VALUES (
            p_user_id,
            p_type,
            p_amount,
            p_description,
            p_payment_id,
            p_status,
            NOW()
        )
        -- 使用ON CONFLICT处理幂等性
        ON CONFLICT (payment_id) WHERE payment_id IS NOT NULL
        DO NOTHING
        RETURNING id INTO v_transaction_id;
        
        -- 如果未能插入新交易（因为已存在相同payment_id），则返回已存在信息
        IF v_transaction_id IS NULL AND p_payment_id IS NOT NULL THEN
            -- 获取已存在交易详情
            SELECT 
                jsonb_build_object(
                    'id', id,
                    'user_id', user_id,
                    'type', type,
                    'amount', amount,
                    'description', description,
                    'status', status,
                    'payment_id', payment_id,
                    'created_at', created_at
                ) INTO v_transaction
            FROM credit_transactions
            WHERE payment_id = p_payment_id;
            
            -- 获取当前余额
            SELECT credits INTO v_current_balance 
            FROM user_credit_balances 
            WHERE user_id = p_user_id;
            
            -- 返回已存在信息
            RETURN jsonb_build_object(
                'status', 'already_exists',
                'message', '该交易记录已存在(插入冲突)，避免重复处理',
                'transaction', v_transaction,
                'balance', COALESCE(v_current_balance, 0)
            );
        END IF;
        
        -- 获取交易详情
        SELECT 
            jsonb_build_object(
                'id', id,
                'user_id', user_id,
                'type', type,
                'amount', amount,
                'description', description,
                'status', status,
                'payment_id', payment_id,
                'created_at', created_at
            ) INTO v_transaction
        FROM credit_transactions
        WHERE id = v_transaction_id;
        
        -- 更新余额，不根据status判断，由上层应用决定是否调用此函数
        -- 使用单条SQL语句实现余额更新或创建（UPSERT模式）
        WITH balance_update AS (
            INSERT INTO user_credit_balances (user_id, credits, last_transaction_id, updated_at)
            VALUES (
                p_user_id, 
                p_amount, 
                v_transaction_id,
                NOW()
            )
            ON CONFLICT (user_id) 
            DO UPDATE SET
                credits = user_credit_balances.credits + p_amount,
                last_transaction_id = EXCLUDED.last_transaction_id,
                updated_at = EXCLUDED.updated_at
            RETURNING credits, credits - p_amount AS previous_credits
        )
        SELECT credits, previous_credits INTO v_new_balance, v_current_balance
        FROM balance_update;
        
        -- 返回结果
        RETURN jsonb_build_object(
            'transaction', v_transaction,
            'new_balance', COALESCE(v_new_balance, 0),
            'previous_balance', COALESCE(v_current_balance, 0)
        );
    EXCEPTION
        WHEN unique_violation THEN
            -- 处理并发情况下可能出现的唯一约束冲突
            -- 获取已存在交易详情
            SELECT 
                jsonb_build_object(
                    'id', id,
                    'user_id', user_id,
                    'type', type,
                    'amount', amount,
                    'description', description,
                    'status', status,
                    'payment_id', payment_id,
                    'created_at', created_at
                ) INTO v_transaction
            FROM credit_transactions
            WHERE payment_id = p_payment_id;
            
            -- 获取当前余额
            SELECT credits INTO v_current_balance 
            FROM user_credit_balances 
            WHERE user_id = p_user_id;
            
            -- 返回已存在信息
            RETURN jsonb_build_object(
                'status', 'already_exists',
                'message', '该交易记录已存在（唯一约束冲突）',
                'transaction', v_transaction,
                'balance', COALESCE(v_current_balance, 0)
            );
    END;
END;
$$;