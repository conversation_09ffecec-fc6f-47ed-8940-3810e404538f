import { Injectable, Inject } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../../common/providers/supabase.provider';
import { BaseCreditStrategy } from './base-credit-strategy';
import { CreditTransactionType } from '../constant';

/**
 * 会员首次开通积分策略
 *
 * 负责处理会员首次开通积分的发放逻辑
 */
@Injectable()
export class MembershipInitialStrategy extends BaseCreditStrategy {
    constructor(
        @Inject(SUPABASE_CLIENT) supabase: SupabaseClient
    ) {
        super(supabase, CreditTransactionType.MEMBERSHIP_INITIAL);
    }

    /**
     * 计算会员应发放的首次开通积分数量
     * @param userId 用户ID
     * @param params 包含planId和可选plan对象的参数对象
     * @returns 计算得到的积分数量
     */
    async calculateAmount(plan: any): Promise<number> {
        try {
            if (!plan) {
                this.logger.error(`计算会员首次开通积分时缺少planId参数`);
                return 0;
            }

            // 查询会员计划对应的积分奖励配置
            const { data: interestData, error: interestError } = await this.supabase
                .from('membership_interests')
                .select('interest_value')
                .eq('interest_type', 'credits')
                .eq('interest_key', 'monthly_grant') // 使用monthly_grant配置项
                .eq('plan_id', plan.id)
                .eq('is_active', true)
                .single();

            if (interestError) {
                if (interestError.code === 'PGRST116') { // 没有找到记录
                    // 根据会员计划名称返回默认值
                    const planName = plan.plan_name?.toUpperCase() || '';
                    if (planName.includes('PRO')) {
                        return 1288; // PRO会员
                    } else if (planName.includes('MAX')) {
                        return 2888; // MAX会员
                    } else {
                        return 0; // 其他会员类型
                    }
                }

                this.logger.error(`获取会员积分配置失败: ${interestError.message}`);
                return 0;
            }

            // 解析interest_value
            let creditsAmount = 0;
            if (typeof interestData.interest_value === 'string') {
                creditsAmount = parseInt(interestData.interest_value, 10);
            } else if (typeof interestData.interest_value === 'object' && interestData.interest_value?.amount) {
                creditsAmount = parseInt(interestData.interest_value.amount, 10);
            }

            return isNaN(creditsAmount) ? 0 : creditsAmount;
        } catch (error) {
            this.logger.error(`计算会员首次开通积分失败: ${error.message}`, error.stack);
            return 0;
        }
    }

    /**
     * 检查是否可以发放会员首次开通积分
     * @param userId 用户ID
     * @param params 包含planId和paymentId的参数对象
     * @returns 是否可以发放积分
     */
    async canGrant(userId: string, params?: { plan: any; paymentId: string }): Promise<boolean> {
        try {
            // 检查是否已经为该订阅发放过首次开通积分
            const { data: existingCredits, error: checkError } = await this.supabase
                .from('credit_transactions')
                .select('id')
                .eq('payment_id', params.paymentId)
                .eq('type', this.type)
                .limit(1);

            if (checkError) {
                this.logger.error(`检查首次积分发放记录失败: ${checkError.message}`);
                return false;
            }

            // 如果已经发放过，则不再发放
            if (existingCredits && existingCredits.length > 0) {
                this.logger.log(`订阅 ${params.paymentId} 已经获得过首次开通积分`);
                return false;
            }

            return true;
        } catch (error) {
            this.logger.error(`检查是否可以发放会员首次开通积分失败: ${error.message}`, error.stack);
            return false;
        }
    }

    /**
     * 执行会员首次开通积分发放
     * @param userId 用户ID
     * @param params 包含planId、subscriptionId、plan对象和可选description的参数对象
     * @returns 发放结果
     */
    async execute(userId: string, params?: {
        paymentId: string;
        plan: any; // 添加plan对象参数，避免重复查询
    }): Promise<any> {
        try {
            if (!params?.paymentId) {
                return {
                    success: false,
                    message: '缺少paymentId参数'
                };
            }

            // 检查是否可以发放积分
            const canGrant = await this.canGrant(userId, params);
            if (!canGrant) {
                return {
                    success: false,
                    message: '不满足发放条件或已经发放过'
                };
            }

            // 获取会员计划信息，用于生成描述
            const membershipType = params.plan.plan_name;

            // 计算积分数量 - 传递plan对象避免重复查询
            const amount = await this.calculateAmount(params.plan);

            if (amount <= 0) {
                this.logger.log(`用户 ${userId} 计算得到的积分为 ${amount}，不执行发放`);
                return {
                    success: false,
                    message: '计算得到的积分数量不大于0'
                };
            }

            // 设置描述
            const description = `${membershipType}会员首次开通发放积分${amount}`;

            // 执行积分发放
            console.log('发放积分执行参数', {
                p_user_id: userId,
                p_amount: amount,
                p_type: this.type,
                p_description: description,
                p_payment_id: params.paymentId,
                p_status: 'completed'
            })
            const { data, error } = await this.supabase.rpc('add_credits', {
                p_user_id: userId,
                p_amount: amount,
                p_type: this.type,
                p_description: description,
                p_payment_id: params.paymentId,
                p_status: 'completed'
            });

            if (error) {
                this.logger.error(`发放会员首次开通积分失败: ${error.message}`, error.stack);
                throw error;
            }

            this.logger.log(`成功为用户 ${userId} 发放会员首次开通积分 ${amount} 积分`);

            return {
                success: true,
                transaction: data.transaction,
                newBalance: data.new_balance,
                amount
            };
        } catch (error) {
            this.logger.error(`执行会员首次开通积分发放失败: ${error.message}`, error.stack);
            throw error;
        }
    }
}
