import React from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import MarkdownRenderer from '@/components/content/MarkdownRenderer';
import { BlogFeatureSection } from '@/components/blog/blog-feature-card';
import { getBlogPostBySlug } from '@/lib/blog'; // 直接使用库函数
import { Suspense } from 'react';
import TopFeed from './TopFeed';

interface BlogPostPageProps {
    params: {
        slug: string;
    };
}

export async function generateMetadata(
    { params }: BlogPostPageProps
): Promise<Metadata> {
    const resolvedParams = await params;
    const post = await getBlogPostBySlug(resolvedParams.slug);

    if (!post) {
        return {
            title: 'Article Not Found',
            description: 'The requested article could not be found.',
        };
    }

    const url = `https://reelmind.ai/blog/${post.slug}`;

    return {
        title: post.meta_title || post.title,
        description: post.meta_description || post.excerpt,
        keywords: post.meta_keywords?.join(', '),
        alternates: {
            canonical: url,
        },
        openGraph: {
            title: post.meta_title || post.title,
            description: post.meta_description || post.excerpt,
            url: url,
            type: 'article',
            publishedTime: post.created_at,
            images: post.cover_image ? [
                {
                    url: post.cover_image,
                    width: 1200,
                    height: 630,
                    alt: post.title,
                },
            ] : [],
        },
    };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
    const resolvedParams = await params;
    const post = await getBlogPostBySlug(resolvedParams.slug);

    if (!post) {
        notFound();
    }

    return (
        <div className="bg-white dark:bg-gray-950 relative overflow-hidden h-full w-full">
            <div className="max-w-screen-2xl mx-auto px-4 sm:px-6 pb-20 h-full overflow-y-auto no-scrollbar">
                {/* Breadcrumb */}
                <nav className="flex mb-8 text-sm text-gray-500 dark:text-gray-400">
                    <Link href="/" className="hover:text-black dark:hover:text-white transition-colors">
                        Home
                    </Link>
                    <span className="mx-2">/</span>
                    <Link href="/blog" className="hover:text-black dark:hover:text-white transition-colors">
                        Blog
                    </Link>
                    <span className="mx-2">/</span>
                    <span className="text-gray-700 dark:text-gray-300 line-clamp-1">{post.title}</span>
                </nav>

                <article className="bg-white dark:bg-gray-900 rounded-2xl shadow-md dark:shadow-black/30 overflow-hidden border border-gray-100 dark:border-gray-800">
                    {/* Article Header */}
                    <div className="px-6 lg:px-10 pt-6">
                        <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold leading-tight mb-6 text-gray-900 dark:text-white">
                            {post.title}
                        </h1>
                    </div>

                    {/* TopFeed 组件 - 使用 Suspense 和动态导入，确保不影响 SSR */}
                    <Suspense>
                        <TopFeed />
                    </Suspense>

                    {/* Article Content */}
                    <div className="px-6 lg:px-10 pb-10">
                        <div className="prose prose-lg md:prose-xl dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-white prose-a:text-black dark:prose-a:text-white hover:prose-a:text-gray-600 dark:hover:prose-a:text-gray-300 max-w-none">
                            <MarkdownRenderer content={post.content} noTitle />
                        </div>
                    </div>
                </article>

                {/* Feature Section placed after the article content - hidden on mobile */}
                <div className="hidden md:block">
                    <BlogFeatureSection />
                </div>
            </div>
        </div>
    );
}