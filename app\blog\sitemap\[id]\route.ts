import { NextResponse } from 'next/server';
import { getOrGenerateSitemap } from '@/lib/sitemap-cache-vercel';

// 处理sitemap请求
export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const { id: rawId } = await params;
        // 获取ID参数
        const id = parseInt(rawId, 10);

        console.log(`处理博客站点地图请求(ID: ${id})...`);

        // 获取或生成站点地图（已压缩）
        const { data: compressedSitemap } = await getOrGenerateSitemap(id);

        // 返回压缩的XML响应
        return new NextResponse(compressedSitemap, {
            headers: {
                'Content-Type': 'application/xml',
                'Content-Encoding': 'gzip',
                'Content-Length': compressedSitemap.length.toString(),
                'Cache-Control': 'public, max-age=3600',
            },
        });
    } catch (error) {
        console.error(`生成博客Sitemap时出错:`, error);
        return NextResponse.json(
            { error: '生成Sitemap时出错' },
            { status: 500 }
        );
    }
}
