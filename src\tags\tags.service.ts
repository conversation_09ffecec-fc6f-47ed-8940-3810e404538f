import { Injectable, Inject, BadRequestException, NotFoundException } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import { TagResponseDto } from './dto/tag-response.dto';
import { TagFilterDto } from './dto/tag-filter.dto';
import { CreateTagDto } from './dto/create-tag.dto';
import { Tag } from './constants';

@Injectable()
export class TagsService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {
        this.logger.setContext(TagsService.name);
    }

    /**
     * 将标签保存到视频-标签关联表
     * @param videoId 视频ID
     * @param tags 标签数组
     */
    async saveVideoTags(videoId: string, tags: Tag[]) {
        try {
            // 首先确保所有标签都存在于标签表中
            for (const tag of tags) {
                // 检查标签是否已存在
                const { data: existingTag } = await this.supabase
                    .from('tags')
                    .select('id')
                    .eq('name', tag.name)
                    .single();

                let tagId;
                if (existingTag) {
                    tagId = existingTag.id;
                } else {
                    // 创建新标签
                    const { data: newTag, error } = await this.supabase
                        .from('tags')
                        .insert({ name: tag.name })
                        .select('id')
                        .single();

                    if (error) throw error;
                    tagId = newTag.id;
                }

                // 先检查关联是否已存在
                const { data: existingRelation } = await this.supabase
                    .from('video_tags')
                    .select('id')
                    .eq('video_id', videoId)
                    .eq('tag_id', tagId)
                    .single();

                // 如果关联不存在，才添加
                if (!existingRelation) {
                    await this.supabase
                        .from('video_tags')
                        .insert({
                            video_id: videoId,
                            tag_id: tagId
                        });
                }
            }

            return tags;
        } catch (error) {
            this.logger.error(`保存视频标签失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 为模型添加标签
     * @param modelId 模型ID
     * @param tags 标签数组
     * @returns 添加的标签
     */
    async addTagsToModel(modelId: string, tags: string[]): Promise<string[]> {
        try {
            // 验证标签格式
            if (!Array.isArray(tags) || tags.some(tag => typeof tag !== 'string')) {
                throw new BadRequestException('标签必须是字符串数组');
            }

            // 将标签保存到数据库
            await this.saveModelTags(modelId, tags);

            return tags;
        } catch (error) {
            this.logger.error(`为模型添加标签失败: ${error.message}`, error.stack);
            throw new BadRequestException('保存模型标签失败');
        }
    }

    /**
     * 将标签保存到模型-标签关联表
     * @param modelId 模型ID
     * @param tags 标签数组
     */
    private async saveModelTags(modelId: string, tags: string[]): Promise<void> {
        try {
            // 首先确保所有标签都存在于标签表中
            for (const tagName of tags) {
                // 检查标签是否已存在
                const { data: existingTag } = await this.supabase
                    .from('tags')
                    .select('id')
                    .eq('name', tagName)
                    .single();

                let tagId;
                if (existingTag) {
                    tagId = existingTag.id;
                } else {
                    // 创建新标签
                    const { data: newTag, error } = await this.supabase
                        .from('tags')
                        .insert({ name: tagName })
                        .select('id')
                        .single();

                    if (error) throw error;
                    tagId = newTag.id;
                }

                // 先检查关联是否已存在
                const { data: existingRelation } = await this.supabase
                    .from('model_tags')
                    .select('id')
                    .eq('model_id', modelId)
                    .eq('tag_id', tagId)
                    .single();

                // 如果关联不存在，才添加
                if (!existingRelation) {
                    await this.supabase
                        .from('model_tags')
                        .insert({
                            model_id: modelId,
                            tag_id: tagId
                        });
                }
            }
        } catch (error) {
            this.logger.error(`保存模型标签失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 获取所有标签
     * @param filter 过滤条件
     * @returns 标签列表
     */
    async getAllTags(filter: TagFilterDto): Promise<TagResponseDto[]> {
        try {
            const { limit = 50, offset = 0, sort_by = 'usage_count', sort_direction = 'desc' } = filter;

            let query = this.supabase
                .from('tags')
                .select('*')
                .range(offset, offset + limit - 1);

            if (sort_by && ['id', 'name', 'usage_count', 'created_at'].includes(sort_by)) {
                const direction = sort_direction === 'asc' ? true : false;
                query = query.order(sort_by, { ascending: direction });
            }

            const { data, error } = await query;

            if (error) throw error;

            return data.map(tag => ({
                id: tag.id,
                name: tag.name,
                usage_count: tag.usage_count || 0,
                created_at: tag.created_at
            }));
        } catch (error) {
            this.logger.error(`获取标签列表失败: ${error.message}`, error.stack);
            throw new BadRequestException('获取标签列表失败');
        }
    }

    /**
     * 获取视频的标签
     * @param videoId 视频ID
     * @returns 标签列表
     */
    async getVideoTags(videoId: string) {
        try {
            // 获取视频关联的标签
            const { data, error } = await this.supabase
                .from('video_tags')
                .select("*, tags(*)")
                .eq('video_id', videoId);

            if (error) throw error;

            return data;
        } catch (error) {
            this.logger.error(`获取视频标签失败: ${error.message}`, error.stack);
            throw new BadRequestException('获取视频标签失败');
        }
    }

    /**
     * 获取模型的标签
     * @param modelId 模型ID
     * @returns 标签列表
     */
    // async getModelTags(modelId: string): Promise<TagResponseDto[]> {
    //     try {
    //         // 检查模型是否存在
    //         const { data: model } = await this.supabase
    //             .from('models')
    //             .select('id')
    //             .eq('id', modelId)
    //             .single();

    //         if (!model) {
    //             throw new NotFoundException('模型不存在');
    //         }

    //         // 获取模型关联的标签
    //         const { data, error } = await this.supabase
    //             .from('model_tags')
    //             .select(`
    //                 tag_id,
    //                 tags:tag_id(id, name, usage_count, created_at)
    //             `)
    //             .eq('model_id', modelId);

    //         if (error) throw error;

    //         return data.map(item => ({
    //             id: item.tags?.id,
    //             name: item.tags?.name,
    //             usage_count: item.tags?.usage_count || 0,
    //             created_at: item.tags?.created_at
    //         }));
    //     } catch (error) {
    //         this.logger.error(`获取模型标签失败: ${error.message}`, error.stack);
    //         throw new BadRequestException('获取模型标签失败');
    //     }
    // }

    /**
     * 根据标签获取视频
     * @param tagName 标签名称
     * @param limit 限制数量
     * @param offset 偏移量
     * @returns 视频列表
     */
    async getVideosByTag(tagName: string, limit: number = 20, offset: number = 0): Promise<any[]> {
        try {
            // 首先获取标签ID
            const { data: tag } = await this.supabase
                .from('tags')
                .select('id')
                .eq('name', tagName)
                .single();

            if (!tag) {
                throw new NotFoundException('标签不存在');
            }

            // 获取使用该标签的视频
            const { data, error } = await this.supabase
                .from('video_tags')
                .select(`
                    video_id,
                    videos:video_id(*)
                `)
                .eq('tag_id', tag.id)
                .range(offset, offset + limit - 1);

            if (error) throw error;

            return data.map(item => item.videos);
        } catch (error) {
            this.logger.error(`根据标签获取视频失败: ${error.message}`, error.stack);
            throw new BadRequestException('根据标签获取视频失败');
        }
    }

    /**
     * 根据标签获取模型
     * @param tagName 标签名称
     * @param limit 限制数量
     * @param offset 偏移量
     * @returns 模型列表
     */
    async getModelsByTag(tagName: string, limit: number = 20, offset: number = 0): Promise<any[]> {
        try {
            // 首先获取标签ID
            const { data: tag } = await this.supabase
                .from('tags')
                .select('id')
                .eq('name', tagName)
                .single();

            if (!tag) {
                throw new NotFoundException('标签不存在');
            }

            // 获取使用该标签的模型
            const { data, error } = await this.supabase
                .from('model_tags')
                .select(`
                    model_id,
                    models:model_id(*)
                `)
                .eq('tag_id', tag.id)
                .range(offset, offset + limit - 1);

            if (error) throw error;

            return data.map(item => item.models);
        } catch (error) {
            this.logger.error(`根据标签获取模型失败: ${error.message}`, error.stack);
            throw new BadRequestException('根据标签获取模型失败');
        }
    }

    /**
     * 创建标签
     * @param createTagDto 创建标签数据
     * @returns 创建的标签
     */
    async createTag(createTagDto: CreateTagDto): Promise<TagResponseDto> {
        try {
            const { name } = createTagDto;

            // 验证标签名称
            if (!name || typeof name !== 'string' || name.trim() === '') {
                throw new BadRequestException('标签名称不能为空');
            }

            // 检查标签是否已存在
            const { data: existingTag, error: checkError } = await this.supabase
                .from('tags')
                .select('id')
                .eq('name', name)
                .single();

            if (checkError && checkError.code !== 'PGRST116') {
                throw new BadRequestException('检查标签失败');
            }

            if (existingTag) {
                throw new BadRequestException(`标签 "${name}" 已存在`);
            }

            // 创建新标签
            const { data, error } = await this.supabase
                .from('tags')
                .insert({
                    name,
                    usage_count: 0
                })
                .select()
                .single();

            if (error) {
                this.logger.error(`创建标签失败: ${error.message}`, error.stack);
                throw new BadRequestException('创建标签失败');
            }

            return {
                id: data.id,
                name: data.name,
                usage_count: data.usage_count || 0,
                created_at: data.created_at
            };
        } catch (error) {
            this.logger.error(`创建标签失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 删除标签
     * @param tagId 标签ID
     */
    async deleteTag(tagId: string): Promise<void> {
        try {
            // 检查标签是否存在
            const { data: existingTag, error: checkError } = await this.supabase
                .from('tags')
                .select('id')
                .eq('id', tagId)
                .single();

            if (checkError) {
                throw new NotFoundException('标签不存在');
            }

            // 删除标签
            const { error } = await this.supabase
                .from('tags')
                .delete()
                .eq('id', tagId);

            if (error) {
                throw new BadRequestException('删除标签失败');
            }
        } catch (error) {
            this.logger.error(`删除标签失败: ${error.message}`, error.stack);
            throw error;
        }
    }
} 