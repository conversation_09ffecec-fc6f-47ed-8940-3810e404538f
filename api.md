# ReelMind API 文档

## 鉴权机制

请求Header里需要有Authorization: Bearer ${supabase auth token}

## 视频生成接口

### 创建视频生成任务

用户在视频生成页面发起视频生成请求，创建一个新的视频生成任务。

- **URL**: `/generation/gen-video`
- **方法**: `POST`
- **权限**: 需要用户登录
- **请求体**:
  ```json
  {
    "prompt": "一只猫在草地上奔跑",
    "gen_type": "text2video",
    "negative_prompt": "模糊, 低质量",
    "guidance_scale": 7.5,
    "steps": 25,
    "seed": 12345,
    "definition": "480P",
    "duration": "5s",
    "ratio": "9:16"
  }
  ```
- **参数说明**:
  - `prompt`: 创意描述，文本类型，必填
  - `gen_type`: 生成类型，枚举值: "text2video" 或 "img2video"，必填
  - `negative_prompt`: 不希望视频中出现的内容，文本类型，可选
  - `guidance_scale`: 创意相关性，数值类型(1-20)，可选
  - `steps`: 迭代步数，数值类型(1-100)，可选
  - `seed`: 初始值，数值类型，可选
  - `definition`: 视频清晰度，枚举值: "480P" 或 "720P"，可选
  - `duration`: 视频时长，枚举值: "5s" 或 "10s"，可选
  - `ratio`: 视频比例，枚举值: "9:16", "16:9" 或 "1:1"，可选
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": "uuid-task-id",
      "status": "pending",
      "progress": 0,
      "queued_at": null,
      "started_at": null,
      "completed_at": null,
      "last_activity_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

### 获取用户任务列表

获取当前用户的所有视频生成任务，支持按状态过滤和分页。

- **URL**: `/generation/user-tasks`
- **方法**: `GET`
- **权限**: 需要用户登录
- **查询参数**:
  - `status`: (可选) 任务状态，可选值: `pending`, `queued`, `processing`, `completed`, `failed`, `canceled`
  - `limit`: (可选) 每页记录数，默认10
  - `offset`: (可选) 分页偏移，默认0
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "tasks": [
        {
          "id": "uuid-task-id",
          "user_id": "uuid-user-id",
          "status": "completed",
          "progress": 100,
          "input_params": {
            "prompt": "一只猫在草地上奔跑",
            "gen_type": "text2video",
            "negative_prompt": "模糊, 低质量",
            "guidance_scale": 7.5,
            "steps": 25,
            "seed": 12345,
            "definition": "480P",
            "duration": "5s",
            "ratio": "9:16"
          },
          "output_result": {
            "video_url": "https://example.com/videos/cat.mp4",
            "thumbnail_url": "https://example.com/videos/cat_thumbnail.jpg"
          },
          "created_at": "2023-01-01T00:00:00Z",
          "completed_at": "2023-01-01T00:05:00Z"
        }
      ],
      "total": 23
    }
  }
  ```

### 获取任务详情

获取特定任务的详情信息。

- **URL**: `/generation/task/:taskId`
- **方法**: `GET`
- **权限**: 需要用户登录
- **路径参数**:
  - `taskId`: 任务ID
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": "uuid-task-id",
      "user_id": "uuid-user-id",
      "status": "completed",
      "progress": 100,
      "input_params": {
        "prompt": "一只猫在草地上奔跑",
        "gen_type": "text2video",
        "negative_prompt": "模糊, 低质量",
        "guidance_scale": 7.5,
        "steps": 25,
        "seed": 12345,
        "definition": "480P",
        "duration": "5s",
        "ratio": "9:16"
      },
      "output_result": {
        "video_url": "https://example.com/videos/cat.mp4",
        "thumbnail_url": "https://example.com/videos/cat_thumbnail.jpg",
        "duration_seconds": 5,
        "file_size_mb": 2.5,
        "format": "mp4"
      },
      "created_at": "2023-01-01T00:00:00Z",
      "queued_at": "2023-01-01T00:01:00Z",
      "started_at": "2023-01-01T00:02:00Z",
      "completed_at": "2023-01-01T00:05:00Z",
      "last_activity_at": "2023-01-01T00:05:00Z",
      "storage_path": "user_videos/uuid-user-id/uuid-task-id.mp4"
    }
  }
  ```

### 取消任务

取消一个正在排队或处理中的任务。

- **URL**: `/generation/task/cancel`
- **方法**: `POST`
- **权限**: 需要用户登录
- **请求体**:
  ```json
  {
    "task_id": "uuid-task-id"
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": "uuid-task-id",
      "user_id": "uuid-user-id",
      "status": "canceled",
      "progress": 0,
      "input_params": {
        /* ... */
      },
      "created_at": "2023-01-01T00:00:00Z",
      "last_activity_at": "2023-01-01T00:03:00Z"
    }
  }
  ```

## 视频生成服务API (供Python服务调用)

### 获取下一个待处理任务

由Python视频生成服务调用，获取队列中下一个待处理的任务。

- **URL**: `/generation/gen_video_task/pop`
- **方法**: `POST`
- **权限**: 需要API密钥（实现中）
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": "uuid-task-id",
      "user_id": "uuid-user-id",
      "status": "processing",
      "progress": 0,
      "input_params": {
        "prompt": "一只猫在草地上奔跑",
        "gen_type": "text2video",
        "negative_prompt": "模糊, 低质量",
        "guidance_scale": 7.5,
        "steps": 25,
        "seed": 12345,
        "definition": "480P",
        "duration": "5s",
        "ratio": "9:16"
      },
      "created_at": "2023-01-01T00:00:00Z",
      "started_at": "2023-01-01T00:02:00Z"
    }
  }
  ```

### 完成视频生成任务

由Python视频生成服务调用，更新任务状态为已完成或失败。

- **URL**: `/generation/gen_video_task/finish`
- **方法**: `POST`
- **权限**: 需要API密钥（实现中）
- **请求体**:
  ```json
  {
    "task_id": "uuid-task-id",
    "status": "completed", // 或 "failed"
    "output_result": {
      "video_url": "https://example.com/videos/cat.mp4",
      "thumbnail_url": "https://example.com/videos/cat_thumbnail.jpg",
      "duration_seconds": 5,
      "file_size_mb": 2.5,
      "format": "mp4"
    },
    "storage_path": "user_videos/uuid-user-id/uuid-task-id.mp4",
    "error_log": {
      // 可选，当status为failed时提供
      "message": "GPU内存不足",
      "code": "GPU_OUT_OF_MEMORY",
      "stack_trace": "...",
      "failed_step": "视频生成",
      "retryable": true
    }
  }
  ```
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": "uuid-task-id",
      "status": "completed",
      "progress": 100,
      "user_id": "uuid-user-id",
      "input_params": {
        /* ... */
      },
      "output_result": {
        /* ... */
      },
      "storage_path": "user_videos/uuid-user-id/uuid-task-id.mp4",
      "created_at": "2023-01-01T00:00:00Z",
      "completed_at": "2023-01-01T00:05:00Z",
      "last_activity_at": "2023-01-01T00:05:00Z"
    }
  }
  ```

**注意：** 在调用此接口时，请确保任务尚未处于终态（已完成、已失败或已取消）。对已处于终态的任务重复调用此接口将不会更改其状态，系统会返回任务的当前状态。
