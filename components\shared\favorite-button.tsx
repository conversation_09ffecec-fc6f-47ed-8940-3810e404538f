"use client"

import { useState } from "react"
import { Bookmark } from "lucide-react"
import { Button } from "@/components/ui/button"
import { FavoriteTargetType } from "@/lib/api/favorite"
import { useAuth } from "@/contexts/auth-context"
import { useFavoriteStatusQuery, useFavoriteMutation } from "@/hooks/useVideoQuery"
import useLoginDialogStore from "@/store/useLoginDialogStore"

interface FavoriteButtonProps {
    targetId: string
    targetType: FavoriteTargetType
    className?: string
    showText?: boolean
}

export function FavoriteButton({ targetId, targetType, className = "", showText = true }: FavoriteButtonProps) {
    const { user } = useAuth()
    const [isLoading, setIsLoading] = useState(false)
    const { openLoginDialog } = useLoginDialogStore()


    // 使用hooks获取收藏状态
    const { data: isFavorite = false, refetch } = useFavoriteStatusQuery(targetId, targetType)
    const { toggleFavorite, isLoading: isMutating } = useFavoriteMutation()

    // 处理收藏/取消收藏
    const handleToggleFavorite = async () => {
        if (!user) {
            // 如果用户未登录，则打开登录弹窗
            openLoginDialog({
                pendingAction: {
                    type: 'api_auth_required',
                    payload: { endpoint: '/api/favorites' }
                }
            })
            return;
        }
        if (isLoading || isMutating) return

        setIsLoading(true)
        try {
            await toggleFavorite(targetId, isFavorite, targetType)
            // 刷新收藏状态
            await refetch()
        } catch (err) {
            console.error("操作收藏失败", err)
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <Button
            variant={isFavorite ? "default" : "outline"}
            size="sm"
            className={`${className} ${isFavorite ? "bg-red-500" : ""}`}
            onClick={handleToggleFavorite}
            disabled={isLoading || isMutating}
        >
            <Bookmark className={`h-4 w-4 mr-1 ${isFavorite ? "fill-white" : ""}`} />
            {showText && (isFavorite ? "Favorited" : "Favorite")}
        </Button>
    )
} 