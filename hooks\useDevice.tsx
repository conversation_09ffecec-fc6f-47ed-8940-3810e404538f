'use client';

import { useState, useEffect, useCallback } from 'react';

export type DeviceType = 'mobile' | 'tablet' | 'desktop';

interface UseDeviceReturn {
    device: DeviceType;
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    isClient: boolean;
}

// 定义响应式断点
export const BREAKPOINTS = {
    MOBILE: 640,  // sm
    TABLET: 768,  // md
    DESKTOP: 1024 // lg
} as const;

/**
 * 检测当前设备类型的 Hook
 * @returns 设备类型信息对象
 */
export function useDevice(): UseDeviceReturn {
    // 默认假设是服务器端渲染环境
    const [isClient, setIsClient] = useState(false);

    // 初始状态设为 desktop，避免水合不匹配问题
    const [device, setDevice] = useState<DeviceType>('desktop');

    // 更新设备类型的函数
    const updateDeviceType = useCallback(() => {
        const width = window.innerWidth;

        let newDevice: DeviceType;
        if (width < BREAKPOINTS.MOBILE) {
            newDevice = 'mobile';
        } else if (width < BREAKPOINTS.DESKTOP) {
            newDevice = 'tablet';
        } else {
            newDevice = 'desktop';
        }

        // 只有当设备类型真正改变时才更新状态
        setDevice(prevDevice => {
            if (prevDevice !== newDevice) {
                console.log(`[useDevice] Device type changed: ${prevDevice} -> ${newDevice}`);
                return newDevice;
            }
            return prevDevice;
        });
    }, []);

    useEffect(() => {
        // 标记客户端环境
        setIsClient(true);

        // 初始化时确定设备类型
        updateDeviceType();

        // 使用防抖的 resize 处理器
        let timeoutId: NodeJS.Timeout;
        const handleResize = () => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(updateDeviceType, 150); // 150ms 防抖
        };

        window.addEventListener('resize', handleResize);

        // 清理事件监听器
        return () => {
            clearTimeout(timeoutId);
            window.removeEventListener('resize', handleResize);
        };
    }, [updateDeviceType]);

    return {
        device,
        isMobile: device === 'mobile',
        isTablet: device === 'tablet',
        isDesktop: device === 'desktop',
        isClient
    };
} 