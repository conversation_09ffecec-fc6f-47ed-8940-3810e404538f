import { headers } from 'next/headers';
import { BREAKPOINTS } from '@/hooks/useDevice';
import type { DeviceType } from '@/hooks/useDevice';

const MOBILE_REGEX = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;

/**
 * 基于 User-Agent 在服务端检测设备类型
 * 注意：这只是初步检测，客户端将会根据实际窗口大小重新评估
 */
export async function detectDeviceServer(): Promise<DeviceType> {
    try {
        const headersList = await headers();
        const userAgent = headersList.get('user-agent') || '';

        // 基于 UA 判断是否为移动设备
        if (MOBILE_REGEX.test(userAgent)) {
            // 进一步检查是平板还是手机
            if (/iPad/i.test(userAgent) || (/Tablet/i.test(userAgent) && !/Mobile/i.test(userAgent))) {
                return 'tablet';
            }
            return 'mobile';
        }
    } catch (error) {
        console.error('Error getting user agent:', error);
    }

    return 'desktop';
}

/**
 * 通过 Next.js 的 headers 函数获取设备屏幕宽度（如果之前通过 header 传递）
 */
export async function getClientHints(): Promise<{ width?: number }> {
    try {
        const headersList = await headers();
        const clientHints = headersList.get('x-client-hints');

        if (clientHints) {
            return JSON.parse(clientHints);
        }
    } catch (error) {
        console.error('Error parsing client hints:', error);
    }

    return {};
}

/**
 * 获取基于尺寸的设备类型
 */
export function getDeviceTypeFromWidth(width: number): DeviceType {
    if (width < BREAKPOINTS.MOBILE) {
        return 'mobile';
    }
    if (width < BREAKPOINTS.DESKTOP) {
        return 'tablet';
    }
    return 'desktop';
}

/**
 * 综合客户端提示和 UA 信息获取最可能的设备类型
 */
export async function getDeviceType(): Promise<DeviceType> {
    const { width } = await getClientHints();

    // 如果有客户端提示的宽度，优先使用
    if (width) {
        return getDeviceTypeFromWidth(width);
    }

    // 否则回退到基于 UA 的检测
    return detectDeviceServer();
}

/**
 * 检查是否为移动设备（包括平板）
 */
export async function isMobileOrTablet(): Promise<boolean> {
    const deviceType = await getDeviceType();
    return deviceType === 'mobile' || deviceType === 'tablet';
}

/**
 * 检查是否为桌面设备
 */
export async function isDesktopDevice(): Promise<boolean> {
    return (await getDeviceType()) === 'desktop';
} 