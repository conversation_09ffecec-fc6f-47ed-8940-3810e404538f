import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PostController } from './post.controller';
import { PostAdminController } from './admin.controller';
import { PostStatsController } from './post-stats.controller';
import { PostService } from './post.service';
import { CustomLogger } from 'src/common/services/logger.service';
import { ClientInfoService } from 'src/common/services/client-info.service';
import { UserModule } from 'src/user/user.module';
import { GenerationModule } from 'src/generation/generation.module';
import { VideoModule } from 'src/video/video.module';
import { LoggerModule } from 'src/common/services/logger.module';

@Module({
    imports: [UserModule, GenerationModule, VideoModule, LoggerModule],
    controllers: [PostController, PostAdminController, PostStatsController],
    providers: [PostService, ClientInfoService, CustomLogger],
    exports: [PostService],
})
export class PostModule {}