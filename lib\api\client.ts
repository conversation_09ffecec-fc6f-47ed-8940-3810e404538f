import { createClient as createSupabaseClient } from '@/lib/supabase/client';
import { API_CONFIG } from '@/lib/config';

// API响应类型
export interface ApiResponse<T = any> {
    code: number;
    message: string;
    data: T;
}

// 请求选项类型
export interface RequestOptions {
    headers?: Record<string, string>;
    params?: Record<string, string>;
    timeout?: number;
}

// API错误类型
export class ApiError extends Error {
    code: number;

    constructor(message: string, code: number = -1) {
        super(message);
        this.code = code;
        this.name = 'ApiError';
    }
}

/**
 * API客户端服务
 */
class ApiClient {
    private baseUrl: string;

    constructor(baseUrl: string) {
        this.baseUrl = baseUrl;
    }

    /**
     * 获取认证头信息
     */
    private async getAuthHeaders(): Promise<Record<string, string> | null> {
        try {
            // 先尝试从AuthStore获取已有会话
            const { default: useAuthStore } = await import('@/store/useAuthStore');
            const { session, isAuthenticated } = useAuthStore.getState();

            // 如果AuthStore中有有效会话，直接使用
            if (isAuthenticated && session?.access_token) {
                return {
                    'Authorization': `Bearer ${session.access_token}`,
                };
            }

            // 作为备用，检查Supabase
            const supabase = createSupabaseClient();
            const { data } = await supabase.auth.getSession();

            if (!data.session || !data.session.access_token || !data.session.user) {
                console.log("No valid session found for API request");
                return null;
            }

            return {
                'Authorization': `Bearer ${data.session.access_token}`,
            };
        } catch (error) {
            console.error("Error getting auth headers:", error);
            return null;
        }
    }

    /**
     * 构造完整URL
     */
    private buildUrl(endpoint: string, params?: Record<string, string>): string {
        let url = `${this.baseUrl}${endpoint}`;

        // 添加查询参数
        if (params && Object.keys(params).length > 0) {
            const queryParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    queryParams.append(key, value);
                }
            });

            url += `?${queryParams.toString()}`;
        }

        return url;
    }

    /**
     * 显示登录弹窗
     */
    private showLoginDialog(endpoint: string): void {
        // 只在浏览器环境中执行
        if (typeof window !== 'undefined') {
            // 动态导入以避免SSR问题
            import('@/store/useLoginDialogStore').then(module => {
                const useLoginDialogStore = module.default;
                const { openLoginDialog } = useLoginDialogStore.getState();

                // 打开登录弹窗
                openLoginDialog({
                    pendingAction: {
                        type: 'api_auth_required',
                        payload: { endpoint }
                    }
                });
            }).catch(err => {
                console.error('Failed to open login dialog:', err);
            });
        }
    }

    /**
     * 处理HTTP响应
     */
    private async handleResponse<T>(response: Response): Promise<T> {
        if (!response.ok) {
            let errorMessage = '请求失败';
            let errorCode = -1;

            try {
                const errorData = await response.json();
                errorMessage = errorData.message || errorMessage;
                errorCode = errorData.code || errorCode;
            } catch (e) {
                // 如果无法解析响应体，使用HTTP状态码和状态文本
                errorCode = response.status;
                errorMessage = response.statusText || errorMessage;
            }

            // 特别处理401状态码 - 认证问题
            if (response.status === 401) {
                errorMessage = 'Please login first';

                // 显示登录弹窗
                this.showLoginDialog(response.url);
            }

            throw new ApiError(errorMessage, errorCode);
        }

        const data = await response.json();
        return data as T;
    }

    /**
     * 发送HTTP请求
     */
    private async request<T>(
        method: string,
        endpoint: string,
        data?: any,
        options: RequestOptions = {},
    ): Promise<T> {
        const {
            headers = {},
            params,
            timeout = API_CONFIG.TIMEOUT
        } = options;

        // 构建URL
        const url = this.buildUrl(endpoint, params);

        // 设置超时控制器
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        try {
            // 准备请求头
            let requestHeaders: Record<string, string> = {
                'Content-Type': 'application/json',
                ...headers,
            };

            // 尝试为所有请求添加认证头（如果用户已登录）
            const authHeaders = await this.getAuthHeaders();
            if (authHeaders) {
                requestHeaders = { ...requestHeaders, ...authHeaders };
            }

            // 发送请求
            const response = await fetch(url, {
                method,
                headers: requestHeaders,
                body: data ? JSON.stringify(data) : undefined,
                signal: controller.signal,
            });

            // 处理响应
            return await this.handleResponse<T>(response);
        } catch (error) {
            if (error instanceof DOMException && error.name === 'AbortError') {
                throw new ApiError('请求超时', 408);
            }

            if (error instanceof ApiError) {
                throw error;
            }

            throw new ApiError((error as Error).message || '网络请求失败');
        } finally {
            clearTimeout(timeoutId);
        }
    }

    /**
     * HTTP GET 请求
     */
    async get<T = ApiResponse>(
        endpoint: string,
        options?: RequestOptions
    ): Promise<T> {
        return this.request<T>('GET', endpoint, undefined, options);
    }

    /**
     * HTTP POST 请求
     */
    async post<T = ApiResponse>(
        endpoint: string,
        data?: any,
        options?: RequestOptions
    ): Promise<T> {
        return this.request<T>('POST', endpoint, data, options);
    }

    /**
     * HTTP PUT 请求
     */
    async put<T = ApiResponse>(
        endpoint: string,
        data?: any,
        options?: RequestOptions
    ): Promise<T> {
        return this.request<T>('PUT', endpoint, data, options);
    }

    /**
     * HTTP DELETE 请求
     */
    async delete<T = ApiResponse>(
        endpoint: string,
        options?: RequestOptions
    ): Promise<T> {
        return this.request<T>('DELETE', endpoint, undefined, options);
    }
}

// 创建API客户端实例
export const apiClient = new ApiClient(API_CONFIG.BASE_URL); 