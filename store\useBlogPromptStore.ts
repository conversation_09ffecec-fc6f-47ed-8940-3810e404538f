import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface BlogPromptState {
    // 状态
    prompt: string | null;
    
    // 操作
    setPrompt: (prompt: string) => void;
    clearPrompt: () => void;
}

// 创建store，使用persist中间件进行持久化存储
const useBlogPromptStore = create<BlogPromptState>()(
    persist(
        (set) => ({
            // 初始状态
            prompt: null,
            
            // 设置prompt
            setPrompt: (prompt) => set({ prompt }),
            
            // 清除prompt
            clearPrompt: () => set({ prompt: null }),
        }),
        {
            name: 'blog-prompt-storage', // localStorage中的key名称
            partialize: (state) => ({ prompt: state.prompt }), // 只持久化prompt字段
        }
    )
);

export default useBlogPromptStore;
