-- 为新用户积分添加唯一约束，防止重复领取
-- 这个约束确保每个用户只能有一条状态为 'completed' 的新用户积分记录

-- 首先检查是否已存在该唯一索引
DO $$
BEGIN
    -- 检查唯一索引是否已存在
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE indexname = 'unique_new_user_bonus_per_user'
          AND tablename = 'credit_transactions'
    ) THEN
        -- 创建部分唯一索引（只对 completed 状态的新用户积分生效）
        CREATE UNIQUE INDEX unique_new_user_bonus_per_user
        ON credit_transactions (user_id)
        WHERE type = 'new_user_bonus' AND status = 'completed';

        RAISE NOTICE '已添加新用户积分唯一索引';
    ELSE
        RAISE NOTICE '新用户积分唯一索引已存在';
    END IF;
END $$;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_type_status
ON credit_transactions (user_id, type, status)
WHERE type = 'new_user_bonus';
