create table public.models (
  id uuid not null default gen_random_uuid (),
  user_id uuid null,
  name character varying(100) not null,
  description text null,
  source text null,
  model_type text null,
  storage_path text null,
  is_public boolean not null default true,
  nsfw_level smallint not null default 1,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  metadata jsonb null,
  supported_features jsonb null default '[]'::jsonb,
  default_config jsonb null default '{}'::jsonb,
  size text null,
  cover_img text null,
  trainable boolean null default false,
  category text null,
  "group" text null,
  constraint user_models_pkey primary key (id),
  constraint user_models_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_user_models_source on public.models using btree (source) TABLESPACE pg_default;

create index IF not exists idx_user_models_user_id on public.models using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_user_models_created_at on public.models using btree (created_at) TABLESPACE pg_default;

create index IF not exists idx_user_models_is_public on public.models using btree (is_public) TABLESPACE pg_default;