import { apiClient } from './client';
import { queryDeduplicationManager } from '../query-deduplication';

export interface ApiResponseData<T> {
    code: number;
    message: string;
    data: T;
}

export interface CreditBalanceData {
    balance: number;
}

export type CreditBalance = ApiResponseData<CreditBalanceData>;

export interface CreditTransaction {
    id: string;
    user_id: string;
    type: string;
    amount: number;
    description: string;
    reference_id: string | null;
    status: string;
    created_at: string;
    updated_at: string;
}

export interface CreditTransactionsData {
    data: CreditTransaction[];
    total: number;
    page: number;
    limit: number;
}

export type CreditTransactionsResponse = ApiResponseData<CreditTransactionsData>;

export interface PurchaseCreditsData {
    checkoutUrl: string;
    amountUsd: number;
    creditsAmount: number;
}

export type PurchaseCreditsResponse = ApiResponseData<PurchaseCreditsData>;

export interface PostRewardTransactionData {
    id: string;
    user_id: string;
    type: string;
    amount: number;
    description: string;
    status: string;
    created_at: string;
}

export interface PostRewardData {
    transaction: PostRewardTransactionData;
    newBalance: number;
    rewardAmount: number;
}

export type PostRewardResponse = ApiResponseData<PostRewardData>;

export const creditsApi = {
    // Get user credit balance
    getUserBalance: async (): Promise<CreditBalance> => {
        return apiClient.get('/credits/balance');
    },

    // Get user credit transactions with optional filters
    getTransactions: async (params?: {
        type?: string;
        status?: string;
        page?: number | string;
        limit?: number | string;
        fromDate?: string;
        toDate?: string;
    }): Promise<CreditTransactionsResponse> => {
        // 将数字转换为字符串
        const stringParams: Record<string, string> = {};
        if (params) {
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined) {
                    stringParams[key] = String(value);
                }
            });
        }
        return apiClient.get('/credits/transactions', { params: stringParams });
    },

    // Purchase credits
    purchaseCredits: async (amount: number): Promise<PurchaseCreditsResponse> => {
        return apiClient.post('/credits/purchase', { amount });
    },

    // Claim post reward
    claimPostReward: async (): Promise<PostRewardResponse> => {
        return apiClient.post('/credits/grant-post-reward');
    },

    // Check if user is eligible to claim new user bonus credits
    checkClaimStatus: async (): Promise<{ code: number; data: { canClaim: boolean; reason?: string; alreadyClaimed?: boolean }; message: string; timestamp: string }> => {
        return queryDeduplicationManager.executeQuery(
            'credits-claim-status',
            () => apiClient.get('/credits/claim-status')
        );
    },

    // Claim new user bonus credits
    claimBonus: async (deviceFingerprint: any): Promise<{ code: number; data: { success: boolean; newBalance?: number; reason?: string } }> => {
        return apiClient.post('/credits/claim-bonus', { fingerprint: deviceFingerprint });
    }
};