import React from 'react';
import Link from 'next/link';
import { Metadata } from 'next';
import BlogForm from '../components/BlogForm';

export const metadata: Metadata = {
    title: 'Create Post - Reelmind Admin',
    description: 'Create a new blog post for your website',
};

export default function NewBlogPostPage() {
    return (
        <div className="bg-white dark:bg-gray-900 min-h-screen p-6">
            <div className="max-w-screen-xl mx-auto">
                <div className="mb-6 flex items-center justify-between">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Create New Blog Post
                    </h1>
                    <Link
                        href="/admin/blog"
                        className="px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
                    >
                        Back to Posts
                    </Link>
                </div>

                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded shadow-sm p-6">
                    <BlogForm mode="create" />
                </div>
            </div>
        </div>
    );
} 