import { Injectable, HttpException, HttpStatus, Inject, BadRequestException } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { DeleteAccountDto, FavoriteItemDto, UpdateNsfwSettingDto, UpdateProfileDto, CheckFavoriteDto, CheckFavoriteResponseDto } from './dto/user.dto';
import { CustomLogger } from '../common/services/logger.service';

@Injectable()
export class UserService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {}

    /**
     * 获取用户个人资料
     */
    async getUserProfile(userId: string) {
        const { data, error } = await this.supabase
            .from('user_profiles')
            .select('*')
            .eq('user_id', userId)
            .single();

        if (error) {
            this.logger.error('获取用户个人资料失败', error);
            throw new HttpException('获取用户个人资料失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return data;
    }

    /**
     * 更新用户个人资料
     */
    async updateProfile(userId: string, updateProfileDto: UpdateProfileDto) {
        const updateData: any = {};

        if (updateProfileDto.nickname !== undefined) updateData.nickname = updateProfileDto.nickname;
        if (updateProfileDto.bio !== undefined) updateData.bio = updateProfileDto.bio;
        if (updateProfileDto.avatar !== undefined) updateData.avatar = updateProfileDto.avatar;
        if (updateProfileDto.links !== undefined) updateData.links = updateProfileDto.links;

        updateData.updated_at = new Date().toISOString();

        const { data, error } = await this.supabase
            .from('user_profiles')
            .update(updateData)
            .eq('user_id', userId)
            .select()
            .single();

        if (error) {
            this.logger.error('更新用户个人资料失败', error);
            throw new HttpException('更新用户个人资料失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return data;
    }

    /**
     * 更新NSFW设置
     */
    async updateNsfwSetting(userId: string, updateNsfwSettingDto: UpdateNsfwSettingDto) {
        const { data, error } = await this.supabase
            .from('user_settings')
            .update({
                nsfw_enabled: updateNsfwSettingDto.nsfwEnabled,
                updated_at: new Date().toISOString(),
            })
            .eq('user_id', userId)
            .select()
            .single();

        if (error) {
            this.logger.error('更新NSFW设置失败', error);
            throw new HttpException('更新NSFW设置失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return data;
    }

    /**
     * 注销账户
     */
    async deleteAccount(userId: string, deleteAccountDto: DeleteAccountDto) {
        // 确认用户输入了正确的确认信息
        if (deleteAccountDto.confirmation !== '我确认注销账户') {
            throw new BadRequestException('确认信息不正确');
        }

        // 开始事务，删除用户相关数据
        const { error: deleteError } = await this.supabase.rpc('delete_user_account', {
            user_id_param: userId,
        });

        if (deleteError) {
            this.logger.error('注销账户失败', deleteError);
            throw new HttpException('注销账户失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        // 删除Supabase Auth中的用户
        const { error: authError } = await this.supabase.auth.admin.deleteUser(userId);

        if (authError) {
            this.logger.error('删除认证用户失败', authError);
            throw new HttpException('删除认证用户失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return { success: true };
    }

    /**
     * 获取用户作品列表
     */
    async getCreations(userId: string) {
        // 获取用户生成的视频作品
        const { data: generatedVideos, error: genError } = await this.supabase
            .from('video_gen_tasks')
            .select('*')
            .eq('user_id', userId)
            .eq('status', 'completed')
            .order('completed_at', { ascending: false });

        if (genError) {
            this.logger.error('获取生成视频作品失败', genError);
            throw new HttpException('获取生成视频作品失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        // 获取用户上传的视频作品
        const { data: uploadedVideos, error: uploadError } = await this.supabase
            .from('user_uploaded_videos')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false });

        if (uploadError) {
            this.logger.error('获取上传视频作品失败', uploadError);
            throw new HttpException('获取上传视频作品失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return {
            generated: generatedVideos || [],
            uploaded: uploadedVideos || [],
        };
    }

    /**
     * 获取用户模型列表
     */
    async getUserModels(userId: string) {
        // 获取用户上传的模型
        const { data: uploadedModels, error: uploadError } = await this.supabase
            .from('user_models')
            .select('*')
            .eq('user_id', userId)
            .eq('source', 'upload')
            .order('created_at', { ascending: false });

        if (uploadError) {
            this.logger.error('获取上传模型失败', uploadError);
            throw new HttpException('获取上传模型失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        // 获取用户训练的模型
        const { data: trainedModels, error: trainError } = await this.supabase
            .from('user_models')
            .select('*')
            .eq('user_id', userId)
            .eq('source', 'train')
            .order('created_at', { ascending: false });

        if (trainError) {
            this.logger.error('获取训练模型失败', trainError);
            throw new HttpException('获取训练模型失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return {
            uploaded: uploadedModels || [],
            trained: trainedModels || [],
        };
    }

    /**
     * 获取用户收藏列表
     */
    async getFavorites(userId: string, item_type: string) {
        // 获取用户收藏的模型
        const { data, error: modelError } = await this.supabase
            .from('user_favorites')
            .select(`* ${item_type === 'post' ? ',posts:post_id (*)' : ',models:model_id (*)'}`)
            .eq('user_id', userId)
            .eq('item_type', item_type)
            .order('created_at', { ascending: false });

        if (modelError) {
            this.logger.error('Get favorites failed', modelError);
            throw new HttpException('Get favorites failed', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return data;
    }

    /**
     * 添加收藏
     */
    async addFavorite(userId: string, favoriteItemDto: FavoriteItemDto) {
        const key = favoriteItemDto.item_type === 'post' ? 'post_id' : 'model_id';
        // 检查是否已经收藏
        const { data: existingFavorite, error: checkError } = await this.supabase
            .from('user_favorites')
            .select('*')
            .eq('user_id', userId)
            .eq(key, favoriteItemDto[key])
            .eq('item_type', favoriteItemDto.item_type)
            .single();

        if (!checkError && existingFavorite) {
            return { success: true, message: 'Already favorited' };
        }

        // 添加收藏
        const { data, error } = await this.supabase
            .from('user_favorites')
            .insert({
                user_id: userId,
                [key]: favoriteItemDto[key],
                item_type: favoriteItemDto.item_type,
            })
            .select()
            .single();

        if (error) {
            this.logger.error('添加收藏失败', error);
            throw new HttpException('Add favorite failed', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return { success: true, data };
    }

    /**
     * 取消收藏
     */
    async removeFavorite(userId: string, favoriteItemDto: FavoriteItemDto) {
        const key = favoriteItemDto.item_type === 'post' ? 'post_id' : 'model_id';
        const { error } = await this.supabase
            .from('user_favorites')
            .delete()
            .eq('user_id', userId)
            .eq(key, favoriteItemDto[key])
            .eq('item_type', favoriteItemDto.item_type);

        if (error) {
            this.logger.error('取消收藏失败', error);
            throw new HttpException('取消收藏失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return { success: true };
    }

    /**
     * 检查用户是否收藏了某个项目
     */
    async checkFavorite(userId: string, checkFavoriteDto: CheckFavoriteDto): Promise<CheckFavoriteResponseDto> {
        const { item_type } = checkFavoriteDto;
        const key = item_type === 'post' ? 'post_id' : 'model_id';

        // 查询用户收藏记录
        const { data, error } = await this.supabase
            .from('user_favorites')
            .select('id')
            .eq('user_id', userId)
            .eq(key, checkFavoriteDto[key])
            .eq('item_type', item_type)
            .maybeSingle();

        if (error) {
            this.logger.error('检查收藏状态失败', error);
            throw new HttpException('检查收藏状态失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        // 返回是否已收藏
        return { favorite: !!data };
    }

    /**
     * 获取用户角色
     */
    async getRole(userId: string) {
        const { data, error } = await this.supabase
            .from('user_roles')
            .select('role')
            .eq('user_id', userId)
            .single();

        if (error) {
            this.logger.error('获取用户角色失败', error);
            throw new HttpException('获取用户角色失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return data;
    }
}
