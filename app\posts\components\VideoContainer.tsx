"use client"

import { PostItemDto } from "@/types/posts"
import { VideoActions } from "./VideoActions"
import { getVideoRatio } from "../utils/videoUtils"
import { VideoPlayer, ServerVideoPlayer } from "./VideoPlayer"
import { ReactNode } from "react"

interface VideoContainerProps {
    post: PostItemDto
    isModal?: boolean
    checkingFavorite?: boolean
    children?: ReactNode
}

/**
 * 视频容器组件 - 负责视频区域的显示
 * 可以在服务端和客户端使用，通过children传入不同的视频播放器
 */
export function VideoContainer({
    post,
    isModal = false,
    checkingFavorite = false,
    children
}: VideoContainerProps) {
    const videoRatio = getVideoRatio(post)
    const ratioClassName =
        videoRatio === "16:9" ? "w-full" :
            videoRatio === "9:16" ? "h-full" :
                "w-4/5 aspect-square"

    return (
        <article className="flex-[1_0_auto] flex flex-col bg-gradient-to-b from-black/80 to-black/95 dark:from-black/90 dark:to-black backdrop-blur-xl rounded-xl border border-slate-200/10 dark:border-white/10 overflow-hidden shadow-[0_0_25px_rgba(59,130,246,0.1)] dark:shadow-[0_0_25px_rgba(59,130,246,0.15)]">
            {/* Top Action Bar */}
            <header className="z-10">
                <VideoActions post={post} isModal={isModal} checkingFavorite={checkingFavorite} />
            </header>

            {/* 影院式视频显示区域 */}
            <section className="flex-1 overflow-hidden flex justify-center items-center p-1 relative z-1">
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent pointer-events-none z-0"></div>
                <figure className="relative z-10 rounded-xl overflow-hidden flex items-center justify-center w-full h-full">
                    {/* 保持视频比例 */}
                    <div className={ratioClassName}>
                        {children}
                    </div>
                </figure>
            </section>
        </article>
    )
}

/**
 * 客户端视频容器 - 包含VideoPlayer
 */
export function ClientVideoContainer({
    post,
    isModal = false,
    checkingFavorite = false
}: Omit<VideoContainerProps, 'children'>) {
    return (
        <VideoContainer post={post} isModal={isModal} checkingFavorite={checkingFavorite}>
            <VideoPlayer post={post} isModal={isModal} />
        </VideoContainer>
    )
}

/**
 * 服务端视频容器 - 包含基础video元素
 */
export function ServerVideoContainer({
    post,
    isModal = false
}: Omit<VideoContainerProps, 'children' | 'checkingFavorite'>) {
    return (
        <VideoContainer post={post} isModal={isModal}>
            <ServerVideoPlayer post={post} isModal={isModal} />
        </VideoContainer>
    )
} 