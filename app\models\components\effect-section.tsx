import { EffectCard } from "./effect-card"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight } from "lucide-react"
import type { Effect } from "@/types/model"

interface EffectSectionProps {
    effects?: Effect[]
}

export function EffectSection({ effects = [] }: EffectSectionProps) {
    const isLoading = false; // 服务端组件不需要loading状态

    return (
        <section className="space-y-6">
            {/* Section Header */}
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold tracking-tight">Effects</h2>
                <Link href="/effects/all">
                    <Button variant="ghost" className="gap-1">
                        View All <ArrowRight className="h-4 w-4" />
                    </Button>
                </Link>
            </div>

            {/* Effects Grid */}
            {isLoading ? (
                <div className="flex justify-center py-8">
                    <div className="w-8 h-8 animate-spin text-gray-400" />
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
                    {effects?.map((effect) => (
                        <EffectCard key={effect.id} effect={effect} autoPlay={false} />
                    ))}
                </div>
            )}
        </section>
    )
} 