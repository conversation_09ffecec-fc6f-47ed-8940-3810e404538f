import { Model } from "@/types/model"
import { GenerationType, QualityMode, Duration, AspectRatio } from "@/types/video"

// 添加LoRa模型接口
export interface LoraModel {
    id: string
    name: string
    strength: number
}

export interface ControlPanelProps {
    generationType: GenerationType
    setGenerationType: (type: GenerationType) => void
    creativityLevel: number
    setCreativityLevel: (level: number) => void
    quality: QualityMode
    setQuality: (quality: QualityMode) => void
    duration: Duration
    setDuration: (duration: Duration) => void
    aspectRatio: AspectRatio
    setAspectRatio: (ratio: AspectRatio) => void
    prompt: string
    setPrompt: (prompt: string) => void
    negativePrompt: string
    setNegativePrompt: (prompt: string) => void
    steps: number
    setSteps: (steps: number) => void
    seed: string
    setSeed: (seed: string) => void
    images: string[]
    handleImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
    removeImage: (index: number) => void
    generateVideo: () => void
    isGenerating: boolean
    error: string | null
    selectedModel: string
    setSelectedModel: (modelId: string) => void
    models: Model[]
    videoCost: number
    isLoadingPresignedUrl?: boolean
    isLoadingModels?: boolean
}