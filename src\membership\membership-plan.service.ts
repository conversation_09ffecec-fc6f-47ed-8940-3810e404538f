import { Injectable, Inject, OnModuleInit, NotFoundException, BadRequestException } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import { EnvironmentConfig } from '../common/config/environment.config';

import {
    MembershipPlanDto,
    CreateMembershipPlanDto,
    UpdateMembershipPlanDto,
    MembershipPlanQueryDto,
    MembershipPlanSummaryDto,
} from './dto/membership-plan.dto';
import { MembershipLevel } from './constant';
import { MembershipPriceDto } from './dto/membership.dto';

/**
 * 会员价格信息接口
 */
export interface MembershipPriceInfo {
    id: string;
    level: MembershipLevel;
    plan_name: string;
    description: string;
    monthly_price: number;
    yearly_price: number;
    is_popular: boolean;
    priority: number;
    is_active: boolean;
    stripe_price_id_month?: string;
    stripe_price_id_year?: string;
}

@Injectable()
export class MembershipPlanService implements OnModuleInit {
    private popularLevel: MembershipLevel;

    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
        private readonly environmentConfig: EnvironmentConfig,
    ) {
        this.logger.setContext(MembershipPlanService.name);
    }

    /**
     * 在模块初始化时同步加载会员配置
     * 如果加载失败，抛出异常终止服务启动
     */
    async onModuleInit(): Promise<void> {
        try {
            this.logger.log('正在初始化会员计划服务...');
            await this.loadMembershipConfigOrFail();
            this.logger.log('会员计划服务初始化完成');
        } catch (error) {
            this.logger.error('会员计划服务初始化失败，终止服务启动', error);
            throw new Error('会员计划服务初始化失败: ' + (error instanceof Error ? error.message : String(error)));
        }
    }

    /**
     * 获取所有会员价格配置
     * @returns 所有有效的会员价格配置
     */
    async getAllPrices(): Promise<MembershipPriceInfo[]> {
        try {
            const { data, error } = await this.supabase
                .from('membership_plans')
                .select('*')
                .eq('env', this.environmentConfig.getDatabaseEnvironment())
                .eq('is_active', true)
                .order('level', { ascending: true });

            if (error) {
                this.logger.error('获取会员价格失败', error);
                return [];
            }

            return data.map(item => this.transformPriceData(item));
        } catch (error) {
            this.logger.error('获取会员价格异常', error);
            return [];
        }
    }

    /**
     * 根据会员计划ID获取价格
     * @param planId 会员计划ID
     * @param months 购买月数（1:月付，12:年付）
     * @returns 价格信息，如果不存在则返回null
     */
    async getPriceById(planId: string, months: number): Promise<MembershipPriceInfo | null> {
        // 只允许获取月度(1个月)或年度(12个月)的价格
        if (months !== 1 && months !== 12) {
            this.logger.warn(`尝试获取非月度/年度的价格: 计划ID=${planId}, 月数=${months}`);
            return null;
        }

        try {
            const { data, error } = await this.supabase
                .from('membership_plans')
                .select('*')
                .eq('id', planId)
                .eq('env', this.environmentConfig.getDatabaseEnvironment())
                .eq('is_active', true)
                .single();

            if (error) {
                this.logger.error(`获取会员计划ID ${planId} 月数 ${months} 的价格失败`, error);
                return null;
            }

            return this.transformPriceData(data, months);
        } catch (error) {
            this.logger.error(`获取会员计划ID ${planId} 月数 ${months} 的价格异常`, error);
            return null;
        }
    }

    /**
     * 转换价格数据为标准格式
     * @param planData 原始计划数据
     * @param months 月数（可选，1:月付，12:年付）
     * @returns 标准格式的价格信息
     */
    private transformPriceData(planData: any, months?: number): MembershipPriceInfo {
        const monthlyPrice = Number(planData.monthly_price);
        const yearlyPrice = Number(planData.yearly_price);

        // 如果指定了月数，根据月数选择价格
        if (months) {
            return {
                id: planData.id,
                level: planData.level,
                plan_name: planData.plan_name || this.getLevelName(planData.level),
                description: planData.description,
                monthly_price: monthlyPrice,
                yearly_price: yearlyPrice,
                is_popular: planData.is_popular,
                priority: planData.priority,
                is_active: planData.is_active,
                stripe_price_id_month: planData.stripe_price_id_month,
                stripe_price_id_year: planData.stripe_price_id_year
            };
        }

        return {
            id: planData.id,
            level: planData.level,
            plan_name: planData.plan_name || this.getLevelName(planData.level),
            description: planData.description,
            monthly_price: monthlyPrice,
            yearly_price: yearlyPrice,
            is_popular: planData.is_popular,
            priority: planData.priority,
            is_active: planData.is_active,
            stripe_price_id_month: planData.stripe_price_id_month,
            stripe_price_id_year: planData.stripe_price_id_year
        };
    }

    /**
     * 获取会员计划概要信息
     * @returns 会员计划概要信息列表
     */
    async getMembershipPlans(): Promise<MembershipPlanDto[]> {
        try {
            const { data, error } = await this.supabase
                .from('membership_plans')
                .select('*')
                .eq('env', this.environmentConfig.getDatabaseEnvironment())
                .eq('is_active', true)
                .order('level', { ascending: true });

            if (error) {
                this.logger.error('获取会员计划失败', error);
                return [];
            }

            return data;
        } catch (error) {
            this.logger.error('获取会员计划异常', error);
            return [];
        }
    }

    /**
     * 获取会员级别名称
     * @param level 会员级别
     * @returns 会员级别名称
     */
    private getLevelName(level: MembershipLevel): string {
        switch (level) {
            case MembershipLevel.FREE:
                return '免费用户';
            case MembershipLevel.PRO:
                return 'PRO会员';
            case MembershipLevel.MAX:
                return 'MAX会员';
            default:
                return '未知会员';
        }
    }

    /**
     * 获取推荐的会员级别
     * @returns 推荐的会员级别
     */
    async getPopularLevel(): Promise<MembershipLevel> {
        try {
            const { data, error } = await this.supabase
                .from('membership_plans')
                .select('level')
                .eq('is_popular', true)
                .eq('is_active', true)
                .single();

            if (error || !data) {
                return this.popularLevel;
            }

            return data.level;
        } catch (error) {
            return this.popularLevel;
        }
    }

    /**
     * 加载会员配置，如果失败则抛出异常
     */
    private async loadMembershipConfigOrFail(): Promise<void> {
        await this.loadMembershipConfig();
        await this.loadPopularLevelOrFail();
    }

    /**
     * 加载会员配置
     */
    private async loadMembershipConfig(): Promise<void> {
        try {
            // 会员计划配置已经在getMembershipPlans方法中从数据库加载
            // 这里不需要额外的加载逻辑
            this.logger.log('会员配置已加载');
        } catch (error) {
            this.logger.error('加载会员配置失败', error);
        }
    }

    /**
     * 加载推荐会员级别，如果失败则抛出异常
     */
    private async loadPopularLevelOrFail(): Promise<void> {
        try {
            this.popularLevel = await this.getPopularLevel();
            this.logger.log(`推荐会员级别已加载: ${this.popularLevel}`);
        } catch (error) {
            this.logger.error('加载推荐会员级别失败', error);
            throw error;
        }
    }

    // 以下是原MembershipPlanService的方法

    /**
     * 获取所有会员计划
     * @param query 查询参数
     * @returns 会员计划列表和总数
     */
    async getAllPlans(query: MembershipPlanQueryDto): Promise<MembershipPlanDto[]> {
        const { limit = 10, offset = 0, level, is_active } = query;

        try {
            let queryBuilder = this.supabase
                .from('membership_plans')
                .select('*', { count: 'exact' });

            if (level !== undefined) {
                queryBuilder = queryBuilder.eq('level', level);
            }

            if (is_active !== undefined) {
                queryBuilder = queryBuilder.eq('is_active', is_active);
            }

            const { data, error } = await queryBuilder
                .order('level', { ascending: true })
                .order('months', { ascending: true })
                .range(offset, offset + limit - 1);

            if (error) {
                this.logger.error('获取会员计划列表失败', error);
                throw new BadRequestException('获取会员计划列表失败');
            }

            return data;
        } catch (error) {
            this.logger.error('获取会员计划列表异常', error);
            throw new BadRequestException('获取会员计划列表失败');
        }
    }

    /**
     * 获取会员计划详情
     * @param id 计划ID
     * @returns 会员计划详情
     */
    async getPlanById(id: string): Promise<MembershipPlanDto> {
        try {
            const { data, error } = await this.supabase
                .from('membership_plans')
                .select('*')
                .eq('id', id)
                .single();

            if (error) {
                this.logger.error(`获取会员计划详情失败: ${id}`, error);
                throw new NotFoundException('会员计划不存在');
            }

            return data;
        } catch (error) {
            this.logger.error(`获取会员计划详情异常: ${id}`, error);
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new BadRequestException('获取会员计划详情失败');
        }
    }

    /**
     * 创建会员计划
     * @param createDto 创建参数
     * @returns 创建的会员计划
     */
    async createPlan(createDto: CreateMembershipPlanDto): Promise<MembershipPlanDto> {
        try {
            // 检查同级别的计划是否已存在（因为现在每个级别只能有一个计划）
            const { data: existingPlan, error: checkError } = await this.supabase
                .from('membership_plans')
                .select('id')
                .eq('level', createDto.level)
                .single();

            if (!checkError && existingPlan) {
                throw new BadRequestException(`该会员级别(${createDto.level})的计划已存在`);
            }

            // 如果设置为推荐选项，需要将其他级别的计划设置为非推荐
            if (createDto.is_popular) {
                await this.resetPopularFlag(createDto.level);
            }

            const { data, error } = await this.supabase
                .from('membership_plans')
                .insert({
                    ...createDto,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                })
                .select('*')
                .single();

            if (error) {
                this.logger.error('创建会员计划失败', error);
                throw new BadRequestException('创建会员计划失败');
            }

            return data;
        } catch (error) {
            this.logger.error('创建会员计划异常', error);
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('创建会员计划失败');
        }
    }

    /**
     * 更新会员计划
     * @param id 计划ID
     * @param updateDto 更新参数
     * @returns 更新后的会员计划
     */
    async updatePlan(id: string, updateDto: UpdateMembershipPlanDto): Promise<MembershipPlanDto> {
        try {
            // 检查计划是否存在
            const { data: existingPlan, error: checkError } = await this.supabase
                .from('membership_plans')
                .select('level')
                .eq('id', id)
                .single();

            if (checkError) {
                throw new NotFoundException('会员计划不存在');
            }

            // 如果更新了级别，需要检查是否与其他计划冲突
            if (updateDto.level !== undefined && updateDto.level !== existingPlan.level) {
                const { data: conflictPlan, error: conflictError } = await this.supabase
                    .from('membership_plans')
                    .select('id')
                    .eq('level', updateDto.level)
                    .neq('id', id)
                    .single();

                if (!conflictError && conflictPlan) {
                    throw new BadRequestException(`该会员级别(${updateDto.level})的计划已存在`);
                }
            }

            // 如果设置为推荐选项，需要将其他级别的计划设置为非推荐
            if (updateDto.is_popular) {
                const level = updateDto.level !== undefined ? updateDto.level : existingPlan.level;
                await this.resetPopularFlag(level);
            }

            const { data, error } = await this.supabase
                .from('membership_plans')
                .update({
                    ...updateDto,
                    updated_at: new Date().toISOString()
                })
                .eq('id', id)
                .select('*')
                .single();

            if (error) {
                this.logger.error(`更新会员计划失败: ${id}`, error);
                throw new BadRequestException('更新会员计划失败');
            }

            return data;
        } catch (error) {
            this.logger.error(`更新会员计划异常: ${id}`, error);
            if (error instanceof NotFoundException || error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('更新会员计划失败');
        }
    }

    /**
     * 删除会员计划
     * @param id 计划ID
     * @returns 是否删除成功
     */
    async deletePlan(id: string): Promise<boolean> {
        try {
            const { error } = await this.supabase
                .from('membership_plans')
                .delete()
                .eq('id', id);

            if (error) {
                this.logger.error(`删除会员计划失败: ${id}`, error);
                throw new BadRequestException('删除会员计划失败');
            }

            return true;
        } catch (error) {
            this.logger.error(`删除会员计划异常: ${id}`, error);
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('删除会员计划失败');
        }
    }

    /**
     * 获取会员计划摘要信息
     * @returns 会员计划摘要信息
     */
    async getPlanSummary(): Promise<MembershipPlanSummaryDto[]> {
        try {
            const { data, error } = await this.supabase
                .from('membership_plans')
                .select('*')
                .eq('is_active', true)
                .order('level', { ascending: true });

            if (error) {
                this.logger.error('获取会员计划摘要失败', error);
                throw new BadRequestException('获取会员计划摘要失败');
            }

            // 转换为前端需要的格式
            const result: MembershipPlanSummaryDto[] = data.map(plan => ({
                level: plan.level,
                name: plan.plan_name || this.getLevelName(plan.level),
                is_popular: plan.is_popular || false,
                is_active: plan.is_active,
                price_options: [
                    {
                        months: 1,
                        price: Number(plan.monthly_price),
                        effective_price: Number(plan.monthly_price),
                        discount_percent: null
                    },
                    {
                        months: 12,
                        price: Number(plan.yearly_price),
                        effective_price: Number(plan.yearly_price) / 12, // 平均每月价格
                        discount_percent: null
                    }
                ]
            }));

            return result;
        } catch (error) {
            this.logger.error('获取会员计划摘要异常', error);
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('获取会员计划摘要失败');
        }
    }

    /**
     * 重置同级别计划的推荐标志
     * @param level 会员级别
     */
    private async resetPopularFlag(level: MembershipLevel): Promise<void> {
        try {
            await this.supabase
                .from('membership_plans')
                .update({ is_popular: false })
                .eq('level', level);
        } catch (error) {
            this.logger.error(`重置会员级别 ${level} 的推荐标志失败`, error);
        }
    }

    /**
     * 根据ID获取会员计划
     * @param planId 会员计划ID
     * @returns 会员计划信息
     */
    async getMembershipPlanById(planId: string): Promise<any | null> {
        try {
            const { data, error } = await this.supabase
                .from('membership_plans')
                .select('*')
                .eq('id', planId)
                .eq('is_active', true)
                .single();

            if (error || !data) {
                this.logger.error(`获取会员计划失败: ${planId}`, error);
                return null;
            }

            return data;
        } catch (error) {
            this.logger.error(`获取会员计划异常: ${planId}`, error);
            return null;
        }
    }

    /**
     * 获取会员计划的价格信息
     * @param planId 会员计划ID
     * @param interval 周期 ('month' 或 'year')
     * @returns Stripe价格ID
     */
    async getStripePriceId(planId: string, interval: 'month' | 'year'): Promise<string | null> {
        try {
            const plan = await this.getMembershipPlanById(planId);
            if (!plan) {
                return null;
            }

            return interval === 'month' ? plan.stripe_price_id_month : plan.stripe_price_id_year;
        } catch (error) {
            this.logger.error(`获取价格ID异常: ${planId}`, error);
            return null;
        }
    }

    /**
     * 获取所有会员价格列表（用于前端显示）
     * @returns 会员价格DTO数组
     */
    async getMembershipPrices(): Promise<MembershipPriceDto[]> {
        try {
            // 获取所有会员计划
            const { data: plans, error } = await this.supabase
                .from('membership_plans')
                .select('*')
                .eq('is_active', true)
                .order('level', { ascending: true });

            if (error) {
                this.logger.error('获取会员价格失败', error);
                return [];
            }

            // 转换为前端可用的DTO格式
            return plans.map(plan => {
                const monthlyPrice = plan.monthly_price;
                const yearlyPrice = plan.yearly_price;

                const priceDto: MembershipPriceDto = {
                    level: plan.level,
                    level_name: MembershipLevel[plan.level],
                    monthly_price: monthlyPrice,
                    yearly_price: yearlyPrice / 12, // 转换为月均价格
                    is_popular: plan.is_popular,
                    features: plan.features || []
                };

                return priceDto;
            });
        } catch (error) {
            this.logger.error('获取会员价格失败', error);
            return [];
        }
    }
}