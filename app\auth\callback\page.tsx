"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"

export default function AuthCallbackPage() {
    const router = useRouter()

    useEffect(() => {
        const supabase = createClient()

        // Handle authentication callback
        const handleAuthCallback = async () => {
            const result = await supabase.auth.getSession();

            if (result.error) {
                console.error("Authentication callback error:", result.error.message)
            }

            // Redirect to home page regardless of success or failure
            router.push("/")
        }

        handleAuthCallback()
    }, [router])

    return (
        <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
                <h2 className="text-2xl font-bold mb-2">Verification Complete</h2>
                <p className="text-muted-foreground">Redirecting to Home Page...</p>
            </div>
        </div>
    )
} 