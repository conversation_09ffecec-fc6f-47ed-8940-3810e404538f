# Model Duration Configuration

这个模块提供了一个可扩展的解决方案来管理不同模型的duration限制。

## 背景

不同的AI模型对视频duration有不同的限制：
- `fal-ai/veo3`: 只支持8秒
- `fal-ai/veo2`: 支持5、6、7、8秒
- `fal-ai/veo2/image-to-video`: 支持5、6、7、8秒
- 其他模型: 默认支持5、10秒

## 解决方案

### 1. 配置驱动的方法

使用配置对象来定义每个模型支持的duration选项：

```typescript
const DEFAULT_MODEL_DURATION_CONFIG = {
  'fal-ai/veo3': ['8'],
  'fal-ai/veo2': ['5', '6', '7', '8'],
  'fal-ai/veo2/image-to-video': ['5', '6', '7', '8'],
  'default': ['5', '10']
};
```

### 2. 工具函数

提供了一系列工具函数来处理模型duration逻辑：

- `getModelAvailableDurations()`: 获取模型支持的duration选项
- `isValidDurationForModel()`: 检查duration是否对模型有效
- `getDefaultDurationForModel()`: 获取模型的默认duration
- `isVeo2Model()`, `isVeo3Model()`, `isVeoModel()`: 模型类型检查

### 3. UI组件集成

在`SettingsPills`组件中动态渲染duration选项：

```typescript
// 动态网格布局
<div className={cn(
  "grid gap-1",
  getAvailableDurations.length <= 2 ? "grid-cols-2" : 
  getAvailableDurations.length === 3 ? "grid-cols-3" : "grid-cols-2"
)}>
  {getAvailableDurations.map((durationOption) => (
    <button key={durationOption} onClick={() => setDuration(durationOption)}>
      {durationOption} second{durationOption !== '1' ? 's' : ''}
    </button>
  ))}
</div>
```

## 扩展性

### 添加新模型

要为新模型添加duration限制，只需在配置中添加一行：

```typescript
const MODEL_DURATION_CONFIG = {
  // 现有配置...
  'fal-ai/new-model': ['3', '6', '12'], // 新模型支持3、6、12秒
};
```

### 自定义配置

所有工具函数都支持自定义配置参数：

```typescript
const customConfig = {
  'special-model': ['1', '2', '4'],
  'default': ['5']
};

getModelAvailableDurations('special-model', customConfig); // ['1', '2', '4']
```

## 使用示例

### 在组件中使用

```typescript
import { useControlPanel } from './ControlPanelContext';

function MyComponent() {
  const { getAvailableDurations, isVeo2Model, duration, setDuration } = useControlPanel();
  
  return (
    <div>
      {getAvailableDurations.map(option => (
        <button 
          key={option}
          onClick={() => setDuration(option)}
          className={duration === option ? 'selected' : ''}
        >
          {option}s
        </button>
      ))}
    </div>
  );
}
```

### 直接使用工具函数

```typescript
import { getModelAvailableDurations, isValidDurationForModel } from './modelDurationConfig';

// 获取模型支持的duration
const durations = getModelAvailableDurations('fal-ai/veo2'); // ['5', '6', '7', '8']

// 验证duration
const isValid = isValidDurationForModel('fal-ai/veo2', '6'); // true
```

## 测试

包含完整的单元测试覆盖所有功能：

```bash
npm test -- modelDurationConfig.test.ts
```

## 优势

1. **可扩展**: 轻松添加新模型和duration限制
2. **类型安全**: 完整的TypeScript支持
3. **可测试**: 纯函数，易于单元测试
4. **可维护**: 配置与逻辑分离
5. **向后兼容**: 不影响现有功能
6. **用户友好**: 动态UI适应不同模型的选项数量
