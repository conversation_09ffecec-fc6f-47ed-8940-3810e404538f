import { create } from 'zustand';
import { generationApi } from '@/lib/api/generation';
import type { VideoTask, TaskStatus } from '@/types/video-task';

interface VideoTasksState {
    // 状态
    videoTasks: VideoTask[];
    currentTask: VideoTask | null;
    total: number;
    isLoading: boolean;
    isFetchingTasks: boolean; // 标记正在获取任务，避免重复请求
    error: string | null;
    isInitial: boolean;
    currentPage: number; // 当前页码
    hasMore: boolean; // 是否还有更多数据

    // 操作
    fetchUserTasks: (status?: TaskStatus, limit?: number, offset?: number, append?: boolean) => Promise<VideoTask[]>;
    loadMoreTasks: (status?: TaskStatus, limit?: number) => Promise<VideoTask[]>;
    setSelectedTask: (taskId: string) => VideoTask | null;
    resetTasksState: () => void;
    // 新增方法：添加新任务到列表顶部
    addNewTask: (task: VideoTask) => void;
}

const useVideoTasksStore = create<VideoTasksState>((set, get) => ({
    // 初始状态
    videoTasks: [],
    currentTask: null,
    total: 0,
    isLoading: false,
    isFetchingTasks: false,
    error: null,
    isInitial: true,
    currentPage: 1,
    hasMore: true,

    // 获取用户任务列表
    fetchUserTasks: async (status?: TaskStatus, limit: number = 5, offset: number = 0, append: boolean = false) => {
        try {
            // 如果正在获取任务，则不重复请求
            if (get().isFetchingTasks) return get().videoTasks;

            set({ isLoading: true, isFetchingTasks: true, error: null });
            const result = await generationApi.getUserTasks(status, limit, offset);

            // 计算是否还有更多数据
            const hasMore = offset + result.tasks.length < result.total;

            if (append) {
                // 追加模式：将新任务添加到现有任务列表末尾
                set({
                    videoTasks: [...get().videoTasks, ...result.tasks],
                    total: result.total,
                    isLoading: false,
                    isFetchingTasks: false,
                    hasMore,
                    currentPage: Math.floor(offset / limit) + 1
                });
            } else {
                // 替换模式：用新任务替换现有任务列表
                set({
                    videoTasks: result.tasks,
                    total: result.total,
                    isLoading: false,
                    isFetchingTasks: false,
                    hasMore,
                    currentPage: 1
                });
            }

            // 如果当前没有选中的任务，且有可用任务，则自动选择第一个
            if (!get().currentTask && result.tasks.length > 0 && !append) {
                set({ currentTask: result.tasks[0] });
            }

            return result.tasks;
        } catch (error) {
            console.error('Cannot fetch tasks:', error);
            set({
                error: error instanceof Error ? error.message : 'Cannot fetch tasks',
                isLoading: false,
                isFetchingTasks: false
            });
            return [];
        } finally {
            set({ isInitial: false });
        }
    },

    // 加载更多任务
    loadMoreTasks: async (status?: TaskStatus, limit: number = 5) => {
        const { currentPage, videoTasks, hasMore } = get();

        // 如果没有更多数据，直接返回当前任务列表
        if (!hasMore) return videoTasks;

        // 计算下一页的偏移量
        const offset = currentPage * limit;

        // 调用fetchUserTasks加载更多数据，并追加到现有列表
        return await get().fetchUserTasks(status, limit, offset, true);
    },

    // 设置当前选中的任务
    setSelectedTask: (taskId: string) => {
        const { videoTasks } = get();
        // 从当前任务列表中查找此任务
        const selectedTask = videoTasks.find(task => task.id === taskId);

        // 如果找到了，设置为当前任务
        if (selectedTask) {
            set({ currentTask: selectedTask });
            return selectedTask;
        }

        // 如果没有找到，显示错误
        set({ error: "Task not found" });
        return null;
    },

    // 重置状态
    resetTasksState: () => {
        set({
            videoTasks: [],
            currentTask: null,
            total: 0,
            isLoading: false,
            isFetchingTasks: false,
            error: null,
            currentPage: 1,
            hasMore: true
        });
    },

    // 添加新任务到列表顶部
    addNewTask: (task: VideoTask) => {
        const { videoTasks, total } = get();

        // 检查任务是否已存在于列表中
        const taskExists = videoTasks.some(existingTask => existingTask.id === task.id);

        if (!taskExists) {
            // 将新任务添加到列表顶部
            set({
                videoTasks: [task, ...videoTasks],
                // 更新总数
                total: total + 1
            });

            // 如果当前没有选中的任务，将新任务设为当前任务
            if (!get().currentTask) {
                set({ currentTask: task });
            }
        }
    }
}));

export default useVideoTasksStore;