import React from "react";
import { cn } from "@/lib/utils";

interface PromptSectionProps {
    prompt?: string;
    icon: React.ComponentType<any>;
    title: string;
    bgColor: string;
    iconColor: string;
    className?: string;
}

/**
 * 可重用的提示词区域组件
 * 用于展示AI生成内容的提示词信息
 */
export function PromptSection({
    prompt,
    icon: Icon,
    title,
    bgColor,
    iconColor,
    className
}: PromptSectionProps) {
    return (
        <div className={cn("p-3 border-slate-200/50 dark:border-white/10", className)}>
            <div className="flex items-center gap-2 mb-4">
                <div className={`w-8 h-8 rounded-full ${bgColor} flex items-center justify-center`}>
                    <Icon className={`w-4 h-4 ${iconColor}`} />
                </div>
                <h3 className="font-medium">{title}</h3>
            </div>
            <div className="bg-slate-100 dark:bg-slate-800/50 rounded-lg p-4">
                <p className="text-slate-700 dark:text-slate-300 whitespace-pre-wrap">
                    {prompt || `No ${title.toLowerCase()} information available`}
                </p>
            </div>
        </div>
    );
} 