'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { Search, Download, ExternalLink, Filter } from 'lucide-react';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { adminModelApi, type FalAiModel } from '@/lib/api/admin-model';
import { toast } from 'sonner';

interface FalAiModelSelectorProps {
    onSuccess: () => void;
}

export function FalAiModelSelector({ onSuccess }: FalAiModelSelectorProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [categoryFilter, setCategoryFilter] = useState<string>('all');
    const [syncingModels, setSyncingModels] = useState<Set<string>>(new Set());


    // 获取fal.ai模型列表
    const { data: falAiModels, isLoading, error, refetch } = useQuery({
        queryKey: ['fal-ai-models'],
        queryFn: adminModelApi.getFalAiModels,
        staleTime: 1000 * 60 * 10, // 10分钟
        retry: 2, // 重试2次
        retryDelay: 1000, // 重试延迟1秒
    });

    // 过滤模型
    const filteredModels = falAiModels?.filter((model) => {
        const matchesSearch = !searchQuery ||
            model.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            model.shortDescription.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesCategory = categoryFilter === 'all' || model.category === categoryFilter;

        return matchesSearch && matchesCategory;
    }) || [];

    // 获取可用的分类
    const categories = Array.from(new Set(falAiModels?.map(m => m.category) || []));

    // 同步模型到数据库
    const handleSyncModel = async (model: FalAiModel) => {
        setSyncingModels(prev => new Set(prev).add(model.id));

        try {
            await adminModelApi.syncFalAiModel(model);
            toast.success(`Model "${model.title}" synced successfully`);
            onSuccess();
        } catch (error) {
            console.error('Failed to sync model:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';

            if (errorMessage.includes('already exists')) {
                toast.error(`Model "${model.title}" already exists in database`);
            } else {
                toast.error(`Failed to sync model: ${errorMessage}`);
            }
        } finally {
            setSyncingModels(prev => {
                const newSet = new Set(prev);
                newSet.delete(model.id);
                return newSet;
            });
        }
    };

    // 获取模型类型的显示样式
    const getModelTypeVariant = (type: string) => {
        switch (type) {
            case 'image-to-video':
                return 'default';
            case 'text-to-video':
                return 'secondary';
            case 'video-to-video':
                return 'outline';
            default:
                return 'outline';
        }
    };

    if (error) {
        return (
            <div className="text-center py-8">
                <p className="text-destructive mb-4">Failed to load fal.ai models</p>

                <Button onClick={() => refetch()}>Retry</Button>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* 搜索和过滤 */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                        <Input
                            placeholder="Search models..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </div>

                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-[200px]">
                        <Filter className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="Filter by category" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        {categories.map((category) => (
                            <SelectItem key={category} value={category}>
                                {category}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            {/* 模型列表 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto">
                {isLoading ? (
                    Array.from({ length: 6 }).map((_, i) => (
                        <Card key={i}>
                            <CardHeader>
                                <div className="flex items-center space-x-4">
                                    <Skeleton className="h-12 w-12 rounded-full" />
                                    <div className="space-y-2 flex-1">
                                        <Skeleton className="h-4 w-[200px]" />
                                        <Skeleton className="h-4 w-[150px]" />
                                    </div>
                                </div>
                            </CardHeader>
                        </Card>
                    ))
                ) : (
                    filteredModels.map((model) => (
                        <Card key={model.id} className="hover:shadow-md transition-shadow">
                            <CardHeader className="pb-3">
                                <div className="flex items-start justify-between">
                                    <div className="flex items-center space-x-3 flex-1">
                                        <Avatar className="h-12 w-12">
                                            <AvatarImage
                                                src={model.thumbnailUrl}
                                                alt={model.title}
                                            />
                                            <AvatarFallback>
                                                {model.title.substring(0, 2).toUpperCase()}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1 min-w-0">
                                            <CardTitle className="text-sm font-medium line-clamp-1">
                                                {model.title}
                                            </CardTitle>
                                            <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                                                {model.shortDescription}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </CardHeader>

                            <CardContent className="pt-0">
                                <div className="flex flex-wrap gap-2 mb-3">
                                    <Badge variant={getModelTypeVariant(model.category)}>
                                        {model.category}
                                    </Badge>
                                    {model.pinned && (
                                        <Badge variant="secondary">Pinned</Badge>
                                    )}
                                    {model.highlighted && (
                                        <Badge variant="default">Featured</Badge>
                                    )}
                                    {model.deprecated && (
                                        <Badge variant="destructive">Deprecated</Badge>
                                    )}
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="text-xs text-muted-foreground">
                                        {model.group?.label || 'No group'}
                                    </div>

                                    <div className="flex gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => window.open(`https://fal.ai/models/${model.id}`, '_blank')}
                                            title="View on fal.ai"
                                        >
                                            <ExternalLink className="h-3 w-3" />
                                        </Button>

                                        <Button
                                            size="sm"
                                            onClick={() => handleSyncModel(model)}
                                            disabled={syncingModels.has(model.id) || model.deprecated}
                                        >
                                            <Download className="h-3 w-3 mr-1" />
                                            {syncingModels.has(model.id) ? 'Syncing...' : 'Sync'}
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))
                )}
            </div>

            {!isLoading && filteredModels.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                    No models found matching your criteria
                </div>
            )}
        </div>
    );
}
