@import "tailwindcss";

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@theme {
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary: #000000;
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

@layer base {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* 隐藏滚动条（Firefox、IE） */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE 和 Edge */
    scrollbar-width: none; /* Firefox */
  }

  /* 安全区域处理，特别是针对iOS设备和微信浏览器 */
  :root {
    --safe-area-inset-top: env(safe-area-inset-top, 0px);
    --safe-area-inset-right: env(safe-area-inset-right, 0px);
    --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
    --safe-area-inset-left: env(safe-area-inset-left, 0px);
  }

  /* 底部安全区域填充类 */
  .pb-safe {
    padding-bottom: var(--safe-area-inset-bottom);
  }

  /* 针对微信浏览器全屏模式的特殊处理 */
  @supports (-webkit-touch-callout: none) {
    .safe-area-bottom {
      padding-bottom: max(16px, var(--safe-area-inset-bottom));
    }
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 0%;

    --radius: 0.5rem;

    /* Typography plugin variables */
    --tw-prose-body: #374151;
    --tw-prose-headings: #111827;
    --tw-prose-lead: #4b5563;
    --tw-prose-links: #2563eb;
    --tw-prose-bold: #111827;
    --tw-prose-counters: #6b7280;
    --tw-prose-bullets: #d1d5db;
    --tw-prose-hr: #e5e7eb;
    --tw-prose-quotes: #111827;
    --tw-prose-quote-borders: #e5e7eb;
    --tw-prose-captions: #6b7280;
    --tw-prose-code: #111827;
    --tw-prose-code-bg: #f3f4f6;
    --tw-prose-pre-code: #e5e7eb;
    --tw-prose-pre-bg: #1f2937;
    --tw-prose-th-borders: #d1d5db;
    --tw-prose-td-borders: #e5e7eb;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 76%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.9%;

    /* Typography plugin variables - dark mode */
    --tw-prose-body: #d1d5db;
    --tw-prose-headings: #f3f4f6;
    --tw-prose-lead: #9ca3af;
    --tw-prose-links: #3b82f6;
    --tw-prose-bold: #f3f4f6;
    --tw-prose-counters: #9ca3af;
    --tw-prose-bullets: #4b5563;
    --tw-prose-hr: #374151;
    --tw-prose-quotes: #f3f4f6;
    --tw-prose-quote-borders: #374151;
    --tw-prose-captions: #9ca3af;
    --tw-prose-code: #f3f4f6;
    --tw-prose-code-bg: #1f2937;
    --tw-prose-pre-code: #d1d5db;
    --tw-prose-pre-bg: #111827;
    --tw-prose-th-borders: #4b5563;
    --tw-prose-td-borders: #374151;
  }

  body {
    @apply bg-background text-foreground;
  }

  * {
    @apply border-border;
  }
}

/* 基础动画 */
@keyframes progress-indeterminate {
  0% {
    left: -15%;
    width: 15%;
  }
  50% {
    width: 30%;
  }
  100% {
    left: 100%;
    width: 15%;
  }
}

@keyframes slide-in-from-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-bottom {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoom-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scale-in-center {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-progress-indeterminate {
  animation: progress-indeterminate 1.4s infinite ease-in-out;
}

.animate-in {
  animation-duration: 300ms;
  animation-timing-function: ease-out;
  animation-fill-mode: forwards;
}

.slide-in-from-right {
  animation-name: slide-in-from-right;
}

.slide-in-from-bottom {
  animation-name: slide-in-from-bottom;
}

.fade-in {
  animation-name: fade-in;
}

.zoom-in {
  animation: var(--zoom-in) cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scale-in-center {
  animation: scale-in-center 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

/* Cyberpunk 风格组件 */
.glitch-text {
  position: relative;
  text-shadow: 0 0 10px #f5efff, 0 0 20px #f5efff;
  animation: glitch 2s infinite alternate-reverse;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  clip: rect(0, 0, 0, 0);
}

.glitch-text::before {
  left: 2px;
  text-shadow: -2px 0 #ff00ff;
  animation: glitch-1 2s infinite linear alternate-reverse;
}

.glitch-text::after {
  left: -2px;
  text-shadow: 2px 0 #00ffff;
  animation: glitch-2 3s infinite linear alternate-reverse;
}

@keyframes glitch {
  0% {
    text-shadow: 0 0 10px #f5efff, 0 0 20px #f5efff;
  }
  50% {
    text-shadow: 0 0 12px #f5efff, 0 0 18px #f5efff;
  }
  100% {
    text-shadow: 0 0 10px #f5efff, 0 0 20px #f5efff;
  }
}

@keyframes glitch-1 {
  0% {
    clip: rect(20px, 9999px, 15px, 0);
  }
  20% {
    clip: rect(42px, 9999px, 78px, 0);
  }
  40% {
    clip: rect(96px, 9999px, 64px, 0);
  }
  60% {
    clip: rect(12px, 9999px, 34px, 0);
  }
  80% {
    clip: rect(53px, 9999px, 86px, 0);
  }
  100% {
    clip: rect(25px, 9999px, 57px, 0);
  }
}

@keyframes glitch-2 {
  0% {
    clip: rect(32px, 9999px, 26px, 0);
  }
  20% {
    clip: rect(67px, 9999px, 13px, 0);
  }
  40% {
    clip: rect(48px, 9999px, 92px, 0);
  }
  60% {
    clip: rect(17px, 9999px, 45px, 0);
  }
  80% {
    clip: rect(73px, 9999px, 36px, 0);
  }
  100% {
    clip: rect(28px, 9999px, 64px, 0);
  }
}

/* 像素边框 */
.pixel-border {
  position: relative;
  border: 2px solid #f5efff;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
  clip-path: polygon(
    0 10px,
    10px 0,
    calc(100% - 10px) 0,
    100% 10px,
    100% calc(100% - 10px),
    calc(100% - 10px) 100%,
    10px 100%,
    0 calc(100% - 10px)
  );
}

.pixel-border::before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: transparent;
  border: 1px solid rgba(0, 255, 0, 0.3);
  clip-path: polygon(
    0 12px,
    12px 0,
    calc(100% - 12px) 0,
    100% 12px,
    100% calc(100% - 12px),
    calc(100% - 12px) 100%,
    12px 100%,
    0 calc(100% - 12px)
  );
  pointer-events: none;
}

/* 霓虹按钮 */
.neon-button {
  position: relative;
  background: #000;
  color: #f5efff;
  border: 2px solid #f5efff;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.7);
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: bold;
  overflow: hidden;
  transition: all 0.3s;
}

.neon-button:hover {
  background: rgba(0, 255, 0, 0.2);
  box-shadow: 0 0 20px rgba(0, 255, 0, 0.9);
}

.neon-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 255, 0, 0.4) 50%,
    transparent 100%
  );
  transition: all 0.4s;
}

.neon-button:hover::after {
  left: 100%;
}

/* 扫描线效果 */
.scanlines {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 15;
  opacity: 0.15;
}

.scanlines::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: repeating-linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 255, 0, 0.05) 0.5%,
    transparent 1%
  );
  animation: scanline 10s linear infinite;
}

@keyframes scanline {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(100%);
  }
}

/* 闪烁光标 */
.blink-cursor::after {
  content: "|";
  animation: blink 1s step-end infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* 赛博朋克卡片 */
.cyber-card {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid #f5efff;
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.cyber-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #f5efff, transparent);
}

.cyber-card::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 2px;
  height: 60%;
  background: linear-gradient(0deg, transparent, #f5efff);
}

/* 打字机效果 */
.typing-effect {
  position: relative;
  overflow: hidden;
  border-right: 2px solid #f5efff;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
  white-space: nowrap;
  display: inline-block;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: #f5efff;
  }
}

/* 故障艺术效果 */
.glitch-effect {
  position: relative;
  display: inline-block;
}

.glitch-effect::before,
.glitch-effect::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

.glitch-effect::before {
  animation: glitch-anim-1 2s infinite linear alternate-reverse;
  left: 2px;
  text-shadow: -1px 0 #ff00ff;
  clip: rect(44px, 450px, 56px, 0);
}

.glitch-effect::after {
  animation: glitch-anim-2 3s infinite linear alternate-reverse;
  left: -2px;
  text-shadow: 2px 0 #00ffff;
  clip: rect(44px, 450px, 46px, 0);
}

@keyframes glitch-anim-1 {
  0% {
    clip: rect(42px, 9999px, 44px, 0);
  }
  10% {
    clip: rect(12px, 9999px, 59px, 0);
  }
  20% {
    clip: rect(48px, 9999px, 29px, 0);
  }
  30% {
    clip: rect(16px, 9999px, 9px, 0);
  }
  40% {
    clip: rect(32px, 9999px, 73px, 0);
  }
  50% {
    clip: rect(2px, 9999px, 35px, 0);
  }
  60% {
    clip: rect(64px, 9999px, 74px, 0);
  }
  70% {
    clip: rect(30px, 9999px, 23px, 0);
  }
  80% {
    clip: rect(88px, 9999px, 40px, 0);
  }
  90% {
    clip: rect(19px, 9999px, 67px, 0);
  }
  100% {
    clip: rect(8px, 9999px, 42px, 0);
  }
}

@keyframes glitch-anim-2 {
  0% {
    clip: rect(8px, 9999px, 40px, 0);
  }
  10% {
    clip: rect(52px, 9999px, 5px, 0);
  }
  20% {
    clip: rect(28px, 9999px, 65px, 0);
  }
  30% {
    clip: rect(14px, 9999px, 79px, 0);
  }
  40% {
    clip: rect(62px, 9999px, 23px, 0);
  }
  50% {
    clip: rect(3px, 9999px, 41px, 0);
  }
  60% {
    clip: rect(74px, 9999px, 6px, 0);
  }
  70% {
    clip: rect(36px, 9999px, 83px, 0);
  }
  80% {
    clip: rect(18px, 9999px, 42px, 0);
  }
  90% {
    clip: rect(15px, 9999px, 37px, 0);
  }
  100% {
    clip: rect(53px, 9999px, 12px, 0);
  }
}

/* 网格背景效果 */
@keyframes grid-pulse {
  0% {
    opacity: 0.05;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
  }
  50% {
    opacity: 0.1;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
  }
  100% {
    opacity: 0.05;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
  }
}

.grid-line-pulse {
  animation: grid-pulse 4s infinite;
}

@keyframes micro-grid-flicker {
  0%,
  100% {
    opacity: 0.03;
  }
  25% {
    opacity: 0.05;
  }
  50% {
    opacity: 0.04;
  }
  75% {
    opacity: 0.02;
  }
}

.micro-grid-flicker {
  animation: micro-grid-flicker 3s infinite ease-in-out;
}

@keyframes grid-shift {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 30px 30px;
  }
}

.grid-shift {
  animation: grid-shift 60s linear infinite;
}

@keyframes dot-matrix-fade {
  0%,
  100% {
    opacity: 0.04;
  }
  50% {
    opacity: 0.06;
  }
}

.dot-matrix-fade {
  animation: dot-matrix-fade 5s infinite ease-in-out;
}

@keyframes perspective-line-flash {
  0%,
  100% {
    opacity: 0.15;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
  }
  50% {
    opacity: 0.25;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.8);
  }
}

.perspective-line-flash {
  animation: perspective-line-flash 2s infinite;
}

@keyframes radial-pulse {
  0% {
    transform: scale(0.95) translate(-50%, -50%);
    opacity: 0.02;
  }
  50% {
    transform: scale(1.05) translate(-50%, -50%);
    opacity: 0.05;
  }
  100% {
    transform: scale(0.95) translate(-50%, -50%);
    opacity: 0.02;
  }
}

.radial-pulse {
  animation: radial-pulse 8s infinite ease-in-out;
  transform-origin: top left;
}

/* 渐变背景动画 */
@keyframes gradientBg {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient-bg {
  background-size: 400% 400%;
  animation: gradientBg 2s ease infinite;
}

/* 布局变量 */
:root {
  --sidebar-width: 14rem;
  --header-height: 4rem;
}

/* 添加一个辅助类，用于隐藏滚动条但保持滚动功能 */
@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
    scroll-behavior: smooth; /* 平滑滚动 */
  }

  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
  
  /* 添加滚动的辅助类，应用在可滚动容器上 */
  .scroll-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch; /* 在iOS上提供滚动惯性 */
  }

  .animate-shimmer {
    animation: shimmer 3s infinite linear;
  }
}

/* 任务进度条样式 */
.h-1\.5 > div:last-child {
  background-color: #10b981 !important; /* 翠绿色 */
}

/* 使用在HistoryCard中的进度条样式 */
.bg-white\/20 + div {
  background-color: #10b981 !important; /* 翠绿色 */
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
