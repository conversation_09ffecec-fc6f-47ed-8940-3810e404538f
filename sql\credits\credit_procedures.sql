-- 创建消费积分存储过程，确保数据一致性和原子性
CREATE OR REPLACE FUNCTION consume_credits(
    p_user_id UUID,
    p_amount INT,
    p_type VARCHAR(50),
    p_description TEXT DEFAULT '',
    p_payment_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_balance INT;
    v_new_balance INT;
    v_transaction_id UUID;
    v_transaction JSONB;
    v_existing_id UUID;
BEGIN
    -- 确保金额为正数
    IF p_amount <= 0 THEN
        RAISE EXCEPTION '消费积分金额必须为正数';
    END IF;

    -- 幂等性检查：检查是否已经存在相同payment_id的交易记录
    IF p_payment_id IS NOT NULL THEN
        -- 检查是否已经存在相同payment_id的交易
        SELECT id INTO v_existing_id
        FROM credit_transactions
        WHERE payment_id = p_payment_id;
        
        -- 如果已存在记录，返回当前信息
        IF v_existing_id IS NOT NULL THEN
            -- 获取交易详情
            SELECT 
                jsonb_build_object(
                    'id', id,
                    'user_id', user_id,
                    'type', type,
                    'amount', amount,
                    'description', description,
                    'status', status,
                    'payment_id', payment_id,
                    'created_at', created_at
                ) INTO v_transaction
            FROM credit_transactions
            WHERE id = v_existing_id;
            
            -- 直接获取当前余额
            SELECT credits INTO v_current_balance 
            FROM user_credit_balances 
            WHERE user_id = p_user_id;
            
            -- 返回已存在的交易信息
            RETURN jsonb_build_object(
                'status', 'already_exists',
                'message', '该交易记录已存在，避免重复处理',
                'transaction', v_transaction,
                'balance', COALESCE(v_current_balance, 0)
            );
        END IF;
    END IF;

    -- 使用事务确保原子性操作
    BEGIN
        -- 获取当前余额（使用FOR UPDATE锁定行）
        SELECT credits INTO v_current_balance
        FROM user_credit_balances
        WHERE user_id = p_user_id
        FOR UPDATE;
        
        -- 如果用户没有余额记录，则初始化一个
        IF v_current_balance IS NULL THEN
            -- 首先检查用户是否存在于系统中
            IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
                RAISE EXCEPTION '用户不存在';
            END IF;
            
            -- 查询用户交易记录，计算余额
            SELECT COALESCE(SUM(amount), 0) INTO v_current_balance
            FROM credit_transactions
            WHERE user_id = p_user_id AND status = 'completed';
            
            -- 如果没有交易记录，设置为0
            v_current_balance := COALESCE(v_current_balance, 0);
            
            -- 插入新的余额记录
            INSERT INTO user_credit_balances (user_id, credits, updated_at)
            VALUES (p_user_id, v_current_balance, NOW());
        END IF;
        
        -- 检查余额是否足够
        IF v_current_balance < p_amount THEN
            RAISE EXCEPTION '积分余额不足 (当前: %, 需要: %)', v_current_balance, p_amount;
        END IF;
        
        -- 计算新余额
        v_new_balance := v_current_balance - p_amount;
        
        -- 创建消费交易记录
        INSERT INTO credit_transactions (
            user_id,
            type,
            amount,
            description,
            payment_id,
            status,
            created_at
        ) VALUES (
            p_user_id,
            p_type,
            -p_amount, -- 消费为负数
            p_description,
            p_payment_id,
            'completed',
            NOW()
        )
        RETURNING id INTO v_transaction_id;
        
        -- 获取交易详情
        SELECT 
            jsonb_build_object(
                'id', id,
                'user_id', user_id,
                'type', type,
                'amount', amount,
                'description', description,
                'status', status,
                'payment_id', payment_id,
                'created_at', created_at
            ) INTO v_transaction
        FROM credit_transactions
        WHERE id = v_transaction_id;
        
        -- 更新用户余额（使用一次性更新，不再使用乐观锁）
        UPDATE user_credit_balances
        SET 
            credits = v_new_balance,
            last_transaction_id = v_transaction_id,
            updated_at = NOW()
        WHERE user_id = p_user_id;
        
        -- 返回结果
        RETURN jsonb_build_object(
            'transaction', v_transaction,
            'new_balance', COALESCE(v_new_balance, 0),
            'previous_balance', COALESCE(v_current_balance, 0)
        );
    EXCEPTION
        WHEN unique_violation THEN
            -- 处理并发情况下可能出现的唯一约束冲突
            IF p_payment_id IS NOT NULL THEN
                -- 获取已存在交易ID
                SELECT id INTO v_existing_id
                FROM credit_transactions
                WHERE payment_id = p_payment_id;
                
                -- 获取交易详情
                IF v_existing_id IS NOT NULL THEN
                    SELECT 
                        jsonb_build_object(
                            'id', id,
                            'user_id', user_id,
                            'type', type,
                            'amount', amount,
                            'description', description,
                            'status', status,
                            'payment_id', payment_id,
                            'created_at', created_at
                        ) INTO v_transaction
                    FROM credit_transactions
                    WHERE id = v_existing_id;
                END IF;
            END IF;
            
            -- 获取当前余额
            SELECT credits INTO v_current_balance 
            FROM user_credit_balances 
            WHERE user_id = p_user_id;
            
            -- 返回已存在信息
            RETURN jsonb_build_object(
                'status', 'already_exists',
                'message', '该交易记录已存在（唯一约束冲突）',
                'transaction', v_transaction,
                'balance', COALESCE(v_current_balance, 0)
            );
            
        WHEN insufficient_privilege THEN
            RAISE EXCEPTION '权限不足，无法执行积分消费操作';
            
        WHEN OTHERS THEN
            -- 捕获其他异常并重新抛出
            RAISE;
    END;
END;
$$;

-- 为credit_transactions表添加payment_id字段和唯一索引
-- 首先添加字段（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'credit_transactions' AND column_name = 'payment_id'
    ) THEN
        ALTER TABLE credit_transactions ADD COLUMN payment_id UUID;
    END IF;
END $$;
