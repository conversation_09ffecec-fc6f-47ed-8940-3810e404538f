import { RefObject } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Checkbox } from "../ui/checkbox";
import { Label } from "../ui/label";
import { ScrollArea } from "../ui/scroll-area";
import { UploadFormState, UploadState, GENERATION_TOOLS, VideoSource } from "@/types/upload";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface UploadFormProps {
    formState: UploadFormState;
    uploadState: UploadState;
    fileInputRef: RefObject<HTMLInputElement>;
    handleInputChange: (field: keyof UploadFormState, value: string) => void;
    handleGenerationTypeChange: (value: "txt2video" | "img2video") => void;
    handleVideoSourceChange: (value: VideoSource) => void;
    handleAddTag: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    removeTag: (tagToRemove: string) => void;
    toggleTool: (tool: string) => void;
    handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    uploadVideo: () => void;
    triggerFileInput: () => void;
    tagError: string | null;
}

export const UploadForm = ({
    formState,
    uploadState,
    fileInputRef,
    handleInputChange,
    handleGenerationTypeChange,
    handleVideoSourceChange,
    handleAddTag,
    removeTag,
    toggleTool,
    handleFileChange,
    uploadVideo,
    triggerFileInput,
    tagError
}: UploadFormProps) => {
    const {
        title, description, tags, prompt, negativePrompt,
        guidanceScale, steps, seed, generationType,
        modelResources, selectedTools, videoSource
    } = formState;

    const {
        uploadError
    } = uploadState;

    // 处理Tabs的值变化，确保类型安全
    const handleTabChange = (value: string) => {
        if (value === "local" || value === "reelmind") {
            handleVideoSourceChange(value);
        }
    };

    return (
        <div className="w-1/2 bg-card text-card-foreground overflow-y-auto">
            <div className="p-6 space-y-6">
                {/* 错误提示 */}
                {uploadError && (
                    <div className="p-3 bg-destructive/10 border border-destructive text-destructive rounded-md">
                        {uploadError}
                    </div>
                )}

                {/* 视频来源选择 - 改为Tab形式 */}
                <Tabs
                    defaultValue={videoSource}
                    onValueChange={handleTabChange}
                    className="w-full"
                >
                    <TabsList className="grid w-full grid-cols-2 h-auto">
                        <TabsTrigger value="local" className="text-lg">本地上传</TabsTrigger>
                        <TabsTrigger value="reelmind" className="text-lg">ReelMind 平台生成</TabsTrigger>
                    </TabsList>
                    <TabsContent value="local" className="mt-2">
                        <div>
                            <input
                                type="file"
                                accept="video/*"
                                onChange={(e) => {
                                    handleFileChange(e);
                                    // 文件选择后自动开始上传
                                    if (e.target.files && e.target.files.length > 0) {
                                        // 使用setTimeout确保状态更新后再上传
                                        setTimeout(() => {
                                            uploadVideo();
                                        }, 100);
                                    }
                                }}
                                className="hidden"
                                ref={fileInputRef}
                            />
                        </div>
                    </TabsContent>
                    <TabsContent value="reelmind" className="mt-2">
                        <div className="p-4 border rounded-md bg-muted/50 text-center">
                            <p>从 ReelMind 平台选择已生成的视频</p>
                            <Button className="mt-2" variant="outline">浏览 ReelMind 视频</Button>
                        </div>
                    </TabsContent>
                </Tabs>

                <Input
                    type="text"
                    placeholder="添加标题..."
                    value={title}
                    onChange={(e) => handleInputChange("title", e.target.value)}
                    className="text-xl font-semibold bg-background"
                />

                <div className="space-y-2">
                    <h3 className="font-medium mb-2">标签</h3>
                    <div className="p-3 bg-background rounded-md border">
                        <div className="flex flex-wrap gap-2 mb-3">
                            {tags.map((tag) => (
                                <Badge
                                    key={tag}
                                    variant="secondary"
                                    className="px-3 py-1.5 text-sm flex items-center gap-1 hover:bg-destructive/10 hover:text-destructive transition-colors"
                                >
                                    {tag}
                                    <span
                                        className="cursor-pointer ml-1 rounded-full hover:bg-destructive/20 p-0.5"
                                        onClick={() => removeTag(tag)}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                            <path d="M18 6 6 18"></path>
                                            <path d="m6 6 12 12"></path>
                                        </svg>
                                    </span>
                                </Badge>
                            ))}
                            {tags.length === 0 && (
                                <p className="text-sm text-muted-foreground">添加标签以便更好地分类您的视频</p>
                            )}
                        </div>
                        <div className="relative">
                            <Input
                                type="text"
                                placeholder="添加标签（按回车确认）"
                                onKeyDown={handleAddTag}
                                className={`text-sm bg-background pr-24 ${tagError ? 'border-destructive' : ''}`}
                            />
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">
                                {tags.length}/10
                            </div>
                        </div>
                        {tagError ? (
                            <p className="mt-1.5 text-xs text-destructive">
                                {tagError}
                            </p>
                        ) : (
                            <p className="mt-1.5 text-xs text-muted-foreground">
                                最多添加10个标签，每个标签不超过20个字符
                            </p>
                        )}
                    </div>
                </div>

                <Textarea
                    placeholder="添加描述..."
                    value={description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    className="min-h-[100px] bg-background"
                />

                {/* 生图类型选择 - 改为方块型2选1 */}
                <div className="space-y-2">
                    <h3 className="font-medium">生图类型</h3>
                    <div className="grid grid-cols-2 gap-4">
                        <div
                            className={`border rounded-md p-4 cursor-pointer transition-all ${generationType === "txt2video"
                                ? "border-primary bg-primary/10"
                                : "border-border hover:border-primary/50"
                                }`}
                            onClick={() => handleGenerationTypeChange("txt2video")}
                        >
                            <div className="flex items-center justify-between mb-2">
                                <h4 className="text-sm">文生图</h4>
                                <div className={`w-4 h-4 rounded-full ${generationType === "txt2video"
                                    ? "bg-primary"
                                    : "border border-muted-foreground"
                                    }`}></div>
                            </div>
                            <p className="text-sm text-muted-foreground">Text to Video</p>
                        </div>
                        <div
                            className={`border rounded-md p-4 cursor-pointer transition-all ${generationType === "img2video"
                                ? "border-primary bg-primary/10"
                                : "border-border hover:border-primary/50"
                                }`}
                            onClick={() => handleGenerationTypeChange("img2video")}
                        >
                            <div className="flex items-center justify-between mb-2">
                                <h4 className="text-sm">图生图</h4>
                                <div className={`w-4 h-4 rounded-full ${generationType === "img2video"
                                    ? "bg-primary"
                                    : "border border-muted-foreground"
                                    }`}></div>
                            </div>
                            <p className="text-sm text-muted-foreground">Image to Video</p>
                        </div>
                    </div>
                </div>

                <div className="space-y-4">
                    <div>
                        <h3 className="font-medium mb-2">生成提示词</h3>
                        <Textarea
                            placeholder="输入用于生成此视频的提示词..."
                            value={prompt}
                            onChange={(e) => handleInputChange("prompt", e.target.value)}
                            className="min-h-[100px] bg-background"
                        />
                    </div>

                    <div>
                        <h3 className="font-medium mb-2">负面提示词</h3>
                        <Textarea
                            placeholder="输入负面提示词..."
                            value={negativePrompt}
                            onChange={(e) => handleInputChange("negativePrompt", e.target.value)}
                            className="min-h-[100px] bg-background"
                        />
                    </div>

                    {/* 模型资源 */}
                    <div>
                        <h3 className="font-medium mb-2">模型资源</h3>
                        <Textarea
                            placeholder="输入用到的模型资源，例如 Models, LoRAs, embeddings 等..."
                            value={modelResources}
                            onChange={(e) => handleInputChange("modelResources", e.target.value)}
                            className="min-h-[100px] bg-background"
                        />
                    </div>

                    {/* AIGC 参数 */}
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <h3 className="font-medium mb-2">Guidance Scale</h3>
                            <Input
                                type="text"
                                placeholder="例如: 7.5"
                                value={guidanceScale}
                                onChange={(e) => handleInputChange("guidanceScale", e.target.value)}
                                className="bg-background"
                            />
                        </div>
                        <div>
                            <h3 className="font-medium mb-2">Steps</h3>
                            <Input
                                type="text"
                                placeholder="例如: 30"
                                value={steps}
                                onChange={(e) => handleInputChange("steps", e.target.value)}
                                className="bg-background"
                            />
                        </div>
                        <div className="col-span-2">
                            <h3 className="font-medium mb-2">Seed</h3>
                            <Input
                                type="text"
                                placeholder="例如: 1234567890"
                                value={seed}
                                onChange={(e) => handleInputChange("seed", e.target.value)}
                                className="bg-background"
                            />
                        </div>
                    </div>

                    {/* 生图工具选择 */}
                    <div>
                        <h3 className="font-medium mb-2">生图工具</h3>
                        <ScrollArea className="h-[200px] border rounded-md p-4 bg-background">
                            <div className="grid grid-cols-2 gap-2">
                                {GENERATION_TOOLS.map((tool) => (
                                    <div key={tool} className="flex items-center space-x-2">
                                        <Checkbox
                                            id={`tool-${tool}`}
                                            checked={selectedTools.includes(tool)}
                                            onCheckedChange={() => toggleTool(tool)}
                                        />
                                        <Label htmlFor={`tool-${tool}`} className="text-sm">{tool}</Label>
                                    </div>
                                ))}
                            </div>
                        </ScrollArea>
                    </div>
                </div>
            </div>
        </div>
    );
}; 