# 会员系统设计与实现指南

本文档介绍会员系统的完整设计和实现，包括会员开通、续费、权益配置、方案配置、优先级管理以及与支付系统的集成。

## 一、设计目标

- 档位、价格可灵活配置，方便运营，快速切入市场
- 会员权益可扩展，支持多种类型的会员特权
- 完整的会员生命周期管理：开通、续费、取消等
- 多种支付方式集成，确保用户支付体验
- 会员优先级系统，提供不同级别会员的差异化服务

## 二、会员档位设定

用户设定为三个档位：

1. 普通用户(FREE) - 非会员，基础功能
2. Pro会员(PRO) - 中级会员，优先处理任务
3. Max会员(MAX) - 高级会员，最高优先级处理任务

可根据运营情况和具体需求，随时添加新的会员档位。

## 三、定价体系

采用月度订阅和年度订阅两种模式，价格为：

1. Pro会员(PRO)，月付12美元，年付10美元/月
2. Max会员(MAX)，月付28美元，年付22美元/月

可根据运营情况和具体需求，随时调整各档位的价格。

## 四、会员权益系统

会员权益系统支持多种类型的权益配置，权益与会员计划(plan)关联，而非直接与会员级别(level)关联，提供更灵活的权益管理。

### 权益类型(InterestType)

- PRIORITY - 优先级权益
- QUOTA - 配额权益
- FEATURE - 功能权益
- DISCOUNT - 折扣权益
- OTHER - 其他权益

### 权益键名(InterestKey)

- TASK_PRIORITY - 任务优先级
- GENERATION_QUOTA - 生成配额
- ADVANCED_EDITING - 高级编辑功能
- NO_WATERMARK - 无水印导出
- CUSTOMER_SUPPORT - 客户支持
- TEAM_COLLABORATION - 团队协作功能
- ...其他权益

## 五、数据结构

### 1. 会员等级（MembershipLevel）

```typescript
enum MembershipLevel {
  FREE = 0, // 非会员
  PRO = 1, // Pro会员
  MAX = 2, // Max会员
}
```

### 2. 会员计划表（membership_plans）

```sql
CREATE TABLE IF NOT EXISTS public.membership_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level SMALLINT NOT NULL, -- 0: FREE, 1: PRO, 2: MAX
    monthly_price DECIMAL(10, 2) NOT NULL,
    yearly_price DECIMAL(10, 2) NOT NULL,
    is_popular BOOLEAN NOT NULL DEFAULT false,
    features JSONB NOT NULL DEFAULT '[]'::jsonb,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    UNIQUE (level)
);
```

### 3. 会员优先级表（membership_priorities）

```sql
CREATE TABLE IF NOT EXISTS public.membership_priorities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level SMALLINT NOT NULL, -- 0: FREE, 1: PRO, 2: MAX
    priority INTEGER NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    UNIQUE (level)
);
```

### 4. 用户会员表（user_memberships）

```sql
CREATE TABLE IF NOT EXISTS public.user_memberships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    level SMALLINT NOT NULL DEFAULT 0, -- 0: FREE, 1: PRO, 2: MAX
    plan_id UUID REFERENCES membership_plans(id),
    is_active BOOLEAN NOT NULL DEFAULT true,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    payment_id UUID,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);
```

### 5. 用户角色表（user_roles）

```sql
CREATE TABLE IF NOT EXISTS public.user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    UNIQUE (user_id, role)
);
```

### 6. 支付表（payments）

```sql
CREATE TABLE IF NOT EXISTS public.payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL, -- 'membership', 'credits', 'other'
    amount DECIMAL(10, 2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    payment_url TEXT,
    transaction_id VARCHAR(100),
    metadata JSONB,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);
```

### 7. 会员权益表（membership_interests）

```sql
CREATE TABLE IF NOT EXISTS public.membership_interests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plan_id UUID NOT NULL REFERENCES membership_plans(id) ON DELETE CASCADE,
    interest_type VARCHAR(20) NOT NULL,
    interest_key VARCHAR(50) NOT NULL,
    interest_value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    UNIQUE (plan_id, interest_key)
);
```

## 六、系统模块

### 1. 会员模块(MembershipModule)

核心模块，包含以下服务：

- MembershipService - 会员管理服务
- MembershipPriceService - 会员价格服务
- MembershipInterestService - 会员权益服务

这些服务共同管理用户的会员状态、权益和价格相关操作。

### 2. API接口

#### 会员管理接口

1. 获取会员信息

   - 路径: `GET /membership`
   - 鉴权: 需要用户登录
   - 返回: 用户当前的会员状态和剩余天数

2. 获取会员方案列表

   - 路径: `GET /membership/plans`
   - 返回: 所有可用的会员方案列表，包含价格和特权

3. 开通会员

   - 路径: `POST /membership/create`
   - 鉴权: 需要用户登录
   - 参数:
     - level: 会员等级
     - months: 购买月份
     - payment_id: 支付ID（可选）

4. 续费会员

   - 路径: `POST /membership/renew`
   - 鉴权: 需要用户登录
   - 参数:
     - level: 会员等级
     - months: 续费月份
     - payment_id: 支付ID（可选）

5. 取消会员
   - 路径: `POST /membership/cancel`
   - 鉴权: 需要用户登录

#### 会员权益接口

1. 获取所有会员权益

   - 路径: `GET /membership-interests`
   - 鉴权: 需要管理员权限
   - 支持查询参数: plan_id, interest_type, interest_key, is_active

2. 获取指定会员计划的权益

   - 路径: `GET /membership-interests/plan/:planId`
   - 鉴权: 需要管理员权限

3. 创建或更新会员权益

   - 路径: `POST /membership-interests`
   - 鉴权: 需要管理员权限
   - 参数:
     - plan_id: 会员计划ID
     - interest_type: 权益类型
     - interest_key: 权益键名
     - interest_value: 权益值
     - description: 权益描述（可选）

4. 禁用会员权益
   - 路径: `POST /membership-interests/:id/deactivate`
   - 鉴权: 需要管理员权限

#### 管理接口

1. 更新会员优先级

   - 路径: `PATCH /membership/admin/priority`
   - 鉴权: 需要管理员权限
   - 参数:
     - level: 会员等级
     - priority: 优先级

2. 更新会员方案
   - 路径: `POST /membership/admin/plan`
   - 鉴权: 需要管理员权限
   - 参数:
     - level: 会员等级
     - monthly_price: 月付价格
     - yearly_price: 年付价格
     - is_popular: 是否为推荐方案
     - features: 会员特权列表（可选）

#### 支付接口

1. 创建支付订单

   - 路径: `POST /payment`
   - 鉴权: 需要用户登录
   - 请求体: 包含支付类型、金额、元数据

2. 获取用户支付记录

   - 路径: `GET /payment/user-payments`
   - 鉴权: 需要用户登录

3. 获取支付详情

   - 路径: `GET /payment/:paymentId`
   - 鉴权: 需要用户登录

4. 取消支付

   - 路径: `POST /payment/:paymentId/cancel`
   - 鉴权: 需要用户登录

5. 支付回调接口（由支付服务商调用）
   - 路径: `POST /payment/callback`
   - 鉴权: 需要API密钥验证

## 七、会员系统工作流程

1. 用户浏览可用会员方案 (`GET /membership/plans`)
2. 用户选择会员级别和购买月份
3. 创建支付订单 (`POST /payment`)
4. 用户完成支付后，支付服务商调用回调接口 (`POST /payment/callback`)
5. 系统验证支付状态，激活用户会员 (`MembershipService.activateMembership`)
6. 用户生成视频任务时自动应用会员权益
   - 系统检查用户会员权益 (`MembershipService.hasInterest`)
   - 应用相应的任务优先级或其他权益
7. 系统处理任务时按优先级排序

## 八、价格计算规则

1. 月付用户：直接使用monthly_price
2. 年付用户：使用yearly_price（按月计算的价格）× 购买月份数
3. 例如：
   - Pro会员月付：$12/月
   - Pro会员年付：$10/月 × 12个月 = $120/年

## 九、配置支付接口

为了使支付功能正常工作，需要配置以下环境变量：

```
# 支付相关配置
PAYMENT_BASE_URL=https://pay.example.com  # 支付网关地址
PAYMENT_API_KEY=your_payment_api_key      # 支付密钥
API_KEY=your_api_key                      # 用于支付回调验证的API密钥
```

在实际应用中，需要根据选择的支付服务商替换支付服务中的`generatePaymentUrl`方法实现，集成真实的支付API。

## 十、部署和迁移指南

### 数据库迁移

执行数据库迁移脚本，创建或更新以下表：

1. membership_plans
2. membership_priorities
3. user_memberships
4. user_roles
5. payments
6. membership_interests

### 代码部署

部署包含以下模块的代码：

1. 会员模块

   - src/membership/membership.module.ts
   - src/membership/membership.service.ts
   - src/membership/membership-price.service.ts
   - src/membership/membership-interest.service.ts
   - src/membership/membership.controller.ts
   - src/membership/membership-interest.controller.ts
   - src/membership/dto/membership.dto.ts

2. 支付模块

   - src/payment/payment.module.ts
   - src/payment/payment.service.ts
   - src/payment/payment.controller.ts
   - src/payment/dto/payment.dto.ts

3. 更新依赖和配置
   - 更新app.module.ts，导入新模块
   - 配置环境变量

### 部署检查清单

- [ ] 执行数据库迁移脚本
- [ ] 部署更新的代码文件
- [ ] 配置环境变量
- [ ] 测试会员开通流程
- [ ] 测试会员续费流程
- [ ] 测试会员权益分配
- [ ] 测试任务优先级功能
- [ ] 验证管理接口功能
- [ ] 确认支付回调正常工作

## 十一、重要说明

1. 会员等级使用`MembershipLevel`枚举定义，确保代码中一致使用
2. 会员优先级可在后台动态配置，默认值为非会员0、Pro会员20、Max会员30
3. 会员权益与会员计划(plan)关联，而非直接与会员级别(level)关联
4. 定期检查用户会员状态，及时更新过期会员
5. 支付系统需要增强安全措施，确保支付回调的可靠性和安全性
6. 所有配置都应从数据库加载，避免硬编码值，提高系统灵活性
