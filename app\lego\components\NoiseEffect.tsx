"use client";

import { useEffect, useRef } from "react";

export default function NoiseEffect() {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d", { alpha: true });
        if (!ctx) return;

        // 设置canvas尺寸为容器尺寸
        const resizeCanvas = () => {
            const parent = canvas.parentElement;
            if (parent) {
                canvas.width = parent.clientWidth;
                canvas.height = parent.clientHeight;
            }
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画参数
        let frame = 0;
        const fps = 30;
        const totalFrames = fps * 6; // 6秒一个循环
        let lastTime = 0;
        let animationId: number;

        // 预计算一些图案模板
        const patternCount = 5;
        const patterns = Array.from({ length: patternCount }, () => {
            const size = 10;
            const pattern = new Uint8ClampedArray(size * size);
            for (let i = 0; i < pattern.length; i++) {
                pattern[i] = Math.random() < 0.5 ? 0 : 255;
            }
            return pattern;
        });

        // 创建噪点动画
        const drawNoise = (timestamp: number) => {
            // 帧率控制
            if (timestamp - lastTime < 1000 / fps) {
                animationId = requestAnimationFrame(drawNoise);
                return;
            }
            lastTime = timestamp;

            // 计算当前清晰度，使用正弦波使其在0.2和0.8之间循环
            const progress = frame / totalFrames;
            const clarity = 0.2 + (Math.sin(progress * Math.PI * 2) + 1) * 0.3;

            // 在整个canvas上绘制噪点
            const imageData = ctx.createImageData(canvas.width, canvas.height);
            const data = imageData.data;

            // 创建几个动态亮点的位置
            const hotspots = [
                // 左上角 - 动态变化
                {
                    x: canvas.width * 0.1 + Math.sin(progress * Math.PI * 3) * canvas.width * 0.05,
                    y: canvas.height * 0.1 + Math.cos(progress * Math.PI * 2) * canvas.height * 0.05,
                    intensity: 0.6 + Math.sin(progress * Math.PI * 4) * 0.4,
                    radius: canvas.width * (0.2 + Math.sin(progress * Math.PI) * 0.1)
                },
                // 右下角 - 动态变化
                {
                    x: canvas.width * 0.9 + Math.cos(progress * Math.PI * 2.5) * canvas.width * 0.05,
                    y: canvas.height * 0.9 + Math.sin(progress * Math.PI * 3.5) * canvas.height * 0.05,
                    intensity: 0.6 + Math.cos(progress * Math.PI * 3) * 0.4,
                    radius: canvas.width * (0.25 + Math.cos(progress * Math.PI * 1.5) * 0.1)
                },
                // 中央点
                {
                    x: canvas.width * 0.5,
                    y: canvas.height * 0.5,
                    intensity: 0.7 + Math.sin(progress * Math.PI * 2) * 0.3,
                    radius: canvas.width * 0.4
                }
            ];

            // 添加渐变效果，结合多个动态亮点
            for (let y = 0; y < canvas.height; y++) {
                for (let x = 0; x < canvas.width; x++) {
                    const index = (y * canvas.width + x) * 4;

                    // 计算到各亮点的距离和影响
                    let totalInfluence = 0;

                    for (const hotspot of hotspots) {
                        const distance = Math.sqrt(Math.pow(x - hotspot.x, 2) + Math.pow(y - hotspot.y, 2));
                        const influence = Math.max(0, 1 - distance / hotspot.radius) * hotspot.intensity;
                        totalInfluence = Math.max(totalInfluence, influence);
                    }

                    // 结合距离和时间因素调整噪点强度
                    const pixelClarity = clarity * (0.4 + totalInfluence * 0.6);

                    // 使用预计算的图案添加纹理
                    const patternX = x % 10;
                    const patternY = y % 10;
                    const patternIndex = Math.floor((x + y) / 20) % patternCount;
                    const patternValue = patterns[patternIndex][patternY * 10 + patternX];

                    // 结合随机性与模式
                    const randomThreshold = Math.max(0.1, Math.min(0.9, pixelClarity));
                    const isNoise = Math.random() < randomThreshold;
                    const randomValue = Math.floor(Math.random() * 200) + 30; // 避免纯黑纯白
                    const noiseValue = isNoise ? randomValue : patternValue;

                    // 根据位置和影响添加颜色变化
                    // 左上角偏蓝，右下角偏紫
                    let r = noiseValue;
                    let g = noiseValue;
                    let b = noiseValue;

                    // 为左上角添加蓝色调
                    if (totalInfluence > 0.5 && x < canvas.width * 0.4 && y < canvas.height * 0.4) {
                        const blueIntensity = totalInfluence * 0.3 * (0.6 + Math.sin(progress * Math.PI * 3) * 0.4);
                        b = Math.min(255, noiseValue + blueIntensity * 50);
                        r = Math.max(0, noiseValue - blueIntensity * 30);
                    }

                    // 为右下角添加紫色调
                    if (totalInfluence > 0.5 && x > canvas.width * 0.6 && y > canvas.height * 0.6) {
                        const purpleIntensity = totalInfluence * 0.3 * (0.6 + Math.cos(progress * Math.PI * 2.5) * 0.4);
                        r = Math.min(255, noiseValue + purpleIntensity * 40);
                        b = Math.min(255, noiseValue + purpleIntensity * 40);
                        g = Math.max(0, noiseValue - purpleIntensity * 20);
                    }

                    // 设置RGBA值
                    data[index] = r;
                    data[index + 1] = g;
                    data[index + 2] = b;
                    data[index + 3] = Math.floor(100 + Math.random() * 30 + totalInfluence * 70); // 透明度随影响变化
                }
            }

            ctx.putImageData(imageData, 0, 0);

            // 更新帧计数
            frame = (frame + 1) % totalFrames;

            // 继续动画循环
            animationId = requestAnimationFrame(drawNoise);
        };

        // 开始动画
        animationId = requestAnimationFrame(drawNoise);

        // 组件卸载时清理
        return () => {
            window.removeEventListener('resize', resizeCanvas);
            cancelAnimationFrame(animationId);
        };
    }, []);

    return (
        <canvas
            ref={canvasRef}
            className="absolute inset-0 w-full h-full bg-black/20"
            style={{ mixBlendMode: 'multiply' }}
        />
    );
} 