import { IsNotEmpty, IsOptional, IsString, IsDateString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateBlogPostDto {
    @ApiProperty({ description: '博客文章标题' })
    @IsNotEmpty({ message: '标题不能为空' })
    @IsString()
    title: string;

    @ApiProperty({ description: '博客文章URL友好的slug' })
    @IsNotEmpty({ message: 'slug不能为空' })
    @IsString()
    slug: string;

    @ApiProperty({ description: '博客文章内容' })
    @IsNotEmpty({ message: '内容不能为空' })
    @IsString()
    content: string;

    @ApiProperty({ description: '博客文章摘要' })
    @IsNotEmpty({ message: '摘要不能为空' })
    @IsString()
    excerpt: string;

    @ApiPropertyOptional({ description: '封面图片URL' })
    @IsNotEmpty()
    @IsString()
    cover_image?: string;

    @ApiPropertyOptional({ description: '发布时间' })
    @IsOptional()
    @IsDateString()
    published_at?: string;

    @ApiPropertyOptional({ description: 'SEO标题' })
    @IsOptional()
    @IsString()
    seo_title?: string;

    @ApiPropertyOptional({ description: 'SEO描述' })
    @IsOptional()
    @IsString()
    seo_description?: string;

    @ApiPropertyOptional({ description: 'SEO关键词' })
    @IsOptional()
    @IsString()
    seo_keywords?: string;

    @ApiPropertyOptional({ description: '文章分类' })
    @IsOptional()
    @IsString()
    category?: string;

    @ApiPropertyOptional({ description: '文章标签，逗号分隔' })
    @IsOptional()
    @IsString()
    tags?: string;

    @ApiPropertyOptional({ description: '封面图片生成提示词' })
    @IsOptional()
    @IsString()
    cover_img_prompt?: string;
}

export class UpdateBlogPostDto {
    @ApiPropertyOptional({ description: '博客文章标题' })
    @IsOptional()
    @IsString()
    title?: string;

    @ApiPropertyOptional({ description: '博客文章URL友好的slug' })
    @IsOptional()
    @IsString()
    slug?: string;

    @ApiPropertyOptional({ description: '博客文章内容' })
    @IsOptional()
    @IsString()
    content?: string;

    @ApiPropertyOptional({ description: '博客文章摘要' })
    @IsOptional()
    @IsString()
    excerpt?: string;

    @ApiPropertyOptional({ description: '封面图片URL' })
    @IsOptional()
    @IsString()
    cover_image?: string;

    @ApiPropertyOptional({ description: '发布时间' })
    @IsOptional()
    @IsDateString()
    published_at?: string;

    @ApiPropertyOptional({ description: 'SEO标题' })
    @IsOptional()
    @IsString()
    seo_title?: string;

    @ApiPropertyOptional({ description: 'SEO描述' })
    @IsOptional()
    @IsString()
    seo_description?: string;

    @ApiPropertyOptional({ description: 'SEO关键词' })
    @IsOptional()
    @IsString()
    seo_keywords?: string;

    @ApiPropertyOptional({ description: '文章分类' })
    @IsOptional()
    @IsString()
    category?: string;

    @ApiPropertyOptional({ description: '文章标签，逗号分隔' })
    @IsOptional()
    @IsString()
    tags?: string;
}

export class BlogPostResponseDto {
    @ApiProperty({ description: '博客文章ID' })
    id: string;

    @ApiProperty({ description: '博客文章标题' })
    title: string;

    @ApiProperty({ description: '博客文章URL友好的slug' })
    slug: string;

    @ApiProperty({ description: '博客文章内容' })
    content: string;

    @ApiProperty({ description: '博客文章摘要' })
    excerpt: string;

    @ApiPropertyOptional({ description: '封面图片URL' })
    cover_image?: string;

    @ApiProperty({ description: '发布时间' })
    published_at: string;

    @ApiPropertyOptional({ description: 'SEO标题' })
    seo_title?: string;

    @ApiPropertyOptional({ description: 'SEO描述' })
    seo_description?: string;

    @ApiPropertyOptional({ description: 'SEO关键词' })
    seo_keywords?: string;

    @ApiPropertyOptional({ description: '文章分类' })
    category?: string;

    @ApiPropertyOptional({ description: '文章标签，逗号分隔' })
    tags?: string;

    @ApiProperty({ description: '创建时间' })
    created_at: string;
}

export class BlogPostListResponseDto {
    @ApiProperty({ description: '博客文章列表', type: [BlogPostResponseDto] })
    posts: BlogPostResponseDto[];

    @ApiProperty({ description: '总数' })
    total: number;
}

export class BlogPostQueryDto {
    @ApiPropertyOptional({ description: '每页数量', default: 10 })
    @IsOptional()
    limit?: number;

    @ApiPropertyOptional({ description: '偏移量', default: 0 })
    @IsOptional()
    offset?: number;

    @ApiPropertyOptional({ description: '分类' })
    @IsOptional()
    @IsString()
    category?: string;
}
