"use client"

import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export function FAQSection() {
  return (
    <div className="w-full max-w-3xl mx-auto">
      <h2 className="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h2>
      <Accordion type="single" collapsible className="w-full">
        {/* Membership FAQs */}
        <AccordionItem value="item-1">
          <AccordionTrigger>What are the benefits of PRO membership?</AccordionTrigger>
          <AccordionContent>
            PRO membership gives you 1000 credits every month (worth $10), priority access to new features,
            faster video generation, advanced editing options, and priority customer support.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-2">
          <AccordionTrigger>Can I cancel my subscription at any time?</AccordionTrigger>
          <AccordionContent>
            Yes, you can cancel your subscription at any time. Your benefits will continue until the end of your current billing period.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-3">
          <AccordionTrigger>Is there a difference between monthly and yearly subscriptions?</AccordionTrigger>
          <AccordionContent>
            Yes, yearly subscriptions offer a significant discount compared to monthly subscriptions.
            You'll save approximately 20% when choosing annual billing.
          </AccordionContent>
        </AccordionItem>

        {/* Credits FAQs */}
        <AccordionItem value="item-4">
          <AccordionTrigger>What are ReelMind Credits?</AccordionTrigger>
          <AccordionContent>
            ReelMind Credits are the virtual currency used on our platform to generate AI videos and images.
            Different features require different amounts of credits. For example, generating a standard video (480p)
            costs 50 credits, while an HD video (720p) costs 80 credits.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-5">
          <AccordionTrigger>How can I earn credits?</AccordionTrigger>
          <AccordionContent>
            You can earn credits in several ways:
            <ul className="list-disc pl-6 mt-2 space-y-1">
              <li>Through membership plans (PRO members get 1000 credits monthly, MAX members get 2000)</li>
              <li>By purchasing credit packs</li>
              <li>New users receive 100 free credits upon registration</li>
              <li>Posting content on ReelMind (50 credits per day)</li>
              <li>Special promotions and rewards</li>
            </ul>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-6">
          <AccordionTrigger>Do credits expire?</AccordionTrigger>
          <AccordionContent>
            The credits you purchase directly will not expire and will remain in your account until used.
            Your monthly granted credits will be reset at the beginning of each month. Any unused credits from previous months will not carry over.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-7">
          <AccordionTrigger>Can I get a refund for unused credits?</AccordionTrigger>
          <AccordionContent>
            We do not offer refunds for purchased credits. However, if you encounter technical issues that
            prevented the proper use of your credits, please contact our support team, and we'll address your concerns.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-8">
          <AccordionTrigger>What's the difference between membership and credits?</AccordionTrigger>
          <AccordionContent>
            Memberships provide a monthly allotment of credits along with additional benefits like priority processing
            and access to exclusive features. Credits can be purchased separately without a membership and are consumed
            when generating content. Choose a membership for regular usage or buy credits for occasional use.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}

