/**
 * 训练任务状态
 */
export enum TrainTaskStatus {
    PENDING = 'pending',    // 等待中
    PROCESSING = 'processing', // 处理中
    COMPLETED = 'completed',   // 已完成
    FAILED = 'failed',      // 失败
    CANCELLED = 'cancelled'    // 已取消
}

/**
 * 模型类型
 */
export enum ModelType {
    CHECKPOINT = 'CHECKPOINT',  // 基础模型
    LORA = 'LORA'               // LoRA微调模型
}

/**
 * 训练相关常量配置
 */
export const TRAIN_CONSTANTS = {
    // 每个视频的基础积分消耗
    BASE_CREDITS_PER_VIDEO: 2000,

    // 默认学习率
    DEFAULT_LEARNING_RATE: 0.0001,

    // 默认训练步数
    DEFAULT_STEPS: 1500,

    // 最大视频数量
    MAX_VIDEOS_COUNT: 50,

    // 视频文件大小上限(字节)
    MAX_VIDEO_SIZE: 1024 * 1024 * 1024, // 1GB
}; 