import { Suspense } from "react"
import { CategoryNav } from "@/components/content/category-nav"
import { ContentGrid } from "@/components/content/content-grid"
import { postApi } from "@/lib/api/post"
import { ClientFeedManager } from "@/components/content/client-feed-manager"
import { JsonLd } from "@/components/seo/JsonLd"
import { FontPreload } from "@/components/seo/font-preload"
import { ResourcePreload } from "@/components/seo/resource-preload"
import { OptimizedLayout } from "@/components/layout/optimized-layout"
import { FeatureCards } from "@/components/content/feature-cards"
import { FluxPromotionalBanner } from "@/components/flux/flux-promotional-banner"
import type { Metadata } from "next"
import { cookies } from "next/headers"
import { PostsResponse } from "@/lib/api/post"

const siteName = "Reelmind";
const url = "https://reelmind.ai/";
const title = "ReelMind - Free AI Video creator community, create AI Videos and Open Source AI Models";
const description = "Explore trending creative videos from content creators around the world. Find inspiration, entertainment and remix your favorites."

// 使用Next.js原生缓存控制 - 增加缓存时间以提高性能
export const revalidate = 180; // 每3分钟重新验证数据

export const metadata: Metadata = {
  title,
  description,
  alternates: {
    canonical: url,
  },
  openGraph: {
    title,
    description,
    type: "website",
    url,
    siteName,
    images: ["/images/og-image.jpg"],
  },
  twitter: {
    card: "summary_large_image",
    title,
    description,
    images: ["/images/twitter-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-video-preview": -1,
      "max-snippet": -1,
    },
  },
}

// 首页结构化数据
const websiteJsonLd = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": siteName,
  "url": url,
  "description": description,
  "potentialAction": {
    "@type": "SearchAction",
    "target": `${url}search?q={search_term_string}`,
    "query-input": "required name=search_term_string"
  }
};

// 首页内容加载骨架屏组件 - 优化为静态组件
function HomeLoadingSkeleton() {
  // 不传递任何项目，只显示加载状态
  return (
    <ContentGrid items={[]} isLoading={true} />
  )
}

// 服务端数据获取函数 - 优化缓存策略
async function getFeedData(categoryId: string): Promise<PostsResponse> {
  try {
    // 使用Next.js的fetch缓存机制，并传入分类参数
    const response = await postApi.getFeed({
      limit: 10,
      offset: 0,
      tag_id: categoryId || undefined
    });

    return response.data;
  } catch (error) {
    console.error('获取Feed数据出错:', error);
    return { posts: [], total: 0 };
  }
}

export default async function Page() {
  // 从cookie中获取选中的分类ID
  const cookieStore = await cookies()
  const selectedCategoryId = cookieStore.get('selected_category_id')?.value || 'all'
  const categoryId = selectedCategoryId === 'all' ? '' : selectedCategoryId

  // 在服务端预加载初始数据
  const initialDataPromise = getFeedData(categoryId)

  return (
    <div className="relative h-full">
      {/* 结构化数据 - 使用组件方式添加 */}
      <JsonLd data={websiteJsonLd} />

      {/* 资源预加载组件 - 优化关键资源加载 */}
      <ResourcePreload />

      {/* 字体预加载组件 - 优化字体加载 */}
      <FontPreload />

      {/* 特性卡片和FLUX推广横幅区域 - 作为首屏关键内容，使用高优先级渲染 */}
      <OptimizedLayout priority={true}>
        <div className="hidden md:block px-3 sm:px-6 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* FLUX推广横幅区域 - 占据1/3宽度 */}
            <div className="lg:col-span-1">
              <FluxPromotionalBanner className="h-full" />
            </div>
            {/* 特性卡片区域 - 占据2/3宽度 */}
            <div className="lg:col-span-2">
              <FeatureCards />
            </div>
          </div>
        </div>
      </OptimizedLayout>

      {/* 分类导航 - 服务器组件，使用高优先级渲染 */}
      <OptimizedLayout priority={true}>
        <CategoryNav selectedCategoryId={selectedCategoryId} />
      </OptimizedLayout>

      {/* 内容区域 - 使用普通优先级渲染 */}
      <OptimizedLayout>
        <div className="px-2 sm:px-4 pb-20 lg:pb-4">
          <Suspense fallback={<HomeLoadingSkeleton />}>
            {/* 流式渲染，不阻塞页面其他部分 */}
            <FeedContent dataPromise={initialDataPromise} />
          </Suspense>
        </div>
      </OptimizedLayout>
      {/* Space to account for mobile navigation bar */}
      <div className="h-16 lg:h-0"></div>
    </div>
  )
}

// 流式渲染内容组件 - 优化错误处理
async function FeedContent({ dataPromise }: { dataPromise: Promise<PostsResponse> }) {
  try {
    // 等待数据加载但不会阻塞页面其他部分
    const initialData = await dataPromise
    return <ClientFeedManager initialData={initialData} />
  } catch (error) {
    console.error('加载Feed数据失败:', error)
    // 返回空数据但不显示加载状态
    return <ClientFeedManager initialData={{ posts: [], total: 0 }} />
  }
}

