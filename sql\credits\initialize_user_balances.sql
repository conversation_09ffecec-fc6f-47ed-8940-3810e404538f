-- 根据现有的credit_transactions表的数据初始化用户余额表
INSERT INTO user_credit_balances (user_id, credits, last_transaction_id, created_at, updated_at)
SELECT 
    user_id,
    SUM(amount) as credits,
    (SELECT id FROM credit_transactions WHERE user_id = ct.user_id AND status = 'completed' ORDER BY created_at DESC LIMIT 1) as last_transaction_id,
    MIN(created_at) as created_at,
    NOW() as updated_at
FROM 
    credit_transactions ct
WHERE 
    status = 'completed'
GROUP BY 
    user_id
ON CONFLICT (user_id) DO UPDATE
SET 
    credits = EXCLUDED.credits,
    last_transaction_id = EXCLUDED.last_transaction_id,
    updated_at = NOW();

-- 查询验证初始化结果
SELECT 
    b.user_id,
    b.credits as balance_table_credits,
    COALESCE(SUM(t.amount), 0) as calculated_credits,
    b.credits - COALESCE(SUM(t.amount), 0) as difference
FROM 
    user_credit_balances b
LEFT JOIN 
    credit_transactions t ON b.user_id = t.user_id AND t.status = 'completed'
GROUP BY 
    b.user_id, b.credits
HAVING 
    b.credits != COALESCE(SUM(t.amount), 0); 