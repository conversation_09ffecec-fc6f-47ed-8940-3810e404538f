import { Controller, Post, Body, Get, Query, UseGuards } from '@nestjs/common';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { MembershipService } from './membership.service';
import { MembershipPlanService } from './membership-plan.service';
import { MembershipResponseDto } from './dto/membership.dto';
import { PaymentService } from '../payment/payment.service';
import { CreateSubscriptionDto } from '../payment/dto/stripe-payment.dto';
import { JwtGuard } from '../common/guards/jwt.guard';

@Controller('membership')
export class MembershipController {
    constructor(
        private readonly membershipService: MembershipService,
        private readonly membershipPlanService: MembershipPlanService,
        private readonly paymentService: PaymentService
    ) {}

    @UseGuards(JwtGuard)
    @Get('info')
    async getMembershipInfo(@CurrentUser() user: any): Promise<MembershipResponseDto | null> {
        const userId = user.id;
        return this.membershipService.getUserMembership(userId);
    }

    @Get('plans')
    async getMembershipPlans() {
        return this.membershipPlanService.getMembershipPlans();
    }

    @UseGuards(JwtGuard)
    @Post('subscribe')
    async createSubscription(
        @CurrentUser() user: any,
        @Body() createSubscriptionDto: CreateSubscriptionDto,
        @Query('origin') origin: string = 'https://reelmind.ai'
    ) {
        const userId = user.id;
        return this.paymentService.createMembershipSubscription(userId, createSubscriptionDto, origin);
    }

    @UseGuards(JwtGuard)
    @Get('subscription')
    async getSubscription(@CurrentUser() user: any) {
        const userId = user.id;
        return this.paymentService.getUserSubscription(userId);
    }
} 