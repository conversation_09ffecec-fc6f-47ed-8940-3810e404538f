'use client';

import React, { useState, useRef, useEffect } from 'react';
import { uploadWithProgress } from '@/lib/upload';

interface ImageUploaderProps {
    onImageUploaded: (imageUrl: string) => void;
}

export default function ImageUploader({ onImageUploaded }: ImageUploaderProps) {
    const [isUploading, setIsUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [uploadStatus, setUploadStatus] = useState('');
    const fileInputRef = useRef<HTMLInputElement>(null);
    const dropAreaRef = useRef<HTMLDivElement>(null);

    // Add paste event listener to window
    useEffect(() => {
        const handlePaste = (e: ClipboardEvent) => {
            if (isUploading) return;

            const items = e.clipboardData?.items;
            if (!items) return;

            for (let i = 0; i < items.length; i++) {
                if (items[i].type.indexOf('image') !== -1) {
                    const file = items[i].getAsFile();
                    if (file) {
                        uploadImage(file);
                        break;
                    }
                }
            }
        };

        window.addEventListener('paste', handlePaste);
        return () => window.removeEventListener('paste', handlePaste);
    }, [isUploading]);

    // Add drag and drop listeners
    useEffect(() => {
        const dropArea = dropAreaRef.current;
        if (!dropArea) return;

        const handleDragOver = (e: DragEvent) => {
            e.preventDefault();
            e.stopPropagation();
            dropArea.classList.add('bg-gray-100', 'dark:bg-gray-600');
        };

        const handleDragLeave = (e: DragEvent) => {
            e.preventDefault();
            e.stopPropagation();
            dropArea.classList.remove('bg-gray-100', 'dark:bg-gray-600');
        };

        const handleDrop = (e: DragEvent) => {
            e.preventDefault();
            e.stopPropagation();
            dropArea.classList.remove('bg-gray-100', 'dark:bg-gray-600');

            if (isUploading) return;

            const dt = e.dataTransfer;
            if (!dt || !dt.files) return;

            const file = dt.files[0];
            if (file && file.type.startsWith('image/')) {
                uploadImage(file);
            }
        };

        dropArea.addEventListener('dragover', handleDragOver);
        dropArea.addEventListener('dragleave', handleDragLeave);
        dropArea.addEventListener('drop', handleDrop);

        return () => {
            dropArea.removeEventListener('dragover', handleDragOver);
            dropArea.removeEventListener('dragleave', handleDragLeave);
            dropArea.removeEventListener('drop', handleDrop);
        };
    }, [isUploading]);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            alert('Please upload an image file');
            return;
        }

        uploadImage(file);
    };

    const uploadImage = async (file: File) => {
        setIsUploading(true);
        setUploadProgress(0);
        setUploadStatus('Preparing to upload...');

        try {
            setUploadStatus('Uploading image...');

            // 使用统一的上传函数，带进度回调
            const publicUrl = await uploadWithProgress(
                file,
                (progress) => {
                    setUploadProgress(progress);
                    if (progress < 30) {
                        setUploadStatus('Preparing upload...');
                    } else if (progress < 70) {
                        setUploadStatus('Getting upload permission...');
                    } else if (progress < 100) {
                        setUploadStatus('Uploading to cloud storage...');
                    } else {
                        setUploadStatus('Upload complete!');
                    }
                },
                { bucketName: 'blog-img' }
            );

            console.log('Image upload successful:', publicUrl);
            onImageUploaded(publicUrl);
        } catch (error) {
            console.error('Error uploading image:', error);
            setUploadStatus('Upload failed');
            // Note: This component should use toast instead of alert
            // TODO: Import and use useToast hook
            alert('Failed to upload image, please try again');
        } finally {
            // 延迟清除上传状态，让用户能看到完成消息
            setTimeout(() => {
                setIsUploading(false);
                setUploadProgress(0);
                setUploadStatus('');
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            }, 2000);
        }
    };

    return (
        <div
            ref={dropAreaRef}
            className="image-uploader w-full border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-md p-4 text-center transition-colors duration-200"
        >
            <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                disabled={isUploading}
                className="hidden"
                id="image-upload"
            />

            {isUploading ? (
                <div className="py-4">
                    <div className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                        {uploadStatus} ({uploadProgress}%)
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${uploadProgress}%` }}></div>
                    </div>
                </div>
            ) : (
                <div>
                    <label
                        htmlFor="image-upload"
                        className="flex flex-col items-center justify-center cursor-pointer"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Click to upload an image</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">or drag and drop</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">You can also paste images directly</span>
                    </label>
                </div>
            )}
        </div>
    );
} 