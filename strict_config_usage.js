/**
 * fal.ai精简版模型参数配置使用指南
 *
 * 本文档说明如何使用fal_ai_models_strict_config.json配置文件
 * 此版本经过严格类型校验，确保与官方API完全一致
 */

// 配置文件结构说明
/*
{
  "models": {
    "模型ID": {
      "category": "模型类别",
      "input": {
        "参数名": {
          "type": "参数类型",
          "required": true/false,  // 是否必填
          "description": "参数描述", // 可选
          "default": "默认值",      // 可选
          "enum": ["选项1", "选项2"], // 可选
          "example": "示例值",      // 可选
          "minimum": 最小值,       // 可选
          "maximum": 最大值        // 可选
          "format": "格式"         // 可选
        }
        // 更多参数...
      },
      "output": {
        // 输出参数结构，与输入类似
      }
    }
    // 更多模型...
  },
  "categories": {
    "类别名": ["模型ID1", "模型ID2", ...],
    // 更多类别...
  },
  "last_updated": "更新时间"
}
*/
