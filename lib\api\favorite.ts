import { ApiResponse } from './client';
import { apiClient } from './client';

// 收藏目标类型枚举
export enum FavoriteTargetType {
    POST = 'post',
    MODEL = 'model',
}

// 添加收藏请求参数
export interface AddFavoriteParams {
    post_id?: string;
    model_id?: string;
    item_type: FavoriteTargetType;
}

// 移除收藏请求参数
export interface RemoveFavoriteParams {
    post_id?: string;
    model_id?: string;
    item_type: FavoriteTargetType;
}

// 检查收藏状态请求参数
export interface CheckFavoriteParams {
    post_id?: string;
    model_id?: string;
    item_type: FavoriteTargetType;
}

// 获取收藏列表请求参数
export interface GetFavoritesParams {
    item_type?: string;
}

// 收藏响应类型
export interface FavoriteResponse {
    id: string;
    userId: string;
    targetId: string;
    targetType: FavoriteTargetType;
    createdAt: string;
}

// 收藏状态响应类型
export interface FavoriteStatusResponse {
    favorite: boolean;
}

// 收藏帖子响应类型
export interface FavoritePostResponse {
    favoriteId: string;
    postId: string;
    userId: string;
    title: string;
    description: string;
    videoUrl: string;
    thumbnailUrl: string;
    likeCount: number;
    commentCount: number;
    createdAt: string;
    favoriteCreatedAt: string;
}

// 收藏模型响应类型
export interface FavoriteModelResponse {
    favoriteId: string;
    modelId: string;
    userId: string;
    name: string;
    description: string;
    modelType: string;
    createdAt: string;
    favoriteCreatedAt: string;
}

// 收藏列表响应类型
export interface FavoritesResponse {
    posts: FavoritePostResponse[];
    models: FavoriteModelResponse[];
}

/**
 * 收藏API服务
 */
export const favoriteApi = {
    /**
     * 获取用户收藏列表
     * @param params 获取收藏列表参数
     * @returns 收藏列表响应
     */
    getFavorites: async (params?: GetFavoritesParams): Promise<FavoritesResponse> => {
        const queryParams: Record<string, string> = {};
        if (params?.item_type) {
            queryParams.item_type = params.item_type;
        }

        const response = await apiClient.get<ApiResponse<any>>('/user/favorites', {
            params: queryParams,
            requireAuth: true,
        });

        const data = response.data;
        console.log(data);
        const result: FavoritesResponse = {
            posts: [],
            models: []
        };

        // 处理不同类型的数据
        if (params?.item_type === 'post') {
            // 处理帖子数据
            result.posts = data.filter((item: any) => !!item.posts).map((item: any) => ({
                favoriteId: item.id,
                postId: item.post_id,
                userId: item.user_id,
                title: item.posts.title,
                description: item.posts.description || '',
                videoUrl: item.posts.video_url,
                thumbnailUrl: item.posts.videos?.cover_img,
                likeCount: item.posts.like_count || 0,
                commentCount: item.posts.comment_count || 0,
                createdAt: item.posts.created_at,
                favoriteCreatedAt: item.created_at
            }));
        } else if (params?.item_type === 'model') {
            // 处理模型数据
            result.models = data.filter((item: any) => !!item.models).map((item: any) => ({
                favoriteId: item.id,
                modelId: item.model_id,
                userId: item.user_id,
                name: item.models.name,
                description: item.models.description || '',
                modelType: item.models.model_type,
                createdAt: item.models.created_at,
                favoriteCreatedAt: item.created_at
            }));
        }

        return result;
    },

    /**
     * 添加收藏
     * @param params 添加收藏参数
     * @returns 收藏响应
     */
    addFavorite: async (params: AddFavoriteParams): Promise<FavoriteResponse> => {
        const response = await apiClient.post<ApiResponse<FavoriteResponse>>('/user/favorites/add', params, {
            requireAuth: true,
        });

        return response.data;
    },

    /**
     * 移除收藏
     * @param params 移除收藏参数
     * @returns 是否成功
     */
    removeFavorite: async (params: RemoveFavoriteParams): Promise<{ success: boolean }> => {
        const response = await apiClient.post<ApiResponse<{ success: boolean }>>('/user/favorites/remove', params, {
            requireAuth: true,
        });

        return response.data;
    },

    /**
     * 检查是否已收藏
     * @param params 检查收藏参数
     * @returns 收藏状态
     */
    checkFavorite: async (params: CheckFavoriteParams): Promise<FavoriteStatusResponse> => {
        const response = await apiClient.post<ApiResponse<FavoriteStatusResponse>>('/user/favorites/check', params, {
            requireAuth: true,
        });

        return response.data;
    },
}; 