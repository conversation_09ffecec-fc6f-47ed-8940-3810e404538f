import { Modu<PERSON> } from '@nestjs/common';
import { LegoController } from './lego.controller';
import { LegoService } from './lego.service';
import { CustomLogger } from 'src/common/services/logger.service';
import { ClientInfoService } from 'src/common/services/client-info.service';
import { UserModule } from 'src/user/user.module';
import { MembershipModule } from 'src/membership/membership.module';
import { CreditsModule } from 'src/credits/credits.module';
import { ErrorAuditModule } from 'src/common/module/error-audit.module';
import { GenerationModule } from 'src/generation/generation.module';

@Module({
    imports: [UserModule, MembershipModule, CreditsModule, ErrorAuditModule, GenerationModule],
    controllers: [LegoController],
    providers: [LegoService, ClientInfoService, CustomLogger],
    exports: [LegoService],
})
export class LegoModule {} 