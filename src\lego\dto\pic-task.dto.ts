import { IsNotEmpty, IsString, IsOptional, IsObject, IsEnum, IsNumber, IsArray, IsDate, ValidateNested, Min, Max, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { PicTaskStatus, PicResolution } from 'src/common/constants/pic';
import { GenPicRequestDto } from './gen-pic.dto';

export class PicUrlDto {
    @IsString()
    @IsNotEmpty()
    url: string;

    @IsEnum(PicResolution)
    resolution: PicResolution;

    @IsString()
    @IsOptional()
    seed?: string;
}

export class PicTaskDto {
    @IsString()
    @IsNotEmpty()
    id: string;

    @IsString()
    @IsNotEmpty()
    user_id: string;

    @IsEnum(PicTaskStatus)
    status: PicTaskStatus;

    @IsNumber()
    @Min(0)
    @Max(100)
    progress: number;

    @IsObject()
    input_params: GenPicRequestDto;

    @IsArray()
    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => PicUrlDto)
    results?: PicUrlDto[];

    @IsString()
    @IsOptional()
    error_message?: string;

    @IsDate()
    created_at: Date;

    @IsDate()
    @IsOptional()
    updated_at?: Date;

    @IsDate()
    @IsOptional()
    completed_at?: Date;

    @IsNumber()
    @IsOptional()
    priority?: number;
}

export class PicTaskResponseDto {
    @IsString()
    @IsNotEmpty()
    task_id: string;

    @IsEnum(PicTaskStatus)
    status: PicTaskStatus;

    @IsNumber()
    estimated_wait_time?: number;

    task: PicTaskDto;
}

export class FinishTaskDto {
    @IsString()
    @IsNotEmpty()
    task_id: string;

    @IsObject()
    @IsOptional()
    output_result?: Record<string, any>;

    @IsString()
    @IsNotEmpty()
    storage_path: string;

    @IsEnum(PicTaskStatus)
    @IsNotEmpty()
    status: PicTaskStatus.COMPLETED | PicTaskStatus.FAILED;

    @IsObject()
    @IsOptional()
    error_log?: Record<string, any>;
}

export class PopTaskDto {
    @IsBoolean()
    @IsOptional()
    has_refer_img?: boolean;

    @IsBoolean()
    @IsOptional()
    is_user_task?: boolean = true;
}