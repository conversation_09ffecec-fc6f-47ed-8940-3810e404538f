import {
    Injectable,
    Inject,
    BadRequestException,
    NotFoundException,
    ForbiddenException,
} from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import { ModelsService } from '../models/models.service';
import { MembershipService } from '../membership/membership.service';
import { CreditsService } from '../credits/credits.service';
import {
    StartTrainRequestDto,
    TrainPriceRequestDto,
    TrainPriceResponseDto,
    LoraTrainTaskDto,
    FinishTrainTaskRequestDto,
    UpdateTrainTaskRequestDto,
} from './dto/train.dto';
import { TrainTaskStatus, ModelType, TRAIN_CONSTANTS } from './constants';
import { v4 as uuidv4 } from 'uuid';
import { CreditTransactionType, CreditTransactionStatus, CreditAmount } from '../credits/constant';

@Injectable()
export class TrainService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly customLogger: CustomLogger,
        private readonly modelsService: ModelsService,
        private readonly membershipService: MembershipService,
        private readonly creditsService: CreditsService,
    ) {
        this.customLogger = this.customLogger.createLoggerWithContext(TrainService.name);
    }

    /**
     * 获取基础模型列表
     * @returns 基础模型列表
     */
    async getBaseModels() {
        try {
            const { data, error } = await this.supabase
                .from('models')
                .select('*')
                .eq('model_type', ModelType.CHECKPOINT)
                .eq('trainable', true)
                .eq('is_public', true)
                .order('created_at', { ascending: false });

            if (error) {
                this.customLogger.error('获取基础模型列表失败', error);
                throw new BadRequestException('获取基础模型列表失败');
            }

            return { base_models: data || [] };
        } catch (error) {
            this.customLogger.error('获取基础模型列表时出错', error);
            throw error;
        }
    }

    /**
     * 计算训练价格
     * @param userId 用户ID
     * @param trainPriceDto 训练价格请求
     * @returns 训练价格信息
     */
    async calculateTrainPrice(userId: string, trainPriceDto: TrainPriceRequestDto): Promise<TrainPriceResponseDto> {
        return {
            credits: CreditAmount.VIDEO_TRAIN,
            discount_info: null
        };
    }

    /**
     * 开始模型训练
     * @param userId 用户ID
     * @param startTrainDto 开始训练请求
     * @returns 创建的训练任务
     */
    async startTraining(userId: string, startTrainDto: StartTrainRequestDto): Promise<LoraTrainTaskDto> {
        try {
            // 验证输入
            if (!startTrainDto.videos || startTrainDto.videos.length === 0) {
                throw new BadRequestException('请至少提供一个视频URL');
            }

            if (startTrainDto.videos.length > TRAIN_CONSTANTS.MAX_VIDEOS_COUNT) {
                throw new BadRequestException(`最多支持${TRAIN_CONSTANTS.MAX_VIDEOS_COUNT}个视频进行训练`);
            }

            if (!startTrainDto.settings.trigger_word) {
                throw new BadRequestException('触发词不能为空');
            }

            // 计算训练价格
            // const priceInfo = await this.calculateTrainPrice(userId, {
            //     videos: startTrainDto.videos,
            //     settings: startTrainDto.settings
            // });

            // 消费积分
            await this.creditsService.consumeCredits({
                userId,
                type: CreditTransactionType.VIDEO_GENERATION, // 需要添加训练模型的交易类型
                amount: CreditAmount.VIDEO_TRAIN, // 固定收费2000积分
                description: `模型训练任务: ${startTrainDto.settings.trigger_word}`,
            });

            // 获取会员优先级
            const priority = await this.membershipService.getUserMembershipPriority(userId);

            // 创建训练任务
            const taskId = uuidv4();
            const { data, error } = await this.supabase
                .from('train_lora_tasks')
                .insert({
                    id: taskId,
                    user_id: userId,
                    progress: 0,
                    status: TrainTaskStatus.PENDING,
                    model_id: startTrainDto.settings.model_id,
                    trigger_word: startTrainDto.settings.trigger_word,
                    videos: startTrainDto.videos,
                    settings: startTrainDto.settings,
                    priority,
                    created_at: new Date(),
                })
                .select()
                .single();

            if (error) {
                this.customLogger.error('创建训练任务失败', error);
                throw new BadRequestException('创建训练任务失败');
            }

            return data;
        } catch (error) {
            this.customLogger.error('开始训练时出错', error);
            throw error;
        }
    }

    /**
     * 获取用户的训练任务列表
     * @param userId 用户ID
     * @param status 任务状态过滤
     * @param limit 限制数量
     * @param offset 偏移量
     * @returns 训练任务列表
     */
    async getUserTrainTasks(
        userId: string,
        status?: string,
        limit: number = 10,
        offset: number = 0
    ) {
        try {
            let query = this.supabase
                .from('train_lora_tasks')
                .select('*', { count: 'exact' })
                .eq('user_id', userId);

            if (status) {
                query = query.eq('status', status);
            }

            const { data, error, count } = await query
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) {
                this.customLogger.error('获取用户训练任务失败', error);
                throw new BadRequestException('获取用户训练任务失败');
            }

            return {
                tasks: data || [],
                total: count || 0
            };
        } catch (error) {
            this.customLogger.error('获取用户训练任务时出错', error);
            throw error;
        }
    }

    /**
     * 获取训练任务详情
     * @param userId 用户ID
     * @param taskId 任务ID
     * @returns 训练任务详情
     */
    async getTrainTaskById(userId: string, taskId: string): Promise<LoraTrainTaskDto> {
        try {
            const { data, error } = await this.supabase
                .from('train_lora_tasks')
                .select('*')
                .eq('id', taskId)
                .eq('user_id', userId)
                .single();

            if (error || !data) {
                throw new NotFoundException('训练任务不存在或无权访问');
            }

            return data;
        } catch (error) {
            this.customLogger.error(`获取训练任务详情失败 [taskId=${taskId}]`, error);
            throw error;
        }
    }

    /**
     * 取消训练任务
     * @param userId 用户ID
     * @param taskId 任务ID
     * @returns 更新后的任务
     */
    async cancelTrainTask(userId: string, taskId: string): Promise<LoraTrainTaskDto> {
        try {
            // 查询任务是否存在且属于该用户
            const task = await this.getTrainTaskById(userId, taskId);

            // 检查任务状态是否可取消
            if (task.status !== TrainTaskStatus.PENDING) {
                throw new BadRequestException('只能取消待处理的训练任务');
            }

            // 更新任务状态为已取消
            const { data, error } = await this.supabase
                .from('train_lora_tasks')
                .update({
                    status: TrainTaskStatus.CANCELLED,
                    completed_at: new Date(),
                })
                .eq('id', taskId)
                .eq('user_id', userId)
                .select()
                .single();

            if (error) {
                this.customLogger.error(`取消训练任务失败 [taskId=${taskId}]`, error);
                throw new BadRequestException('取消训练任务失败');
            }

            // 如果集成了积分系统，这里需要调用积分服务退还积分
            // await this.creditsService.refundCreditsForTraining(userId, taskId);

            return data;
        } catch (error) {
            this.customLogger.error(`取消训练任务时出错 [taskId=${taskId}]`, error);
            throw error;
        }
    }

    /**
     * 获取下一个待处理的训练任务
     * @returns 下一个训练任务
     */
    async popTrainTask() {
        try {
            // 调用数据库函数获取并更新下一个任务
            const { data, error } = await this.supabase.rpc('get_next_lora_train_task');

            if (error) {
                this.customLogger.error('获取下一个训练任务失败', error);
                throw new BadRequestException('获取训练任务失败');
            }

            // 如果没有待处理的任务
            if (!data || data.length === 0) {
                return null;
            }

            return data[0];
        } catch (error) {
            this.customLogger.error('获取下一个训练任务时出错', error);
            throw error;
        }
    }

    /**
     * 完成训练任务
     * @param finishDto 完成任务请求
     * @returns 处理结果
     */
    async finishTrainTask(finishDto: FinishTrainTaskRequestDto) {
        try {
            // 查询任务是否存在
            const { data: task, error: taskError } = await this.supabase
                .from('train_lora_tasks')
                .select('*')
                .eq('id', finishDto.task_id)
                .single();

            if (taskError || !task) {
                throw new NotFoundException('训练任务不存在');
            }

            // 检查任务状态
            if (task.status !== TrainTaskStatus.PROCESSING) {
                throw new BadRequestException('只能完成处理中的训练任务');
            }

            // 更新任务状态
            const status = finishDto.success ? TrainTaskStatus.COMPLETED : TrainTaskStatus.FAILED;
            const updateData: any = {
                status,
                completed_at: new Date(),
                progress: finishDto.success ? 100 : task.progress,
                storage_url: finishDto.storage_url,
                metadata: finishDto.metadata,
            };

            // 如果失败，添加错误信息
            if (!finishDto.success && finishDto.error_message) {
                updateData.error_message = finishDto.error_message;
            }

            let modelId = null;

            // 如果训练成功，创建模型记录
            if (finishDto.success && finishDto.storage_url) {
                // 创建模型记录
                const modelData = {
                    user_id: task.user_id,
                    name: task.trigger_word || `Trained model ${new Date().toISOString().split('T')[0]}`,
                    description: `trained by task ${task.id}`,
                    source: 'train',
                    model_type: 'lora',
                    storage_url: finishDto.storage_url,
                    is_public: false,
                    nsfw_level: 1,
                    metadata: finishDto.metadata || {},
                    supported_features: task.settings?.supported_features || [],
                    default_config: task.settings?.default_config || {},
                    size: finishDto.metadata?.size || null,
                    cover_img: finishDto.metadata?.cover_img || null
                };

                const { data: modelResult, error: modelError } = await this.supabase
                    .from('models')
                    .insert(modelData)
                    .select()
                    .single();

                if (modelError) {
                    this.customLogger.error(`创建模型记录失败 [taskId=${finishDto.task_id}]`, modelError);
                    throw new BadRequestException('创建模型记录失败');
                }

                modelId = modelResult.id;
                // 将模型ID添加到更新数据中
                updateData.model_id = modelId;
            }

            // 更新任务
            const { data, error } = await this.supabase
                .from('train_lora_tasks')
                .update(updateData)
                .eq('id', finishDto.task_id)
                .select()
                .single();

            if (error) {
                this.customLogger.error(`更新训练任务状态失败 [taskId=${finishDto.task_id}]`, error);
                throw new BadRequestException('更新训练任务状态失败');
            }

            // 如果任务失败，退还积分
            if (!finishDto.success) {
                try {
                    // 创建退款交易记录
                    await this.creditsService.createCreditTransaction({
                        userId: task.user_id,
                        type: CreditTransactionType.REFUND,
                        amount: CreditAmount.VIDEO_TRAIN,
                        description: `训练任务失败退款: ${task.trigger_word || finishDto.task_id}`,
                        status: CreditTransactionStatus.COMPLETED
                    });

                    this.customLogger.log(`成功为用户 ${task.user_id} 退还训练任务积分`);
                } catch (refundError) {
                    this.customLogger.error(`退还训练任务积分失败 [taskId=${finishDto.task_id}]`, refundError);
                    // 不抛出异常，以避免影响主流程
                }
            }

            return {
                success: true,
                task: data,
                model_id: modelId
            };
        } catch (error) {
            this.customLogger.error(`完成训练任务时出错 [taskId=${finishDto.task_id}]`, error);
            throw error;
        }
    }

    /**
     * 更新训练任务
     * @param updateDto 更新任务请求
     * @returns 处理结果
     */
    async updateTrainTask(updateDto: UpdateTrainTaskRequestDto) {
        try {
            // 查询任务是否存在
            const { data: task, error: taskError } = await this.supabase
                .from('train_lora_tasks')
                .select('*')
                .eq('id', updateDto.task_id)
                .single();

            if (taskError || !task) {
                throw new NotFoundException('训练任务不存在');
            }

            // 准备更新数据
            const updateData: any = {
                status: updateDto.status,
                progress: updateDto.progress,
            };

            // 如果状态是已完成，设置完成时间
            if (updateDto.status === TrainTaskStatus.COMPLETED) {
                updateData.completed_at = new Date();
                updateData.progress = 100; // 确保完成时进度为100%
            }

            // 如果状态是失败，设置完成时间和错误信息
            if (updateDto.status === TrainTaskStatus.FAILED) {
                updateData.completed_at = new Date();
                if (updateDto.error_message) {
                    updateData.error_message = updateDto.error_message;
                }
            }

            // 如果提供了存储URL
            if (updateDto.storage_url) {
                updateData.storage_url = updateDto.storage_url;
            }

            let modelId = null;

            // 如果任务已完成并有存储URL，创建模型记录
            if (updateDto.status === TrainTaskStatus.COMPLETED && updateDto.storage_url) {
                // 创建模型记录
                const modelData = {
                    user_id: task.user_id,
                    name: task.trigger_word || `Trained model ${new Date().toISOString().split('T')[0]}`,
                    description: `trained by task ${task.user_id}`,
                    source: 'train',
                    model_type: 'lora',
                    storage_url: updateDto.storage_url,
                    is_public: false,
                    nsfw_level: 1,
                    metadata: task.metadata || {},
                    supported_features: task.settings?.supported_features || [],
                    default_config: task.settings?.default_config || {},
                    size: task.metadata?.size || null,
                    cover_img: task.metadata?.cover_img || null
                };

                const { data: modelResult, error: modelError } = await this.supabase
                    .from('models')
                    .insert(modelData)
                    .select()
                    .single();

                if (modelError) {
                    this.customLogger.error(`创建模型记录失败 [taskId=${updateDto.task_id}]`, modelError);
                    throw new BadRequestException('创建模型记录失败');
                }

                modelId = modelResult.id;
                // 将模型ID添加到更新数据中
                updateData.model_id = modelId;
            }

            // 更新任务
            const { data, error } = await this.supabase
                .from('train_lora_tasks')
                .update(updateData)
                .eq('id', updateDto.task_id)
                .select()
                .single();

            if (error) {
                this.customLogger.error(`更新训练任务状态失败 [taskId=${updateDto.task_id}]`, error);
                throw new BadRequestException('更新训练任务状态失败');
            }

            // 如果任务失败，退还积分
            if (updateDto.status === TrainTaskStatus.FAILED) {
                try {
                    // 创建退款交易记录
                    await this.creditsService.createCreditTransaction({
                        userId: task.user_id,
                        type: CreditTransactionType.REFUND,
                        amount: CreditAmount.VIDEO_TRAIN,
                        description: `训练任务失败退款: ${task.trigger_word || updateDto.task_id}`,
                        status: CreditTransactionStatus.COMPLETED
                    });

                    this.customLogger.log(`成功为用户 ${task.user_id} 退还训练任务积分`);
                } catch (refundError) {
                    this.customLogger.error(`退还训练任务积分失败 [taskId=${updateDto.task_id}]`, refundError);
                    // 不抛出异常，以避免影响主流程
                }
            }

            return {
                success: true,
                task: data,
                model_id: modelId
            };
        } catch (error) {
            this.customLogger.error(`更新训练任务时出错 [taskId=${updateDto.task_id}]`, error);
            throw error;
        }
    }
} 