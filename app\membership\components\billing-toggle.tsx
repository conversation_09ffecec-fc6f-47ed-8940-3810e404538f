"use client"

import { Gift, Percent } from "lucide-react"

interface BillingToggleProps {
  billingCycle: "monthly" | "yearly"
  onChange: (cycle: "monthly" | "yearly") => void
}

export function BillingToggle({ billingCycle, onChange }: BillingToggleProps) {
  return (
    <div className="flex flex-col items-center space-y-4">
      {/* 计费周期切换 */}
      <div className="flex items-center bg-muted/50 p-2 rounded-full border border-border/50">
        <button
          onClick={() => onChange("yearly")}
          className={`relative px-8 py-3 rounded-full transition-all duration-300 ${billingCycle === "yearly"
            ? "bg-gradient-to-r from-purple-500 to-blue-500 text-white font-medium shadow-lg"
            : "text-muted-foreground hover:text-foreground hover:bg-muted/30"
            }`}
        >
          <div className="flex items-center gap-2">
            <Gift className="h-4 w-4" />
            <span>Annual</span>
          </div>
          {billingCycle === "yearly" && (
            <div className="absolute -top-2 -right-2 bg-purple-500 text-white text-xs px-2 py-0.5 rounded-full">
              FREE MONTH
            </div>
          )}
        </button>
        <button
          onClick={() => onChange("monthly")}
          className={`relative px-8 py-3 rounded-full transition-all duration-300 ${billingCycle === "monthly"
            ? "bg-gradient-to-r from-green-500 to-emerald-500 text-white font-medium shadow-lg"
            : "text-muted-foreground hover:text-foreground hover:bg-muted/30"
            }`}
        >
          <div className="flex items-center gap-2">
            <Percent className="h-4 w-4" />
            <span>Monthly</span>
          </div>
          {billingCycle === "monthly" && (
            <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-0.5 rounded-full">
              90% OFF
            </div>
          )}
        </button>
      </div>
    </div>
  )
}

