import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';

// Cloudflare R2 设置
const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID || '';
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID || '';
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY || '';

// 设置 R2 / S3 客户端
const s3Client = new S3Client({
    region: 'auto',
    endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    credentials: {
        accessKeyId: R2_ACCESS_KEY_ID,
        secretAccessKey: R2_SECRET_ACCESS_KEY,
    },
});

// 存储桶配置
const BUCKET_CONFIG = {
    'gen-refer-img': {
        publicUrl: process.env.R2_PUBLIC_URL || 'https://gen-refer-img.reelmind.ai',
    },
    'train-videos': {
        publicUrl: process.env.TRAIN_VIDEOS_PUBLIC_URL || 'https://train-videos.reelmind.ai',
    },
    'blog-img': {
        publicUrl: process.env.R2_BLOG_PUBLIC_URL || 'https://blog-img.reelmind.ai',
    },
    'gen-img-task': {
        publicUrl: process.env.R2_GEN_IMG_TASK_PUBLIC_URL || 'https://gen-img-task.reelmind.ai',
    },
} as const;

/**
 * 生成唯一且安全的文件名
 */
export function generateSecureFilename(userId: string, originalFilename: string): string {
    const fileExt = originalFilename.split('.').pop() || '';
    const timestamp = Date.now();
    const uniqueId = uuidv4().replace(/-/g, '').substring(0, 8);
    return `${userId}/${timestamp}-${uniqueId}.${fileExt}`;
}

/**
 * 为文件上传生成预签名URL
 */
export async function generatePresignedUrl(
    filename: string,
    contentType: string,
    expiresInSeconds: number = 3600,
    bucketName: keyof typeof BUCKET_CONFIG = 'gen-refer-img'
): Promise<{
    url: string;
    key: string;
    fields: Record<string, string>;
    publicUrl: string;
    expires: string;
}> {
    // 创建上传命令
    const command = new PutObjectCommand({
        Bucket: bucketName,
        Key: filename,
        ContentType: contentType,
        // Cloudflare R2 支持的ACL
        ACL: 'public-read',
    });

    // 生成预签名URL
    const signedUrl = await getSignedUrl(s3Client, command, {
        expiresIn: expiresInSeconds,
    });

    // 生成过期时间
    const expirationDate = new Date();
    expirationDate.setSeconds(expirationDate.getSeconds() + expiresInSeconds);

    // 构建返回值
    return {
        url: signedUrl,
        key: filename,
        fields: {}, // PUT 请求不需要表单字段
        publicUrl: `${BUCKET_CONFIG[bucketName].publicUrl}/${filename}`,
        expires: expirationDate.toISOString(),
    };
}

/**
 * 验证文件类型
 */
export function validateFileType(fileType: string): boolean {
    const allowedVideoTypes = [
        'video/mp4',
        'video/webm',
        'video/ogg',
        'video/quicktime',
        'video/x-msvideo',
        'video/x-flv',
        'video/x-matroska',
    ];

    const allowedImageTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
    ];

    return fileType.startsWith('video/') ||
        allowedVideoTypes.includes(fileType) ||
        fileType.startsWith('image/') ||
        allowedImageTypes.includes(fileType);
}

/**
 * 验证文件大小
 */
export function validateFileSize(fileSize: number, maxSizeMB: number = 500): boolean {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return fileSize <= maxSizeBytes;
} 