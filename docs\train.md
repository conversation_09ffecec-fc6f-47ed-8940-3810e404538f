# 在线视频模型训练页面

请为本项目添加新页面 train，功能是用户可以在平台上训练自己的 LoRa 模型。

## 前端逻辑

1. 用户上传视频（上传的视频总大小上限为 1GB）；
2. 调整各项设置；
3. 储存用户视频到 oss，调用后端接口，将参数传递给后端。

## 页面布局

### 左侧

左侧是视频上传区域，点击后选择文件上传，可以上传多个视频，所有视频加起来总共不超过 1GB。

用户选择本地的视频文件后，左侧区域变成展示区域，视频以卡片的形式展现在左侧区域，从左往右自上而下排列，相同大小。

展示区域的第一个是 Add videos 卡片，点击后可以继续追加上传视频。

用户可以预览上传的视频，还可以看到每个视频的大小和总的视频大小。

### 右侧

1. 参数设置区域，用户可以设置各项参数：
   - 基础模型
   - 触发词
   - learning_rate：学习率（默认 1e-5）。
   - accumulate_grad_batches：梯度累积批次（默认 1）。
   - max_epochs：训练 epoch 数（默认 1）。
   - lora_target_modules：LoRA 训练的目标层（默认 q,k,v,o,ffn.0,ffn.2）。
   - init_lora_weights：LoRA 初始化方法，可选 gaussian 或 kaiming（默认 kaiming）。
   - lora_rank：LoRA 低秩矩阵的维度（默认 4）。
   - lora_alpha：LoRA 适配器的缩放因子（默认 4.0）。
2. 价格显示（不要占用太多空间，不要有太多装饰）
3. 开始训练按钮

## 后端接口接入

后端项目已经提供了接口，请不要在此项目中实现接口

1. 计算价格：POST，/train/price，{ videos, settings: {xxx}}
2. 开始训练：POST，/train/start，{ videos, settings }
3. 基础模型列表：GET，/train/base_models
