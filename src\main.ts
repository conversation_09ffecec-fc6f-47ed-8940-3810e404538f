import "./instrument";

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import * as bodyParser from 'body-parser';
import { STRIPE_WEBHOOK_MODULE, STRIPE_WEBHOOK_ROUTE } from './common/constants/route';
import helmet from 'helmet';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bodyParser: false,  // 禁用内置的bodyParser
  });

  // 安全头部配置 - 使用 helmet 中间件
  app.use(helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false, // 避免与某些API冲突
  }));

  const stripeWebhookRoute = `${STRIPE_WEBHOOK_MODULE}${STRIPE_WEBHOOK_ROUTE}`;

  // 为Stripe webhook路由配置原始请求体处理
  app.use(stripeWebhookRoute,
    bodyParser.raw({ type: 'application/json' })
  );

  // 为其他路由配置JSON解析
  app.use((req, res, next) => {
    if (req.url.includes(stripeWebhookRoute)) {
      next();
    } else {
      bodyParser.json({
        limit: '10mb', // 限制JSON请求体大小
        verify: (req, res, buf) => {
          // 验证请求体不为空且格式正确
          if (buf && buf.length === 0) {
            throw new Error('Empty request body');
          }
        }
      })(req, res, next);
    }
  });

  // 添加CORS配置
  app.enableCors({
    origin: ['http://localhost:3001', 'http://localhost:3000', 'http://localhost:5173', 'https://*.reelmind.ai'],
    methods: ['GET', 'POST', 'PUT', 'OPTIONS'],
    credentials: true,
  });

  app.useGlobalPipes(new ValidationPipe({
    // whitelist: true, // 去除未定义的属性
    transform: true, // 自动转换类型
    // forbidNonWhitelisted: true, // 禁止未定义的属性
    transformOptions: {
      enableImplicitConversion: true, // 启用隐式转换
    },
  }));

  // 设置服务器超时
  const server = await app.listen(process.env.PORT ?? 3000);
  server.setTimeout(30000); // 30秒超时

  console.log('Environment:', process.env.NODE_ENV);
  console.log(`Application is running on: http://localhost:${process.env.PORT ?? 3000}`);
}

bootstrap();
