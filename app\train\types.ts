export interface Video {
    file: File
    id: string
    preview: string
    url?: string
}

export interface BaseModel {
    id: string
    name: string
    description: string
    model_type: string
    is_public: boolean
    created_at: string
}

export interface TrainingSettings {
    trigger_word: string
    model_id?: string
    learning_rate: number
    steps: number
    accumulate_grad_batches: number
    max_epochs: number
    init_lora_weights: "gaussian" | "kaiming"
    lora_rank: number
    lora_alpha: number
}

export const defaultSettings: TrainingSettings = {
    trigger_word: "",
    model_id: undefined,
    learning_rate: 0.0001,
    steps: 1500,
    accumulate_grad_batches: 1,
    max_epochs: 1,
    init_lora_weights: "gaussian",
    lora_rank: 8,
    lora_alpha: 8
}

// 训练相关常量
export const MAX_SIZE_BYTES = 1024 * 1024 * 1024 // 1GB in bytes
export const MIN_LEARNING_RATE = 0.00001 // 最小学习率
export const MAX_LEARNING_RATE = 0.01 // 最大学习率
export const MIN_STEPS = 500 // 最小训练步数
export const MAX_STEPS = 5000 // 最大训练步数

