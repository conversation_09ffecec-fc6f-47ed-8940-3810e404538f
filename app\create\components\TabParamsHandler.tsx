"use client"

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import useTabControlStore from "../hooks/useTabControl";
import useVideoGeneratorStore from "@/store/useVideoGeneratorStore";
import useModelSelectorStore from "@/store/useModelSelectorStore";
import { useEffectList, useModelList } from "../queries";
import type { Effect } from "@/types/model";
import { TABS } from "../constants";
import {
    getReferenceImage,
    getReferencePrompt,
    clearReferenceData
} from "@/lib/storage/tempDataStorage";

interface TabParamsHandlerProps {
    onTabChange?: (tab: string) => void;
}

export function TabParamsHandler({ onTabChange }: TabParamsHandlerProps) {
    const searchParams = useSearchParams();
    const { setActiveTab } = useTabControlStore();
    const { setSelectedEffectName, addImage, setPrompt } = useVideoGeneratorStore();
    const { setSelectedModelId } = useModelSelectorStore();
    const { data: effectData } = useEffectList();
    const { data: modelData } = useModelList();

    useEffect(() => {
        // 处理tab参数
        const urlActiveTab = searchParams?.get("active_tab");
        let tab;

        // 根据URL参数确定激活的选项卡
        if (urlActiveTab === "effect") {
            tab = TABS.EFFECT;
        } else if (urlActiveTab === "text-to-video") {
            tab = TABS.TEXT_TO_VIDEO;
        } else if (urlActiveTab === "image-to-video") {
            tab = TABS.IMAGE_TO_VIDEO;
        } else if (urlActiveTab === "video-to-video") {
            tab = TABS.VIDEO_TO_VIDEO;
        } else {
            // 默认选择图片到视频选项卡
            tab = TABS.IMAGE_TO_VIDEO;
        }

        // 优先使用传入的回调函数，兼容旧代码
        if (onTabChange) {
            onTabChange(tab);
        } else {
            setActiveTab(tab);
        }

        // 处理modelId参数 - 从模型详情页跳转过来
        const modelId = searchParams?.get("modelId");
        if (modelId && modelData?.models) {
            // 查找与ID匹配的模型
            const foundModel = modelData.models.find(model => model.id === modelId);
            if (foundModel) {
                // 根据模型类型设置当前选中的选项卡
                const modelType = foundModel.type?.toLowerCase() || '';
                let targetTab;

                if (modelType === 'image-to-video') {
                    targetTab = TABS.IMAGE_TO_VIDEO;
                } else if (modelType === 'video-to-video') {
                    targetTab = TABS.VIDEO_TO_VIDEO;
                } else if (modelType === 'text-to-video') {
                    targetTab = TABS.TEXT_TO_VIDEO;
                } else {
                    // 默认使用文本到视频选项卡
                    targetTab = TABS.TEXT_TO_VIDEO;
                }

                // 设置当前选中的选项卡
                if (onTabChange) {
                    onTabChange(targetTab);
                } else {
                    setActiveTab(targetTab);
                }

                // 直接更新 ModelSelectorStore 中的状态
                setSelectedModelId(foundModel.id);

                console.log(`Applied model: ${foundModel.name} from URL parameter, tab: ${targetTab}`);
            }
        }

        // 处理effectId参数
        const effectId = searchParams?.get("effectId");
        if (effectId && effectData) {
            // 获取效果列表并安全地转换类型
            const effects = (effectData as any)?.effects as Effect[] || [];

            // 查找与ID匹配的效果
            const foundEffect = effects.find((effect: Effect) => effect.id === effectId);
            if (foundEffect) {
                // 设置当前选中的效果
                setSelectedEffectName(foundEffect.name);

                // 自动切换到效果标签页
                if (onTabChange) {
                    onTabChange("effect");
                } else {
                    setActiveTab("effect");
                }

                console.log(`Applied effect: ${foundEffect.name} from URL parameter`);
            }
        }
        // 处理ref参数 - 从Lego页面跳转过来
        const ref = searchParams?.get("ref");
        if (ref === 'lego') {
            try {
                // 从本地存储获取参考图片URL
                const referenceImage = getReferenceImage();

                if (referenceImage) {
                    // 添加图片到状态
                    addImage(referenceImage);

                    // 获取并设置提示词
                    const prompt = getReferencePrompt();
                    if (prompt) {
                        setPrompt(prompt);
                    }

                    // 确保切换到图像到视频选项卡，因为有参考图片
                    if (onTabChange) {
                        onTabChange(TABS.IMAGE_TO_VIDEO);
                    } else {
                        setActiveTab(TABS.IMAGE_TO_VIDEO);
                    }

                    // 清除本地存储中的数据，避免下次进入页面时仍然使用
                    clearReferenceData();

                    console.log(`Applied reference image and prompt from local storage`);
                }
            } catch (error) {
                console.error("Failed to process reference data:", error);
            }
        }
    }, [searchParams, onTabChange, setActiveTab, setSelectedEffectName, addImage, setPrompt, effectData, modelData]);

    return null;
}