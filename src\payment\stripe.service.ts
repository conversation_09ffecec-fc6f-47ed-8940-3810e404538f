import { Injectable, BadRequestException } from '@nestjs/common';
import Stripe from 'stripe';
import { CustomLogger } from '../common/services/logger.service';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { Inject } from '@nestjs/common';
import { getStripeCouponIdForSubscription } from './config/stripe-coupons.config';
import { EnvironmentConfig } from '../common/config/environment.config';

@Injectable()
export class StripeService {
    private stripe: Stripe;
    private readonly stripeConfig: any;

    constructor(
        private readonly logger: CustomLogger,
        private readonly environmentConfig: EnvironmentConfig,
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient
    ) {
        this.logger.setContext(StripeService.name);

        // 获取环境相关的Stripe配置
        this.stripeConfig = this.environmentConfig.getStripeConfig();

        if (!this.stripeConfig.apiVersion) {
            throw new Error('STRIPE_API_VERSION 未配置');
        }
        if (!this.stripeConfig.secretKey) {
            const keyName = this.stripeConfig.isTestMode ? 'STRIPE_TEST_SECRET_KEY' : 'STRIPE_SECRET_KEY';
            throw new Error(`${keyName} 未配置`);
        }

        this.stripe = new Stripe(this.stripeConfig.secretKey, {
            apiVersion: this.stripeConfig.apiVersion as any
        });

        // 记录当前使用的Stripe环境
        this.logger.log(`Stripe服务初始化完成 - 环境: ${this.stripeConfig.isTestMode ? '测试' : '生产'}`);
    }

    /**
     * 从Webhook请求构造Stripe事件
     * @param payload 请求体内容
     * @param signature 签名头
     * @returns Stripe事件
     */
    async constructEventFromWebhook(
        payload: string | Buffer,
        signature: string,
    ): Promise<Stripe.Event> {
        try {
            const webhookSecret = this.stripeConfig.webhookSecret;
            if (!webhookSecret) {
                const keyName = this.stripeConfig.isTestMode ? 'STRIPE_TEST_WEBHOOK_SECRET' : 'STRIPE_WEBHOOK_SECRET';
                throw new Error(`${keyName} 未配置`);
            }

            // 如果payload是Buffer，不需要转换
            // 如果是字符串，可能需要转换为Buffer
            const rawPayload = Buffer.isBuffer(payload)
                ? payload
                : Buffer.from(payload);

            return this.stripe.webhooks.constructEvent(
                rawPayload,
                signature,
                webhookSecret,
            );
        } catch (error) {
            this.logger.error(`构建Stripe事件失败: ${error.message}`, error);
            throw new BadRequestException('无效的Webhook请求 ' + error.message);
        }
    }

    /**
     * 获取或创建Stripe客户ID
     * @param userId 用户ID
     * @returns Stripe客户ID
     */
    async getOrCreateStripeCustomerId(userId: string): Promise<string> {
        const { data: userData, error: userError } = await this.supabase
            .from('user_profiles')
            .select('*')
            .eq('user_id', userId)
            .single();

        if (userError) {
            this.logger.error(`获取用户资料失败: ${userError.message}`, userError);
            throw new BadRequestException('获取用户资料失败或用户资料不存在');
        }

        let customerId = userData?.stripe_customer_id;
        // 如果用户没有customer_id，则创建一个
        if (!customerId) {
            const customer = await this.stripe.customers.create({
                email: userData.email,
                metadata: {
                    user_id: userId
                }
            });

            customerId = customer.id;

            // 保存客户ID到用户资料
            const { error: updateError } = await this.supabase
                .from('user_profiles')
                .update({ stripe_customer_id: customerId })
                .eq('user_id', userId);

            if (updateError) {
                this.logger.error(`保存客户ID到用户资料失败: ${updateError.message}`, updateError);
                throw new BadRequestException('保存客户ID到用户资料失败');
            }
        }

        return customerId;
    }

    /**
     * 创建会员订阅支付会话
     * @param userId 用户ID
     * @param planId 会员计划ID
     * @param interval 订阅周期 ('month' 或 'year')
     * @param successUrl 支付成功跳转URL
     * @param cancelUrl 支付取消跳转URL
     * @returns Stripe支付会话
     */
    async createSubscriptionSession({
        userId,
        plan,
        price_id,
        payment_id,
        billing_cycle,
        successUrl,
        cancelUrl,
        couponData = null
    }): Promise<{ sessionId: string; url: string }> {
        try {
            // 查询用户是否已有customer_id
            const customerId = await this.getOrCreateStripeCustomerId(userId);

            let stripeCouponId = null;

            // 如果有优惠券数据，根据订阅类型和会员计划选择合适的Stripe优惠券ID
            if (couponData) {
                stripeCouponId = getStripeCouponIdForSubscription(
                    billing_cycle,
                    plan.plan_name,
                    this.environmentConfig
                );
            }

            const sessionConfig: any = {
                payment_method_types: ['card'],
                line_items: [
                    {
                        price: price_id,
                        quantity: 1,
                    },
                ],
                mode: 'subscription',
                success_url: successUrl,
                cancel_url: cancelUrl,
                client_reference_id: userId,
                customer: customerId,
                metadata: {
                    user_id: userId,
                    plan_id: plan.id,
                    price_id,
                    billing_cycle,
                    payment_id,
                    coupon_id: couponData?.id || null,
                    has_coupon: !!couponData
                },
                subscription_data: {
                    // 不在 subscription_data.metadata 中设置 priceId ，是因为 subscription 是可以改变订阅的 price 的。
                    metadata: {
                        plan_id: plan.id,
                        user_id: userId,
                        billing_cycle,
                        payment_id,
                        coupon_id: couponData?.id || null
                    }
                }
            };

            // 如果有Stripe优惠券，添加到会话配置中
            if (stripeCouponId) {
                sessionConfig.discounts = [{
                    coupon: stripeCouponId
                }];
            }

            const session = await this.stripe.checkout.sessions.create(sessionConfig);

            return {
                sessionId: session.id,
                url: session.url!,
            };
        } catch (error) {
            this.logger.error(`创建会员订阅会话失败: ${error.message}`, error);
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('创建订阅会话失败，请重试');
        }
    }

    /**
     * 验证Stripe优惠券是否存在
     * @param couponId Stripe优惠券ID
     * @returns 是否存在
     */
    async validateStripeCoupon(couponId: string): Promise<boolean> {
        try {
            const { valid } = await this.stripe.coupons.retrieve(couponId);
            return valid;
        } catch (error) {
            this.logger.error(`验证Stripe优惠券失败: ${couponId}`, error);
            return false;
        }
    }

    /**
     * 获取订阅详情
     * @param subscriptionId 订阅ID
     * @returns 订阅详情
     */
    async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
        try {
            return await this.stripe.subscriptions.retrieve(subscriptionId);
        } catch (error) {
            this.logger.error(`获取订阅详情失败: ${subscriptionId}`, error);
            throw new BadRequestException('获取订阅详情失败');
        }
    }

    /**
     * 创建客户门户会话链接
     * @param customerId Stripe客户ID
     * @param returnUrl 返回URL
     * @returns 客户门户会话
     */
    async createCustomerPortalSession(customerId: string, returnUrl: string): Promise<string> {
        try {
            const session = await this.stripe.billingPortal.sessions.create({
                customer: customerId,
                return_url: returnUrl,
            });
            return session.url;
        } catch (error) {
            this.logger.error(`创建客户门户会话失败: ${customerId}`, error);
            throw new BadRequestException('创建客户门户会话失败');
        }
    }

    /**
     * 取消订阅
     * @param subscriptionId 订阅ID
     * @returns 取消后的订阅
     */
    async cancelSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
        try {
            return await this.stripe.subscriptions.cancel(subscriptionId);
        } catch (error) {
            this.logger.error(`取消订阅失败: ${subscriptionId}`, error);
            throw new BadRequestException('取消订阅失败');
        }
    }

    /**
     * 暂停订阅（在当前周期结束后取消）
     * @param subscriptionId 订阅ID
     * @returns 更新后的订阅
     */
    async pauseSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
        try {
            return await this.stripe.subscriptions.update(subscriptionId, {
                cancel_at_period_end: true
            });
        } catch (error) {
            this.logger.error(`暂停订阅失败: ${subscriptionId}`, error);
            throw new BadRequestException('暂停订阅失败');
        }
    }

    /**
     * 恢复暂停的订阅
     * @param subscriptionId 订阅ID
     * @returns 更新后的订阅
     */
    async resumeSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
        try {
            return await this.stripe.subscriptions.update(subscriptionId, {
                cancel_at_period_end: false
            });
        } catch (error) {
            this.logger.error(`恢复订阅失败: ${subscriptionId}`, error);
            throw new BadRequestException('恢复订阅失败');
        }
    }

    /**
     * 更新订阅的价格（升级或降级计划）
     * @param subscriptionId 订阅ID
     * @param newPriceId 新的价格ID
     * @returns 更新后的订阅
     */
    async updateSubscriptionPrice(subscriptionId: string, newPriceId: string): Promise<Stripe.Subscription> {
        try {
            const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);

            // 获取当前订阅项ID
            const itemId = subscription.items.data[0].id;

            // 更新订阅价格
            return await this.stripe.subscriptions.update(subscriptionId, {
                items: [
                    {
                        id: itemId,
                        price: newPriceId,
                    },
                ],
            });
        } catch (error) {
            this.logger.error(`更新订阅价格失败: ${subscriptionId}`, error);
            throw new BadRequestException('更新订阅价格失败');
        }
    }

    /**
     * 创建一次性支付会话（用于购买积分）
     * @param userId 用户ID
     * @param amountUsd 美元金额
     * @param creditsAmount 积分数量
     * @param paymentId 支付记录ID
     * @param successUrl 支付成功跳转URL
     * @param cancelUrl 支付取消跳转URL
     * @returns Stripe支付会话
     */
    async createCreditsPurchaseSession({
        userId,
        amountUsd,
        creditsAmount,
        paymentId,
        successUrl,
        cancelUrl
    }): Promise<{ sessionId: string; url: string }> {
        const customerId = await this.getOrCreateStripeCustomerId(userId);

        // 创建一次性支付会话
        const session = await this.stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [
                {
                    price_data: {
                        currency: 'usd',
                        product_data: {
                            name: `Reelmind Credits - ${creditsAmount}`,
                            description: `Deposit ${creditsAmount} credits to your account`,
                        },
                        unit_amount: Math.round(amountUsd * 100), // 转换为美分
                    },
                    quantity: 1,
                },
            ],
            mode: 'payment',
            success_url: successUrl,
            cancel_url: cancelUrl,
            client_reference_id: userId,
            customer: customerId,
            metadata: {
                user_id: userId,
                payment_id: paymentId,
                payment_type: 'credits_purchase',
                credits_amount: creditsAmount.toString(),
                usd_amount: amountUsd.toString(),
            },
        });

        return {
            sessionId: session.id,
            url: session.url!,
        };
    }
}
