"use client"

import React, { CSSProperties, useState, useEffect, useRef } from "react"
import { <PERSON>rk<PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface RemixButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    shimmerColor?: string
    shimmerSize?: string
    borderRadius?: string
    shimmerDuration?: string
    background?: string
    className?: string
    children?: React.ReactNode
    onClick?: () => void
}

export const RemixButton = React.forwardRef<HTMLButtonElement, RemixButtonProps>(({
    shimmerColor = "#ffffff",
    shimmerSize = "0.05em",
    shimmerDuration = "3s",
    borderRadius = "100px",
    background = "rgba(0, 0, 0, 1)",
    className,
    children,
    onClick,
    ...props
}, ref) => {
    const buttonRef = useRef<HTMLButtonElement>(null);
    const [isHovered, setIsHovered] = useState(false);
    const [particles, setParticles] = useState<Array<{
        id: number;
        x: number;
        y: number;
        size: number;
        color: string;
        opacity: number;
    }>>([]);

    useEffect(() => {
        if (!isHovered) return;

        const interval = setInterval(() => {
            if (!buttonRef.current) return;

            const width = buttonRef.current.offsetWidth;
            const height = buttonRef.current.offsetHeight;
            const newParticle = {
                id: Date.now(),
                x: Math.random() * width,
                y: Math.random() * height,
                size: Math.random() * 4 + 2,
                color: getRandomColor(),
                opacity: 1
            };

            setParticles(prev => [...prev, newParticle]);
            setTimeout(() => {
                setParticles(prev => prev.filter(p => p.id !== newParticle.id));
            }, 1000);
        }, 100);

        return () => clearInterval(interval);
    }, [isHovered]);

    const getRandomColor = () => {
        const colors = [
            "rgb(239, 68, 68)", "rgb(249, 115, 22)", "rgb(59, 130, 246)",
            "rgb(16, 185, 129)", "rgb(139, 92, 246)",
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    };

    return (
        <button
            ref={(node) => {
                if (typeof ref === 'function') ref(node);
                else if (ref) ref.current = node;
                buttonRef.current = node;
            }}
            style={{
                "--spread": "90deg",
                "--shimmer-color": shimmerColor,
                "--radius": borderRadius,
                "--speed": shimmerDuration,
                "--cut": shimmerSize,
                "--bg": background,
            } as CSSProperties}
            className={cn(
                "group relative z-0 flex cursor-pointer items-center justify-center gap-2 overflow-hidden whitespace-nowrap border border-white/10 px-6 py-3 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-foreground",
                "transform-gpu transition-all duration-300 ease-in-out active:translate-y-px",
                "hover:shadow-[0_0_15px_rgba(59,130,246,0.5)]",
                className,
            )}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={onClick}
            {...props}
        >
            {particles.map((particle) => (
                <span
                    key={particle.id}
                    className="absolute pointer-events-none z-10 rounded-full animate-pulse"
                    style={{
                        left: particle.x,
                        top: particle.y,
                        width: particle.size,
                        height: particle.size,
                        backgroundColor: particle.color,
                        opacity: particle.opacity,
                        transform: "translate(-50%, -50%)",
                        transition: "opacity 1s ease-out",
                    }}
                />
            ))}
            <div className={cn("-z-30 blur-[2px]", "absolute inset-0 overflow-visible [container-type:size]")}>
                <div className="absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]">
                    <div className="animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]" />
                </div>
            </div>
            <Sparkles size={22} />
            <span className="transition-all duration-300 group-hover:translate-x-1 font-bold">
                {children || "Remix This Video"}
            </span>
            <div className={cn("insert-0 absolute size-full", "rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]",
                "transform-gpu transition-all duration-300 ease-in-out",
                "group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]",
                "group-active:shadow-[inset_0_-10px_10px_#ffffff3f]",
            )} />
            <div className={cn("absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]")} />
            <div className="absolute inset-0 -z-10 opacity-0 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-500 blur-md transition-opacity duration-300 group-hover:opacity-70"></div>
        </button>
    );
});

RemixButton.displayName = "RemixButton"; 