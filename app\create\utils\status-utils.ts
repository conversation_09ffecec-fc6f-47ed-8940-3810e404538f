// 状态映射常量
export const STATUS_LABEL_MAP: Record<string, string> = {
    pending: "Pending",
    queued: "Queued",
    processing: "Processing",
    completed: "Completed",
    failed: "Failed",
    canceled: "Canceled",
};

export const STATUS_COLOR_MAP: Record<string, string> = {
    pending: "bg-yellow-500/10 text-yellow-500",
    queued: "bg-blue-500/10 text-blue-500",
    processing: "bg-blue-500/10 text-blue-500",
    completed: "bg-green-500/10 text-green-500",
    failed: "bg-red-500/10 text-red-500",
    canceled: "bg-gray-500/10 text-gray-500",
};

/**
 * 获取任务状态文本
 */
export const getStatusText = (status: string): string => {
    return STATUS_LABEL_MAP[status] || status;
};

/**
 * 获取任务状态颜色
 */
export const getStatusColor = (status: string): string => {
    return STATUS_COLOR_MAP[status] || "bg-gray-500/10 text-gray-500";
};
