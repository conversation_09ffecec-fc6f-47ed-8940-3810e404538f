"use client"

import React from 'react';
import { Upload, X } from 'lucide-react';
import Image from 'next/image';
import { useControlPanel } from './ControlPanelContext';

export const StartToEndImageUploader: React.FC = () => {
  const {
    startImages,
    endImages,
    startFileInputRef,
    endFileInputRef,
    handleStartImageUpload,
    handleEndImageUpload,
    removeStartImage,
    removeEndImage,
    isUploadingStartToEnd,
    imageSizeError
  } = useControlPanel();

  const handleStartUploadClick = () => {
    startFileInputRef.current?.click();
  };

  const handleEndUploadClick = () => {
    endFileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      <div className="text-sm font-medium text-foreground mb-3">
        Start & End Frame Images
      </div>

      <div className="grid grid-cols-2 gap-4">
        {/* Start Image Upload */}
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground text-center">Start Frame</div>
          <div className="max-w-[160px] mx-auto">
            <div className="h-[160px] aspect-square rounded-xl overflow-hidden">
              {startImages.length > 0 ? (
                <div className="relative w-full h-full">
                  <Image
                    src={startImages[0]}
                    alt="Start frame image"
                    fill
                    className={`object-cover ${imageSizeError ? 'border-2 border-red-500' : ''}`}
                  />
                  <button
                    onClick={() => removeStartImage(0)}
                    className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 transition-colors"
                    disabled={isUploadingStartToEnd}
                  >
                    <X size={16} />
                  </button>
                </div>
              ) : (
                <div
                  onClick={handleStartUploadClick}
                  className="w-full h-full border-2 border-dashed border-muted-foreground/30 rounded-xl flex flex-col items-center justify-center cursor-pointer hover:border-muted-foreground/50 transition-colors bg-muted/20"
                >
                  <Upload size={24} className="text-muted-foreground mb-2" />
                  <span className="text-xs text-muted-foreground text-center px-2">
                    Drop start image here
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* End Image Upload */}
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground text-center">End Frame</div>
          <div className="max-w-[160px] mx-auto">
            <div className="h-[160px] aspect-square rounded-xl overflow-hidden">
              {endImages.length > 0 ? (
                <div className="relative w-full h-full">
                  <Image
                    src={endImages[0]}
                    alt="End frame image"
                    fill
                    className={`object-cover ${imageSizeError ? 'border-2 border-red-500' : ''}`}
                  />
                  <button
                    onClick={() => removeEndImage(0)}
                    className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 transition-colors"
                    disabled={isUploadingStartToEnd}
                  >
                    <X size={16} />
                  </button>
                </div>
              ) : (
                <div
                  onClick={handleEndUploadClick}
                  className="w-full h-full border-2 border-dashed border-muted-foreground/30 rounded-xl flex flex-col items-center justify-center cursor-pointer hover:border-muted-foreground/50 transition-colors bg-muted/20"
                >
                  <Upload size={24} className="text-muted-foreground mb-2" />
                  <span className="text-xs text-muted-foreground text-center px-2">
                    Drop end image here
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {imageSizeError && (
        <div className="text-red-500 text-xs text-center bg-red-50 dark:bg-red-900/20 p-2 rounded-lg">
          {imageSizeError}
        </div>
      )}

      {/* Upload Instructions */}
      <div className="text-xs text-muted-foreground text-center space-y-1">
        <div>Upload start and end frame images for video generation</div>
        <div>Minimum size: 300×300 pixels</div>
      </div>

      {/* Hidden File Inputs */}
      <input
        ref={startFileInputRef}
        type="file"
        accept="image/*"
        onChange={handleStartImageUpload}
        className="hidden"
      />
      <input
        ref={endFileInputRef}
        type="file"
        accept="image/*"
        onChange={handleEndImageUpload}
        className="hidden"
      />
    </div>
  );
};
