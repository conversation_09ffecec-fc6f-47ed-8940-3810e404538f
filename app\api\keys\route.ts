import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 获取 API 密钥列表
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // 验证用户会话
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }
    
    // 转发请求到后端 API
    const response = await fetch(`http://localhost:8080/api/keys`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }
    
    const responseData = await response.json();
    console.log('后端API响应数据:', responseData);
    
    // 检查响应格式并提取正确的数据
    if (responseData.data && responseData.data.keys) {
      // 格式化返回的数据以匹配前端期望的格式
      const formattedKeys = responseData.data.keys.map(key => ({
        id: key.id,
        name: key.name,
        key: key.api_key,
        created_at: key.created_at,
        last_used_at: key.last_used_at
      }));
      return NextResponse.json(formattedKeys);
    } else if (responseData.keys) {
      // 如果响应直接包含keys属性
      const formattedKeys = responseData.keys.map(key => ({
        id: key.id,
        name: key.name,
        key: key.api_key,
        created_at: key.created_at,
        last_used_at: key.last_used_at
      }));
      return NextResponse.json(formattedKeys);
    } else {
      // 如果无法识别格式，直接返回原始数据
      console.warn('无法识别的API密钥响应格式:', responseData);
      return NextResponse.json(responseData);
    }
  } catch (error) {
    console.error('获取 API 密钥失败:', error);
    return NextResponse.json({ error: '获取 API 密钥失败' }, { status: 500 });
  }
}

// 创建 API 密钥
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // 验证用户会话
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }
    
    // 获取请求体
    const body = await request.json();
    console.log('前端发送的请求体:', body);
    
    // 确保请求体包含必需字段
    if (!body.name) {
      return NextResponse.json({ error: '密钥名称不能为空' }, { status: 400 });
    }
    
    // 如果没有描述字段，添加一个空字符串
    if (body.description === undefined) {
      body.description = '';
    }
    
    // 获取API服务器URL
    const apiServerUrl = process.env.REELAPI_SERVER_URL || 'http://localhost:8080';
    const url = `${apiServerUrl}/api/keys`;
    console.log('请求后端API URL:', url);
    
    // 转发请求到后端 API
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify(body)
    });
    
    // 记录响应状态
    console.log('后端API响应状态:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('后端API错误响应:', errorData);
      return NextResponse.json(errorData, { status: response.status });
    }
    
    const responseData = await response.json();
    console.log('后端API成功响应:', responseData);
    
    // 格式化返回的数据以匹配前端期望的格式
    if (responseData.data) {
      // 响应包含在data属性中
      const formattedKey = {
        id: responseData.data.id,
        name: responseData.data.name,
        key: responseData.data.api_key,
        created_at: responseData.data.created_at,
        last_used_at: responseData.data.last_used_at
      };
      return NextResponse.json(formattedKey);
    } else {
      // 直接返回响应数据
      const formattedKey = {
        id: responseData.id,
        name: responseData.name,
        key: responseData.api_key,
        created_at: responseData.created_at,
        last_used_at: responseData.last_used_at
      };
      return NextResponse.json(formattedKey);
    }
  } catch (error) {
    console.error('创建 API 密钥失败:', error);
    return NextResponse.json({ error: '创建 API 密钥失败' }, { status: 500 });
  }
}

// 删除 API 密钥
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // 验证用户会话
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }
    
    // 获取请求体
    const body = await request.json();
    
    // 转发请求到后端 API
    const response = await fetch(`${process.env.REELAPI_SERVER_URL}/api/keys`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify(body)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('删除 API 密钥失败:', error);
    return NextResponse.json({ error: '删除 API 密钥失败' }, { status: 500 });
  }
} 