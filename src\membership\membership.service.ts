import {
    Injectable,
    Inject,
    BadRequestException,
    NotFoundException,
    forwardRef,
} from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import {
    MembershipResponseDto,
    MembershipSubscriptionDto,
    CancelMembershipDto,
} from './dto/membership.dto';
import { MembershipInterestService } from './membership-interest.service';
import { MembershipPlanService } from './membership-plan.service';
import { MembershipLevel } from './constant';
import { CreditsService } from '../credits/credits.service';

// 会员优先级配置
export const DEFAULT_MEMBERSHIP_PRIORITIES = {
    [MembershipLevel.FREE]: 0,
    [MembershipLevel.PRO]: 20,
    [MembershipLevel.MAX]: 30,
};

@Injectable()
export class MembershipService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
        private readonly interestService: MembershipInterestService,
        private readonly planService: MembershipPlanService,
        @Inject(forwardRef(() => CreditsService))
        private readonly creditsService: CreditsService,
    ) {
        this.logger.setContext(MembershipService.name);
    }

    /**
     * 获取用户会员信息
     * @param userId 用户ID
     * @returns 会员信息
     */
    async getUserMembership(
        userId: string,
    ): Promise<MembershipResponseDto | null> {
        try {
            const { data, error } = await this.supabase
                .from('user_memberships')
                .select('*')
                .eq('user_id', userId)
                .eq('is_active', true)
                .order('created_at', { ascending: false })
                .limit(1)
                .single();

            if (error || !data) {
                return null;
            }

            // 检查会员是否已过期
            const now = new Date();
            const expiresAt = new Date(data.expires_at);

            // 如果已过期，将is_active设置为false
            if (expiresAt < now && data.is_active) {
                this.logger.log(`用户会员已过期: ${userId}, 过期时间: ${expiresAt.toISOString()}`);

                // 更新数据库中的会员状态
                const { error: updateError } = await this.supabase
                    .from('user_memberships')
                    .update({
                        is_active: false,
                        updated_at: now.toISOString(),
                    })
                    .eq('id', data.id);

                if (updateError) {
                    this.logger.error(`更新过期会员状态失败: ${data.id}`, updateError);
                } else {
                    // 更新本地数据
                    data.is_active = false;
                }
            }

            // 获取会员计划名称
            let planName = 'Free';
            if (data.plan_id) {
                const plan = await this.getMembershipPlanById(data.plan_id);
                if (plan) {
                    planName = plan.plan_name;
                }
            }

            // 计算剩余天数
            const periodEndDate = new Date(
                data.current_period_end || data.expires_at,
            );
            const daysLeft = Math.ceil(
                (periodEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
            );

            return {
                ...data,
                plan_name: planName,
                level_name: MembershipLevel[data.level],
                days_left: daysLeft > 0 ? daysLeft : 0,
            };
        } catch (error) {
            this.logger.error(`获取用户会员信息失败: ${userId}`, error);
            return null;
        }
    }

    /**
     * 根据ID获取会员计划
     * @param planId 计划ID
     * @returns 会员计划
     */
    async getMembershipPlanById(planId: string) {
        return this.planService.getMembershipPlanById(planId);
    }

    /**
     * 开通会员订阅
     * @param userId 用户ID
     * @param createMembershipDto 创建会员订阅请求
     * @returns 创建的会员信息
     */
    async activateMembershipSubscription(
        userId: string,
        createMembershipDto: MembershipSubscriptionDto,
    ): Promise<boolean> {
        try {
            const {
                subscription_id,
                plan_id,
                billing_cycle,
                current_period_end,
                cancel_at_period_end,
                paymentId,
            } = createMembershipDto;

            // 检查计划是否存在
            const plan = await this.getMembershipPlanById(plan_id);

            if (!plan) {
                throw new NotFoundException(`会员计划不存在: ${plan_id}`);
            };

            // 创建用户会员记录
            const { error } = await this.supabase
                .from('user_memberships')
                .insert({
                    user_id: userId,
                    plan_id,
                    is_active: true,
                    subscription_id,
                    billing_cycle,
                    expires_at: current_period_end,
                    cancel_at_period_end: cancel_at_period_end || false,
                })
                .select('*')
                .single();

            if (error) {
                this.logger.error(`创建会员订阅失败: ${error.message}`, error);
                throw new BadRequestException(`创建会员订阅失败: ${error.message}`);
            }

            // 发放初始会员积分 - 传递已查询的plan对象，避免重复查询
            try {
                const {
                    success,
                    transaction,
                    message,
                    amount
                } = await this.creditsService.grantMembershipInitialCredits(userId, plan_id, paymentId, plan);
                if (!success) {
                    this.logger.error(`发放会员首次开通积分失败: ${transaction}`);
                    throw new BadRequestException(message);
                }
                this.logger.log(`已为用户 ${userId} 发放 ${amount} 会员首次开通积分`);
            } catch (creditError) {
                // 记录积分发放失败，但不影响会员订阅流程
                this.logger.error(`会员订阅成功但初始积分发放失败: ${creditError.message}`, creditError);
            }

            return true;
        } catch (error) {
            this.logger.error(`开通会员订阅失败: ${error.message}`, error);
            throw error;
        }
    }

    /**
     * 取消会员订阅
     * @param userId 用户ID
     * @param cancelDto 取消会员订阅请求
     * @returns 是否成功
     */
    async cancelMembershipSubscription(
        userId: string,
        cancelDto: CancelMembershipDto,
    ): Promise<boolean> {
        try {
            const { subscription_id } = cancelDto;

            // 获取用户会员信息
            const { data, error } = await this.supabase
                .from('user_memberships')
                .select('*')
                .eq('user_id', userId)
                .eq('subscription_id', subscription_id)
                .eq('is_active', true)
                .single();

            if (error || !data) {
                this.logger.error(
                    `取消会员订阅失败，未找到对应订阅: ${userId}, ${subscription_id}`,
                );
                throw new BadRequestException('未找到有效的会员订阅');
            }

            // 更新会员记录为非活跃
            const { error: updateError } = await this.supabase
                .from('user_memberships')
                .update({
                    is_active: false,
                    subscription_status: 'canceled',
                    updated_at: new Date().toISOString(),
                })
                .eq('id', data.id);

            if (updateError) {
                this.logger.error(`更新会员状态失败: ${data.id}`, updateError);
                return false;
            }

            return true;
        } catch (error) {
            this.logger.error(`取消会员订阅失败: ${userId}`, error);
            if (error instanceof BadRequestException) {
                throw error;
            }
            return false;
        }
    }

    /**
     * 处理订阅更新（续费、状态变更等）
     * @param subscriptionId 订阅ID
     * @param status 订阅状态
     * @param currentPeriodEnd 当前周期结束时间
     * @param customerInfo 客户信息
     * @returns 是否更新成功
     */
    async handleSubscriptionUpdate(
        subscriptionId: string,
        status: string,
        currentPeriodEnd: Date,
        customerInfo?: {
            customerId?: string;
            cancelAtPeriodEnd?: boolean;
            isRenewal?: boolean;
            invoiceId?: string;
        },
    ): Promise<boolean> {
        try {
            // 查询当前订阅
            const { data: subscriptions, error: fetchError } = await this.supabase
                .from('user_memberships')
                .select('*, user:user_id(*)')
                .eq('subscription_id', subscriptionId)
                .eq('is_active', true)
                .order('created_at', { ascending: false })
                .limit(1);

            if (fetchError) {
                this.logger.error(`获取订阅信息失败: ${fetchError.message}`, fetchError);
                throw new BadRequestException(`获取订阅信息失败: ${fetchError.message}`);
            }

            if (!subscriptions || subscriptions.length === 0) {
                this.logger.error(`未找到有效的订阅: ${subscriptionId}`);
                return false;
            }

            const subscription = subscriptions[0];
            const userId = subscription.user_id;
            const planId = subscription.plan_id;

            // 获取计划详情
            const plan = await this.getMembershipPlanById(planId);
            const isNewPeriod = new Date(subscription.current_period_end) < currentPeriodEnd;

            // 判断是否为续费
            const isRenewal = customerInfo?.isRenewal || isNewPeriod;

            // 更新会员订阅记录
            const updateData: any = {
                updated_at: new Date().toISOString(),
            };

            // 当前周期有变化时更新
            if (currentPeriodEnd) {
                updateData.current_period_end = currentPeriodEnd.toISOString();
                updateData.expires_at = currentPeriodEnd.toISOString();
            }

            // 更新取消状态
            if (customerInfo && customerInfo.cancelAtPeriodEnd !== undefined) {
                updateData.cancel_at_period_end = customerInfo.cancelAtPeriodEnd;
            }

            // 更新订阅状态
            if (status) {
                updateData.status = status;

                // 处理续费、过期等特殊状态
                if (status === 'active') {
                    updateData.is_active = true;
                } else if (['canceled', 'expired'].includes(status)) {
                    if (new Date() >= new Date(subscription.current_period_end)) {
                        // 只有当前周期已结束时才标记为非活跃
                        updateData.is_active = false;
                    }
                }
            }

            // 如果是续费，记录日志
            if (isRenewal) {
                this.logger.log(`检测到会员续费: 用户=${userId}, 订阅=${subscriptionId}, 发票ID=${customerInfo?.invoiceId || '未知'}`);
            }

            // 更新订阅记录
            const { error: updateError } = await this.supabase
                .from('user_memberships')
                .update(updateData)
                .eq('id', subscription.id);

            if (updateError) {
                this.logger.error(`更新订阅记录失败: ${updateError.message}`, updateError);
                throw new BadRequestException(`更新订阅记录失败: ${updateError.message}`);
            }

            // 处理续费时的积分发放
            if (isRenewal && status === 'active' && plan) {
                // 发放会员月度积分
                try {
                    // 直接使用plan.plan_name而不是通过level推断
                    const membershipType = plan.plan_name || 'unknown';

                    // 非免费会员才发放积分
                    if (membershipType.toLowerCase() !== 'free' && !membershipType.toLowerCase().includes('免费')) {
                        // 准备积分发放参数
                        const creditParams: any = {
                            planId,
                            plan,
                            isRenewal: true
                        };

                        // 如果有发票ID，添加到参数中
                        if (customerInfo?.invoiceId) {
                            creditParams.invoiceId = customerInfo.invoiceId;
                            creditParams.description = `${membershipType}会员续费赠送积分 (发票: ${customerInfo.invoiceId})`;
                        }

                        // 传递已查询的plan对象和额外参数，避免重复查询
                        const creditResult = await this.creditsService.grantMembershipMonthlyCredits(userId, planId, creditParams);

                        if (creditResult && creditResult.success) {
                            // 记录积分刷新成功
                            this.logger.log(`会员续费积分刷新成功: 用户=${userId}, 会员类型=${membershipType}, 新积分=${creditResult.newBalance}, 旧积分=${creditResult.previousBalance}, 交易ID=${creditResult.transaction}`);
                        } else if (creditResult) {
                            this.logger.warn(`会员续费积分刷新失败: 用户=${userId}, 原因=${creditResult.message}`);
                        }
                    } else {
                        this.logger.log(`免费会员不发放积分: 用户=${userId}, 会员类型=${membershipType}`);
                    }
                } catch (creditError) {
                    // 记录积分刷新失败，但不影响订阅更新流程
                    this.logger.error(`订阅更新成功但月度积分刷新失败: ${creditError.message}`, creditError);
                    // 这里可以添加告警或重试队列逻辑
                }
            } else if (!isRenewal) {
                this.logger.log(`非续费更新，不发放积分: 用户=${userId}, 订阅=${subscriptionId}`);
            }

            return true;
        } catch (error) {
            this.logger.error(`处理订阅更新失败: ${error.message}`, error);
            throw error;
        }
    }

    /**
     * 获取用户当前会员级别
     * @param userId 用户ID
     * @returns 会员级别
     */
    async getUserMembershipLevel(userId: string): Promise<MembershipLevel> {
        const membership = await this.getUserMembership(userId);
        return membership ? membership.level : MembershipLevel.FREE;
    }

    /**
     * 获取用户会员优先级
     * @param userId 用户ID
     * @returns 会员优先级
     */
    async getUserMembershipPriority(userId: string): Promise<number> {
        // 获取用户会员信息
        const membership = await this.getUserMembership(userId);

        // 如果用户没有会员或会员未激活，返回默认优先级0
        if (!membership || !membership.is_active || !membership.plan_id) {
            return 0;
        }

        // 从会员权益表中获取视频生成优先级
        const priority = await this.interestService.getVideoGenPriority(membership.plan_id);

        // 如果权益表中没有设置优先级，则使用默认配置
        if (priority === undefined || priority === null) {
            const level = await this.getUserMembershipLevel(userId);
            return DEFAULT_MEMBERSHIP_PRIORITIES[level] || 0;
        }

        return priority;
    }

    /**
     * 检查用户是否有特定的会员权益
     * @param userId 用户ID
     * @param interestKey 权益键名
     * @returns 权益值，如果没有返回null
     */
    async checkUserMembershipInterest(
        userId: string,
        interestKey: string,
    ): Promise<any> {
        const membership = await this.getUserMembership(userId);
        if (!membership || !membership.is_active || !membership.plan_id) {
            return null;
        }

        return this.interestService.getMembershipInterestValue(
            membership.plan_id,
            interestKey,
        );
    }
}
