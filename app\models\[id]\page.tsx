import Image from "next/image"
import { modelApi } from "@/lib/api/model"
import { notFound } from "next/navigation"
import { Metadata } from "next"
import BackButton from "./components/BackButton"
import ModelActions from "./components/ModelActions"

// 动态生成Metadata
export async function generateMetadata(
    { params }: { params: { id: string } }
): Promise<Metadata> {
    try {
        const { id } = await params;
        const model = await modelApi.getModelById(id)

        return {
            title: `${model.name} - AI Model | ReelMind`,
            description: model.description || `Explore ${model.name} model for video generation on ReelMind.`,
            openGraph: {
                images: [model.cover_img || '/placeholder.svg'],
                title: `${model.name} - AI Model | ReelMind`,
                description: model.description || `Explore ${model.name} model for video generation on ReelMind.`,
            }
        }
    } catch (error) {
        return {
            title: "Model Details | ReelMind",
            description: "Explore AI models for video generation on ReelMind."
        }
    }
}

// 服务端渲染组件
export default async function ModelDetailPage({ params }: { params: { id: string } }) {
    const { id: modelId } = await params;

    // 服务端获取数据
    try {
        const modelData = await modelApi.getModelById(modelId)

        // 处理数据，适配前端显示
        const model = {
            ...modelData,
            type: modelData.model_type.toLowerCase(),
        }

        return (
            <main className="container mx-auto px-4 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Model Preview */}
                    <div className="relative aspect-video rounded-xl overflow-hidden">
                        <Image
                            src={model.cover_img || '/placeholder.svg'}
                            alt={model.name}
                            fill
                            className="object-cover"
                            sizes="(max-width: 768px) 100vw, 50vw"
                            priority
                        />
                    </div>

                    {/* Model Details */}
                    <div className="space-y-6">
                        <BackButton />

                        <h1 className="text-3xl font-bold">{model.name}</h1>

                        <div className="flex flex-wrap gap-2">
                            <div className="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm">
                                {model.type === "checkpoint" ? "Checkpoint" : "LoRA"}
                            </div>
                            {model.tags?.map((tag) => (
                                <div key={tag} className="bg-secondary/10 text-secondary rounded-full px-3 py-1 text-sm">
                                    {tag}
                                </div>
                            ))}
                        </div>

                        {model.description && (
                            <p className="text-lg text-muted-foreground">{model.description}</p>
                        )}

                        {/* Model Features */}
                        {model.supported_features && model.supported_features.length > 0 && (
                            <div className="space-y-2">
                                <h3 className="text-xl font-medium">Supported Features</h3>
                                <ul className="list-disc list-inside space-y-1">
                                    {model.supported_features.map((feature) => (
                                        <li key={feature}>{feature}</li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        {/* Call to Action - 客户端交互 */}
                        <ModelActions modelId={model.id} />
                    </div>
                </div>
            </main>
        )
    } catch (error) {
        // 如果找不到模型，返回404
        console.error("Failed to load model:", error)
        notFound()
    }
}