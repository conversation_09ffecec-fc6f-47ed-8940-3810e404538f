export enum MembershipPlanName {
    FREE = "FREE",
    PRO = "PRO",
    MAX = "MAX",
    UNLIMITED = "UNLIMITED",
}

// 会员计划接口（匹配后端MembershipPriceDto）
export interface MembershipPlan {
    id: string;
    level: number;
    monthly_price: number;
    yearly_price: number;
    is_popular: boolean;
    features: string[];
    is_active: boolean;
    created_at: string;
    updated_at: string;
    plan_name: string;
    stripe_price_id_month: string;
    stripe_price_id_year: string;
}

// 会员特权接口
export interface MembershipFeature {
    text: string;
    included: boolean;
}

// 用户会员信息接口
export interface UserMembership {
    billing_cycle: string;
    cancel_at_period_end: boolean;
    created_at: string;
    days_left: number;
    expires_at: string;
    id: string;
    is_active: boolean;
    plan_id: string;
    plan_name: string;
    subscription_id: string;
    updated_at: string;
}

// 支付信息接口
export interface PaymentInfo {
    id: string;
    status: 'pending' | 'completed' | 'failed' | 'cancelled';
    payment_url: string;
}

// 创建会员请求接口
export interface CreateMembershipRequest {
    plan_id: string;
    months: number;
    payment_id?: string;
}