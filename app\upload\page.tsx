"use client"

import { useVideoUpload } from "@/hooks/use-video-upload";
import { useUploadForm } from "@/hooks/use-upload-form";
import { TopBar } from "./components/TopBar";
import { VideoPreview } from "./components/VideoPreview";
import { UploadForm } from "./components/UploadForm";
import { UploadMetadata } from "@/types/upload";
import { RefObject } from "react";

export default function UploadPage() {
  // 使用自定义hooks
  const {
    uploadState,
    fileInputRef,
    handleFileChange,
    handleRemoveVideo,
    triggerFileInput,
    uploadVideo: startUpload
  } = useVideoUpload();

  const {
    formState,
    tagError,
    handleInputChange,
    handleGenerationTypeChange,
    handleVideoSourceChange,
    handleAddTag,
    removeTag,
    toggleTool,
    handlePublish
  } = useUploadForm();

  // 上传视频
  const uploadVideo = () => {
    const metadata: UploadMetadata = {
      title: formState.title,
      description: formState.description,
      tags: formState.tags,
      prompt: formState.prompt,
      negativePrompt: formState.negativePrompt,
      guidanceScale: formState.guidanceScale,
      steps: formState.steps,
      seed: formState.seed,
      generationType: formState.generationType,
      modelResources: formState.modelResources,
      selectedTools: formState.selectedTools,
      videoSource: formState.videoSource
    };

    startUpload(metadata);
  };

  // 处理发布
  const onPublish = () => {
    if (!uploadState.videoFile) {
      // 设置错误信息
      return;
    }

    if (!formState.title.trim()) {
      // 设置错误信息
      return;
    }

    handlePublish(uploadState.uploadComplete, startUpload);
  };

  return (
    <div className="flex flex-col h-screen bg-background text-foreground">
      {/* 顶部导航 */}
      <TopBar
        isPublishing={formState.isPublishing}
        handlePublish={onPublish}
      />

      {/* 主内容 */}
      <div className="flex flex-1 overflow-hidden py-4">
        {/* 左侧：视频预览 */}
        <VideoPreview
          uploadState={uploadState}
          triggerFileInput={triggerFileInput}
          handleRemoveVideo={handleRemoveVideo}
        />

        {/* 右侧：上传表单 */}
        <UploadForm
          formState={formState}
          uploadState={uploadState}
          fileInputRef={fileInputRef as RefObject<HTMLInputElement>}
          handleInputChange={handleInputChange}
          handleGenerationTypeChange={handleGenerationTypeChange}
          handleVideoSourceChange={handleVideoSourceChange}
          handleAddTag={handleAddTag}
          removeTag={removeTag}
          toggleTool={toggleTool}
          handleFileChange={handleFileChange}
          uploadVideo={uploadVideo}
          triggerFileInput={triggerFileInput}
          tagError={tagError}
        />
      </div>
    </div>
  );
}

