"use client"

import { useEffect } from "react"
import { useUserTasks } from "../queries"
import { useTrainStore } from "@/store/useTrainStore"
import useAuthStore from "@/store/useAuthStore"
import { Loader2, Clock, CheckCircle, AlertCircle, XCircle, RefreshCw, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { formatTimeAgo } from "@/lib/utils"
import { TrainTaskStatus } from "@/lib/api/train"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"

export function TrainRecordsSection() {
    const { userTasksFilter, setUserTasks, updateUserTasksFilter } = useTrainStore()
    const { isAuthenticated } = useAuthStore()

    const { data, isLoading, refetch } = useUserTasks(
        userTasksFilter.status,
        userTasksFilter.limit,
        userTasksFilter.offset,
        { enabled: isAuthenticated }
    )

    // 当数据加载完成后，更新store
    useEffect(() => {
        if (data) {
            setUserTasks(data.tasks, data.total)
        }
    }, [data, setUserTasks])

    // 渲染状态图标
    const renderStatusIcon = (status: TrainTaskStatus) => {
        switch (status) {
            case TrainTaskStatus.PENDING:
                return <Clock className="h-4 w-4 text-yellow-400" />
            case TrainTaskStatus.PROCESSING:
                return <RefreshCw className="h-4 w-4 text-blue-400 animate-spin" />
            case TrainTaskStatus.COMPLETED:
                return <CheckCircle className="h-4 w-4 text-green-400" />
            case TrainTaskStatus.FAILED:
                return <AlertCircle className="h-4 w-4 text-red-400" />
            case TrainTaskStatus.CANCELLED:
                return <XCircle className="h-4 w-4 text-gray-400" />
        }
    }

    // 格式化状态文本
    const formatStatus = (status: TrainTaskStatus) => {
        switch (status) {
            case TrainTaskStatus.PENDING:
                return "Pending"
            case TrainTaskStatus.PROCESSING:
                return "Processing"
            case TrainTaskStatus.COMPLETED:
                return "Completed"
            case TrainTaskStatus.FAILED:
                return "Failed"
            case TrainTaskStatus.CANCELLED:
                return "Cancelled"
        }
    }

    // 处理状态过滤变化
    const handleStatusChange = (value: string) => {
        updateUserTasksFilter({
            status: value === "all" ? undefined : value as TrainTaskStatus,
            offset: 0 // 重置分页
        })
    }

    return (
        <div className="cyber-card pixel-border rounded-xl p-4 flex flex-col h-full">
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                    <span className="text-[#F5EFFF] font-mono uppercase text-xs tracking-wider">RECORDS</span>

                    {/* 只在登录状态下显示过滤器 */}
                    {isAuthenticated && (
                        <Select
                            value={userTasksFilter.status || "all"}
                            onValueChange={handleStatusChange}
                        >
                            <SelectTrigger className="w-28 h-8 border-[#F5EFFF]/30 bg-black text-[#F5EFFF] text-xs">
                                <SelectValue placeholder="Filter" />
                            </SelectTrigger>
                            <SelectContent className="bg-black border-[#F5EFFF]/30 text-[#F5EFFF]">
                                <SelectItem value="all" className="text-xs font-mono">
                                    All
                                </SelectItem>
                                {Object.values(TrainTaskStatus).map(status => (
                                    <SelectItem
                                        key={status}
                                        value={status}
                                        className="text-xs font-mono flex items-center"
                                    >
                                        <div className="flex items-center">
                                            {renderStatusIcon(status)}
                                            <span className="ml-2">{formatStatus(status)}</span>
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    )}
                </div>

                {isAuthenticated && (
                    <Button
                        variant="outline"
                        size="sm"
                        className="border-[#F5EFFF]/30 text-[#F5EFFF] hover:bg-[#F5EFFF]/10 h-8 px-2"
                        onClick={() => refetch()}
                    >
                        <RefreshCw className="h-3 w-3" />
                    </Button>
                )}
            </div>

            {/* 任务列表 */}
            <div className="flex-1 overflow-y-auto pr-1 custom-scrollbar">
                {!isAuthenticated ? (
                    <div className="text-center text-[#F5EFFF]/70 font-mono text-sm h-full flex items-center justify-center flex-col gap-3">
                        <span>Please login to view your training records</span>
                    </div>
                ) : isLoading ? (
                    <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-6 w-6 text-[#F5EFFF] animate-spin" />
                    </div>
                ) : data?.tasks.length === 0 ? (
                    <div className="text-center text-[#F5EFFF]/50 font-mono text-sm h-full flex items-center justify-center">
                        <span>No training records found</span>
                    </div>
                ) : (
                    <div className="space-y-3">
                        {data?.tasks.map(task => (
                            <div
                                key={task.id}
                                className="cyber-card bg-black/40 border border-[#F5EFFF]/30 rounded-lg p-3 hover:bg-black/60 transition-colors"
                            >
                                <div className="flex items-center justify-between mb-2">
                                    <div className="flex items-center space-x-2">
                                        {renderStatusIcon(task.status)}
                                        <span className="font-mono text-xs uppercase text-[#F5EFFF]">
                                            {formatStatus(task.status)}
                                        </span>
                                    </div>
                                    <span className="text-[#F5EFFF]/70 text-xs">
                                        {formatTimeAgo(task.created_at)}
                                    </span>
                                </div>

                                {/* 触发词 */}
                                <div className="mb-2">
                                    <span className="text-[#F5EFFF] font-mono">{task.settings.trigger_word}</span>
                                </div>

                                {/* 视频数量 */}
                                <div className="flex items-center text-xs text-[#F5EFFF]/70 mb-2">
                                    <span>{task.videos.length} video{task.videos.length !== 1 ? 's' : ''}</span>
                                </div>

                                {/* 进度条 */}
                                {(task.status === TrainTaskStatus.PROCESSING || task.status === TrainTaskStatus.PENDING) && (
                                    <div className="w-full bg-[#F5EFFF]/10 rounded-full h-2 mb-2">
                                        <div
                                            className="bg-[#F5EFFF] h-2 rounded-full transition-all duration-500"
                                            style={{ width: `${task.progress * 100}%` }}
                                        ></div>
                                    </div>
                                )}

                                {/* 错误信息 */}
                                {task.error_message && (
                                    <div className="text-xs text-red-400 font-mono border border-red-400/30 p-2 bg-red-400/10 rounded mt-2">
                                        {task.error_message}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* 分页控制 */}
            {isAuthenticated && data && data.total > userTasksFilter.limit && (
                <div className="flex justify-between items-center mt-4 text-xs font-mono">
                    <Button
                        variant="outline"
                        size="sm"
                        className="border-[#F5EFFF]/30 text-[#F5EFFF] hover:bg-[#F5EFFF]/10"
                        disabled={userTasksFilter.offset === 0}
                        onClick={() => updateUserTasksFilter({
                            offset: Math.max(0, userTasksFilter.offset - userTasksFilter.limit)
                        })}
                    >
                        Previous
                    </Button>

                    <span className="text-[#F5EFFF]/70">
                        {userTasksFilter.offset + 1}-
                        {Math.min(userTasksFilter.offset + userTasksFilter.limit, data.total)} of {data.total}
                    </span>

                    <Button
                        variant="outline"
                        size="sm"
                        className="border-[#F5EFFF]/30 text-[#F5EFFF] hover:bg-[#F5EFFF]/10"
                        disabled={userTasksFilter.offset + userTasksFilter.limit >= data.total}
                        onClick={() => updateUserTasksFilter({
                            offset: userTasksFilter.offset + userTasksFilter.limit
                        })}
                    >
                        Next
                    </Button>
                </div>
            )}
        </div>
    )
} 