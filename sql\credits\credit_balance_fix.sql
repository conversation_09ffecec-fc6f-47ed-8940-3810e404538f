-- 创建用于修复用户余额的存储过程
CREATE OR REPLACE FUNCTION fix_user_credit_balance(
    p_user_id UUID,
    p_expected_balance INT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_balance INT;
    v_version INT;
    v_transaction_id UUID;
BEGIN
    -- 开始事务
    BEGIN
        -- 检查用户是否存在
        IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
            RAISE EXCEPTION '用户不存在';
        END IF;
        
        -- 获取当前余额记录
        SELECT credits, version INTO v_current_balance, v_version
        FROM user_credit_balances
        WHERE user_id = p_user_id
        FOR UPDATE;
        
        -- 如果没有余额记录，创建一个新的
        IF NOT FOUND THEN
            INSERT INTO user_credit_balances (
                user_id, 
                credits, 
                version, 
                created_at, 
                updated_at
            ) VALUES (
                p_user_id, 
                p_expected_balance, 
                1, 
                NOW(), 
                NOW()
            );
            
            RETURN jsonb_build_object(
                'status', 'created',
                'previous_balance', NULL,
                'new_balance', p_expected_balance
            );
        END IF;
        
        -- 如果余额已经正确，无需更新
        IF v_current_balance = p_expected_balance THEN
            RETURN jsonb_build_object(
                'status', 'no_change',
                'message', '余额已经正确，无需更新',
                'balance', v_current_balance
            );
        END IF;
        
        -- 创建一个余额修正记录
        INSERT INTO credit_transactions (
            user_id,
            type,
            amount,
            description,
            status,
            created_at
        ) VALUES (
            p_user_id,
            'balance_adjustment',
            p_expected_balance - v_current_balance,
            '系统余额校正',
            'completed',
            NOW()
        )
        RETURNING id INTO v_transaction_id;
        
        -- 更新余额
        UPDATE user_credit_balances
        SET 
            credits = p_expected_balance,
            version = version + 1,
            last_transaction_id = v_transaction_id,
            updated_at = NOW()
        WHERE user_id = p_user_id;
        
        -- 返回结果
        RETURN jsonb_build_object(
            'status', 'fixed',
            'previous_balance', v_current_balance,
            'new_balance', p_expected_balance,
            'adjustment', p_expected_balance - v_current_balance,
            'transaction_id', v_transaction_id
        );
    EXCEPTION
        WHEN OTHERS THEN
            -- 回滚事务
            RAISE;
    END;
END;
$$;

-- 添加 balance_adjustment 类型到交易类型的检查约束（如果需要）
DO $$ 
BEGIN
    -- 检查是否需要添加新的类型
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_constraint 
        WHERE conname = 'credit_transactions_type_check' 
        AND contype = 'c'
    ) THEN
        -- 如果不存在约束，创建一个新的
        ALTER TABLE credit_transactions 
        ADD CONSTRAINT credit_transactions_type_check 
        CHECK (type IN ('membership_monthly', 'direct_purchase', 'platform_reward', 
                        'new_user_bonus', 'post_reward', 'video_generation_sd', 
                        'video_generation_hd', 'balance_adjustment'));
    ELSE
        -- 如果约束已存在，需要手动更新它
        -- 这部分需要DBA手动操作或使用动态SQL
        RAISE NOTICE '请手动更新credit_transactions表type字段的CHECK约束以包含balance_adjustment类型';
    END IF;
END $$; 