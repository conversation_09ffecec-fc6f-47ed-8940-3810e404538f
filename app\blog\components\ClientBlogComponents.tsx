'use client';

import { useInfiniteQuery, QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BlogPostCard } from '@/components/content/BlogPostCard';
import type { BlogPost } from '@/types/blog';
import { useEffect, useState, useRef } from 'react';

// 创建QueryClient实例
const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            staleTime: 1000 * 60 * 5, // 5分钟内不重新请求
            refetchOnWindowFocus: false,
        },
    },
});

// 统一API调用函数
async function fetchBlogPosts({ pageParam = 1 }) {
    const response = await fetch(`/api/blog?page=${pageParam}&pageSize=10`);

    if (!response.ok) {
        throw new Error('Failed to fetch posts');
    }

    return response.json();
}

interface ClientBlogComponentsProps {
    initialPosts: BlogPost[];
    initialHasMore?: boolean;
}

// 主客户端组件 - 提供QueryClient上下文
export default function ClientBlogComponents({ initialPosts, initialHasMore = true }: ClientBlogComponentsProps) {
    return (
        <QueryClientProvider client={queryClient}>
            <BlogContent initialPosts={initialPosts} initialHasMore={initialHasMore} />
        </QueryClientProvider>
    );
}

// 博客内容组件 - 使用react-query
function BlogContent({ initialPosts, initialHasMore }: ClientBlogComponentsProps) {
    // 使用useInfiniteQuery来处理无限滚动
    const {
        data,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage,
        status,
        error
    } = useInfiniteQuery({
        queryKey: ['blogPosts'],
        queryFn: fetchBlogPosts,
        initialPageParam: 1,
        getNextPageParam: (lastPage) => {
            return lastPage.hasMore ? lastPage.page + 1 : undefined;
        },
        initialData: {
            pages: [{ posts: initialPosts, hasMore: initialHasMore, page: 1 }],
            pageParams: [1],
        },
    });

    // 观察元素的ref，用于无限滚动
    const observerTarget = useRef(null);

    // 设置IntersectionObserver来监听加载更多区域
    useEffect(() => {
        const observer = new IntersectionObserver(
            entries => {
                if (entries[0]?.isIntersecting && hasNextPage && !isFetchingNextPage) {
                    fetchNextPage();
                }
            },
            { threshold: 0.1, rootMargin: '400px 0px' }
        );

        const currentTarget = observerTarget.current;
        if (currentTarget) observer.observe(currentTarget);

        return () => {
            if (currentTarget) observer.unobserve(currentTarget);
        };
    }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

    // 处理加载或错误状态
    if (status === 'error') {
        return (
            <div className="text-center text-red-500 py-4">
                {(error as Error).message}
                <button
                    onClick={() => fetchNextPage()}
                    className="ml-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                >
                    重试
                </button>
            </div>
        );
    }

    // 将所有页面的posts合并成一个数组
    const allPosts = data?.pages.flatMap(page => page.posts) || [];

    return (
        <>
            <ScrollProgressBar />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
                {allPosts.map((post, index) => (
                    <div key={`${post.id}-${index}`} className="animate-fadeIn">
                        <BlogPostCard post={post} />
                    </div>
                ))}
            </div>

            {hasNextPage && (
                <div ref={observerTarget} className="py-10 flex justify-center">
                    {isFetchingNextPage && <LoadingSpinner />}
                </div>
            )}

            {/* 如果没有更多内容，并且列表不为空，显示结束提示 */}
            {!hasNextPage && allPosts.length > 0 && (
                <div className="flex items-center justify-center py-12">
                    <div className="flex items-center justify-center px-6 py-3 rounded-full bg-gray-100 dark:bg-gray-800 shadow-sm">
                        <span className="text-gray-600 dark:text-gray-400 text-sm font-medium">You've reached the end</span>
                    </div>
                </div>
            )}
        </>
    );
}

// 进度条组件 - 客户端
function ScrollProgressBar() {
    const [scrollProgress, setScrollProgress] = useState(0);

    useEffect(() => {
        const updateScrollProgress = () => {
            const currentProgress = window.scrollY;
            const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;

            if (scrollHeight > 0) { // 避免除以零
                setScrollProgress(Number((currentProgress / scrollHeight).toFixed(2)));
            } else {
                setScrollProgress(0); // 如果不能滚动，进度为0
            }
        };

        window.addEventListener('scroll', updateScrollProgress);
        // 初始化时也计算一次进度
        updateScrollProgress();

        return () => window.removeEventListener('scroll', updateScrollProgress);
    }, []);

    return (
        <div
            className="fixed top-0 left-0 right-0 h-1 bg-primary-600 z-50 origin-left"
            style={{ transform: `scaleX(${scrollProgress})`, transition: 'transform 0.1s' }}
        />
    );
}

// 加载中组件
function LoadingSpinner() {
    return (
        <div className="relative flex items-center justify-center">
            <div className="absolute w-16 h-16 border-4 border-gray-200 dark:border-gray-800 rounded-full opacity-20"></div>
            <div className="w-16 h-16 border-3 border-transparent border-t-foreground rounded-full animate-spin"></div>
            <svg className="absolute text-primary-600 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
        </div>
    );
} 