"use client"

import { useSearchParams } from "next/navigation";
import { useEffect, useRef } from "react";
import { useToast } from "@/components/ui/toast";
import useVideoGeneratorStore from "@/store/useVideoGeneratorStore";
import useTabControlStore from "../hooks/useTabControl";
import useModelSelectorStore from "@/store/useModelSelectorStore";
import { generationApi } from "@/lib/api/generation";

export default function RemixTaskLoader() {
    const searchParams = useSearchParams();
    const remixTaskId = searchParams?.get("remix") || "";
    const processedTaskIdRef = useRef<string>("");
    const { loadTaskParams } = useVideoGeneratorStore();
    const { setActiveTab } = useTabControlStore();
    const { setSelectedModelId } = useModelSelectorStore();
    const toast = useToast();

    useEffect(() => {
        // 只有当有任务ID且与之前处理的不同时才执行
        if (remixTaskId && remixTaskId !== processedTaskIdRef.current) {
            handleLoadTask(remixTaskId);
            processedTaskIdRef.current = remixTaskId;
        }
    }, [remixTaskId]);

    const handleLoadTask = async (taskId: string) => {
        if (!taskId) return;

        try {
            // 获取任务详情，检查是否有effect_name
            const taskDetail = await generationApi.getTaskDetail(taskId);

            // 加载任务参数
            await loadTaskParams(taskId);

            // 如果任务有model_id，设置到ModelSelectorStore中
            if (taskDetail && taskDetail.input_params && taskDetail.input_params.model_id) {
                // 设置模型ID，确保在create页面自动选择相同的模型
                setSelectedModelId(taskDetail.input_params.model_id);
            }

            // 如果任务有effect_name，切换到effect标签页
            if (taskDetail && taskDetail.input_params && taskDetail.input_params.effect_name) {
                setActiveTab('effect');
            } else {
                setActiveTab('model');
            }

            // 显示成功提示
            toast.success(
                "Remix Task Loaded",
                "Video parameters loaded from original task, you can directly generate or modify parameters."
            );
        } catch (error) {
            console.error("Failed to load remix task:", error);

            // 显示错误提示
            toast.error(
                "Failed to load parameters",
                "Unable to load parameters from original task, please manually set parameters."
            );
        }
    };

    return null; // 这个组件不渲染任何内容，只处理逻辑
}