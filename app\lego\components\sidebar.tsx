"use client";

import { useCallback, ReactNode } from "react";
import { Plus, User, MapPin, Paintbrush, ChevronRight } from "lucide-react";
import { useLegoStore } from "../store";
import { CardType, SectionType } from "../types";
import { Card } from "./card";
import { motion, AnimatePresence } from "framer-motion";

export function Sidebar() {
    const {
        themeCards,
        sceneCards,
        styleCards,
        setIsUploading,
        addCard,
        updateCard,
        removeCard,
        toggleCardSelection,
        isSidebarCollapsed,
        setIsSidebarCollapsed
    } = useLegoStore();

    // 生成唯一ID
    const generateId = useCallback(() => {
        return `${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    }, []);

    // 添加新卡片
    const handleAddCard = useCallback((section: SectionType) => {
        addCard(section, {
            id: generateId(),
            type: "empty",
            content: "",
            selected: true
        });
    }, [addCard, generateId]);

    // 删除卡片
    const handleDeleteCard = useCallback((section: SectionType, id: string) => {
        removeCard(section, id);
    }, [removeCard]);

    // 切换卡片选择状态
    const handleToggleSelection = useCallback((section: SectionType, id: string) => {
        toggleCardSelection(section, id);
    }, [toggleCardSelection]);

    // 更新卡片内容
    const handleUpdateContent = useCallback((section: SectionType, id: string, type: CardType, content: string) => {
        updateCard(section, id, {
            type,
            content,
            selected: true
        });
    }, [updateCard]);

    // 开始上传图片
    const handleStartUpload = useCallback((section: SectionType, id: string) => {
        setIsUploading(true);
        updateCard(section, id, {
            type: "image",
            isUploading: true,
            selected: true
        });
    }, [setIsUploading, updateCard]);

    // 完成上传图片
    const handleFinishUpload = useCallback((section: SectionType, id: string, success: boolean, url?: string) => {
        setIsUploading(false);

        if (success && url) {
            updateCard(section, id, {
                type: "image",
                content: url,
                isUploading: false,
                selected: true
            });
        } else {
            updateCard(section, id, {
                type: "empty",
                content: "",
                isUploading: false,
                selected: true
            });
        }
    }, [setIsUploading, updateCard]);

    // 获取区域卡片
    const getSectionCards = useCallback((section: SectionType) => {
        switch (section) {
            case "theme":
                return themeCards;
            case "scene":
                return sceneCards;
            case "style":
                return styleCards;
        }
    }, [themeCards, sceneCards, styleCards]);

    // 获取区域图标
    const getSectionIcon = useCallback((section: SectionType): ReactNode => {
        switch (section) {
            case "theme":
                return <User size={24} />;
            case "scene":
                return <MapPin size={24} />;
            case "style":
                return <Paintbrush size={24} />;
        }
    }, []);

    // 检查卡片是否为空并添加初始卡片的逻辑已移至 store 的初始状态

    // 渲染区域
    const renderSection = useCallback((title: string, section: SectionType) => {
        const sectionCards = getSectionCards(section);

        // 如果没有卡片，显示加载中
        if (!sectionCards || sectionCards.length === 0) {
            return (
                <div className="mb-4 md:mb-6">
                    <h3 className="font-medium">{title}</h3>
                    <div className="h-70 flex items-center justify-center">
                        <div className="w-8 h-8 border-2 border-t-transparent border-white rounded-full animate-spin"></div>
                    </div>
                </div>
            );
        }

        // Add extra bottom margin for the Style section on mobile
        const isStyleSection = section === "style";
        const extraMobileClass = isStyleSection ? "mb-20 md:mb-6" : "mb-4 md:mb-6";

        return (
            <div className={extraMobileClass}>
                <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium">{title}</h3>
                    {sectionCards.length < 10 && (
                        <button
                            onClick={() => handleAddCard(section)}
                            className="p-1 rounded-full hover:bg-[#222]/20 transition-colors"
                            title="Add new card"
                        >
                            <Plus size={16} />
                        </button>
                    )}
                </div>

                {sectionCards.map(card => (
                    <Card
                        key={card.id}
                        card={card}
                        section={section}
                        onDelete={(id) => handleDeleteCard(section, id)}
                        onToggleSelect={(id) => handleToggleSelection(section, id)}
                        onUpdateContent={(id, type, content) => handleUpdateContent(section, id, type, content)}
                        onStartUpload={(id) => handleStartUpload(section, id)}
                        onFinishUpload={(id, success, url) => handleFinishUpload(section, id, success, url)}
                        sectionIcon={getSectionIcon(section)}
                    />
                ))}
            </div>
        );
    }, [getSectionCards, handleAddCard, handleDeleteCard, handleToggleSelection, handleUpdateContent, handleStartUpload, handleFinishUpload, getSectionIcon]);

    // 处理侧边栏折叠/展开
    const handleToggleSidebar = () => {
        setIsSidebarCollapsed(!isSidebarCollapsed);
    };

    return (
        <motion.div
            initial={false}
            animate={{
                width: isSidebarCollapsed ? "50px" : "300px",
                transition: { duration: 0.3, ease: "easeInOut" }
            }}
            className="h-full overflow-hidden relative"
        >
            {/* 侧边栏内容 */}
            <AnimatePresence>
                {isSidebarCollapsed ? (
                    <motion.div
                        key="collapsed"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="h-full flex flex-col items-center pt-4 gap-6"
                    >
                        <div
                            className="p-2 rounded-full bg-secondary/30 hover:bg-secondary/50 cursor-pointer"
                            title="Theme"
                            onClick={handleToggleSidebar}
                        >
                            <User size={20} />
                        </div>
                        <div
                            className="p-2 rounded-full bg-secondary/30 hover:bg-secondary/50 cursor-pointer"
                            title="Scene"
                            onClick={handleToggleSidebar}
                        >
                            <MapPin size={20} />
                        </div>
                        <div
                            className="p-2 rounded-full bg-secondary/30 hover:bg-secondary/50 cursor-pointer"
                            title="Style"
                            onClick={handleToggleSidebar}
                        >
                            <Paintbrush size={20} />
                        </div>

                        {/* 展开按钮 - Changed to point left */}
                        <div
                            className="mt-4 p-2 rounded-full bg-primary/20 hover:bg-primary/30 cursor-pointer flex items-center justify-center"
                            title="Expand sidebar"
                            onClick={handleToggleSidebar}
                        >
                            <ChevronRight size={18} className="rotate-180" />
                        </div>
                    </motion.div>
                ) : (
                    <motion.div
                        key="expanded"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="w-full h-full pl-2 pr-2 md:pr-4 overflow-y-auto no-scrollbar pb-36 md:pb-0"
                    >
                        {renderSection("Theme", "theme")}
                        {renderSection("Scene", "scene")}
                        {renderSection("Style", "style")}
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.div>
    );
}
