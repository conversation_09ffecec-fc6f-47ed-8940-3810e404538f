# LoRA 模型训练接口文档

## 基础信息

- 基础路径: `/train`
- 所有需要认证的接口请在请求头中添加 `Authorization: Bearer ${token}`

## 接口列表

### 1. 获取基础模型列表

获取可用于训练的基础模型列表。

```typescript
GET /train/base_models

// 响应示例
{
  "base_models": [
    {
      "id": "uuid",
      "name": "模型名称",
      "description": "模型描述",
      "model_type": "CHECKPOINT",
      "is_public": true,
      "created_at": "2024-03-21T12:00:00Z"
    }
  ]
}
```

### 2. 计算训练价格

计算训练所需的积分，会根据会员等级返回折扣信息。

```typescript
POST /train/price

// 请求体
{
  "videos": string[],  // 视频URL数组
  "settings": {
    "trigger_word": string,      // 触发词
    "base_model_id"?: string,    // 可选，基础模型ID
    "learning_rate"?: number,    // 可选，学习率，默认0.0001
    "steps"?: number            // 可选，训练步数，默认1500
  }
}

// 响应示例
{
  "credits": 1000,                    // 所需积分
  "discount_info": "MAX会员可享受8折优惠" // 可选，折扣信息
}
```

### 3. 开始训练

创建一个新的训练任务。

```typescript
POST /train/start

// 请求体
{
  "videos": string[],  // 视频URL数组，最多10个视频
  "settings": {
    "trigger_word": string,      // 触发词
    "base_model_id"?: string,    // 可选，基础模型ID
    "learning_rate"?: number,    // 可选，学习率，范围：0.00001-0.01
    "steps"?: number            // 可选，训练步数，范围：500-5000
  }
}

// 响应示例
{
  "id": "uuid",
  "user_id": "uuid",
  "status": "pending",
  "progress": 0,
  "videos": ["video_url1", "video_url2"],
  "settings": { ... },
  "priority": 0,
  "created_at": "2024-03-21T12:00:00Z"
}
```

### 4. 获取用户训练任务列表

获取当前用户的训练任务列表。

```typescript
GET /train/user-tasks?status={status}&limit={limit}&offset={offset}

// 参数说明
status?: string   // 可选，任务状态过滤：pending|processing|completed|failed|cancelled
limit?: number    // 可选，每页数量，默认10
offset?: number   // 可选，偏移量，默认0

// 响应示例
{
  "tasks": [
    {
      "id": "uuid",
      "status": "pending",
      "progress": 0,
      "videos": ["video_url1"],
      "settings": {
        "trigger_word": "sks person",
        "steps": 1500
      },
      "model_id": null,
      "created_at": "2024-03-21T12:00:00Z",
      "started_at": null,
      "completed_at": null
    }
  ],
  "total": 100  // 总任务数
}
```

### 5. 获取训练任务详情

获取特定训练任务的详细信息。

```typescript
GET /train/task/{taskId}

// 响应示例
{
  "id": "uuid",
  "status": "completed",
  "progress": 100,
  "videos": ["video_url1"],
  "settings": {
    "trigger_word": "sks person",
    "base_model_id": "uuid",
    "steps": 1500
  },
  "model_id": "uuid",  // 训练完成后的模型ID
  "created_at": "2024-03-21T12:00:00Z",
  "started_at": "2024-03-21T12:05:00Z",
  "completed_at": "2024-03-21T13:00:00Z"
}
```

### 6. 取消训练任务

取消一个待处理的训练任务。

```typescript
POST /train/task/cancel/{taskId}

// 响应示例
{
  "id": "uuid",
  "status": "cancelled",
  "progress": 0,
  // ... 其他任务信息
}
```

## 状态码说明

- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 无权限
- `404`: 资源不存在
- `500`: 服务器错误

## 注意事项

1. 视频限制：

   - 单个视频大小上限：1GB
   - 单次训练最多支持 10 个视频
   - 视频 URL 需要是可访问的

2. 训练参数限制：

   - 学习率范围：0.00001-0.01
   - 训练步数范围：500-5000

3. 积分消费：

   - 开始训练时会立即扣除积分
   - 训练失败或取消时会自动退还积分
   - 会员用户享有积分折扣

4. 任务状态流转：

   ```
   pending -> processing -> completed/failed
   pending -> cancelled
   ```

5. 任务优先级：
   - 根据会员等级和创建时间排序
   - 会员等级越高，优先级越高

## TypeScript 类型定义

```typescript
// 训练任务状态
enum TrainTaskStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
}

// 训练设置
interface TrainSettings {
  trigger_word: string;
  base_model_id?: string;
  learning_rate?: number;
  steps?: number;
}

// 训练任务
interface TrainTask {
  id: string;
  user_id: string;
  status: TrainTaskStatus;
  progress: number;
  videos: string[];
  settings: TrainSettings;
  model_id?: string;
  priority: number;
  created_at: Date;
  started_at?: Date;
  completed_at?: Date;
  error_message?: string;
}
```
