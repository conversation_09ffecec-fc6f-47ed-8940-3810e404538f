import { IsNotEmpty, IsString, IsOptional, IsUUID } from 'class-validator';

/**
 * 创建订阅支付DTO
 */
export class CreateSubscriptionDto {
    @IsString()
    @IsNotEmpty()
    price_id: string;  // 会员计划ID

    @IsString()
    billing_cycle: 'monthly' | 'yearly';

    @IsOptional()
    @IsUUID()
    coupon_id?: string;  // 优惠券ID（可选）
}

/**
 * 支付结账DTO
 */
export class CheckoutPaymentDto {
    @IsString()
    @IsNotEmpty()
    sessionId: string;  // Stripe会话ID
}

/**
 * Stripe Webhook事件DTO
 */
export class StripeWebhookDto {
    @IsString()
    @IsNotEmpty()
    id: string;

    @IsString()
    @IsNotEmpty()
    type: string;

    @IsNotEmpty()
    data: {
        object: any;
    };
}