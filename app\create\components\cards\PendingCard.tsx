"use client"

import { useEffect, useState } from "react";
import { Clock } from "lucide-react";
import type { VideoTask } from "@/types/video-task";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { useQuery } from "@tanstack/react-query";
import { generationApi } from "@/lib/api/generation";
import { getStatusText, getStatusColor } from "../../utils/status-utils";
import { CardInfoSection } from "./CardInfoSection";

interface PendingCardProps {
    video: VideoTask;
    onStatusChange?: (videoId: string, newStatus: string, updatedVideo?: VideoTask) => void;
}

/**
 * PendingCard Component - 显示待处理状态的视频任务
 * 每10秒查询一次状态更新，不显示进度条
 */
export const PendingCard = ({
    video,
    onStatusChange
}: PendingCardProps) => {
    // 本地状态
    const [status, setStatus] = useState(video.status);

    // 使用react-query获取任务详情，每10秒轮询一次
    const { data: taskDetail } = useQuery({
        queryKey: ['pendingTaskDetail', video.id],
        queryFn: async () => {
            try {
                const result = await generationApi.getTaskDetail(video.id);
                return result;
            } catch (error) {
                console.error("Error fetching pending task detail:", error);
                return null;
            }
        },
        // 启用查询
        enabled: true,
        // 每10秒查询一次
        refetchInterval: 10000,
        // 当组件失去焦点时继续刷新
        refetchIntervalInBackground: true,
        staleTime: 5000,
    });

    // 处理状态变化
    useEffect(() => {
        // 如果从API获取到新状态
        if (taskDetail && taskDetail.status !== status) {
            // 更新本地状态
            setStatus(taskDetail.status);

            // 如果状态变为processing，通知父组件
            if (taskDetail.status === "processing" && onStatusChange) {
                onStatusChange(video.id, taskDetail.status, taskDetail);
            }
        }
    }, [taskDetail, status, video.id, onStatusChange]);

    return (
        <>
            {/* Video preview area */}
            <div className="relative w-full overflow-hidden bg-muted/60 cursor-pointer">
                {/* Status badge */}
                <div
                    className={cn(
                        "absolute top-2 right-2 z-10 px-2.5 py-1 rounded-full text-xs font-medium",
                        "transition-opacity duration-300",
                        getStatusColor(status)
                    )}
                >
                    {getStatusText(status)}
                </div>

                {/* Pending status display */}
                <div className="aspect-video">
                    <Skeleton className="w-full h-full" />
                    {/* Display reference image if available */}
                    {video.input_params.refer_img_url && (
                        <div className="absolute inset-0 flex items-center justify-center">
                            <img
                                src={video.input_params.refer_img_url}
                                alt="Reference image"
                                className="max-h-full max-w-full object-contain opacity-60"
                            />
                        </div>
                    )}
                </div>
                <div className="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-b from-background/70 to-background/40 backdrop-blur-sm">
                    <div className="flex items-center justify-center w-10 h-10 bg-yellow-500/20 rounded-full">
                        <Clock size={20} className="text-yellow-500" />
                    </div>
                    <div className="mt-3 text-sm font-medium text-primary-foreground">
                        {status === "pending" ? 'Pending...' : 'Queued...'}
                    </div>
                    {/* 队列状态信息 */}
                    <div className="mt-2 px-2 py-1 bg-yellow-500/10 rounded-full text-xs text-yellow-400">
                        Waiting in queue...
                    </div>
                </div>

                {/* Video info overlay - only show info, no buttons */}
                <div className={cn(
                    "absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent",
                    "transition-opacity duration-300 flex items-end p-3",
                    "hover:opacity-100 opacity-0"
                )}>
                    <div className="text-xs text-white/90 font-medium">
                        {video.input_params.ratio} · {video.input_params.definition || "SD"}
                    </div>
                </div>
            </div>

            {/* Information area */}
            <CardInfoSection video={video} />
        </>
    );
};
