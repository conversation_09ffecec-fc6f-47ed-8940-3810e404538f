import Image from "next/image"
import { Send, MessageCircle, Loader2, Clock } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Comment } from "@/types/posts"
import { formatTimeAgo } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { useAuthProtectedCallback } from "@/components/auth/with-auth"

interface CommentItemProps {
    comment: Comment
}

function CommentItem({ comment }: CommentItemProps) {
    const time = formatTimeAgo(comment.created_at);

    return (
        <>
            <div className="relative">
                <div className="absolute -inset-0.5 bg-gradient-to-tr from-blue-500/10 to-purple-500/10 rounded-full blur-sm" />
                <Image
                    src={comment.user_profiles.avatar || "/placeholder.svg"}
                    alt={comment.user_profiles.nickname}
                    width={40}
                    height={40}
                    className="rounded-full object-cover relative ring-1 ring-white/10"
                    unoptimized
                />
            </div>
            <div className="flex-1">
                <div className="flex items-center gap-2 mb-1.5">
                    <span className="font-medium text-white">{comment.user_profiles.nickname}</span>
                    <div className="flex items-center text-xs text-gray-400">
                        <Clock size={10} className="mr-1 text-gray-500" />
                        <span>{time}</span>
                    </div>
                </div>
                <p className="text-sm text-gray-300 leading-relaxed">{comment.content}</p>
            </div>
        </>
    );
}

// 评论骨架屏
function CommentSkeleton() {
    return (
        <div className="flex gap-3 p-4 rounded-lg bg-gray-900/40 backdrop-blur-sm">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-12" />
                </div>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
            </div>
        </div>
    );
}

interface CommentSectionProps {
    videoId: string
    comments: Comment[]
    loadingComments: boolean
    commentContent: string
    submittingComment: boolean
    setCommentContent: (content: string) => void
    submitComment: (postId: string, content: string) => void
}

export function CommentSection({
    videoId,
    comments,
    loadingComments,
    commentContent,
    submittingComment,
    setCommentContent,
    submitComment
}: CommentSectionProps) {
    const containerId = `comment-section-${videoId}`;
    // 使用useAuthProtectedCallback来包装评论提交功能
    const handleSubmitComment = useAuthProtectedCallback(() => {
        if (commentContent.trim() && !submittingComment) {
            submitComment(videoId, commentContent);
        }
    }, "submit_comment");

    const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleSubmitComment();
        }
    };

    // 显示内容（评论列表或骨架屏）
    const renderComments = () => {
        if (loadingComments) {
            return (
                <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                        <CommentSkeleton key={`skeleton-${i}`} />
                    ))}
                </div>
            );
        }

        if (comments.length === 0) {
            return (
                <div
                    className="flex flex-col items-center justify-center py-10 text-center"
                >
                    <div className="relative w-16 h-16 mb-4">
                        <div className="absolute inset-0 bg-blue-500/5 rounded-full animate-pulse"></div>
                        <MessageCircle className="w-16 h-16 text-blue-500/20 absolute inset-0" />
                    </div>
                    <h3 className="text-lg font-medium text-white mb-2">No comments yet</h3>
                    <p className="text-sm text-gray-400 max-w-xs">
                        Be the first to share your thoughts about this creation!
                    </p>
                </div>
            );
        }

        return (
            <div className="space-y-3">
                {comments.map((comment) => (
                    <CommentItem key={comment.id} comment={comment} />
                ))}
            </div>
        );
    };

    return (
        <div id={containerId} className="p-6">
            <div
                className="flex items-center gap-2 mb-5"
            >
                <MessageCircle size={18} className="text-blue-400" />
                <h3 className="text-base font-medium text-white">Comments</h3>
                {!loadingComments && comments.length > 0 && (
                    <span className="text-xs px-2 py-0.5 bg-blue-900/30 text-blue-300 rounded-full">
                        {comments.length}
                    </span>
                )}
                {loadingComments && (
                    <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                )}
            </div>

            {/* Comments List */}
            <div
                className="space-y-4 mb-6"
            >
                {renderComments()}
            </div>

            {/* Comment Input */}
            <div
                className="mt-6"
            >
                <div className="relative overflow-hidden rounded-xl bg-gray-900/50 backdrop-blur-sm focus-within:ring-1 focus-within:ring-blue-500/50 transition-all duration-200">
                    <textarea
                        className="w-full p-4 bg-transparent text-white resize-none outline-none scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent"
                        placeholder="Is this video cool?"
                        value={commentContent}
                        onChange={(e) => setCommentContent(e.target.value)}
                        onKeyPress={handleKeyPress}
                        disabled={submittingComment}
                    ></textarea>
                    <div className="flex justify-between items-center p-3">
                        <Button
                            type="button"
                            size="sm"
                            disabled={!commentContent.trim() || submittingComment}
                            onClick={handleSubmitComment}
                            className="ml-auto rounded-lg bg-gradient-to-r from-blue-600/80 to-purple-600/80 hover:from-blue-600 hover:to-purple-600 text-white"
                        >
                            {submittingComment ? (
                                <Loader2 className="w-4 h-4 mr-1.5 animate-spin" />
                            ) : (
                                <Send className="w-4 h-4 mr-1.5" />
                            )}
                            Send
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}