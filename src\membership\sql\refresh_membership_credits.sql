-- 创建会员积分刷新存储过程
-- 此存储过程在一个事务中完成积分清零和重新发放，确保原子性

CREATE OR REPLACE FUNCTION refresh_membership_credits(
    p_user_id UUID,
    p_plan_id UUID,
    p_description TEXT DEFAULT '会员积分月度刷新'
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_monthly_grant INT;
    v_result JSONB;
    v_transaction_id UUID;
    v_plan_name TEXT;
    v_current_balance INT;
    v_reset_transaction_id UUID;
BEGIN
    -- 开始事务
    BEGIN
        -- 1. 查询会员应获得的月度积分数量
        SELECT interest_value::INT INTO v_monthly_grant
        FROM membership_interests
        WHERE plan_id = p_plan_id
        AND interest_type = 'credits'
        AND interest_key = 'monthly_grant'
        AND is_active = true;
        
        -- 如果没有配置，使用默认值
        IF v_monthly_grant IS NULL THEN
            -- 查询会员计划名称，根据名称设置默认值
            SELECT plan_name INTO v_plan_name
            FROM membership_plans
            WHERE id = p_plan_id;
            
            IF v_plan_name ILIKE '%PRO%' THEN
                v_monthly_grant := 1288;
            ELSIF v_plan_name ILIKE '%MAX%' THEN
                v_monthly_grant := 2888;
            ELSE
                v_monthly_grant := 80; -- 默认值
            END IF;
        END IF;
        
        -- 2. 获取当前积分余额（用于记录清零数量）
        SELECT credits INTO v_current_balance
        FROM user_credit_balances
        WHERE user_id = p_user_id;
        
        -- 如果没有余额记录，设置为0
        v_current_balance := COALESCE(v_current_balance, 0);
        
        -- 3. 记录清零交易
        INSERT INTO credit_transactions (
            user_id,
            type,
            amount,
            description,
            status,
            created_at
        ) VALUES (
            p_user_id,
            'credits_reset',
            -v_current_balance, -- 负数表示减少积分
            '会员积分周期结束，积分清零',
            'completed',
            NOW()
        )
        RETURNING id INTO v_reset_transaction_id;
        
        -- 4. 清零用户当前积分（设置为0）
        UPDATE user_credit_balances
        SET credits = 0,
            last_transaction_id = v_reset_transaction_id,
            updated_at = NOW()
        WHERE user_id = p_user_id;
        
        -- 5. 发放新的积分
        INSERT INTO credit_transactions (
            user_id,
            type,
            amount,
            description,
            status,
            created_at
        ) VALUES (
            p_user_id,
            'membership_monthly',
            v_monthly_grant,
            p_description,
            'completed',
            NOW()
        )
        RETURNING id INTO v_transaction_id;
        
        -- 6. 更新用户积分余额
        UPDATE user_credit_balances
        SET credits = v_monthly_grant,
            last_transaction_id = v_transaction_id,
            updated_at = NOW()
        WHERE user_id = p_user_id;
        
        -- 7. 更新最后刷新日期
        UPDATE user_memberships
        SET last_credits_refresh_date = NOW(),
            updated_at = NOW()
        WHERE user_id = p_user_id
        AND is_active = true;
        
        -- 返回结果
        v_result := jsonb_build_object(
            'success', true,
            'user_id', p_user_id,
            'new_balance', v_monthly_grant,
            'previous_balance', v_current_balance,
            'refresh_date', NOW()
        );
        
        RETURN v_result;
    EXCEPTION
        WHEN OTHERS THEN
            -- 记录错误并回滚
            RAISE NOTICE 'Error refreshing credits for user %: %', p_user_id, SQLERRM;
            RETURN jsonb_build_object(
                'success', false,
                'error', SQLERRM
            );
    END;
END;
$$;