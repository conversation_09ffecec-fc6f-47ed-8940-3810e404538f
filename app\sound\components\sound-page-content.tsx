"use client";

import { useState, useEffect } from "react";
import { MainOperationPanel } from "./main-operation-panel";
import { ControlPanel } from "./control-panel";
import { HistoryPanel } from "./history-panel";
import { MobileSoundLayout } from "./mobile-sound-layout";

export function SoundPageContent() {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    if (isMobile) {
        return <MobileSoundLayout />;
    }

    return (
        <div className="relative z-10 flex h-full overflow-hidden bg-white dark:bg-black">
            {/* Main Content Area */}
            <div className="flex-1 flex overflow-hidden">
                {/* Left Control Panel */}
                <div className="hidden lg:block">
                    <ControlPanel />
                </div>

                {/* Main Operation Panel */}
                <div className="flex-1 flex flex-col min-w-0">
                    <MainOperationPanel />
                </div>

                {/* Right History Panel */}
                <div className="hidden lg:block">
                    <HistoryPanel />
                </div>
            </div>
        </div>
    );
}
