"use client";

import { useState, useEffect } from "react";
import { MainOperationPanel } from "./main-operation-panel";
import { SettingsPanel } from "./settings-panel";
import { HistoryPanel } from "./history-panel";
import { MobileSoundLayout } from "./mobile-sound-layout";
import { FunctionToggle } from "./function-toggle";

export function SoundPageContent() {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    if (isMobile) {
        return <MobileSoundLayout />;
    }

    return (
        <div className="relative z-10 flex h-full overflow-hidden bg-white dark:bg-black">
            {/* Function Toggle - Full Width */}
            <div className="absolute top-0 left-0 right-0 z-10 p-6 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800">
                <FunctionToggle />
            </div>

            {/* Main Content Area with Top Padding */}
            <div className="flex-1 flex overflow-hidden pt-24">
                {/* Left Settings Panel */}
                <div className="hidden lg:block">
                    <SettingsPanel />
                </div>

                {/* Main Operation Panel */}
                <div className="flex-1 flex flex-col min-w-0">
                    <MainOperationPanel />
                </div>

                {/* Right History Panel */}
                <div className="hidden lg:block">
                    <HistoryPanel />
                </div>
            </div>
        </div>
    );
}
