import Image from "next/image"
import Link from "next/link"
import { Download, Eye, ThumbsUp, Video, Image as ImageIcon, RefreshCcw } from "lucide-react"
import type { Model } from "@/types/model"
import { Badge } from "@/components/ui/badge"

interface ModelCardProps {
    model: Model
}

export function ModelCard({ model }: ModelCardProps) {
    // 获取模型类型的标签和样式
    const getModelTypeInfo = () => {
        const type = model.type?.toLowerCase() || "";
        
        if (type === "text-to-video") {
            return {
                label: "TEXT TO VIDEO",
                className: "bg-blue-500/90 text-xs font-medium",
                icon: <Video className="h-3 w-3 mr-1" />
            };
        }
        
        if (type === "image-to-video") {
            return {
                label: "IMAGE TO VIDEO",
                className: "bg-green-500/90 text-xs font-medium",
                icon: <ImageIcon className="h-3 w-3 mr-1" />
            };
        }
        
        if (type === "video-to-video") {
            return {
                label: "VIDEO TO VIDEO",
                className: "bg-purple-500/90 text-xs font-medium",
                icon: <RefreshCcw className="h-3 w-3 mr-1" />
            };
        }
        
        // 原有的模型类型处理逻辑保留
        if (type === "checkpoint") {
            return {
                label: "CHECKPOINT",
                className: "bg-blue-500/90 text-xs font-medium",
                icon: null
            };
        }
        
        if (type === "lora") {
            return {
                label: "LORA",
                className: "bg-purple-500/90 text-xs font-medium",
                icon: null
            };
        }
        
        return {
            label: type.toUpperCase(),
            className: "bg-gray-500/90 text-xs font-medium",
            icon: null
        };
    };

    const modelTypeInfo = getModelTypeInfo();

    return (
        <Link href={`/models/${model.id}`}>
            <div className="relative aspect-[4/5] rounded-xl overflow-hidden group h-full">
                {/* Cover Image */}
                <Image
                    src={model.cover_img || "/placeholder.svg"}
                    alt={model.name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent" />

                {/* Top Badges */}
                <div className="absolute top-3 left-3 flex gap-2 items-center">
                    <Badge className={`${modelTypeInfo.className} border-0 backdrop-blur-sm flex items-center`}>
                        {modelTypeInfo.icon}
                        {modelTypeInfo.label}
                    </Badge>
                </div>

                {/* Bottom Content */}
                <div className="absolute bottom-0 left-0 right-0 p-4 space-y-3">
                    {/* Creator Info */}
                    <div className="flex items-center gap-2">
                        <Image
                            src={model.creator?.avatar || "/placeholder.svg"}
                            alt={model.creator?.name || "Creator"}
                            width={28}
                            height={28}
                            className="rounded-full ring-2 ring-white/50"
                        />
                        <span className="text-sm text-white/90">{model.creator?.name || "Creator"}</span>
                    </div>

                    {/* Model Name */}
                    <h3 className="text-white font-medium text-xl leading-tight">{model.name}</h3>
                </div>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-black/10 opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
        </Link>
    )
}

