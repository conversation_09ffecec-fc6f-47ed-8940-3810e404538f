import { Injectable, OnModuleInit } from '@nestjs/common';
import { CustomLogger } from '../../common/services/logger.service';
import { StripeService } from '../stripe.service';
import { EnvironmentConfig } from '../../common/config/environment.config';
import { getStripeCouponConfig } from '../config/stripe-coupons.config';

@Injectable()
export class StripeCouponValidatorService implements OnModuleInit {
    constructor(
        private readonly stripeService: StripeService,
        private readonly logger: CustomLogger,
        private readonly environmentConfig: EnvironmentConfig
    ) {
        this.logger.setContext(StripeCouponValidatorService.name);
    }

    /**
     * 模块初始化时验证所有配置的Stripe优惠券
     */
    async onModuleInit() {
        await this.validateAllCoupons();
    }

    /**
     * 验证所有配置的Stripe优惠券是否存在
     */
    private async validateAllCoupons(): Promise<void> {
        const isTestMode = this.environmentConfig.isStripeTestMode();
        const envName = isTestMode ? '测试' : '生产';

        this.logger.log(`开始验证${envName}环境的Stripe优惠券配置...`);

        const couponConfig = getStripeCouponConfig(isTestMode);
        const coupons = Object.entries(couponConfig);
        const validationResults = await Promise.allSettled(
            coupons.map(([name, couponId]) => this.validateSingleCoupon(name, couponId as string))
        );

        let validCount = 0;
        let invalidCount = 0;

        validationResults.forEach((result, index) => {
            const [name] = coupons[index];
            if (result.status === 'fulfilled' && result.value) {
                validCount++;
                this.logger.log(`✓ ${envName}环境优惠券 ${name} 验证成功`);
            } else {
                invalidCount++;
                this.logger.error(`✗ ${envName}环境优惠券 ${name} 验证失败`);
            }
        });

        this.logger.log(`${envName}环境Stripe优惠券验证完成: ${validCount} 个有效, ${invalidCount} 个无效`);

        if (invalidCount > 0) {
            this.logger.warn(`部分${envName}环境Stripe优惠券配置无效，请检查Stripe后台设置`);
        }
    }

    /**
     * 验证单个Stripe优惠券
     */
    private async validateSingleCoupon(name: string, couponId: string): Promise<boolean> {
        try {
            const isValid = await this.stripeService.validateStripeCoupon(couponId);
            return isValid;
        } catch (error) {
            this.logger.error(`验证优惠券 ${name} (${couponId}) 时发生错误:`, error);
            return false;
        }
    }

    /**
     * 手动验证所有优惠券（用于健康检查）
     */
    async healthCheck(): Promise<{
        environment: string;
        valid: string[];
        invalid: string[];
        total: number;
    }> {
        const isTestMode = this.environmentConfig.isStripeTestMode();
        const envName = isTestMode ? '测试' : '生产';

        const couponConfig = getStripeCouponConfig(isTestMode);
        const coupons = Object.entries(couponConfig);
        const results = await Promise.allSettled(
            coupons.map(([name, couponId]) =>
                this.validateSingleCoupon(name, couponId as string).then(isValid => ({ name, couponId, isValid }))
            )
        );

        const valid: string[] = [];
        const invalid: string[] = [];

        results.forEach((result) => {
            if (result.status === 'fulfilled') {
                const { name, isValid } = result.value;
                if (isValid) {
                    valid.push(name);
                } else {
                    invalid.push(name);
                }
            } else {
                // 如果Promise被拒绝，也算作无效
                invalid.push('unknown');
            }
        });

        return {
            environment: envName,
            valid,
            invalid,
            total: coupons.length
        };
    }
}
