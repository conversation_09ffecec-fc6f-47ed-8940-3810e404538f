import { Controller, Get, Param, Put, Body, Query, UseGuards } from '@nestjs/common';
import { ErrorAuditService, ErrorCategory, ErrorSeverity } from '../services/error-audit.service';
import { AdminGuard } from '../guards/admin.guard';
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiResponse } from '@nestjs/swagger';

class ResolveErrorDto {
    resolution_notes: string;
}

@ApiTags('管理员 - 错误审计')
@Controller('admin/error-audit')
@UseGuards(AdminGuard)
export class ErrorAuditController {
    constructor(private readonly errorAuditService: ErrorAuditService) {}

    @Get()
    @ApiOperation({ summary: '获取未解决的错误列表' })
    @ApiQuery({ name: 'category', required: false, enum: ErrorCategory })
    @ApiQuery({ name: 'severity', required: false, enum: ErrorSeverity })
    @ApiQuery({ name: 'limit', required: false, type: Number })
    @ApiQuery({ name: 'offset', required: false, type: Number })
    @ApiResponse({ status: 200, description: '返回未解决的错误列表' })
    async getUnresolvedErrors(
        @Query('category') category?: ErrorCategory,
        @Query('severity') severity?: ErrorSeverity,
        @Query('limit') limit?: number,
        @Query('offset') offset?: number,
    ) {
        return this.errorAuditService.getUnresolvedErrors(
            category,
            severity,
            limit ? parseInt(limit.toString(), 10) : 50,
            offset ? parseInt(offset.toString(), 10) : 0,
        );
    }

    @Put(':id/resolve')
    @ApiOperation({ summary: '将错误标记为已解决' })
    @ApiParam({ name: 'id', description: '错误ID' })
    @ApiResponse({ status: 200, description: '错误已标记为已解决' })
    async resolveError(
        @Param('id') id: string,
        @Body() resolveErrorDto: ResolveErrorDto,
    ) {
        const success = await this.errorAuditService.markAsResolved(
            id,
            resolveErrorDto.resolution_notes,
        );

        return {
            success,
            message: success ? '错误已标记为已解决' : '标记错误为已解决失败',
        };
    }

    @Get('stats')
    @ApiOperation({ summary: '获取错误统计信息' })
    @ApiResponse({ status: 200, description: '返回错误统计信息' })
    async getErrorStats() {
        // 这里可以实现获取错误统计信息的逻辑
        // 例如，按类别、严重性统计未解决的错误数量
        // 由于当前ErrorAuditService没有实现此功能，这里返回一个空对象
        return {
            message: '此功能尚未实现',
        };
    }
} 