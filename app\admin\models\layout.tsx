import React from 'react';

interface AdminModelsLayoutProps {
    children: React.ReactNode;
}

export default function AdminModelsLayout({ children }: AdminModelsLayoutProps) {
    return (
        <div className="min-h-screen bg-background">
            <div className="border-b">
                <div className="container mx-auto px-4 py-4">
                    <nav className="flex items-center space-x-4">
                        <h1 className="text-xl font-semibold">Admin Panel</h1>
                        <span className="text-muted-foreground">/</span>
                        <span className="text-muted-foreground">Models</span>
                    </nav>
                </div>
            </div>
            {children}
        </div>
    );
}
