-- 创建获取下一个待处理任务的SQL函数
CREATE OR REPLACE FUNCTION public.get_next_pending_task(has_refer_img BOOLEAN DEFAULT NULL)
RETURNS SETOF public.video_gen_tasks
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- 获取并更新最高优先级且最早创建的pending状态任务
  RETURN QUERY
  WITH task AS (
    SELECT *
    FROM public.video_gen_tasks
    WHERE status = 'pending'
    AND handler IS NULL
    AND (
      -- 如果has_refer_img为NULL，则不过滤
      has_refer_img IS NULL
      -- 如果has_refer_img为true，只获取有参考图像的任务
      OR (has_refer_img = true AND input_params->>'refer_img_url' IS NOT NULL AND input_params->>'refer_img_url' != '')
      -- 如果has_refer_img为false，只获取没有参考图像的任务
      OR (has_refer_img = false AND (input_params->>'refer_img_url' IS NULL OR input_params->>'refer_img_url' = ''))
    )
    ORDER BY priority DESC, created_at ASC
    LIMIT 1
    FOR UPDATE SKIP LOCKED
  ),
  updated AS (
    UPDATE public.video_gen_tasks
    SET
      status = 'processing',
      started_at = NOW(),
      last_activity_at = NOW(),
      progress = 0
    WHERE id IN (SELECT id FROM task)
    RETURNING *
  )
  SELECT * FROM updated;
END;
$$;