import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 计算应用优惠券后的价格
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // 验证用户会话
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }
    
    // 获取请求体
    const body = await request.json();
    
    // 转发请求到后端 API
    const response = await fetch(`${process.env.REELAPI_SERVER_URL}/api/coupon/calculate-price`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify(body)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }
    
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('计算价格失败:', error);
    return NextResponse.json(
      { error: '计算价格失败' }, 
      { status: 500 }
    );
  }
}
