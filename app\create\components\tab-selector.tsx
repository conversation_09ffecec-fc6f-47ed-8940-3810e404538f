import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { memo, ReactNode, useCallback, useState } from "react"

interface TabSelectorProps {
    activeTab: string
    setActiveTab: (tab: string) => void
    tabs: { id: string; label: string; icon?: ReactNode; shortLabel?: string }[]
    className?: string
}

export function TabSelector({ activeTab, setActiveTab, tabs, className }: TabSelectorProps) {
    // 使用React.memo优化渲染性能
    const TabButton = memo(({
        tab,
        isActive,
        onClick
    }: {
        tab: { id: string; label: string; icon?: ReactNode; shortLabel?: string };
        isActive: boolean;
        onClick: () => void
    }) => {
        const [isHovered, setIsHovered] = useState(false);
        
        // Use abbreviated label unless hovered or active
        const displayLabel = (isHovered || isActive || !tab.shortLabel) ? tab.label : tab.shortLabel;
        
    return (
                <div
                    key={tab.id}
                className="relative"
                onClick={onClick}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                >
                    <button
                        className={cn(
                        "px-2.5 py-1.5 rounded-md text-xs md:text-sm font-medium relative z-10 border",
                        "transition-all duration-700 ease-in-out flex items-center whitespace-nowrap",
                        "w-full justify-center h-9",
                        isActive
                            ? "text-foreground bg-card/30 border-border"
                            : "text-muted-foreground hover:text-foreground/80 border-transparent"
                        )}
                    >
                    {tab.icon && <span className={cn(
                      "transition-all duration-700 ease-in-out flex items-center justify-center", 
                      isHovered ? "mr-2" : "mr-1",
                      "md:block h-4 w-4",
                      !displayLabel || displayLabel === tab.shortLabel ? "block" : "hidden"
                    )}>{tab.icon}</span>}
                    <span className={cn(
                      tab.shortLabel && !isHovered && !isActive ? "block md:hidden" : "block",
                      "truncate"
                    )}>{displayLabel}</span>
                    </button>
                {isActive && (
                        <motion.div
                            layoutId="active-tab"
                        className="absolute inset-0 bg-card/30 rounded-md border border-border"
                            initial={false}
                        transition={{ type: "spring", duration: 1, bounce: 0.05 }}
                        style={{ opacity: 1 }}
                    />
                )}
                {isHovered && !isActive && (
                    <motion.div
                        className="absolute inset-0 bg-card/20 rounded-md border-transparent"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.7, ease: "easeInOut" }}
                        />
                    )}
                </div>
        );
    });

    // 避免在每次渲染时重新创建onClick函数
    const getTabClickHandler = useCallback((tabId: string) => {
        return () => setActiveTab(tabId);
    }, [setActiveTab]);

    return (
        <div className={cn("flex mb-1 gap-1", className)}>
            {tabs.map((tab) => (
                <TabButton
                    key={tab.id}
                    tab={tab}
                    isActive={activeTab === tab.id}
                    onClick={getTabClickHandler(tab.id)}
                />
            ))}
        </div>
    )
} 