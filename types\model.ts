import { GenerationType } from "./video";


// 模型默认配置
export interface ModelDefaultConfig {
    max_steps: number;
    min_steps: number;
    default_steps: number;
    allow_seed_input: boolean;
    supported_ratios: string[];
    max_guidance_scale: number;
    min_guidance_scale: number;
    supported_durations: string[];
    allow_negative_prompt: boolean;
    allow_reference_image: boolean;
    supported_definitions: string[];
    default_guidance_scale: number;
    start_to_end?: boolean; // 是否为首尾帧模型
}

export interface Model {
    id: string;
    name: string;
    model_type: GenerationType; // 后端返回的是model_type而不是type，且是大写
    description: string | null;
    cover_img: string | null; // 后端返回的是cover_img而不是coverImage
    size: string;
    user_id: string | null;
    source: string | null;
    storage_path: string | null;
    is_public: boolean;
    nsfw_level: number;
    created_at: string;
    updated_at: string;
    metadata: any | null;
    supported_features: string[];
    default_config: ModelDefaultConfig;
    type?: string; // 兼容前端现有代码
    tags?: string[];
    cover_video?: string;
    price?: number; // 模型价格（积分）
    weight?: number; // 模型权重（用于排序）
    group?: string; // 模型分组
}

// 效果类型定义
export interface Effect {
    id: string
    name: string
    desc?: string
    cover_img?: string
    created_at?: string
    trigger_words?: string[]
}