import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { UploadFormState, UploadMetadata, GenerationType, VideoSource } from '@/types/upload';

export const useUploadForm = () => {
    const router = useRouter();
    const [formState, setFormState] = useState<UploadFormState>({
        title: '',
        description: '',
        tags: [],
        prompt: '',
        negativePrompt: '',
        guidanceScale: '',
        steps: '',
        seed: '',
        generationType: 'txt2video',
        modelResources: '',
        selectedTools: [],
        videoSource: 'local',
        isPublishing: false,
    });

    // 添加标签错误状态
    const [tagError, setTagError] = useState<string | null>(null);

    // 处理表单字段变化
    const handleInputChange = (field: keyof UploadFormState, value: string) => {
        setFormState(prev => ({ ...prev, [field]: value }));
    };

    // 处理生成类型变化
    const handleGenerationTypeChange = (value: GenerationType) => {
        setFormState(prev => ({ ...prev, generationType: value }));
    };

    // 处理视频来源变化
    const handleVideoSourceChange = (value: VideoSource) => {
        setFormState(prev => ({ ...prev, videoSource: value }));
    };

    // 处理标签添加
    const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === "Enter" && e.currentTarget.value) {
            const newTag = e.currentTarget.value.trim();

            // 清除之前的错误
            setTagError(null);

            // 检查是否为空标签
            if (!newTag) {
                setTagError("标签不能为空");
                return;
            }

            // 检查标签长度
            if (newTag.length > 20) {
                setTagError("标签长度不能超过20个字符");
                return;
            }

            // 检查是否已存在相同标签
            if (formState.tags.includes(newTag)) {
                setTagError("该标签已存在");
                return;
            }

            // 限制最多10个标签
            if (formState.tags.length >= 20) {
                setTagError("最多只能添加20个标签");
                return;
            }

            setFormState(prev => ({
                ...prev,
                tags: [...prev.tags, newTag]
            }));

            // 清空输入框
            e.currentTarget.value = "";
            e.preventDefault(); // 防止表单提交
        }
    };

    // 处理标签移除
    const removeTag = (tagToRemove: string) => {
        setFormState(prev => ({
            ...prev,
            tags: prev.tags.filter(tag => tag !== tagToRemove)
        }));
    };

    // 处理工具选择
    const toggleTool = (tool: string) => {
        setFormState(prev => ({
            ...prev,
            selectedTools: prev.selectedTools.includes(tool)
                ? prev.selectedTools.filter(t => t !== tool)
                : [...prev.selectedTools, tool]
        }));
    };

    // 发布处理
    const handlePublish = async (uploadComplete: boolean, uploadVideo: (metadata: UploadMetadata) => Promise<void>) => {
        setFormState(prev => ({ ...prev, isPublishing: true }));

        try {
            // 如果视频已上传完成，直接发布
            if (uploadComplete) {
                // 发布逻辑，例如发送标题、描述等元数据到服务器
                await publishPost();
            } else {
                // 如果视频尚未上传，先上传再发布
                const metadata: UploadMetadata = {
                    title: formState.title,
                    description: formState.description,
                    tags: formState.tags,
                    prompt: formState.prompt,
                    negativePrompt: formState.negativePrompt,
                    guidanceScale: formState.guidanceScale,
                    steps: formState.steps,
                    seed: formState.seed,
                    generationType: formState.generationType,
                    modelResources: formState.modelResources,
                    selectedTools: formState.selectedTools,
                    videoSource: formState.videoSource
                };

                await uploadVideo(metadata);
                await publishPost();
            }
        } catch (error) {
            console.error('发布失败:', error);
            // 错误处理可以在调用此函数的组件中进行
        } finally {
            setFormState(prev => ({ ...prev, isPublishing: false }));
        }
    };

    // 发布帖子
    const publishPost = async () => {
        // 模拟发布请求
        await new Promise((resolve) => setTimeout(resolve, 1000));
        router.push("/"); // 发布成功后重定向到首页
    };

    return {
        formState,
        tagError,
        handleInputChange,
        handleGenerationTypeChange,
        handleVideoSourceChange,
        handleAddTag,
        removeTag,
        toggleTool,
        handlePublish
    };
}; 