"use client"

import { useEffect, useRef, useState, useMemo, useCallback } from "react"
import type { PostItem } from "@/types/posts"
import { ContentCard } from "./content-card"
import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface ContentGridProps {
  items: PostItem[]
  isLoading?: boolean
}

// 常量定义 - 使用const对象集中管理
const GRID_CONFIG = {
  CARD_WIDTH: 280, // 固定卡片宽度
  CARD_GAP: 16, // 卡片间距
  CONTENT_HEIGHT: 76, // 图片下方内容高度
  BREAKPOINTS: {
    MOBILE: 700, // 移动设备断点
    TABLET: 768, // 平板断点
    DESKTOP: 1536, // 桌面断点
  },
  COLUMNS: {
    MOBILE: 1,
    TABLET: 4,
    DESKTOP: 6,
  }
} as const

// 缓存高度计算结果，避免重复计算
const heightCache = new Map<string, number>()

// 计算卡片估计高度的函数
const getEstimatedHeight = (aspectRatio: string = "9:16"): number => {
  // 检查缓存中是否已有计算结果
  const cacheKey = `height-${aspectRatio}`
  if (heightCache.has(cacheKey)) {
    return heightCache.get(cacheKey)!
  }

  // 计算高度
  let height: number
  switch (aspectRatio) {
    case "16:9":
      height = (GRID_CONFIG.CARD_WIDTH * 9) / 16 + GRID_CONFIG.CONTENT_HEIGHT
      break
    case "9:16":
      height = (GRID_CONFIG.CARD_WIDTH * 16) / 9 + GRID_CONFIG.CONTENT_HEIGHT
      break
    case "1:1":
      height = GRID_CONFIG.CARD_WIDTH + GRID_CONFIG.CONTENT_HEIGHT
      break
    default:
      height = GRID_CONFIG.CARD_WIDTH + GRID_CONFIG.CONTENT_HEIGHT // 默认为正方形
  }

  // 缓存计算结果
  heightCache.set(cacheKey, height)
  return height
}

export function ContentGrid({ items, isLoading }: ContentGridProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [columns, setColumns] = useState<number>(GRID_CONFIG.COLUMNS.DESKTOP)
  const prevItemsLength = useRef(0)
  const prevColumnsCount = useRef(columns)

  // 计算最合适的列数
  const updateColumns = useCallback(() => {
    if (!containerRef.current) return

    const containerWidth = containerRef.current.offsetWidth

    // 根据容器宽度设置列数
    if (containerWidth >= GRID_CONFIG.BREAKPOINTS.DESKTOP) {
      setColumns(GRID_CONFIG.COLUMNS.DESKTOP)
    } else if (containerWidth >= GRID_CONFIG.BREAKPOINTS.TABLET) {
      setColumns(GRID_CONFIG.COLUMNS.TABLET)
    } else {
      setColumns(GRID_CONFIG.COLUMNS.MOBILE)
    }
  }, [])

  // 监听容器尺寸变化
  useEffect(() => {
    // 初始更新
    updateColumns()

    // 使用ResizeObserver监听尺寸变化
    const resizeObserver = new ResizeObserver(updateColumns)

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => resizeObserver.disconnect()
  }, [updateColumns])

  // 优化的瀑布流布局计算 - 使用防抖和更高效的算法
  const columnItems = useMemo(() => {
    // 如果列数或项目数量没有变化，不重新计算
    const currentItemsLength = items.length

    // 只有当列数变化或项目数量增加时才重新计算
    // 添加额外条件：如果项目数量减少超过一定比例，也需要重新计算
    const itemsDecreaseThreshold = 0.2 // 20%的减少阈值
    const significantDecrease =
      currentItemsLength > 0 &&
      prevItemsLength.current > 0 &&
      (prevItemsLength.current - currentItemsLength) / prevItemsLength.current > itemsDecreaseThreshold

    if (columns === prevColumnsCount.current &&
      currentItemsLength === prevItemsLength.current &&
      currentItemsLength > 0 &&
      !significantDecrease) {
      return null // 返回null表示使用之前的计算结果
    }

    // 更新引用值
    prevColumnsCount.current = columns
    prevItemsLength.current = currentItemsLength

    // 如果没有项目，返回空列
    if (currentItemsLength === 0) {
      return Array.from({ length: columns }, () => [])
    }

    // 创建列数组和高度跟踪数组
    const cols: PostItem[][] = Array.from({ length: columns }, () => [])
    const colHeights = new Array(columns).fill(0)

    // 使用更高效的算法分配项目
    // 预先计算所有项目的高度，避免重复计算
    const itemHeights = items.map(item => getEstimatedHeight(item.inputParams?.ratio))

    // 分配项目到最短的列
    items.forEach((item, index) => {
      // 使用预计算的高度
      const estimatedHeight = itemHeights[index]

      // 使用Math.min和indexOf的组合比展开操作符更高效
      let minHeight = colHeights[0]
      let shortestColIndex = 0

      for (let i = 1; i < colHeights.length; i++) {
        if (colHeights[i] < minHeight) {
          minHeight = colHeights[i]
          shortestColIndex = i
        }
      }

      // 将项目添加到最短列
      cols[shortestColIndex].push(item)

      // 更新列高度
      colHeights[shortestColIndex] += estimatedHeight + GRID_CONFIG.CARD_GAP
    })

    return cols
  }, [items, columns])

  // 缓存列布局
  const cachedColumnItems = useRef<PostItem[][]>([])

  // 更新缓存的列布局
  if (columnItems !== null) {
    cachedColumnItems.current = columnItems
  }

  // 动态生成列数类名
  const gridColumnClass = cn(
    "grid gap-2 sm:gap-4",
    columns === 1 && "grid-cols-1",
    columns === 2 && "grid-cols-2",
    columns === 4 && "grid-cols-4",
    columns === 6 && "grid-cols-6",
  )

  return (
    <div className="space-y-2 px-2 sm:px-4 pb-4">
      <div ref={containerRef} className="w-full">
        <div className={gridColumnClass}>
          {cachedColumnItems.current.map((column, columnIndex) => (
            <div
              key={columnIndex}
              className="space-y-2 sm:space-y-4"
              // 使用content-visibility优化渲染性能
              style={{
                contentVisibility: 'auto',
                containIntrinsicSize: '0 500px'
              }}
            >
              {column.map((item) => (
                <ContentCard
                  item={item}
                  key={item.id || `item-${item.taskId}-${columnIndex}`}
                />
              ))}
            </div>
          ))}
        </div>
      </div>

      {isLoading && (
        <div className="flex justify-center py-8">
          <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
        </div>
      )}
    </div>
  )
}

