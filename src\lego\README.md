# Lego 图片生成模块

## 功能简介

Lego是一个提供AI图片生成服务的模块，类似于视频生成模块(generation)，但专注于图片的创建。该模块提供完整的图片生成工作流，包括创建任务、获取任务状态、取消任务等功能。

## 主要API

### 用户相关API

#### 1. 创建图片生成任务

- **路径**: `POST /lego/gen-pic`
- **权限**: 需要用户登录
- **功能**: 创建一个新的图片生成任务
- **请求体**:
  ```json
  {
    "model_id": "model123", // 可选
    "effect_name": "effect1", // 可选
    "prompt": "一个美丽的海滩日落", // 必填
    "negative_prompt": "模糊，变形", // 可选
    "guidance_scale": 7.5, // 可选
    "steps": 50, // 可选
    "seed": "12345", // 可选
    "resolution": "medium", // 可选，默认为low
    "samples": 1, // 可选，生成的图片数量
    "style": "写实", // 可选
    "refer_img_url": "https://example.com/reference.jpg" // 可选，参考图片URL
  }
  ```
- **返回**:
  ```json
  {
    "task_id": "550e8400-e29b-41d4-a716-************",
    "status": "pending",
    "estimated_wait_time": 60
  }
  ```

#### 2. 增强图片生成提示词

- **路径**: `POST /lego/enhance-prompt/pic`
- **权限**: 需要用户登录
- **功能**: 将用户简单的描述转换为更详细、适合AI图片生成的提示词
- **请求体**:
  ```json
  {
    "prompt": "海滩日落"
  }
  ```
- **返回**:
  ```json
  {
    "enhanced_prompt": "A breathtaking sunset over a pristine beach, with golden rays reflecting off gentle waves, silhouettes of palm trees swaying in the warm breeze, and rich orange and purple hues painting the sky"
  }
  ```

#### 3. 获取用户图片生成任务列表

- **路径**: `GET /lego/user-tasks`
- **权限**: 需要用户登录
- **功能**: 获取当前用户的所有图片生成任务
- **查询参数**:
  - `status`: 可选，根据任务状态筛选
  - `limit`: 可选，分页大小，默认10
  - `offset`: 可选，分页偏移，默认0
- **返回**:
  ```json
  {
    "tasks": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "status": "completed",
        "progress": 100,
        "results": [
          {
            "url": "https://example.com/image1.png",
            "resolution": "medium",
            "seed": "12345"
          }
        ],
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:05:00Z",
        "completed_at": "2023-01-01T00:05:00Z"
      }
    ],
    "total": 1
  }
  ```

#### 4. 获取任务详情

- **路径**: `GET /lego/task/:taskId`
- **权限**: 需要用户登录
- **功能**: 获取指定图片生成任务的详细信息
- **返回**: 与上述任务列表中的单个任务结构相同

#### 5. 取消任务

- **路径**: `POST /lego/task/cancel`
- **权限**: 需要用户登录
- **功能**: 取消一个尚未完成的图片生成任务
- **请求体**:
  ```json
  {
    "task_id": "550e8400-e29b-41d4-a716-************"
  }
  ```
- **返回**: 取消后的任务详情

#### 6. 获取任务队列信息

- **路径**: `GET /lego/task/queue-info/:taskId`
- **权限**: 需要用户登录
- **功能**: 获取指定任务在队列中的位置和预计等待时间
- **返回**:
  ```json
  {
    "position": 3,
    "total_tasks": 10,
    "estimated_wait_time": 90,
    "task_id": "550e8400-e29b-41d4-a716-************",
    "message": "排队中，前面还有2个任务"
  }
  ```

### 服务端API

以下API供图片生成微服务调用，需要API密钥验证：

#### 1. 获取下一个待处理任务

- **路径**: `POST /lego/gen_pic_task/pop`
- **功能**: 获取并标记下一个待处理的图片生成任务
- **返回**: 任务详情或null（无任务时）

#### 2. 完成图片生成任务

- **路径**: `POST /lego/gen_pic_task/finish`
- **功能**: 标记一个任务为已完成，并提供生成结果
- **请求体**:
  ```json
  {
    "task_id": "550e8400-e29b-41d4-a716-************",
    "results": [
      {
        "url": "https://example.com/image1.png",
        "resolution": "medium",
        "seed": "12345"
      }
    ],
    "is_success": true
  }
  ```
- **返回**: 更新后的任务详情

#### 3. 更新任务进度

- **路径**: `POST /lego/gen_pic_task/tick-progress`
- **功能**: 更新任务的进度百分比
- **请求体**:
  ```json
  {
    "task_id": "550e8400-e29b-41d4-a716-************",
    "progress": 50,
    "message": "正在生成图片..."
  }
  ```
- **返回**: 更新成功与否的布尔值

## 积分消费

- 低分辨率（512x512）图片生成：2积分
- 中等分辨率（1024x1024）图片生成：5积分
- 高分辨率（2048x2048）图片生成：10积分

## 数据库表结构

### pic_gen_tasks表

| 字段          | 类型      | 说明        |
| ------------- | --------- | ----------- |
| id            | uuid      | 主键        |
| user_id       | uuid      | 用户ID      |
| status        | text      | 任务状态    |
| progress      | integer   | 进度(0-100) |
| input_params  | jsonb     | 输入参数    |
| results       | jsonb     | 生成结果    |
| error_message | text      | 错误信息    |
| created_at    | timestamp | 创建时间    |
| updated_at    | timestamp | 更新时间    |
| completed_at  | timestamp | 完成时间    |
| priority      | integer   | 优先级      |
