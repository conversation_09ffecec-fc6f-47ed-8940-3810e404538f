import { Suspense } from 'react';
import BlogPagination from './components/BlogPagination';
import { BlogPostCard } from '@/components/content/BlogPostCard';
import type { Metadata } from 'next';
import { getBlogPosts, getBlogPostsCount } from '@/lib/blog';

// 为页面提供元数据
export const metadata: Metadata = {
    title: 'Blog | ReelMind',
    description: 'Discover cutting-edge AI video creation techniques, professional guides for AI Movie & MV production, and expert tips on AI model training. Empowering creators with advanced tutorials and industry insights to master next-generation video production.',
};

// 分析搜索参数，设置默认值
export default async function BlogPage({
    searchParams,
}: {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
    const { page: pageParam } = await searchParams;
    // 获取当前页码，默认为第1页
    const page = typeof pageParam === 'string'
        ? parseInt(pageParam)
        : 1;

    // 每页显示数量，默认21篇
    const pageSize = 21;

    // 从API获取当前页数据
    const [blogData, total] = await Promise.all([
        getBlogPosts(page, pageSize),
        getBlogPostsCount()
    ]);

    // 使用获取到的博客列表数据
    const posts = blogData.posts;

    return (
        <div className="relative w-full h-full">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
                <div className="my-9 text-center">
                    <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-4 text-gray-900 dark:text-white">Blog</h1>
                </div>

                <Suspense fallback={<BlogLoadingSkeleton />}>
                    {/* 博客列表 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
                        {posts.map((post) => (
                            <div key={post.id} className="animate-fadeIn">
                                <BlogPostCard post={post} />
                            </div>
                        ))}
                    </div>

                    {/* 没有文章时显示提示 */}
                    {(!posts || posts.length === 0) && (
                        <div className="text-center py-20">
                            <p className="text-gray-500 dark:text-gray-400">No blog posts found</p>
                        </div>
                    )}

                    {/* 分页导航 */}
                    <BlogPagination
                        currentPage={page}
                        totalPages={Math.ceil(total / pageSize)}
                        totalItems={total}
                    />
                </Suspense>
            </div>
        </div>
    );
}

// 错误状态边界组件
export function ErrorBoundary({
    error,
    reset
}: {
    error: Error & { digest?: string };
    reset: () => void;
}) {
    return (
        <div className="max-w-lg mx-auto text-center py-16 px-8 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-red-100 dark:bg-red-900/20 mb-6">
                <svg className="w-10 h-10 text-red-600 dark:text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <h3 className="text-xl font-medium mb-2">Error loading posts</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">{error.message}</p>
            <button
                onClick={reset}
                className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-full hover:bg-primary-700 transition-colors"
            >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Try Again
            </button>
        </div>
    );
}

// 加载骨架屏
function BlogLoadingSkeleton() {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
            {[...Array(6)].map((_, i) => (
                <div key={i} className="overflow-hidden rounded-xl border border-gray-100 bg-white shadow dark:bg-gray-900 dark:border-gray-800">
                    <div className="h-48 bg-gray-200 dark:bg-gray-800 animate-pulse"></div>
                    <div className="p-6">
                        <div className="w-24 h-4 bg-gray-200 dark:bg-gray-800 rounded mb-4 animate-pulse"></div>
                        <div className="h-6 bg-gray-200 dark:bg-gray-800 rounded-md mb-3 animate-pulse"></div>
                        <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded mb-2 animate-pulse"></div>
                        <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded mb-2 animate-pulse"></div>
                        <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-2/3 animate-pulse"></div>
                    </div>
                </div>
            ))}
        </div>
    );
}