# lora模型训练

后端模块：train

## 前端与后端对接

1. 后端提供接口，向前端返回基础模型列表：GET，/train/base_models，展示用户可以用来训练lora的基础模型（model_type=CHECKPOINT)；
2. 用户在网页上传视频（1gb上限），设置触发词
3. 前端储存用户视频到oss，获取视频链接3.前端向后端发起接口请求计算价格：POST，/train/price，{ videos, settings: {xxx}}。
4. 前端向后端发起接口请求，开始训练：POST，/train/start，{ videos, settings }，把用户id、视频链接、触发词入库train-lora-tasks表中。

## 后端与训练服务对接

1. 训练任务根据用户的会员等级的高低和创建时间的先后顺序，有优先级排序；
2. 后端提供接口/train/pop_task，训练服务会轮询该接口，获取下一个要执行的训练任务。
3. 后端提供接口/train/finish，训练服务在完成一个模型的训练任务后，会调用此接口，给后端返回相应的结果，后端将模型存储的oss地址等参数存入数据库lora-models
