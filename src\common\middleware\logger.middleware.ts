import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CustomLogger } from '../services/logger.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
    private middlewareLogger: CustomLogger;

    constructor(private readonly logger: CustomLogger) {
        // 创建一个专用于中间件的logger实例
        this.middlewareLogger = this.logger.createLoggerWithContext('LoggerMiddleware');
    }

    use(req: Request, res: Response, next: NextFunction) {
        const middlewareLogger = this.middlewareLogger;
        const requestId = uuidv4();
        req['requestId'] = requestId;
        const startTime = Date.now();

        const requestLog = {
            requestId,
            timestamp: new Date().toISOString(),
            method: req.method,
            url: req.originalUrl,
            ip: req.ip,
            userAgent: req.get('user-agent') || '',
            body: Buffer.isBuffer(req.body) ? 'Logger省略[Buffer]内容...' : req.body,
            query: req.query,
            params: req.params,
        };

        // 使用中间件专用logger记录请求
        middlewareLogger.log(`[Request] ${JSON.stringify(requestLog)}`);

        // 使用事件监听方式记录响应日志
        res.on('finish', () => {
            const responseTime = Date.now() - startTime;
            const responseLog = {
                requestId,
                timestamp: new Date().toISOString(),
                statusCode: res.statusCode,
                responseTime: `${responseTime}ms`,
                headers: res.getHeaders(),
            };
            middlewareLogger.log(`[Response] ${JSON.stringify(responseLog)}`);
        });

        next();
    }
} 