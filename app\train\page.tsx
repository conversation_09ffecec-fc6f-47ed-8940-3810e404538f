"use client"

import { VideoUploadSection } from "./components/video-upload-section"
import { TrainingSettingsSection } from "./components/training-settings-section"
import { PriceDisplaySection } from "./components/price-display-section"
import { TrainRecordsSection } from "./components/train-records-section"

export default function TrainPage() {
    return (
        <div className="h-full bg-black text-white flex flex-col overflow-x-hidden">
            {/* Cyberpunk grid background */}
            <div className="fixed inset-0 bg-[url('/grid-pattern.svg')] bg-center opacity-20 pointer-events-none"></div>

            {/* Cyberpunk grid lines */}
            <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
                {/* 水平网格线 - 更密集 */}
                <div className="absolute inset-0 
                    bg-[linear-gradient(0deg,transparent_14px,#F5EFFF_15px,#F5EFFF_15.5px,transparent_16px)] 
                    bg-[length:100%_30px] opacity-[0.07] grid-line-pulse grid-shift"></div>

                {/* 垂直网格线 - 更密集 */}
                <div className="absolute inset-0 
                    bg-[linear-gradient(90deg,transparent_14px,#F5EFFF_15px,#F5EFFF_15.5px,transparent_16px)] 
                    bg-[length:30px_100%] opacity-[0.07] grid-line-pulse grid-shift" style={{ animationDelay: "2s" }}></div>

                {/* 次要网格线 - 更小的网格 */}
                <div className="absolute inset-0 
                    bg-[linear-gradient(0deg,transparent_6px,#F5EFFF_7px,transparent_8px)] 
                    bg-[length:100%_15px] opacity-[0.04] micro-grid-flicker"></div>
                <div className="absolute inset-0 
                    bg-[linear-gradient(90deg,transparent_6px,#F5EFFF_7px,transparent_8px)] 
                    bg-[length:15px_100%] opacity-[0.04] micro-grid-flicker" style={{ animationDelay: "1.5s" }}></div>

                {/* 极小网格 */}
                <div className="absolute inset-0 
                    bg-[linear-gradient(45deg,transparent_2px,#F5EFFF_3px,transparent_4px)] 
                    bg-[length:8px_8px] opacity-[0.02] grid-shift" style={{ animationDelay: "0.5s", animationDuration: "120s" }}></div>

                {/* 远景透视效果 */}
                <div className="absolute left-1/2 top-0 bottom-0 w-[1px] bg-[#F5EFFF] 
                    shadow-[0_0_10px_rgba(0,255,0,0.5)] perspective-line-flash"></div>
                <div className="absolute inset-x-0 top-1/2 h-[1px] bg-[#F5EFFF] 
                    shadow-[0_0_10px_rgba(0,255,0,0.5)] perspective-line-flash" style={{ animationDelay: "1s" }}></div>

                {/* 辐射线 */}
                <div className="absolute left-1/2 top-1/2 w-[800px] h-[800px] -translate-x-1/2 -translate-y-1/2 
                    bg-[radial-gradient(circle,transparent_150px,#F5EFFF_300px,transparent_350px)] opacity-[0.03] radial-pulse"></div>
            </div>

            {/* 小点状的背景元素 - 更密集 */}
            <div className="fixed inset-0 pointer-events-none z-0">
                <div className="absolute w-full h-full 
                    bg-[radial-gradient(#F5EFFF_0.5px,transparent_0.5px)] 
                    bg-[size:10px_10px] opacity-[0.05] dot-matrix-fade"></div>
            </div>

            {/* Glowing lines top */}
            <div className="fixed top-[70px] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-[#F5EFFF] to-transparent shadow-[0_0_10px_#F5EFFF] z-10"></div>

            {/* Scanlines effect */}
            <div className="scanlines"></div>

            <div className="h-full w-full mx-auto pb-2 px-2 relative z-10 flex-1 flex flex-col">
                {/* Main content with pixel style borders */}
                <div className="h-full flex flex-1 gap-6 min-h-[600px] overflow-y-hidden">
                    {/* 左侧区域 - 80% */}
                    <div className="w-[80%] flex flex-col h-full">
                        {/* 上部分 - 视频上传 */}
                        <div className="flex-auto min-h-[300px] overflow-y-auto">
                            <VideoUploadSection />
                        </div>

                        {/* 分隔线 */}
                        <div className="h-[1px] bg-gradient-to-r from-transparent via-[#F5EFFF]/30 to-transparent mb-6"></div>

                        {/* 下部分 - 设置和价格 */}
                        <div className="flex-1 flex flex-col min-h-[250px]">
                            <div className="flex-grow overflow-y-auto">
                                <TrainingSettingsSection />
                            </div>
                            <div className="mt-4">
                                <PriceDisplaySection />
                            </div>
                        </div>
                    </div>

                    {/* 右侧区域 - 20% - 训练记录 */}
                    <div className="w-[20%] min-w-[240px] h-full overflow-hidden">
                        <TrainRecordsSection />
                    </div>
                </div>

                {/* 简化的品牌标识 */}
                <div className="text-xs text-[#F5EFFF]/40 font-mono text-right mt-1">
                    REELMIND LoRa TRAINING V1.0.0
                </div>
            </div>
        </div>
    )
}

