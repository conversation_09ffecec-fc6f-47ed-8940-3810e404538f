import { useState, useRef, useCallback } from "react";

/**
 * 处理视频控制的自定义Hook
 */
export function useVideoControls() {
    // 视频悬停状态
    const [hoveredVideoId, setHoveredVideoId] = useState<string | null>(null);
    
    // 视频元素引用
    const videoRefs = useRef<Record<string, HTMLVideoElement | null>>({});

    // 设置视频引用
    const setVideoRef = useCallback((videoId: string) => (element: HTMLVideoElement | null) => {
        videoRefs.current[videoId] = element;
    }, []);

    // 处理视频悬停
    const handleVideoHover = useCallback((videoId: string) => {
        setHoveredVideoId(videoId);

        // 优化视频播放逻辑，降低DOM操作频率
        const videoElement = videoRefs.current[videoId];
        if (videoElement && videoElement.paused) {
            videoElement.play().catch(() => {
                // 静默处理错误，避免控制台警告
            });
        }
    }, []);

    // 处理视频离开
    const handleVideoLeave = useCallback((videoId: string) => {
        setHoveredVideoId(null);

        // 优化视频暂停逻辑
        const videoElement = videoRefs.current[videoId];
        if (videoElement && !videoElement.paused) {
            videoElement.pause();
            videoElement.currentTime = 0;
        }
    }, []);

    return {
        hoveredVideoId,
        setVideoRef,
        handleVideoHover,
        handleVideoLeave,
        isHovered: (videoId: string) => hoveredVideoId === videoId,
    };
}
