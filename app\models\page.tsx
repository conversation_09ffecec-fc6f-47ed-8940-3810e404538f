import { ModelSection } from "./components/model-section"
import { EffectSection } from "./components/effect-section"
import { getModels, getEffects } from "./data"
import Link from "next/link"
import { Metadata } from "next"

// 为页面添加元数据，有利于SEO
export const metadata: Metadata = {
    title: "AI Models & Effects for Video Generation | ReelMind",
    description: "Browse our collection of state-of-the-art AI models and effects for video generation. Create stunning videos with our advanced AI technology.",
    keywords: "AI models, video generation, AI effects, video creation, ReelMind",
}

export default async function ModelsPage() {
    // 服务端获取所有数据，有利于SEO
    const [allModels, effects] = await Promise.all([
        getModels(200), // 获取更多模型数据
        getEffects()
    ]);

    return (
        <main className="w-full px-4 py-8">
            <div className="space-y-2 mb-8 max-w-screen-2xl mx-auto">
                <h1 className="text-3xl font-bold tracking-tight">AI Models & Effects</h1>
                <p className="text-muted-foreground">Browse our collection of state-of-the-art AI models and effects for video generation</p>
            </div>

            <div className="mt-8 space-y-12 max-w-screen-2xl mx-auto">
                {/* Featured Models Section */}
                <ModelSection models={allModels} />

                {/* Effects Section */}
                <EffectSection effects={effects} />
            </div>

            {/* 添加模型总数信息，有利于SEO */}
            <div className="mt-16 text-center max-w-screen-2xl mx-auto">
                <p className="text-muted-foreground">
                    Discover our collection of {allModels.length} AI models for video generation.
                    <Link href="/models/all" className="ml-2 hover:underline">
                        View all models
                    </Link>
                </p>
            </div>

            {/* Space to account for mobile navigation bar */}
            <div className="h-16 lg:h-0"></div>
        </main>
    )
}

