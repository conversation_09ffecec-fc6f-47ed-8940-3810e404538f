"use client"

import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON>Circle, ArrowRight } from "lucide-react"
import { useEffect, useState, Suspense } from "react"
import { paymentApi } from "@/lib/api/payment"

// 取消页面内容组件
function CancelPageContent() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [notificationStatus, setNotificationStatus] = useState<"pending" | "success" | "error">("pending")
    const [errorMessage, setErrorMessage] = useState<string | null>(null)

    // 从URL参数中获取支付类型
    const paymentType = searchParams?.get("type") || "membership"

    useEffect(() => {
        const notifyPaymentCancellation = async () => {
            try {
                // 从URL中获取payment_id参数
                const paymentId = searchParams?.get("payment_id") || null

                if (!paymentId) {
                    console.error("Payment cancellation notification failed: payment_id parameter not found")
                    setNotificationStatus("error")
                    setErrorMessage("Payment ID not found, cannot complete cancellation")
                    return
                }

                // 调用后端接口通知支付取消
                await paymentApi.cancelPayment(paymentId)
                console.log("Payment cancellation notification successful:", paymentId)
                setNotificationStatus("success")
            } catch (error) {
                console.error("Payment cancellation notification failed:", error)
                setNotificationStatus("error")
                setErrorMessage("Notification of payment cancellation failed, but you can continue to use the website")
            }
        }

        notifyPaymentCancellation()
    }, [])

    return (
        <div className="bg-card rounded-xl border border-destructive/30 shadow-lg p-8 max-w-md w-full text-center">
            <div className="flex justify-center mb-6">
                <XCircle className="h-16 w-16 text-destructive" />
            </div>
            <h1 className="text-2xl font-bold mb-4">Payment Cancelled</h1>
            <p className="text-muted-foreground mb-8">
                You have cancelled the {paymentType === "credits" ? "credit purchase" : "membership subscription"} process. If you encountered any issues during the payment process, please contact our customer support team.
            </p>

            {notificationStatus === "error" && errorMessage && (
                <div className="mb-6 p-3 bg-destructive/10 border border-destructive/30 rounded-md text-destructive text-sm">
                    {errorMessage}
                </div>
            )}

            <div className="space-y-3">
                <button
                    onClick={() => router.push("/membership")}
                    className="inline-flex items-center bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-md transition-colors w-full justify-center"
                >
                    Back to Purchase Page
                    <ArrowRight className="ml-2 h-4 w-4" />
                </button>
                <button
                    onClick={() => router.push("/")}
                    className="inline-flex items-center bg-muted hover:bg-muted/80 text-muted-foreground px-4 py-2 rounded-md transition-colors w-full justify-center"
                >
                    Back to Home Page
                </button>
            </div>
        </div>
    )
}

// 加载中状态显示
function LoadingState() {
    return (
        <div className="bg-card rounded-xl border border-primary/30 shadow-lg p-8 max-w-md w-full text-center">
            <div className="py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-muted-foreground">Loading...</p>
            </div>
        </div>
    )
}

// 主页面组件
export default function PaymentCancelPage() {
    return (
        <div className="min-h-screen bg-background text-foreground flex flex-col">
            <main className="flex-1 flex items-center justify-center p-6">
                <Suspense fallback={<LoadingState />}>
                    <CancelPageContent />
                </Suspense>
            </main>
            <footer className="text-center py-4 text-muted-foreground text-sm border-t border-border">
                © {new Date().getFullYear()} ReelMind. All rights reserved.
            </footer>
        </div>
    )
} 