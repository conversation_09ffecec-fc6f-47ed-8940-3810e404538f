'use client';

import Link from "next/link"
import { cn } from "@/lib/utils"

interface BlogFeatureCardProps {
    title: string
    description: string
    icon: React.ReactNode
    href: string
    bgClass?: string
    modelCount?: number
    className?: string
}

export function BlogFeatureCard({
    title,
    description,
    icon,
    href,
    bgClass = "bg-indigo-900/90",
    modelCount,
    className
}: BlogFeatureCardProps) {
    return (
        <Link href={href} className={cn("block group", className)}>
            <div className={cn("rounded-xl relative overflow-hidden transition-all duration-300 group-hover:translate-y-[-4px] group-hover:shadow-xl border border-gray-100 dark:border-gray-800", bgClass)}>
                {/* 悬停效果 */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-10 bg-white transition-opacity duration-300"></div>

                {/* 模型数量徽章 */}
                {modelCount && (
                    <div className="absolute top-0 right-0 z-10">
                        <div className="relative">
                            {/* 徽章背景 */}
                            <div className="bg-gradient-to-r from-red-500 to-purple-600 px-4 py-1.5 rounded-bl-lg shadow-lg">
                                {/* 闪光效果 */}
                                <div className="absolute inset-0 overflow-hidden">
                                    <div className="absolute top-0 left-[-100%] w-[50%] h-full bg-gradient-to-r from-transparent via-white/30 to-transparent skew-x-[-20deg] animate-shimmer-slide"></div>
                                </div>

                                {/* 徽章文字 */}
                                <div className="flex items-center justify-center">
                                    <span className="text-white font-bold text-base mr-1">{modelCount}</span>
                                    <span className="text-white/90 text-sm">Video Models</span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* 卡片内容 */}
                <div className="p-6 flex items-start">
                    <div className="text-white">
                        {icon}
                    </div>
                    <div className="ml-4">
                        <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
                        <p className="text-gray-200/90">{description}</p>
                    </div>
                </div>
            </div>
        </Link>
    )
}

export function BlogFeatureSection() {
    return (
        <div className="absolute bottom-0 left-0 right-0 z-50 shadow-[0_-4px_20px_rgba(0,0,0,0.15)]">
            <div className="max-w-screen-2xl mx-auto">
                <div className="bg-gradient-to-r from-indigo-600 to-purple-700 overflow-hidden border border-gray-100 dark:border-gray-800 shadow-lg rounded-t-xl">
                    <div className="flex items-center justify-between p-4 md:p-5">
                        <div className="flex-col items-center space-x-4">
                            <h2 className="text-lg md:text-xl font-bold text-white">Reelmind offers powerful tools to bring your ideas to life.</h2>
                            <p className="text-white/80 text-sm md:text-base">Transform your ideas into stunning visuals with 101 AI models</p>
                        </div>

                        <Link
                            href="/create"
                            className="flex items-center bg-white text-indigo-700 hover:bg-indigo-50 px-4 py-2 md:px-6 md:py-3 rounded-lg font-medium transition-all duration-300 group"
                        >
                            <span className="mr-2">Try Now</span>
                            <span className="group-hover:translate-x-1 transition-transform duration-300">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                            </span>
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    )
}
