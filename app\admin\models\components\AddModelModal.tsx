'use client';

import { useState } from 'react';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FalAiModelSelector } from './FalAiModelSelector';
import { ManualModelForm } from './ManualModelForm';

interface AddModelModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
}

export function AddModelModal({ isOpen, onClose, onSuccess }: AddModelModalProps) {
    const [activeTab, setActiveTab] = useState('fal-ai');

    const handleClose = () => {
        setActiveTab('fal-ai');
        onClose();
    };

    const handleSuccess = () => {
        onSuccess();
        handleClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>Add New Model</DialogTitle>
                </DialogHeader>

                <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="fal-ai">From fal.ai</TabsTrigger>
                        <TabsTrigger value="manual">Manual Entry</TabsTrigger>
                    </TabsList>

                    <TabsContent value="fal-ai" className="space-y-4">
                        <div className="text-sm text-muted-foreground mb-4">
                            Browse and sync the latest models from fal.ai
                        </div>
                        <FalAiModelSelector onSuccess={handleSuccess} />
                    </TabsContent>

                    <TabsContent value="manual" className="space-y-4">
                        <div className="text-sm text-muted-foreground mb-4">
                            Manually create a custom model entry
                        </div>
                        <ManualModelForm onSuccess={handleSuccess} onCancel={handleClose} />
                    </TabsContent>
                </Tabs>
            </DialogContent>
        </Dialog>
    );
}
