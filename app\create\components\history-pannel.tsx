"use client"

import { useState, useEffect, useCallback, memo, useRef } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { useQuery } from "@tanstack/react-query";
import useVideoTasksStore from "@/store/useVideoTasksStore";
import useVideoGeneratorStore from "@/store/useVideoGeneratorStore";
import type { TaskStatus } from "@/types/video-task";
import { HistoryCard } from "./history-card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/contexts/auth-context";
import { PublishDialog } from "./publish-dialog";
import { Button } from "@/components/ui/button";
import { RefreshCcw, Loader2 } from "lucide-react";
import { useTaskActions } from "../hooks/useTaskActions";

// 创建自定义的防抖hook
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(timer);
        };
    }, [value, delay]);

    return debouncedValue;
}

// 记忆化EmptyState组件减少重渲染
const EmptyState = memo(({ status }: { status?: string }) => {
    // 状态映射
    const statusMap: Record<string, string> = {
        pending: "Pending",
        queued: "Queued",
        processing: "Processing",
        completed: "Completed",
        failed: "Failed",
    };

    return (
        <div className="text-center py-12 text-muted-foreground">
            <p>No {statusMap[status || "completed"]} tasks found</p>
        </div>
    );
});
EmptyState.displayName = "EmptyState";

// 记忆化骨架屏组件
const SkeletonLoader = memo(() => (
    <div className="space-y-3">
        {[1, 2, 3, 4, 5, 6].map((item) => (
            <div key={item} className="rounded-lg overflow-hidden">
                <Skeleton className="w-full aspect-video bg-muted/50" />
                <div className="p-4 bg-card">
                    <div className="space-y-2">
                        <Skeleton className="h-4 w-3/4 bg-primary/10" />
                        <Skeleton className="h-3 w-1/4 bg-muted" />
                    </div>
                    <div className="flex justify-between mt-3">
                        <div className="flex space-x-2">
                            <Skeleton className="h-6 w-16 rounded-md bg-primary/10" />
                            <Skeleton className="h-6 w-16 rounded-md bg-primary/10" />
                        </div>
                        <Skeleton className="h-8 w-8 rounded-full bg-primary/10" />
                    </div>
                </div>
            </div>
        ))}
    </div>
));
SkeletonLoader.displayName = "SkeletonLoader";

export function HistoryPanel() {
    const [status, setStatus] = useState<TaskStatus | undefined>(undefined);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const { isAuthenticated } = useAuth();
    const loaderRef = useRef<HTMLDivElement>(null);

    const {
        selectedVideo,
        publishDialogOpen,
        setPublishDialogOpen,
        isDownloading,
        handleVideoClick,
        handleRemix,
        handleDownload,
        handlePublish,
        handleShare
    } = useTaskActions();

    // 使用Zustand store获取和管理任务
    const {
        videoTasks,
        fetchUserTasks,
        loadMoreTasks,
        isLoading,
        hasMore
    } = useVideoTasksStore();

    // 防抖的状态值，避免频繁触发refetch
    const debouncedStatus = useDebounce(status, 300);

    // 使用react-query获取历史任务，并优化refetch策略
    const { refetch, isFetching } = useQuery({
        queryKey: ["userTasks", debouncedStatus, isAuthenticated],
        queryFn: async () => {
            if (isAuthenticated) {
                return await fetchUserTasks(debouncedStatus);
            }
            return [];
        },
        // 减少重复请求和不必要的重渲染
        staleTime: 30000, // 30秒内数据不会被标记为过期
        refetchOnWindowFocus: false, // 窗口聚焦时不自动重新获取
        refetchInterval: false, // 禁用自动轮询
    });

    // 加载更多任务
    const handleLoadMore = useCallback(async () => {
        if (isLoadingMore || !hasMore || !isAuthenticated) return;

        setIsLoadingMore(true);
        try {
            await loadMoreTasks(debouncedStatus);
        } catch (error) {
            console.error('Load error:', error);
        } finally {
            setIsLoadingMore(false);
        }
    }, [isLoadingMore, hasMore, isAuthenticated, loadMoreTasks, debouncedStatus]);

    // 设置Intersection Observer来监测滚动到底部
    useEffect(() => {
        if (!loaderRef.current || !hasMore) return;

        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && hasMore && !isLoadingMore && !isLoading && !isFetching) {
                    handleLoadMore();
                }
            },
            { threshold: 0.1, rootMargin: '200px 0px' }
        );

        observer.observe(loaderRef.current);
        return () => {
            if (loaderRef.current) {
                observer.unobserve(loaderRef.current);
            }
        };
    }, [hasMore, isLoadingMore, isLoading, isFetching, handleLoadMore]);

    // 使用useCallback记忆化刷新函数
    const handleRefresh = useCallback(() => {
        if (!isRefreshing && !isFetching) {
            setIsRefreshing(true);
            refetch().finally(() => {
                // 使用setTimeout模拟防抖，避免频繁点击
                setTimeout(() => {
                    setIsRefreshing(false);
                }, 2000);
            });
        }
    }, [isRefreshing, isFetching, refetch]);

    // 监听任务列表刷新事件
    useEffect(() => {
        const handleRefreshTaskList = () => {
            handleRefresh();
        };

        window.addEventListener('refreshTaskList', handleRefreshTaskList);
        return () => {
            window.removeEventListener('refreshTaskList', handleRefreshTaskList);
        };
    }, [handleRefresh]);

    // 使用useCallback记忆化状态变更处理函数
    const handleStatusChange = useCallback((value: string) => {
        setStatus(value === "all" ? undefined : value as TaskStatus);
    }, []);

    // 结合isLoading和isFetching，避免闪烁
    const isLoadingData = isLoading || isFetching;

    return (
        <div className="h-full flex flex-col w-full md:w-[60%] overflow-y-auto bg-gradient-to-r from-gray-500/10 to-gray-700/10">
            <div className="sticky top-0 z-10 flex justify-start items-center px-4">
                <Select
                    value={status === undefined ? "all" : status}
                    onValueChange={handleStatusChange}
                >
                    <SelectTrigger className="w-[180px] bg-unset py-7">
                        <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="processing">Processing</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                </Select>

                <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleRefresh}
                    disabled={isRefreshing || isLoadingData}
                    className="ml-auto mr-3"
                >
                    <RefreshCcw className={`h-4 w-4 ${isRefreshing || isFetching ? 'animate-spin' : ''}`} />
                </Button>
            </div>

            <div className="px-4 py-2 space-y-2 flex-1 overflow-y-auto">
                {isLoadingData && videoTasks.length === 0 ? (
                    <SkeletonLoader />
                ) : videoTasks.length === 0 ? (
                    <EmptyState status={status} />
                ) : (
                    <>
                        <div className="grid grid-cols-1 gap-3">
                            {videoTasks.map((video) => (
                                <div key={video.id} className="rounded-lg overflow-hidden">
                                    <HistoryCard
                                        video={video}
                                        onClick={() => handleVideoClick(video)}
                                        onPublishClick={handlePublish}
                                        onShareClick={handleShare}
                                        onDownloadClick={handleDownload}
                                        onRemixClick={(videoId) => handleRemix(videoId)}
                                        isDownloading={isDownloading}
                                    />
                                </div>
                            ))}
                        </div>

                        {/* 无限加载指示器 */}
                        {hasMore && (
                            <div
                                ref={loaderRef}
                                className="py-4 flex justify-center items-center"
                            >
                                {isLoadingMore ? (
                                    <div className="flex items-center gap-2">
                                        <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                                    </div>
                                ) : (
                                    <div className="h-8 w-full" />
                                )}
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* 发布对话框 */}
            {selectedVideo && (
                <PublishDialog
                    open={publishDialogOpen}
                    onOpenChange={setPublishDialogOpen}
                    task={selectedVideo}
                />
            )}
        </div>
    );
}

