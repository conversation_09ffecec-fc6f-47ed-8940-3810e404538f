import { Injectable } from '@nestjs/common';
import { ICreditStrategy, ICreditStrategyFactory } from './credit-strategy.interface';

/**
 * 积分策略工厂实现
 * 
 * 负责创建和管理不同类型的积分策略
 * 遵循工厂模式和单例模式设计
 */
@Injectable()
export class CreditStrategyFactory implements ICreditStrategyFactory {
    // 存储已注册的策略
    private readonly strategies: Map<string, ICreditStrategy> = new Map();
    
    /**
     * 获取指定类型的积分策略
     * @param type 策略类型
     * @returns 对应的积分策略实例
     * @throws Error 如果找不到对应类型的策略
     */
    getStrategy(type: string): ICreditStrategy {
        const strategy = this.strategies.get(type);
        if (!strategy) {
            throw new Error(`未找到类型为 ${type} 的积分策略`);
        }
        return strategy;
    }
    
    /**
     * 注册新的积分策略
     * @param strategy 积分策略实例
     */
    registerStrategy(strategy: ICreditStrategy): void {
        this.strategies.set(strategy.type, strategy);
    }
}
