import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { couponApi } from '@/lib/api/coupon';
import { couponManager } from '@/lib/coupon-manager';
import {
    UserCoupon,
    CouponUsageHistory,
    CouponStatusResponse,
    ClaimCouponResponse,
    PriceCalculation,
    CouponStatus
} from '@/app/membership/types/coupon';

interface CouponState {
    // 状态
    coupons: UserCoupon[];
    activeCoupon: UserCoupon | null;
    usageHistory: CouponUsageHistory[];
    isLoading: boolean;
    error: string | null;
    isEligibleForFirstMonth: boolean;
    hasCheckedEligibility: boolean;
    lastChecked: number | null;

    // 操作
    checkCouponStatus: () => Promise<CouponStatusResponse>;
    claimFirstMonthDiscount: () => Promise<ClaimCouponResponse>;
    getUserCoupons: () => Promise<void>;
    getUsageHistory: () => Promise<void>;
    calculatePrice: (originalPrice: number, couponId?: string) => Promise<PriceCalculation>;
    checkFirstMonthEligibility: () => Promise<void>;
    resetCouponState: () => void;
    setError: (error: string | null) => void;
    clearError: () => void;
    forceRefresh: () => Promise<void>;

    // 辅助方法
    getActiveCoupon: () => UserCoupon | null;
    hasValidCoupon: () => boolean;
    isFirstMonthDiscountAvailable: () => boolean;
}

// 缓存刷新间隔（5分钟）
const CACHE_REFRESH_INTERVAL = 5 * 60 * 1000;

const useCouponStore = create<CouponState>()(
    persist(
        (set, get) => ({
            // 初始状态
            coupons: [],
            activeCoupon: null,
            usageHistory: [],
            isLoading: false,
            error: null,
            isEligibleForFirstMonth: false,
            hasCheckedEligibility: false,
            lastChecked: null,

            // 检查优惠券状态
            checkCouponStatus: async () => {
                const state = get();

                // 使用全局管理器检查是否可以执行
                if (!couponManager.canCheckStatus()) {
                    console.log('[CouponStore] Skipping coupon status check - managed by global coupon manager');
                    return {
                        coupon: state.activeCoupon,
                        is_eligible: state.isEligibleForFirstMonth
                    } as CouponStatusResponse;
                }

                try {
                    couponManager.startStatusCheck();
                    console.log('[CouponStore] Checking coupon status');
                    set({ isLoading: true, error: null });
                    const response = await couponApi.getCouponStatus();

                    set({
                        activeCoupon: response.coupon || null,
                        isEligibleForFirstMonth: response.is_eligible,
                        hasCheckedEligibility: true,
                        lastChecked: Date.now(),
                        isLoading: false
                    });

                    return response;
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Failed to check coupon status';
                    set({ error: errorMessage, isLoading: false });
                    throw error;
                } finally {
                    couponManager.endStatusCheck();
                }
            },

            // 领取首月折扣优惠券
            claimFirstMonthDiscount: async () => {
                try {
                    set({ isLoading: true, error: null });
                    const response = await couponApi.claimFirstMonthDiscount();

                    if (response.success && response.coupon) {
                        // Update all relevant state after successful claim
                        const currentCoupons = get().coupons;
                        set({
                            activeCoupon: response.coupon,
                            coupons: [...currentCoupons, response.coupon],
                            isEligibleForFirstMonth: false,
                            hasCheckedEligibility: true,
                            lastChecked: Date.now(),
                            isLoading: false,
                            error: null
                        });

                        // Force refresh to ensure all components see the update
                        setTimeout(() => {
                            get().forceRefresh();
                        }, 100);
                    } else {
                        set({
                            error: response.message || 'Failed to claim coupon',
                            isLoading: false
                        });
                    }

                    return response;
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Failed to claim coupon';
                    set({ error: errorMessage, isLoading: false });
                    throw error;
                }
            },

            // 获取用户优惠券列表
            getUserCoupons: async () => {
                try {
                    set({ isLoading: true, error: null });
                    const coupons = await couponApi.getUserCoupons();

                    // 找到活跃的优惠券
                    const activeCoupon = coupons.find(coupon =>
                        coupon.status === CouponStatus.ACTIVE &&
                        new Date(coupon.expires_at) > new Date()
                    ) || null;

                    set({
                        coupons,
                        activeCoupon,
                        lastChecked: Date.now(),
                        isLoading: false
                    });
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Failed to get user coupons';
                    set({ error: errorMessage, isLoading: false });
                    throw error;
                }
            },

            // 获取使用历史
            getUsageHistory: async () => {
                try {
                    set({ isLoading: true, error: null });
                    const usageHistory = await couponApi.getCouponUsageHistory();
                    set({ usageHistory, isLoading: false });
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Failed to get usage history';
                    set({ error: errorMessage, isLoading: false });
                    throw error;
                }
            },

            // 计算价格
            calculatePrice: async (originalPrice: number, couponId?: string) => {
                try {
                    return await couponApi.calculatePrice(originalPrice, couponId);
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Failed to calculate price';
                    set({ error: errorMessage });
                    throw error;
                }
            },

            // 检查首月折扣资格
            checkFirstMonthEligibility: async () => {
                const state = get();

                // 如果已经检查过资格，跳过
                if (state.hasCheckedEligibility) {
                    console.log('[CouponStore] Skipping eligibility check - already checked');
                    return;
                }

                // 使用全局管理器检查是否可以执行
                if (!couponManager.canCheckEligibility()) {
                    console.log('[CouponStore] Skipping eligibility check - managed by global coupon manager');
                    return;
                }

                try {
                    couponManager.startEligibilityCheck();
                    console.log('[CouponStore] Checking first month eligibility');
                    const response = await couponApi.checkFirstMonthDiscountEligibility();
                    set({
                        isEligibleForFirstMonth: response.is_eligible,
                        hasCheckedEligibility: true,
                        lastChecked: Date.now()
                    });
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Failed to check eligibility';
                    set({ error: errorMessage });
                    throw error;
                } finally {
                    couponManager.endEligibilityCheck();
                }
            },

            // 重置状态
            resetCouponState: () => {
                set({
                    coupons: [],
                    activeCoupon: null,
                    usageHistory: [],
                    isLoading: false,
                    error: null,
                    isEligibleForFirstMonth: false,
                    hasCheckedEligibility: false,
                    lastChecked: null
                });
            },

            // 设置错误
            setError: (error: string | null) => {
                set({ error });
            },

            // 清除错误
            clearError: () => {
                set({ error: null });
            },

            // 强制刷新所有状态
            forceRefresh: async () => {
                try {
                    set({ isLoading: true, error: null });

                    // 并行执行状态检查和资格检查
                    const [statusResponse, eligibilityResponse] = await Promise.all([
                        couponApi.getCouponStatus(),
                        couponApi.checkFirstMonthDiscountEligibility()
                    ]);

                    set({
                        activeCoupon: statusResponse.coupon || null,
                        isEligibleForFirstMonth: eligibilityResponse.is_eligible,
                        hasCheckedEligibility: true,
                        lastChecked: Date.now(),
                        isLoading: false,
                        error: null
                    });
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Failed to refresh coupon state';
                    set({ error: errorMessage, isLoading: false });
                    throw error;
                }
            },

            // 获取活跃优惠券
            getActiveCoupon: () => {
                const { activeCoupon } = get();
                if (!activeCoupon) return null;

                // 检查是否过期
                if (new Date(activeCoupon.expires_at) <= new Date()) {
                    set({ activeCoupon: null });
                    return null;
                }

                return activeCoupon;
            },

            // 检查是否有有效优惠券
            hasValidCoupon: () => {
                return get().getActiveCoupon() !== null;
            },

            // 检查是否可以获得首月折扣
            isFirstMonthDiscountAvailable: () => {
                const { isEligibleForFirstMonth, hasCheckedEligibility, lastChecked } = get();

                // 如果缓存过期，需要重新检查
                if (!hasCheckedEligibility ||
                    !lastChecked ||
                    Date.now() - lastChecked > CACHE_REFRESH_INTERVAL) {
                    return false;
                }

                return isEligibleForFirstMonth;
            }
        }),
        {
            name: 'coupon-storage',
            // 仅持久化部分安全的字段
            partialize: (state) => ({
                lastChecked: state.lastChecked,
                hasCheckedEligibility: state.hasCheckedEligibility,
                isEligibleForFirstMonth: state.isEligibleForFirstMonth
            }),
        }
    )
);

export default useCouponStore;
