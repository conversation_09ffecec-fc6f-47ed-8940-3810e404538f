import { CreditTransactionStatus, CreditTransactionType } from '../../credits/constant';

/**
 * 积分交易记录接口
 */
export interface CreditTransaction {
    id: string;
    user_id: string;
    type: CreditTransactionType;
    amount: number;
    description: string;
    reference_id?: string;
    payment_id?: string;
    status: CreditTransactionStatus;
    created_at: Date;
    updated_at: Date;
}

/**
 * 积分消费结果接口
 */
export interface CreditConsumptionResult {
    transaction: CreditTransaction;
    remainingBalance: number;
    transactionId: string; // 交易ID
}

/**
 * 积分退款结果接口
 */
export interface CreditRefundResult {
    transaction: CreditTransaction;
    newBalance: number;
    transactionId: string; // 交易ID
}
