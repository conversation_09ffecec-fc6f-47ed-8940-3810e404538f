# Admin Model Management System

这是一个用于管理视频生成模型的管理员界面，支持浏览、编辑、添加和删除模型。

## 功能特性

### 1. 模型浏览
- 显示所有数据库中的模型
- 支持分页浏览（每页20个模型）
- 支持按名称和描述搜索
- 显示模型统计信息（总数、公开模型数、fal.ai模型数、自定义模型数）

### 2. 模型编辑
- 编辑模型基本信息（名称、描述、分类、分组）
- 设置模型媒体（封面图片、封面视频）
- 配置模型参数（价格、权重、NSFW等级）
- 切换模型公开/私有状态

### 3. 新增模型
支持两种方式添加模型：

#### 从fal.ai同步
- 获取最新的fal.ai模型列表
- 支持按分类筛选（image-to-video、text-to-video、video-to-video）
- 支持搜索模型
- 一键同步模型到数据库

#### 手动创建
- 手动输入模型信息
- 自定义模型配置
- 支持自定义来源和存储路径

### 4. 模型管理
- 删除不需要的模型
- 批量操作支持
- 实时数据更新

## 技术实现

### 前端技术栈
- **Next.js 15** - React框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **shadcn/ui** - UI组件库
- **React Query** - 数据获取和缓存
- **React Hook Form** - 表单管理
- **Zod** - 数据验证

### 后端API
- **NestJS** - Node.js框架
- **Supabase** - 数据库和认证
- **PostgreSQL** - 关系型数据库

### 数据库结构
模型表包含以下字段：
- `id` - 模型唯一标识
- `name` - 模型名称
- `description` - 模型描述
- `source` - 模型来源（fal.ai、custom等）
- `model_type` - 模型类型（image-to-video、text-to-video、video-to-video）
- `storage_path` - 模型存储路径
- `price` - 模型价格（积分）
- `weight` - 模型权重（用于排序）
- `cover_img` - 封面图片链接
- `cover_video` - 封面视频链接
- `is_public` - 是否公开
- `nsfw_level` - NSFW等级
- `category` - 模型分类
- `group` - 模型分组
- `metadata` - 元数据（JSON）
- `default_config` - 默认配置（JSON）
- `supported_features` - 支持的功能（JSON数组）

## 使用说明

### 访问管理界面
1. 确保具有管理员权限
2. 访问 `/admin/models` 页面

### 浏览模型
1. 页面会自动加载所有模型
2. 使用搜索框按名称或描述筛选
3. 使用分页导航浏览更多模型

### 编辑模型
1. 点击模型行的"更多"按钮（三个点）
2. 选择"Edit"
3. 在弹出的编辑窗口中修改信息
4. 点击"Save Changes"保存

### 添加新模型
1. 点击"Add Model"按钮
2. 选择添加方式：
   - **From fal.ai**: 从fal.ai同步最新模型
   - **Manual Entry**: 手动创建自定义模型
3. 填写必要信息并保存

### 删除模型
1. 点击模型行的"更多"按钮
2. 选择"Delete"
3. 确认删除操作

## 部署说明

### 数据库迁移
在使用前需要运行数据库迁移脚本添加必要字段：

```sql
-- 添加price和weight字段
ALTER TABLE public.models ADD COLUMN IF NOT EXISTS price DECIMAL(10, 2) DEFAULT 0;
ALTER TABLE public.models ADD COLUMN IF NOT EXISTS weight DECIMAL(5, 2) DEFAULT 1.0;
ALTER TABLE public.models ADD COLUMN IF NOT EXISTS cover_video TEXT;

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_models_price ON public.models(price);
CREATE INDEX IF NOT EXISTS idx_models_weight ON public.models(weight);
CREATE INDEX IF NOT EXISTS idx_models_category ON public.models(category);
CREATE INDEX IF NOT EXISTS idx_models_group ON public.models("group");
```

### 环境配置
确保以下环境变量已配置：
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase项目URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase匿名密钥
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase服务角色密钥（后端使用）

### 权限配置
确保管理员用户具有以下权限：
- 读取models表
- 写入models表
- 删除models表记录
- 访问fal.ai API

## 故障排除

### 常见问题

1. **页面无法加载**
   - 检查网络连接
   - 确认Supabase配置正确
   - 检查用户权限

2. **无法编辑模型**
   - 确认具有管理员权限
   - 检查后端API是否正常运行
   - 查看浏览器控制台错误信息

3. **fal.ai模型同步失败**
   - 检查网络连接到fal.ai
   - 确认API限制和配额
   - 查看错误日志

4. **数据库字段缺失**
   - 运行数据库迁移脚本
   - 检查数据库表结构
   - 确认字段权限

### 日志查看
- 前端错误：浏览器开发者工具控制台
- 后端错误：服务器日志
- 数据库错误：Supabase仪表板日志

## 开发说明

### 本地开发
1. 克隆项目
2. 安装依赖：`pnpm install`
3. 配置环境变量
4. 启动开发服务器：`pnpm dev`
5. 访问 `http://localhost:3000/admin/models`

### 代码结构
```
app/admin/models/
├── page.tsx                    # 主页面组件
├── layout.tsx                  # 布局组件
├── components/
│   ├── ModelTable.tsx          # 模型表格组件
│   ├── ModelEditModal.tsx      # 模型编辑弹窗
│   ├── AddModelModal.tsx       # 添加模型弹窗
│   ├── FalAiModelSelector.tsx  # fal.ai模型选择器
│   └── ManualModelForm.tsx     # 手动创建模型表单
└── README.md                   # 说明文档

lib/api/
└── admin-model.ts              # Admin模型API接口

src/models/
├── models.admin.controller.ts  # 后端管理员控制器
├── models.service.ts           # 后端模型服务
└── dto/model.dto.ts           # 数据传输对象
```

### 扩展功能
可以考虑添加的功能：
- 批量导入/导出模型
- 模型使用统计
- 模型版本管理
- 模型性能监控
- 自动化测试
- 模型备份和恢复
