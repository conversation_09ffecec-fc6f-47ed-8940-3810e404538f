"use client"

import { useState } from "react"
import { Check, Rocket, Trophy, Crown, OctagonX } from "lucide-react"
import { MembershipPlan, MembershipPlanName, UserMembership } from "../types"
import { paymentApi, CreateMembershipPaymentRequest } from "@/lib/api/payment"
import { CouponPriceDisplay } from "./coupon-price-display"
import useCouponStore from "@/store/useCouponStore"

interface PricingCardProps {
  plan: MembershipPlan
  billingCycle: "monthly" | "yearly"
  userMembership?: UserMembership
  isLoadingUserMembership: boolean
}

export function PricingCard({ plan, billingCycle, userMembership, isLoadingUserMembership }: PricingCardProps) {
  const [isProcessing, setIsProcessing] = useState(false)

  // 获取优惠券状态
  const { getActiveCoupon } = useCouponStore()

  // 计算价格
  const price = billingCycle === "yearly" ? plan.yearly_price / 12 : plan.monthly_price;

  // 计算折扣
  const discount = billingCycle === "yearly" && plan.monthly_price > 0
    ? Math.round(((plan.monthly_price - plan.yearly_price) / plan.monthly_price) * 100)
    : 0;

  // 判断是否为当前会员等级
  const isCurrentPlan = (userMembership?.plan_id === plan.id) && userMembership?.is_active;

  const handleSubscribe = async () => {
    try {
      setIsProcessing(true);

      const priceId = billingCycle === "yearly" ? plan.stripe_price_id_year : plan.stripe_price_id_month;

      // 获取当前活跃的优惠券
      const activeCoupon = getActiveCoupon();

      const paymentRequest: CreateMembershipPaymentRequest = {
        price_id: priceId,
        billing_cycle: billingCycle,
      };

      // 如果用户有活跃的优惠券且不是UNLIMITED计划，添加coupon_id
      // UNLIMITED计划不参与任何优惠
      if (activeCoupon && plan.plan_name !== MembershipPlanName.UNLIMITED) {
        paymentRequest.coupon_id = activeCoupon.id;
      }

      // 1.创建支付订单
      const { checkoutUrl } = await paymentApi.createMembershipPayment(paymentRequest);

      // 重定向到Stripe结账页面
      window.location.href = checkoutUrl;
    } catch (err) {
      setIsProcessing(false);
      console.error('支付创建错误:', err);
    }
  };

  // 将特权字符串转换为特权对象数组
  const featureObjects = plan.features.map(text => {
    // 如果特权文本以"无"或"不"开头，则标记为未包含
    const isNegative = text.startsWith('-') || text.startsWith('不');

    // Replace specific text with new wording
    let modifiedText = text.replace(/^[-不]/, '');
    if (modifiedText === "Access to WAN-T2V-1.3B, WAN-I2V-14B-720P") {
      modifiedText = "Fully access to 101 top AI video models";
    }

    return {
      text: modifiedText,
      included: !isNegative
    };
  });

  const renderIcon = () => {
    // 根据会员等级选择图标
    switch (plan.plan_name) {
      case MembershipPlanName.FREE:
        return <Rocket className="h-8 w-8" />
      case MembershipPlanName.PRO:
        return <Trophy className="h-8 w-8" />
      case MembershipPlanName.MAX:
        return <Crown className="h-8 w-8" />
      case MembershipPlanName.UNLIMITED:
        return <Crown className="h-8 w-8 text-amber-500" /> // 使用金色皇冠图标
      default:
        return null
    }
  }

  return (
    <>
      <div className={`relative rounded-2xl overflow-hidden border-2 transition-all duration-300 hover:scale-105 hover:shadow-2xl
        ${plan.plan_name === MembershipPlanName.UNLIMITED
          ? 'border-amber-500/50 bg-gradient-to-br from-amber-50/5 via-card to-amber-100/10 shadow-amber-500/20'
          : plan.is_popular
            ? 'border-blue-500/50 bg-gradient-to-br from-blue-50/5 via-card to-purple-100/10 shadow-blue-500/20'
            : 'border-border/50 bg-gradient-to-br from-card to-muted/20 shadow-lg'}
        shadow-xl backdrop-blur-sm`}>
        {plan.is_popular && (
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm font-bold py-2 text-center shadow-lg">
            🔥 Most Popular Choice
          </div>
        )}

        {plan.plan_name === MembershipPlanName.UNLIMITED && (
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-amber-500 to-orange-500 text-white text-sm font-bold py-2 text-center shadow-lg">
            ⚡ Unlimited Generation • Premium Pricing
          </div>
        )}

        {discount > 0 && (
          <div className="absolute top-4 right-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse">
            Save {discount}%
          </div>
        )}

        <div className={`p-6 ${plan.is_popular || plan.plan_name === MembershipPlanName.UNLIMITED ? 'pt-12' : ''}`}>
          <div className="flex items-center gap-3 mb-4">
            {renderIcon()}
            <h3 className="text-xl font-bold">{plan.plan_name}</h3>
          </div>

          <div className="mb-6">
            <CouponPriceDisplay
              monthlyPrice={plan.monthly_price}
              yearlyPrice={plan.yearly_price}
              billingCycle={billingCycle}
              planName={plan.plan_name}
            />
            {billingCycle === "yearly" && plan.monthly_price > price && (
              <div className="text-sm text-muted-foreground mt-1">
                <span className="line-through">${plan.monthly_price.toFixed(2)}/Monthly</span>
              </div>
            )}
          </div>

          {plan.level > 0 && !isLoadingUserMembership && (
            <button
              type="button"
              onClick={handleSubscribe}
              disabled={isCurrentPlan || isProcessing}
              className={`group relative w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300 mb-6 overflow-hidden
                ${isCurrentPlan
                  ? 'bg-muted/50 text-muted-foreground cursor-not-allowed border border-muted'
                  : isProcessing
                    ? 'bg-gradient-to-r from-blue-500/80 to-purple-500/80 text-white cursor-wait'
                    : plan.plan_name === 'UNLIMITED'
                      ? 'bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                      : plan.is_popular
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                        : 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white shadow-md hover:shadow-lg transform hover:scale-102'
                }`}
            >
              {/* 背景动画效果 */}
              {!isCurrentPlan && !isProcessing && (
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              )}

              {/* 按钮文字 */}
              <span className="relative z-10 flex items-center justify-center gap-2">
                {isCurrentPlan ? (
                  <>
                    <Check className="h-5 w-5" />
                    Current Plan
                  </>
                ) : isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Rocket className="h-5 w-5 group-hover:animate-pulse" />
                    {plan.plan_name === 'UNLIMITED' ? 'Get Unlimited Access' : 'Start Subscription'}
                  </>
                )}
              </span>

              {/* 特殊标识 */}
              {!isCurrentPlan && !isProcessing && plan.is_popular && (
                <div className="absolute top-1 right-1 bg-white/20 text-white text-xs px-2 py-0.5 rounded-full">
                  Most Popular
                </div>
              )}
            </button>
          )}

          <ul className="space-y-3">
            {featureObjects.map((feature, index) => (
              <li key={index} className="flex items-start gap-2">
                {feature.included ? (
                  <Check className="h-5 w-5 text-primary-foreground shrink-0 mt-0.5" />
                ) : (
                  <OctagonX className="h-5 w-5 text-muted-foreground shrink-0 mt-0.5" />
                )}
                <span className={feature.included ? "text-foreground" : "text-muted-foreground"}>{feature.text}</span>
              </li>
            ))}

            {plan.plan_name === MembershipPlanName.UNLIMITED && (
              <li className="mt-4 pt-4 border-t border-amber-200/50">
                <div className="text-amber-700 text-sm font-medium">
                  Note: Maximum 5 concurrent video tasks per 10 minutes
                </div>
              </li>
            )}
          </ul>
        </div>
      </div>
    </>
  )
}

