-- LoRA模型训练任务表
CREATE TABLE public.train_lora_tasks (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  status text not null,
  progress smallint not null default 0,
  videos text[] not null,
  settings jsonb not null,
  model_id uuid null,
  priority smallint not null default 0,
  created_at timestamp with time zone not null default now(),
  started_at timestamp with time zone null,
  completed_at timestamp with time zone null,
  error_message text null,
  trigger_word text null,
  storage_url text null,
  metadata jsonb null,
  CONSTRAINT train_lora_tasks_pkey PRIMARY KEY (id),
  CONSTRAINT train_lora_tasks_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users (id) ON DELETE CASCADE,
  CONSTRAINT train_lora_tasks_model_id_fkey FOREIGN KEY (model_id) REFERENCES public.models (id) ON DELETE SET NULL,
  CONSTRAINT train_lora_tasks_status_check CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled'))
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_train_lora_tasks_user_id ON public.train_lora_tasks USING btree (user_id);
CREATE INDEX IF NOT EXISTS idx_train_lora_tasks_status ON public.train_lora_tasks USING btree (status);
CREATE INDEX IF NOT EXISTS idx_train_lora_tasks_created_at ON public.train_lora_tasks USING btree (created_at);
CREATE INDEX IF NOT EXISTS idx_train_lora_tasks_priority ON public.train_lora_tasks USING btree (priority);

-- 获取下一个要处理的训练任务函数
CREATE OR REPLACE FUNCTION public.get_next_lora_train_task()
RETURNS SETOF public.train_lora_tasks
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  UPDATE public.train_lora_tasks AS t
  SET 
    status = 'processing',
    started_at = NOW()
  WHERE t.id = (
    SELECT t2.id
    FROM public.train_lora_tasks t2
    WHERE t2.status = 'pending'
    ORDER BY t2.priority DESC, t2.created_at ASC
    LIMIT 1
    FOR UPDATE SKIP LOCKED
  )
  RETURNING *;
END;
$$; 