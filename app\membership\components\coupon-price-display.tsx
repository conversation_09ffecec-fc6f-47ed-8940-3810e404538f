"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import useCouponStore from "@/store/useCouponStore"
import { CouponStatus } from "@/app/membership/types/coupon"
import { useCouponLogic } from "@/hooks/useCouponLogic"

interface CouponPriceDisplayProps {
    monthlyPrice: number;
    yearlyPrice: number;
    billingCycle: "monthly" | "yearly";
    planName: string;
    className?: string;
}

export function CouponPriceDisplay({
    monthlyPrice,
    yearlyPrice,
    billingCycle,
    planName,
    className
}: CouponPriceDisplayProps) {
    const [discountedPrice, setDiscountedPrice] = useState<number>(0)
    const [discountAmount, setDiscountAmount] = useState<number>(0)
    const [hasDiscount, setHasDiscount] = useState<boolean>(false)
    const [discountType, setDiscountType] = useState<'percentage' | 'fixed_amount'>('percentage')
    const [originalPrice, setOriginalPrice] = useState<number>(0)

    const { activeCoupon, getActiveCoupon } = useCouponStore()
    const { isActiveMember } = useCouponLogic()

    // 计算折扣价格 - 前端固定逻辑
    useEffect(() => {
        // 计算显示价格（月均价格）
        const displayPrice = billingCycle === 'yearly' ? yearlyPrice / 12 : monthlyPrice
        setOriginalPrice(displayPrice)

        // 如果用户已经是活跃会员，不显示优惠券折扣
        if (isActiveMember) {
            setDiscountAmount(0)
            setDiscountedPrice(displayPrice)
            setHasDiscount(false)
            return
        }

        // UNLIMITED计划不参与任何优惠
        if (planName === 'UNLIMITED' || planName === 'TEST_UNLIMITED') {
            setDiscountAmount(0)
            setDiscountedPrice(displayPrice)
            setHasDiscount(false)
            return
        }

        const validCoupon = getActiveCoupon()

        if (validCoupon && validCoupon.status === CouponStatus.ACTIVE) {
            let discount = 0
            let finalPrice = displayPrice
            let type: 'percentage' | 'fixed_amount' = 'percentage'

            if (billingCycle === 'monthly') {
                // 月度订阅：首月90%优惠
                // 优惠金额 = 月度价格 * 0.9（精确计算，不取整）
                discount = monthlyPrice * 0.9
                finalPrice = displayPrice - discount
                type = 'percentage'
            } else if (billingCycle === 'yearly') {
                // 年度订阅：首月免费
                // 优惠金额 = 年度价格 / 12（精确计算，不取整）
                discount = yearlyPrice / 12
                finalPrice = displayPrice - discount
                type = 'fixed_amount'
            }

            setDiscountAmount(discount)
            setDiscountedPrice(finalPrice)
            setHasDiscount(true)
            setDiscountType(type)
        } else {
            setDiscountAmount(0)
            setDiscountedPrice(displayPrice)
            setHasDiscount(false)
        }
    }, [monthlyPrice, yearlyPrice, billingCycle, planName, activeCoupon, getActiveCoupon, isActiveMember])

    // 格式化价格显示
    const formatPrice = (price: number) => {
        return price.toFixed(2)
    }

    // 如果没有折扣，显示原价
    if (!hasDiscount) {
        return (
            <div className={`space-y-2 text-center ${className}`}>
                <div className="text-3xl md:text-4xl font-bold">
                    ${formatPrice(originalPrice)}
                </div>
                <div className="text-sm text-muted-foreground">
                    per {billingCycle === "yearly" ? "year" : "month"}
                </div>
            </div>
        )
    }

    // 计算折扣百分比用于显示
    const discountPercentage = discountType === 'percentage' ? 90 : Math.round((discountAmount / originalPrice) * 100)

    // 有折扣时的显示
    return (
        <div className={`space-y-3 ${className}`}>
            {/* 折扣标识 */}
            <div className="flex items-center justify-center gap-2">
                <Badge
                    variant="secondary"
                    className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-400 border-green-500/30 text-sm font-semibold px-3 py-1"
                >
                    {discountType === 'fixed_amount'
                        ? `$${formatPrice(discountAmount)} OFF`
                        : `${discountPercentage}% OFF`
                    }
                </Badge>
                <span className="text-sm font-medium text-green-400">
                    {discountType === 'fixed_amount' && billingCycle === 'yearly'
                        ? '🎁 First Month Free'
                        : '🔥 First Month Discount'
                    }
                </span>
            </div>

            {/* 价格显示 */}
            <div className="space-y-2">
                {/* 原价（划线） */}
                <div className="flex items-center justify-center gap-2">
                    <span className="text-xl text-muted-foreground line-through">
                        ${formatPrice(originalPrice)}
                    </span>
                    <span className="text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded">Original</span>
                </div>

                {/* 折扣价 */}
                <div className="text-center">
                    <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                        ${formatPrice(discountedPrice)}
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                        per {billingCycle === "yearly" ? "year" : "month"}
                    </div>
                </div>
            </div>

            {/* 折扣详情 */}
            <div className="p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl">
                <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Original Price:</span>
                        <span className="font-medium">${formatPrice(originalPrice)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Your Discount:</span>
                        <span className="text-green-400 font-semibold">-${formatPrice(discountAmount)}</span>
                    </div>
                    <Separator className="my-2 bg-green-500/20" />
                    <div className="flex items-center justify-between text-sm font-semibold">
                        <span className="text-green-400">
                            {discountType === 'fixed_amount' && billingCycle === 'yearly'
                                ? 'You Pay:'
                                : 'First Month:'
                            }
                        </span>
                        <span className="text-green-400 text-lg">${formatPrice(discountedPrice)}</span>
                    </div>
                </div>
            </div>

            {/* 说明文字 */}
            <div className="text-center text-xs text-muted-foreground bg-muted/30 p-2 rounded-lg">
                {discountType === 'fixed_amount' && billingCycle === 'yearly'
                    ? '💡 First month completely free, then regular annual billing applies'
                    : '💡 90% discount applies to first month only, subsequent months at regular price'
                }
            </div>
        </div>
    )
}
