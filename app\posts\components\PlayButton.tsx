import { Pause, Play } from "lucide-react"
import { motion } from "framer-motion"

interface PlayButtonProps {
    isPlaying?: boolean
    showMotion?: boolean
}

/**
 * 通用播放按钮组件 - 客户端和服务端共用
 */
export function PlayButton({ isPlaying = false, showMotion = false }: PlayButtonProps) {
    const Button = showMotion ? motion.button : "button"

    const buttonProps = showMotion ? {
        initial: { scale: 0.8, opacity: 0 },
        animate: { scale: 1, opacity: 1 },
        exit: { scale: 0.8, opacity: 0 },
        transition: { duration: 0.2 }
    } : {}

    return (
        <Button
            {...buttonProps}
            className="w-20 h-20 rounded-full bg-blue-500/20 backdrop-blur-md flex items-center justify-center pointer-events-auto"
        >
            {isPlaying ? (
                <Pause className="w-10 h-10 text-white" />
            ) : (
                <Play className="w-10 h-10 text-white ml-1" />
            )}
        </Button>
    )
} 