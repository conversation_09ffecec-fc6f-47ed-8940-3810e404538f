import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    try {
        const { videoUrl, filename } = await request.json();

        if (!videoUrl) {
            return NextResponse.json(
                { error: 'Video URL is required' },
                { status: 400 }
            );
        }

        // 验证URL格式
        try {
            new URL(videoUrl);
        } catch {
            return NextResponse.json(
                { error: 'Invalid video URL' },
                { status: 400 }
            );
        }

        // 获取视频
        const response = await fetch(videoUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; ReelMind/1.0)',
            },
        });

        if (!response.ok) {
            return NextResponse.json(
                { error: `Failed to fetch video: ${response.status}` },
                { status: response.status }
            );
        }

        const videoBuffer = await response.arrayBuffer();
        const contentType = response.headers.get('content-type') || 'video/mp4';

        // 返回视频数据
        return new NextResponse(videoBuffer, {
            headers: {
                'Content-Type': contentType,
                'Content-Disposition': `attachment; filename="${filename || 'video.mp4'}"`,
                'Cache-Control': 'no-cache',
            },
        });
    } catch (error) {
        console.error('Download video proxy error:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
} 