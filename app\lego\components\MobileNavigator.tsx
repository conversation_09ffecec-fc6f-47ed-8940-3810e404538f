"use client"

import React, { useState } from 'react';
import { Blocks, History } from 'lucide-react';
import { Sidebar } from './sidebar';
import HistoryList from './HistoryList';
import { ControlPanel } from './control-panel';
import { Button } from "@/components/ui/button";

export default function MobileNavigator() {
  const [showHistory, setShowHistory] = useState<boolean>(false);
  
  return (
    <div className="flex flex-col h-full">
      {/* Main Content Area */}
      {showHistory ? (
        <div className="relative h-full">
          {/* Back button to return from history */}
          <div className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm px-4 py-2">
            <Button 
              variant="outline" 
              onClick={() => setShowHistory(false)}
              className="w-full flex items-center justify-center"
            >
              ← Back to Builder
            </Button>
              </div>
              
          <div className="h-[calc(100vh-110px)] overflow-auto px-4">
            <HistoryList />
                </div>
              </div>
      ) : (
        <div className="h-full flex flex-col relative">
          {/* Custom History button at the top position (red box in screenshot) */}
          <div className="px-4 py-2 z-20">
            <Button
              onClick={() => setShowHistory(true)}
              variant="outline"
              className="justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4 flex items-center gap-2 w-full"
            >
              <History className="h-4 w-4" />
              <span>History</span>
            </Button>
            </div>
          
          {/* Main content area */}
          <div className="flex-1 flex flex-row-reverse overflow-hidden">
            {/* Sidebar content - Now on the right */}
            <div className="h-full">
              <Sidebar />
            </div>
            
            {/* Empty space for main content on the left */}
            <div className="flex-1"></div>
          </div>
          
          {/* Control Panel fixed at bottom 1/4 */}
          <div className="fixed bottom-[22vh] left-0 right-0 z-10">
            <ControlPanel />
          </div>
      </div>
      )}
    </div>
  );
}

// Mobile-optimized Control Panel (not used)
function MobileControlPanel() {
  return (
    <div className="bg-background rounded-xl shadow-xl p-3">
      <ControlPanel />
    </div>
  );
} 