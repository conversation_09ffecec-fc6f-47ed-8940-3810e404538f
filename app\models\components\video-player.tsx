"use client"

import { useRef, useEffect } from "react"

interface VideoPlayerProps {
    src: string
    poster?: string
    autoPlay?: boolean
}

export function VideoPlayer({ src, poster = "/placeholder.svg", autoPlay = true }: VideoPlayerProps) {
    const videoRef = useRef<HTMLVideoElement>(null)

    useEffect(() => {
        // 初始化视频设置
        if (videoRef.current) {
            videoRef.current.muted = true
            videoRef.current.loop = true
            videoRef.current.playsInline = true
            // 自动播放视频
            videoRef.current.play().catch(error => {
                console.error("Video autoplay failed:", error)
            })
        }
    }, [])

    return (
        <video
            ref={videoRef}
            className="absolute inset-0 w-full h-full object-cover"
            muted
            loop
            autoPlay={autoPlay}
            playsInline
            poster={poster}
            src={src}
        />
    )
} 