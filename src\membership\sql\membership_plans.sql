create table public.user_memberships (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  is_active boolean not null default true,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  plan_id uuid null,
  subscription_id text null,
  cancel_at_period_end boolean null default false,
  expires_at timestamp with time zone null,
  constraint user_memberships_pkey primary key (id),
  constraint user_memberships_plan_id_fkey foreign KEY (plan_id) references membership_plans (id),
  constraint user_memberships_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_user_memberships_user_id on public.user_memberships using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_user_memberships_is_active on public.user_memberships using btree (is_active) TABLESPACE pg_default;

create index IF not exists idx_user_memberships_subscription_id on public.user_memberships using btree (subscription_id) TABLESPACE pg_default;
