import { create } from 'zustand';
import { User } from '@supabase/supabase-js';
import { createClient } from '@/lib/supabase/client';

interface UserState {
    // 状态
    user: User | null;
    isLoading: boolean;
    error: string | null;

    // 操作
    fetchUser: () => Promise<void>;
    resetUserState: () => void;
}

const useUserStore = create<UserState>((set) => {
    const supabase = createClient();

    return {
        // 初始状态
        user: null,
        isLoading: false,
        error: null,

        // 获取用户信息
        fetchUser: async () => {
            try {
                set({ isLoading: true, error: null });
                const { data } = await supabase.auth.getUser();
                set({ user: data.user, isLoading: false });
            } catch (error) {
                console.error('获取用户信息失败:', error);
                set({
                    error: error instanceof Error ? error.message : '获取用户信息失败',
                    isLoading: false
                });
            }
        },

        // 重置状态
        resetUserState: () => {
            set({
                user: null,
                isLoading: false,
                error: null
            });
        }
    };
});

export default useUserStore; 