-- 反滥用系统数据表
-- 设计原则：基于数据库约束确保可靠性，支持多实例部署

-- 滥用检查日志表
CREATE TABLE IF NOT EXISTS public.abuse_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    fingerprint_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    action VARCHAR(50) NOT NULL, -- 'claim_success', 'validation_passed', 'validation_failed'
    risk_score INTEGER DEFAULT 0,
    risk_factors JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_abuse_logs_user_id ON abuse_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_abuse_logs_fingerprint_id ON abuse_logs(fingerprint_id);
CREATE INDEX IF NOT EXISTS idx_abuse_logs_ip_address ON abuse_logs(ip_address);
CREATE INDEX IF NOT EXISTS idx_abuse_logs_created_at ON abuse_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_abuse_logs_action ON abuse_logs(action);

-- 复合索引用于优化常用查询
CREATE INDEX IF NOT EXISTS idx_abuse_logs_fingerprint_action_time 
ON abuse_logs(fingerprint_id, action, created_at);

CREATE INDEX IF NOT EXISTS idx_abuse_logs_ip_action_time 
ON abuse_logs(ip_address, action, created_at);

-- 设置表注释
COMMENT ON TABLE abuse_logs IS '反滥用日志表，用于记录用户行为和风险检查结果';
COMMENT ON COLUMN abuse_logs.user_id IS '用户ID';
COMMENT ON COLUMN abuse_logs.fingerprint_id IS '设备指纹ID';
COMMENT ON COLUMN abuse_logs.ip_address IS 'IP地址';
COMMENT ON COLUMN abuse_logs.user_agent IS '用户代理字符串';
COMMENT ON COLUMN abuse_logs.action IS '操作类型：claim_success, validation_passed, validation_failed';
COMMENT ON COLUMN abuse_logs.risk_score IS '风险评分';
COMMENT ON COLUMN abuse_logs.risk_factors IS '风险因素JSON';

-- 创建一个视图来快速查看风险统计
CREATE OR REPLACE VIEW abuse_stats AS
SELECT 
    DATE(created_at) as date,
    action,
    COUNT(*) as count,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT fingerprint_id) as unique_devices,
    COUNT(DISTINCT ip_address) as unique_ips,
    AVG(risk_score) as avg_risk_score
FROM abuse_logs 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at), action
ORDER BY date DESC, action;

-- 创建一个函数来快速检查用户是否已领取
CREATE OR REPLACE FUNCTION check_user_claim(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- 检查用户是否在 credit_transactions 表中有 new_user_bonus 记录
    RETURN EXISTS (
        SELECT 1 
        FROM credit_transactions 
        WHERE user_id = p_user_id 
        AND type = 'new_user_bonus' 
        AND status = 'completed'
        LIMIT 1
    );
END;
$$;

-- 创建一个函数来记录滥用检查日志
CREATE OR REPLACE FUNCTION log_abuse_check(
    p_user_id UUID,
    p_fingerprint_id VARCHAR(255),
    p_ip_address INET,
    p_user_agent TEXT,
    p_action VARCHAR(50),
    p_risk_score INTEGER DEFAULT 0,
    p_risk_factors JSONB DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO abuse_logs (
        user_id,
        fingerprint_id,
        ip_address,
        user_agent,
        action,
        risk_score,
        risk_factors
    ) VALUES (
        p_user_id,
        p_fingerprint_id,
        p_ip_address,
        p_user_agent,
        p_action,
        p_risk_score,
        p_risk_factors
    );
END;
$$;

-- 创建一个函数来获取设备指纹使用次数
CREATE OR REPLACE FUNCTION get_fingerprint_usage_count(
    p_fingerprint_id VARCHAR(255),
    p_hours INTEGER DEFAULT 24
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    usage_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO usage_count
    FROM abuse_logs
    WHERE fingerprint_id = p_fingerprint_id
    AND action = 'claim_success'
    AND created_at >= NOW() - INTERVAL '1 hour' * p_hours;
    
    RETURN COALESCE(usage_count, 0);
END;
$$;

-- 创建一个函数来获取IP使用次数
CREATE OR REPLACE FUNCTION get_ip_usage_count(
    p_ip_address INET,
    p_hours INTEGER DEFAULT 24
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    usage_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO usage_count
    FROM abuse_logs
    WHERE ip_address = p_ip_address
    AND action = 'claim_success'
    AND created_at >= NOW() - INTERVAL '1 hour' * p_hours;
    
    RETURN COALESCE(usage_count, 0);
END;
$$;

-- 创建定期清理旧数据的函数
CREATE OR REPLACE FUNCTION cleanup_abuse_logs()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除30天前的日志
    DELETE FROM abuse_logs 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;

-- 设置行级安全策略（如果需要）
-- ALTER TABLE abuse_logs ENABLE ROW LEVEL SECURITY;

-- 使用说明注释
/*
反滥用系统使用说明：

1. 主要依赖数据库约束和查询，确保多实例一致性
2. abuse_logs 表记录所有关键操作，用于风险评估和审计
3. 可以通过 check_user_claim 函数快速检查用户是否已领取
4. 使用 log_abuse_check 函数记录关键操作
5. 使用 get_fingerprint_usage_count 和 get_ip_usage_count 函数检查使用频率
6. 定期运行 cleanup_abuse_logs() 清理旧数据

部署步骤：
1. 执行此SQL文件创建表和函数
2. 在应用中使用 AntiAbuseService 替换原有的 SimpleAntiAbuseService
3. 监控 abuse_stats 视图了解系统运行情况

性能优化：
- 索引优化了常用查询路径
- 存储过程减少了网络往返
- 支持异步记录日志，不阻塞主流程
*/
