/**
 * Rate limiting utility to prevent spam and abuse
 */
export class RateLimiter {
  private static submissions = new Map<string, number[]>();
  private static readonly MAX_SUBMISSIONS_PER_HOUR = 5;
  private static readonly MAX_SUBMISSIONS_PER_MINUTE = 2;
  private static readonly COOLDOWN_PERIOD = 30 * 1000; // 30 seconds
  private static readonly CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour

  static {
    // Cleanup old entries periodically
    setInterval(() => {
      this.cleanup();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Check if user can submit feedback
   */
  static canSubmit(userId: string): {
    allowed: boolean;
    reason?: string;
    retryAfter?: number;
  } {
    const now = Date.now();
    const userSubmissions = this.submissions.get(userId) || [];

    // Filter submissions within the last hour
    const recentSubmissions = userSubmissions.filter(
      timestamp => now - timestamp < 60 * 60 * 1000
    );

    // Check hourly limit
    if (recentSubmissions.length >= this.MAX_SUBMISSIONS_PER_HOUR) {
      const oldestSubmission = Math.min(...recentSubmissions);
      const retryAfter = oldestSubmission + 60 * 60 * 1000 - now;
      return {
        allowed: false,
        reason: 'Too many submissions. Please try again later.',
        retryAfter: Math.ceil(retryAfter / 1000), // seconds
      };
    }

    // Check minute limit
    const lastMinuteSubmissions = recentSubmissions.filter(
      timestamp => now - timestamp < 60 * 1000
    );

    if (lastMinuteSubmissions.length >= this.MAX_SUBMISSIONS_PER_MINUTE) {
      return {
        allowed: false,
        reason: 'Please wait before submitting another feedback.',
        retryAfter: 60, // 1 minute
      };
    }

    // Check cooldown period
    if (recentSubmissions.length > 0) {
      const lastSubmission = Math.max(...recentSubmissions);
      const timeSinceLastSubmission = now - lastSubmission;

      if (timeSinceLastSubmission < this.COOLDOWN_PERIOD) {
        const retryAfter = this.COOLDOWN_PERIOD - timeSinceLastSubmission;
        return {
          allowed: false,
          reason: 'Please wait before submitting another feedback.',
          retryAfter: Math.ceil(retryAfter / 1000), // seconds
        };
      }
    }

    return { allowed: true };
  }

  /**
   * Record a submission
   */
  static recordSubmission(userId: string): void {
    const now = Date.now();
    const userSubmissions = this.submissions.get(userId) || [];
    
    userSubmissions.push(now);
    this.submissions.set(userId, userSubmissions);
  }

  /**
   * Get remaining submissions for user
   */
  static getRemainingSubmissions(userId: string): {
    hourly: number;
    minute: number;
    nextSubmissionAllowedAt?: number;
  } {
    const now = Date.now();
    const userSubmissions = this.submissions.get(userId) || [];

    const recentSubmissions = userSubmissions.filter(
      timestamp => now - timestamp < 60 * 60 * 1000
    );

    const lastMinuteSubmissions = recentSubmissions.filter(
      timestamp => now - timestamp < 60 * 1000
    );

    let nextSubmissionAllowedAt: number | undefined;

    if (recentSubmissions.length > 0) {
      const lastSubmission = Math.max(...recentSubmissions);
      const timeSinceLastSubmission = now - lastSubmission;

      if (timeSinceLastSubmission < this.COOLDOWN_PERIOD) {
        nextSubmissionAllowedAt = lastSubmission + this.COOLDOWN_PERIOD;
      }
    }

    return {
      hourly: Math.max(0, this.MAX_SUBMISSIONS_PER_HOUR - recentSubmissions.length),
      minute: Math.max(0, this.MAX_SUBMISSIONS_PER_MINUTE - lastMinuteSubmissions.length),
      nextSubmissionAllowedAt,
    };
  }

  /**
   * Clean up old submissions
   */
  private static cleanup(): void {
    const now = Date.now();
    const cutoff = now - 60 * 60 * 1000; // 1 hour ago

    for (const [userId, submissions] of this.submissions.entries()) {
      const recentSubmissions = submissions.filter(timestamp => timestamp > cutoff);
      
      if (recentSubmissions.length === 0) {
        this.submissions.delete(userId);
      } else {
        this.submissions.set(userId, recentSubmissions);
      }
    }
  }

  /**
   * Reset rate limit for a user (admin function)
   */
  static resetUserLimit(userId: string): void {
    this.submissions.delete(userId);
  }

  /**
   * Get rate limit status for debugging
   */
  static getStatus(): {
    totalUsers: number;
    totalSubmissions: number;
    config: {
      maxPerHour: number;
      maxPerMinute: number;
      cooldownSeconds: number;
    };
  } {
    let totalSubmissions = 0;
    for (const submissions of this.submissions.values()) {
      totalSubmissions += submissions.length;
    }

    return {
      totalUsers: this.submissions.size,
      totalSubmissions,
      config: {
        maxPerHour: this.MAX_SUBMISSIONS_PER_HOUR,
        maxPerMinute: this.MAX_SUBMISSIONS_PER_MINUTE,
        cooldownSeconds: this.COOLDOWN_PERIOD / 1000,
      },
    };
  }
}

/**
 * IP-based rate limiting for additional protection
 */
export class IPRateLimiter {
  private static ipSubmissions = new Map<string, number[]>();
  private static readonly MAX_SUBMISSIONS_PER_IP_PER_HOUR = 20;
  private static readonly MAX_SUBMISSIONS_PER_IP_PER_MINUTE = 5;

  /**
   * Check if IP can submit
   */
  static canSubmit(ip: string): {
    allowed: boolean;
    reason?: string;
    retryAfter?: number;
  } {
    const now = Date.now();
    const ipSubmissions = this.ipSubmissions.get(ip) || [];

    // Filter submissions within the last hour
    const recentSubmissions = ipSubmissions.filter(
      timestamp => now - timestamp < 60 * 60 * 1000
    );

    // Check hourly limit
    if (recentSubmissions.length >= this.MAX_SUBMISSIONS_PER_IP_PER_HOUR) {
      return {
        allowed: false,
        reason: 'Too many submissions from this IP address.',
        retryAfter: 3600, // 1 hour
      };
    }

    // Check minute limit
    const lastMinuteSubmissions = recentSubmissions.filter(
      timestamp => now - timestamp < 60 * 1000
    );

    if (lastMinuteSubmissions.length >= this.MAX_SUBMISSIONS_PER_IP_PER_MINUTE) {
      return {
        allowed: false,
        reason: 'Too many submissions from this IP address.',
        retryAfter: 60, // 1 minute
      };
    }

    return { allowed: true };
  }

  /**
   * Record IP submission
   */
  static recordSubmission(ip: string): void {
    const now = Date.now();
    const ipSubmissions = this.ipSubmissions.get(ip) || [];
    
    ipSubmissions.push(now);
    this.ipSubmissions.set(ip, ipSubmissions);
  }

  /**
   * Get client IP from request
   */
  static getClientIP(request: Request): string {
    // Try various headers for IP detection
    const headers = request.headers;
    
    return (
      headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
      headers.get('x-real-ip') ||
      headers.get('x-client-ip') ||
      headers.get('cf-connecting-ip') || // Cloudflare
      headers.get('x-forwarded') ||
      headers.get('forwarded-for') ||
      headers.get('forwarded') ||
      'unknown'
    );
  }
}
