import { useCallback } from 'react';
import { useSoundStore, SoundTask } from '@/store/useSoundStore';
import { soundApi, pollTaskStatus, validateSoundRequest, handleApiError } from '@/lib/api/sound';

export function useSoundGeneration() {
    const {
        text,
        selectedVoiceModel,
        speed,
        stability,
        similarityBoost,
        styleExaggeration,
        isGenerating,
        setIsGenerating,
        setCurrentTask,
        setError,
        addToHistory,
    } = useSoundStore();

    const generateSpeech = useCallback(async () => {
        if (!selectedVoiceModel) {
            setError('Please select a voice model');
            return;
        }

        const request = {
            text: text.trim(),
            voice_model_id: selectedVoiceModel.id,
            speed,
            stability,
            similarity_boost: similarityBoost,
            style_exaggeration: styleExaggeration,
        };

        // Validate request
        const validationError = validateSoundRequest(request);
        if (validationError) {
            setError(validationError);
            return;
        }

        setIsGenerating(true);
        setError(null);

        try {
            // Start generation
            const response = await soundApi.generateSpeech(request);
            
            if (!response.success || !response.data) {
                throw new Error(response.error || 'Failed to start speech generation');
            }

            const initialTask: SoundTask = {
                id: response.data.task_id,
                text: request.text,
                voice_model_id: request.voice_model_id,
                voice_model_name: selectedVoiceModel.name,
                speed: request.speed,
                stability: request.stability,
                similarity_boost: request.similarity_boost,
                style_exaggeration: request.style_exaggeration,
                status: response.data.status,
                created_at: new Date().toISOString(),
                audio_url: response.data.audio_url,
                error_message: response.data.error_message,
            };

            setCurrentTask(initialTask);
            addToHistory(initialTask);

            // Poll for completion if not already completed
            if (initialTask.status === 'pending' || initialTask.status === 'processing') {
                try {
                    const completedTask = await pollTaskStatus(
                        initialTask.id,
                        (updatedTask) => {
                            setCurrentTask(updatedTask);
                            // Update task in history
                            addToHistory(updatedTask);
                        }
                    );

                    setCurrentTask(completedTask);
                } catch (pollError) {
                    console.error('Polling failed:', pollError);
                    setError(handleApiError(pollError));
                }
            }
        } catch (error) {
            console.error('Speech generation failed:', error);
            setError(handleApiError(error));
        } finally {
            setIsGenerating(false);
        }
    }, [
        text,
        selectedVoiceModel,
        speed,
        stability,
        similarityBoost,
        styleExaggeration,
        setIsGenerating,
        setCurrentTask,
        setError,
        addToHistory,
    ]);

    const canGenerate = useCallback(() => {
        return (
            !isGenerating &&
            text.trim().length > 0 &&
            selectedVoiceModel !== null &&
            text.length <= 5000
        );
    }, [isGenerating, text, selectedVoiceModel]);

    return {
        generateSpeech,
        canGenerate: canGenerate(),
        isGenerating,
    };
}
