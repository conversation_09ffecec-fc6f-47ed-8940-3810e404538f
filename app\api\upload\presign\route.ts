import { NextResponse } from 'next/server';
import { z } from 'zod';
import { createClient } from '@/lib/supabase/server';
import {
    generateSecureFilename,
    generatePresignedUrl,
    validateFileType,
    validateFileSize
} from '../tools';
import { MAX_UPLOAD_FILE_SIZE_MB } from '@/constants/upload';

// 验证请求格式
const requestSchema = z.object({
    filename: z.string().min(1),
    fileType: z.string().min(1),
    fileSize: z.number().positive(),
    bucketName: z.string().optional(),
});

/**
 * 处理预签名URL请求
 * @description 生成安全的预签名URL，允许客户端直接上传文件到Cloudflare R2
 */
export async function POST(request: Request) {
    try {
        // 1. 身份验证 - 使用更安全的方法验证用户身份
        const supabase = await createClient();
        const { data: { user }, error } = await supabase.auth.getUser();

        if (error || !user) {
            return NextResponse.json(
                { error: '未授权访问', message: 'Please login first' },
                { status: 401 }
            );
        }

        // 使用Supabase用户ID
        const userId = user.id;

        // 2. 验证请求数据
        const body = await request.json();
        const validation = requestSchema.safeParse(body);

        if (!validation.success) {
            return NextResponse.json(
                { error: 'Invalid request data', details: validation.error.format() },
                { status: 400 }
            );
        }

        const { filename, fileType, fileSize, bucketName } = validation.data;

        // 3. 验证文件类型
        if (!validateFileType(fileType)) {
            return NextResponse.json(
                { error: '不支持的文件类型', message: '仅接受视频或图片文件' },
                { status: 400 }
            );
        }

        // 4. 验证文件大小
        if (!validateFileSize(fileSize, MAX_UPLOAD_FILE_SIZE_MB)) {
            return NextResponse.json(
                {
                    error: '文件过大',
                    message: `文件大小超过限制（最大${MAX_UPLOAD_FILE_SIZE_MB}MB）`
                },
                { status: 400 }
            );
        }

        // 5. 生成安全的文件名
        const secureFilename = generateSecureFilename(userId, filename);

        // 6. 生成预签名URL
        const presignedData = await generatePresignedUrl(
            secureFilename,
            fileType,
            3600, // 默认的过期时间
            (bucketName as any) || 'gen-refer-img' // 如果有提供桶名则使用，否则使用默认桶
        );

        // 7. 返回预签名URL和其他必要信息
        return NextResponse.json({
            success: true,
            data: presignedData
        });

    } catch (error) {
        console.error('生成预签名URL失败:', error);
        return NextResponse.json(
            { error: 'Upload error', message: '处理上传请求时出错，请稍后重试' },
            { status: 500 }
        );
    }
} 