// import { Input } from "@/components/ui/input";
// import { PlusCircle } from "lucide-react";

// export default function LoraSelector() {
//     return <div className="mt-4">
//         <div className="flex items-center justify-between mb-2">
//             <h4 className="text-sm font-medium text-foreground">LoRa Models</h4>
//             <div className="relative">
//                 <button
//                     onClick={() => setShowLoraSelector(!showLoraSelector)}
//                     className="text-xs text-primary-foreground flex items-center"
//                     disabled={isGenerating}
//                 >
//                     <PlusCircle size={14} className="mr-1" />
//                     Add LoRa
//                 </button>

//                 {/* LoRa Selector Dropdown */}
//                 {showLoraSelector && (
//                     <div className="absolute top-full right-0 mt-2 bg-popover shadow-lg rounded-xl border border-border z-50 overflow-hidden w-64">
//                         <div className="p-2 border-b border-border">
//                             <Input
//                                 placeholder="Search LoRa models..."
//                                 value={modelSearchQuery}
//                                 onChange={(e) => setModelSearchQuery(e.target.value)}
//                                 className="text-xs"
//                                 size={20}
//                             />
//                         </div>
//                         <div className="max-h-[280px] overflow-y-auto p-1">
//                             {models
//                                 .filter(model => model.type === "lora" &&
//                                     !selectedLoraModels.some(lora => lora.id === model.id) &&
//                                     model.name.toLowerCase().includes(modelSearchQuery.toLowerCase()))
//                                 .map(model => (
//                                     <div
//                                         key={model.id}
//                                         className="p-2 flex items-center hover:bg-accent/50 cursor-pointer rounded-md transition-colors"
//                                         onClick={() => {
//                                             addLoraModel(model);
//                                             setShowLoraSelector(false);
//                                         }}
//                                     >
//                                         <div className="w-8 h-8 relative rounded-md overflow-hidden flex-shrink-0">
//                                             <Image
//                                                 src={model.coverImage}
//                                                 alt={model.name}
//                                                 fill
//                                                 className="object-cover"
//                                             />
//                                         </div>
//                                         <div className="ml-2 flex-1">
//                                             <div className="font-medium text-xs">{model.name}</div>
//                                             <div className="text-[10px] text-muted-foreground">Style modifier</div>
//                                         </div>
//                                         <PlusCircle size={14} className="text-primary-foreground ml-1" />
//                                     </div>
//                                 ))}
//                         </div>
//                     </div>
//                 )}
//             </div>
//         </div>

//         <div className="space-y-2">
//             {selectedLoraModels.length === 0 ? (
//                 <div className="p-3 bg-card/20 border border-dashed border-border/40 rounded-lg text-xs text-muted-foreground text-center">
//                     No LoRa models added
//                 </div>
//             ) : (
//                 selectedLoraModels.map(lora => (
//                     <div key={lora.id} className="p-3 bg-card/30 border border-border/50 rounded-lg">
//                         <div className="flex items-center justify-between mb-2">
//                             <span className="text-sm font-medium">{lora.name}</span>
//                             <button
//                                 onClick={() => removeLoraModel(lora.id)}
//                                 className="text-muted-foreground hover:text-destructive transition-colors"
//                             >
//                                 <X size={14} />
//                             </button>
//                         </div>
//                         <div className="flex items-center">
//                             <span className="text-xs text-muted-foreground mr-2">Strength:</span>
//                             <input
//                                 type="range"
//                                 min="0.1"
//                                 max="1"
//                                 step="0.1"
//                                 value={lora.strength}
//                                 onChange={(e) => updateLoraStrength(lora.id, parseFloat(e.target.value))}
//                                 className="flex-1 h-1.5 bg-muted rounded-full appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-3 [&::-webkit-slider-thumb]:h-3 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-primary"
//                             />
//                             <span className="text-xs ml-2 w-8 text-right">{lora.strength.toFixed(1)}</span>
//                         </div>
//                     </div>
//                 ))
//             )}
//         </div>
//     </div>
// }
