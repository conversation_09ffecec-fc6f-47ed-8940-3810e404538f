'use client';

import { useEffect } from 'react';
import { useLegoStore } from '../store';
import { useBlogPromptStore } from '@/store';
import { useSearchParams } from 'next/navigation';

/**
 * 处理URL参数和BlogPromptStore中的prompt，并初始化Lego页面的相关状态
 */
export default function PromptInitializer() {
    const { setUserPrompt, setQueueInfo, ensureCardsExist, selectModelByName, availableModels } = useLegoStore();
    const searchParams = useSearchParams();

    // 从BlogPromptStore中获取prompt和清除方法
    const blogPrompt = useBlogPromptStore(state => state.prompt);
    const clearBlogPrompt = useBlogPromptStore(state => state.clearPrompt);

    useEffect(() => {
        // 0. 确保每个部分至少有一个卡片
        ensureCardsExist();

        // 1. 处理BlogPromptStore中的prompt
        if (blogPrompt) {
            // 设置到store中
            setUserPrompt(blogPrompt);
            // 使用后清除，避免下次进入页面时仍然使用旧的prompt
            clearBlogPrompt();
        }

        // 2. 处理URL参数中的taskId
        if (searchParams) {
            const taskId = searchParams.get('taskId');
            if (taskId) {
                console.log('URL中检测到taskId:', taskId);
                // 设置到store中，触发进度追踪
                setQueueInfo({
                    taskId,
                    position: 0,
                    totalInQueue: 0,
                    isProcessing: true
                });
            }
        }

        // 3. 处理URL参数中的model选择
        const modelParam = searchParams?.get('model');
        if (modelParam && availableModels.length > 0) {
            console.log('URL中检测到model参数:', modelParam);
            // 延迟一点时间确保模型数据已经加载完成
            const timer = setTimeout(() => {
                selectModelByName(modelParam);
            }, 50);
            return () => clearTimeout(timer);
        }
    }, [setUserPrompt, blogPrompt, clearBlogPrompt, searchParams, setQueueInfo, ensureCardsExist, selectModelByName, availableModels]);

    // 单独的useEffect处理模型选择，确保在模型数据加载完成后执行
    useEffect(() => {
        const modelParam = searchParams?.get('model');
        if (modelParam && availableModels.length > 0) {
            console.log('模型数据加载完成，处理URL模型参数:', modelParam);
            selectModelByName(modelParam);
        }
    }, [availableModels, searchParams, selectModelByName]);

    // 这个组件不渲染任何内容
    return null;
}
