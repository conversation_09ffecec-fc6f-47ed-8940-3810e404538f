import type { Category } from "@/types"
import { getCategories, ALL_CATEGORY } from "@/lib/api/categories"
import { CategoryItem } from "./category-item"

export async function CategoryNav({ selectedCategoryId }: { selectedCategoryId: string }) {
  // 在服务端获取类别数据
  const categories = await getCategories()

  // 处理错误 - 如果获取失败，categories将只包含ALL_CATEGORY
  const hasError = categories.length === 1 && categories[0].id === ALL_CATEGORY.id

  return (
    <div className="relative px-2 sm:px-8 pb-4 overflow-hidden">
      {hasError ? (
        // 错误状态
        <div className="text-sm text-red-500 mb-4 flex items-center justify-between">
          <span>Failed to load categories</span>
          <form action="/api/revalidate?path=/categories" method="POST">
            <button type="submit" className="text-primary-foreground hover:underline">
              Retry
            </button>
          </form>
        </div>
      ) : (
        // 正常显示类别
        <div className="flex gap-2 sm:gap-4 overflow-x-auto pb-1 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
          {categories.map((category: Category) => (
            <CategoryItem
              key={category.id}
              category={category}
              isSelected={selectedCategoryId === category.id || (category.id === ALL_CATEGORY.id && !selectedCategoryId)}
            />
          ))}
        </div>
      )}
    </div>
  );
}
