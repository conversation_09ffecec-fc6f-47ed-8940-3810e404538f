"use client"

import { useCallback, useEffect, useState, useRef } from "react"
import { ContentGrid } from "./content-grid"
import { useInfiniteScroll } from "@/hooks/use-infinite-scroll"
import { PostsResponse } from "@/lib/api/post"
import useFeedStore from "@/store/useFeedStore"
import { Loader2 } from "lucide-react"

interface ClientFeedManagerProps {
    initialData: PostsResponse
}

export function ClientFeedManager({ initialData }: ClientFeedManagerProps) {
    // 使用Zustand store
    const {
        items,
        isLoading,
        hasMore,
        loadMore,
        error: storeError,
        initialize
    } = useFeedStore()

    // 跟踪组件是否已挂载
    const [mounted, setMounted] = useState(false)
    const initialLoadTriggered = useRef(false)
    const initialCheckPerformed = useRef(false)

    // 初始化时加载数据
    useEffect(() => {
        // 使用服务端获取的初始数据初始化store
        if (initialData.posts && initialData.posts.length > 0) {
            initialize(initialData)
        }
        setMounted(true)
    }, [initialize, initialData])

    // 使用改进的无限滚动hook - 优化rootMargin以减少不必要的加载
    const { loaderRef, isFetching, error: scrollError, triggerLoadMore } = useInfiniteScroll(loadMore, {
        rootMargin: "200px 0px", // 减小预加载距离，优化性能
        isLoading,
        hasMore,
        retryOnError: true,
        retryDelay: 3000
        // 注意：threshold参数在hook中不可用，通过rootMargin控制加载时机
    })

    // 改进的初始内容检查逻辑
    const checkInitialContent = useCallback(() => {
        if (!mounted || initialLoadTriggered.current || !initialCheckPerformed.current) return

        // 标记已执行初始检查
        initialCheckPerformed.current = true

        // 获取视口高度和内容高度
        const viewportHeight = window.innerHeight
        const contentHeight = document.documentElement.scrollHeight

        // 如果内容高度不足以填满视口的1.5倍，且有更多内容可加载，则触发加载
        if (contentHeight < viewportHeight * 1.5 && hasMore && !isLoading && !isFetching) {
            initialLoadTriggered.current = true

            // 使用requestAnimationFrame确保在下一帧执行，优先完成当前渲染
            requestAnimationFrame(() => {
                // 使用triggerLoadMore而不是直接调用loadMore，以便利用hook中的错误处理和重试机制
                triggerLoadMore()
            })
        }
    }, [mounted, hasMore, isLoading, isFetching, triggerLoadMore])

    // 初始加载检查 - 使用ResizeObserver而不是resize事件，性能更好
    useEffect(() => {
        if (typeof window === 'undefined' || initialLoadTriggered.current) return

        // 使用RAF确保DOM已完全渲染
        requestAnimationFrame(() => {
            // 标记初始检查已准备好执行
            initialCheckPerformed.current = true
            checkInitialContent()
        })

        // 使用ResizeObserver监听尺寸变化，比resize事件更高效
        const resizeObserver = new ResizeObserver(() => {
            if (!initialLoadTriggered.current) {
                checkInitialContent()
            }
        })

        // 观察document.body的尺寸变化
        resizeObserver.observe(document.body)

        return () => {
            resizeObserver.disconnect()
        }
    }, [checkInitialContent])

    // 组合错误状态
    const hasError = storeError || scrollError

    // 显示加载状态的条件：正在加载且不是初始数据加载
    const showLoading = (isLoading || isFetching) && !(initialData.posts && initialData.posts.length > 0 && !mounted)

    return (
        <>
            <ContentGrid
                items={items}
                isLoading={false} // 不在ContentGrid中显示加载状态，而是在下面单独显示
            />

            {/* 加载状态或错误状态 */}
            {(showLoading || hasError) && (
                <div
                    ref={loaderRef}
                    className="flex flex-col items-center justify-center py-8 space-y-4"
                >
                    {showLoading && (
                        <div className="flex items-center space-x-2">
                            <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                            <span className="text-sm text-gray-500">Loading...</span>
                        </div>
                    )}

                    {hasError && !showLoading && (
                        <div className="flex flex-col items-center space-y-2">
                            <p className="text-sm text-red-500">Retrying...</p>
                            <button
                                onClick={() => triggerLoadMore()}
                                className="px-4 py-2 text-sm bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
                            >
                                Retry
                            </button>
                        </div>
                    )}
                </div>
            )}

            {/* 无限滚动观察元素 - 即使没有显示加载状态也需要保留，以便触发加载 */}
            {!showLoading && !hasError && hasMore && (
                <div ref={loaderRef} className="h-10 w-full" />
            )}
        </>
    )
}