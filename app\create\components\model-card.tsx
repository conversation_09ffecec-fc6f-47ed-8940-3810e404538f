"use client"

import { Model } from "@/types/model";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { useState, useRef, useEffect } from "react";
import { Video, Image as ImageIcon, RefreshCcw } from "lucide-react";

interface ModelCardProps {
    model: Model;
    isSelected: boolean;
    onClick: () => void;
}

export function ModelCard({ model, onClick }: ModelCardProps) {
    const isPro = model.storage_path?.includes('pro');
    const [isVideoLoaded, setIsVideoLoaded] = useState(false);
    const [shouldLoadVideo, setShouldLoadVideo] = useState(false);
    const cardRef = useRef<HTMLDivElement>(null);

    // 获取模型类型的标签和样式
    const getModelTypeInfo = () => {
        const type = model.type?.toLowerCase() || "";

        if (type === "text-to-video") {
            return {
                label: "TEXT TO VIDEO",
                className: "bg-blue-500/90 text-xs font-medium",
                icon: <Video className="h-3 w-3 mr-1" />
            };
        }

        if (type === "image-to-video") {
            return {
                label: "IMAGE TO VIDEO",
                className: "bg-green-500/90 text-xs font-medium",
                icon: <ImageIcon className="h-3 w-3 mr-1" />
            };
        }

        if (type === "video-to-video") {
            return {
                label: "VIDEO TO VIDEO",
                className: "bg-purple-500/90 text-xs font-medium",
                icon: <RefreshCcw className="h-3 w-3 mr-1" />
            };
        }

        // 原有的模型类型处理逻辑保留
        if (type === "checkpoint") {
            return {
                label: "CHECKPOINT",
                className: "bg-blue-500/90 text-xs font-medium",
                icon: null
            };
        }

        if (type === "lora") {
            return {
                label: "LORA",
                className: "bg-purple-500/90 text-xs font-medium",
                icon: null
            };
        }

        return {
            label: (type || "MODEL").toUpperCase(),
            className: "bg-gray-500/90 text-xs font-medium",
            icon: null
        };
    };

    const modelTypeInfo = getModelTypeInfo();

    const handleVideoLoaded = () => {
        setIsVideoLoaded(true);
    }

    // 使用 Intersection Observer 检测元素是否在视口中
    useEffect(() => {
        if (!cardRef.current) return

        const observer = new IntersectionObserver(
            (entries) => {
                const [entry] = entries

                // 当卡片进入视图，且没有加载过视频时，控制延迟加载视频
                if (entry.isIntersecting && !shouldLoadVideo) {
                    // 延迟1秒加载视频，优先加载其他内容
                    requestAnimationFrame(() => {
                        setShouldLoadVideo(true)
                    })
                }
            },
            {
                root: null, // 使用视口作为根
                rootMargin: '100px', // 提前100px开始加载
                threshold: 0.1, // 当10%的元素可见时触发
            }
        )

        observer.observe(cardRef.current)
        return () => {
            if (cardRef.current) {
                observer.unobserve(cardRef.current)
            }
        }
    }, [shouldLoadVideo])

    return (
        <div
            ref={cardRef}
            className="cursor-pointer transition-all hover:scale-[1.02]"
            onClick={onClick}
        >
            <div className="relative aspect-[4/5] rounded-xl overflow-hidden group">
                {/* 模型封面图 */}
                {!isVideoLoaded && <Image
                    src={model.cover_img || "/placeholder.svg"}
                    alt={model.name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />}

                {/* 懒加载视频元素 */}
                {model.cover_video && shouldLoadVideo && (
                    <video
                        src={model.cover_video}
                        preload="metadata"
                        muted
                        playsInline
                        autoPlay
                        loop
                        onLoadedData={handleVideoLoaded}
                        className={`absolute inset-0 w-full h-full object-cover ${isVideoLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
                        poster={model.cover_img || "/placeholder.svg"} // 添加poster属性提高SEO和初始显示
                        // 增加关键属性以改善自动播放行为
                        data-silent="true"
                        disablePictureInPicture
                    />
                )}
                {/* 渐变叠加层 - 整体图片 */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent" />

                {/* 底部额外遮罩层 - 增强底部内容可读性 */}
                <div className="absolute bottom-0 left-0 right-0 h-39 bg-gradient-to-t from-black/90 to-transparent" />

                {/* 顶部标签 */}
                <div className="absolute top-3 left-3 flex gap-2 items-center">
                    <Badge className={`${modelTypeInfo.className} border-0 backdrop-blur-sm flex items-center`}>
                        {modelTypeInfo.icon}
                        {modelTypeInfo.label}
                    </Badge>
                    {isPro && (
                        <Badge className="bg-blue-500/90 text-xs font-medium border-0 backdrop-blur-sm">
                            PREMIUM
                        </Badge>
                    )}
                </div>

                {/* 底部内容 */}
                <div className="absolute bottom-0 left-0 right-0 p-4 space-y-2 z-20">
                    {/* 模型名称和价格 */}
                    <div className="flex justify-between items-start">
                        <h3 className="text-white font-medium text-xl leading-tight">
                            {model.name}
                        </h3>

                        {/* 价格信息 */}
                        {(model as any).price > 0 && (
                            <Badge className="bg-yellow-500/90 text-black text-xs font-medium border-0 backdrop-blur-sm">
                                {(model as any).price} Credits
                            </Badge>
                        )}
                    </div>

                    {/* 标签 */}
                    {model.metadata?.tags && (
                        <div className="flex flex-wrap gap-1.5 mt-1">
                            {(model.metadata?.tags || model.tags)?.map((tag: string, index: number) => (
                                <Badge
                                    key={index}
                                    className="bg-white/25 hover:bg-white/35 text-white text-xs font-medium border-0 backdrop-blur-sm px-2 py-0.5"
                                    variant="outline"
                                >
                                    #{tag}
                                </Badge>
                            ))}
                        </div>
                    )}
                </div>

                {/* 悬停叠加层 */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <div className="px-4 py-2 bg-white text-black font-medium rounded-lg" tabIndex={0}>
                        Select
                    </div>
                </div>
            </div>
        </div>
    );
}
