import { IsNotEmpty, IsString, IsArray, ArrayNotEmpty, IsOptional } from 'class-validator';

/**
 * 创建标签DTO
 */
export class CreateTagDto {
    /**
     * 标签名称
     */
    @IsNotEmpty()
    @IsString()
    name: string;
}

/**
 * 为视频添加标签DTO
 */
export class AddVideoTagsDto {
    /**
     * 视频生成的提示词
     */
    @IsNotEmpty()
    @IsString()
    prompt: string;
}

/**
 * 为模型添加标签DTO
 */
export class AddModelTagsDto {
    /**
     * 标签数组
     */
    @IsArray()
    @ArrayNotEmpty()
    @IsString({ each: true })
    tags: string[];
} 