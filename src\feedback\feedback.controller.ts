import {
    Controller,
    Post,
    Body,
    Req,
    UseGuards,
    BadRequestException
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { FeedbackService } from './feedback.service';
import {
    CreateFeedbackDto,
    FeedbackResponseDto
} from './dto/feedback.dto';
import { JwtGuard } from '../common/guards/jwt.guard';
import { Request } from 'express';

@ApiTags('Feedback')
@Controller('feedback')
export class FeedbackController {
    constructor(private readonly feedbackService: FeedbackService) {}

    /**
     * 创建反馈 - 超级简化版
     */
    @Post()
    @UseGuards(JwtGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: '快速提交反馈' })
    @ApiResponse({
        status: 201,
        description: '反馈提交成功',
        type: FeedbackResponseDto
    })
    @ApiResponse({ status: 400, description: '请求参数错误' })
    @ApiResponse({ status: 401, description: '未授权' })
    async createFeedback(
        @Req() req: Request,
        @Body() createFeedbackDto: CreateFeedbackDto,
    ): Promise<FeedbackResponseDto> {
        const userId = req.user?.id;
        if (!userId) {
            throw new BadRequestException('Invalid user');
        }

        return this.feedbackService.createFeedback(userId, createFeedbackDto);
    }
}
