import { create } from 'zustand';
import { TABS } from '../constants';

export type TabType = 'image-to-video' | 'text-to-video' | 'video-to-video' | 'effect';

interface TabControlState {
    activeTab: string;
    setActiveTab: (tab: string) => void;
}

const useTabControlStore = create<TabControlState>((set) => ({
    activeTab: TABS.IMAGE_TO_VIDEO, // 默认选择图片到视频选项卡
    setActiveTab: (tab: string) => set({ activeTab: tab }),
}));

export default useTabControlStore;