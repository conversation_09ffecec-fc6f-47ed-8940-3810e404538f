import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { SUPABASE_CLIENT } from '../providers/supabase.provider';
import { SupabaseClient } from '@supabase/supabase-js';
import { CustomLogger } from '../services/logger.service';

@Injectable()
export class JwtGuard implements CanActivate {
    private guardLogger: CustomLogger;

    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {
        this.guardLogger = this.logger.createLoggerWithContext(JwtGuard.name);
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();

        try {
            // 从请求头中获取JWT
            const token = this.extractTokenFromHeader(request);
            if (!token) {
                throw new UnauthorizedException('缺少访问令牌');
            }

            // 使用Supabase验证JWT
            const { data, error } = await this.supabase.auth.getUser(token);

            if (error || !data?.user) {
                this.guardLogger.error('JWT验证失败', error?.message);
                throw new UnauthorizedException('无效的访问令牌');
            }

            // 将用户信息附加到请求对象上
            request.user = data.user;

            return true;
        } catch (error) {
            this.guardLogger.error('JWT验证异常', error?.message);
            throw new UnauthorizedException('认证失败');
        }
    }

    private extractTokenFromHeader(request: Request): string | undefined {
        const authHeader = request.headers['authorization'];
        if (!authHeader) return undefined;

        const [type, token] = authHeader.split(' ');
        return type === 'Bearer' ? token : undefined;
    }
} 