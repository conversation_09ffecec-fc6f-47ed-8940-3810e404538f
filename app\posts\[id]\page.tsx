import { Metadata, ResolvingMetadata } from "next"
import { postApi } from "@/lib/api/post"
import { ServerVideoDetail } from "../components/ServerVideoDetail"

// SEO动态元数据生成
export async function generateMetadata(
    { params }: { params: { id: string } },
    parent: ResolvingMetadata
): Promise<Metadata> {
    // 确保先await params
    const resolvedParams = await Promise.resolve(params);
    const videoId = resolvedParams.id

    try {
        const { data } = await postApi.getPost(videoId)
        const previousImages = (await parent).openGraph?.images || []
        const videoImageUrl = data.videos?.cover_img

        return {
            title: `${data.title || 'Video'} | ReelMind`,
            keywords: data.videos?.input_params.prompt || 'ReelMind',
            description: data.description || 'Watch this creative AI generated video on ReelMind',
            openGraph: {
                title: data.title + ' - ReelMind Video',
                description: data.description || 'Watch this creative AI generated video on ReelMind',
                url: `https://reelmind.ai/posts/${videoId}`,
                type: 'video.other',
                images: videoImageUrl ? [videoImageUrl, ...previousImages] : previousImages,
                videos: data.video_url ? [{ url: data.video_url, width: 1280, height: 720 }] : undefined,
            },
            twitter: {
                card: 'summary_large_image',
                title: data.title || 'ReelMind Video',
                description: data.description || 'Watch this creative AI generated video on ReelMind',
                images: videoImageUrl ? [videoImageUrl] : undefined,
            },
            alternates: {
                canonical: `https://reelmind.ai/posts/${videoId}`,
            },
            other: {
                "og:video": data.video_url,
                "og:video:secure_url": data.video_url?.replace("http:", "https:"),
                "og:video:type": "video/mp4",
                "og:video:width": "1080",
                "og:video:height": "1920",
                "twitter:player": `https://reelmind.ai/posts/embed/${videoId}`,
                "twitter:player:width": "1080",
                "twitter:player:height": "1920",
            }
        }
    } catch (error) {
        return {
            title: 'Video Not Found | ReelMind',
            description: 'The requested video could not be found',
        }
    }
}

// 数据获取函数
async function fetchPostData(videoId: string) {
    try {
        const [postResponse, commentsResponse] = await Promise.all([
            postApi.getPost(videoId),
            postApi.getPostComments(videoId)
        ])

        return {
            post: postResponse.data,
            comments: commentsResponse.data.comments || [],
            error: null
        }
    } catch (error) {
        console.error('Failed to fetch post data:', error)
        return {
            post: null,
            comments: [],
            error: 'Failed to load video'
        }
    }
}

// 页面组件
export default async function PostDetailPage({ params }: { params: { id: string } }) {
    // 确保先await params
    const resolvedParams = await Promise.resolve(params);
    const videoId = resolvedParams.id

    const { post, comments, error } = await fetchPostData(videoId)

    // 错误状态显示
    if (error || !post) {
        return (
            <main className="min-h-screen bg-gradient-to-br from-slate-50 via-slate-100 to-slate-200 dark:from-slate-900 dark:via-slate-800 dark:to-slate-950 flex items-center justify-center p-4">
                <article className="max-w-md w-full bg-white dark:bg-slate-800 rounded-2xl shadow-lg overflow-hidden border border-slate-200/50 dark:border-slate-700/50">
                    <header className="p-6 border-b border-slate-200/50 dark:border-slate-700/50">
                        <h2 className="text-xl font-semibold text-red-500 dark:text-red-400 mb-2">
                            Load Video Failed
                        </h2>
                        <p className="text-slate-600 dark:text-slate-300">
                            {error || "The requested video cannot be found or is currently unavailable."}
                        </p>
                    </header>
                    <footer className="p-4 bg-slate-50 dark:bg-slate-800/50 flex justify-end">
                        <button
                            onClick={() => window.history.back()}
                            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors shadow-sm"
                        >
                            Back
                        </button>
                    </footer>
                </article>
            </main>
        )
    }

    return <ServerVideoDetail post={post} comments={comments} />
}

