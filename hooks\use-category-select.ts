import { useCallback } from 'react';
import useFeedStore from '@/store/useFeedStore';

/**
 * 自定义hook，用于处理分类选择
 * 封装了分类选择的状态和操作，避免在组件中直接管理状态
 */
export function useCategorySelect() {
    const { setCategory, selectedCategoryId } = useFeedStore();

    // 处理分类选择的回调函数
    const handleCategorySelect = useCallback(
        (categoryId: string) => {
            setCategory(categoryId);
        },
        [setCategory]
    );

    return {
        selectedCategoryId,
        handleCategorySelect,
    };
} 