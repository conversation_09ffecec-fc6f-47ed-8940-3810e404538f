import { IsNumber, IsOptional, IsUUID, IsDate, IsBoolean } from 'class-validator';

/**
 * 队列信息响应DTO
 */
export class QueueInfoResponseDto {
    @IsUUID()
    task_id: string;

    @IsNumber()
    queue_position: number;

    @IsNumber()
    total_tasks_in_queue: number;

    @IsNumber()
    @IsOptional()
    estimated_wait_time_seconds?: number;

    @IsDate()
    @IsOptional()
    estimated_start_time?: Date;

    @IsDate()
    @IsOptional()
    estimated_completion_time?: Date;

    @IsBoolean()
    @IsOptional()
    is_processing?: boolean;

    @IsNumber()
    @IsOptional()
    elapsed_time_seconds?: number;

    @IsNumber()
    @IsOptional()
    remaining_time_seconds?: number;
}
