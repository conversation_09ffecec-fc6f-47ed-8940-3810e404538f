import { create } from 'zustand';

interface LoginAction {
    type: string;
    payload?: any;
    onSuccess?: () => void;
}

interface LoginDialogState {
    // 状态
    isOpen: boolean;
    pendingAction: LoginAction | null;
    referrer: string | null;

    // 操作
    openLoginDialog: (options?: { pendingAction?: LoginAction, referrer?: string }) => void;
    closeLoginDialog: () => void;
    setPendingAction: (action: LoginAction | null) => void;
    executePendingAction: () => void;
    resetState: () => void;
}

const useLoginDialogStore = create<LoginDialogState>((set, get) => ({
    // 初始状态
    isOpen: false,
    pendingAction: null,
    referrer: null,

    // 打开登录弹窗
    openLoginDialog: (options = {}) => set({
        isOpen: true,
        pendingAction: options.pendingAction || null,
        referrer: options.referrer || null
    }),

    // 关闭登录弹窗
    closeLoginDialog: () => set({
        isOpen: false
    }),

    // 设置待处理操作
    setPendingAction: (action) => set({
        pendingAction: action
    }),

    // 执行待处理操作
    executePendingAction: () => {
        const { pendingAction } = get();
        if (pendingAction?.onSuccess) {
            pendingAction.onSuccess();
        }
        set({ pendingAction: null });
    },

    // 重置状态
    resetState: () => set({
        isOpen: false,
        pendingAction: null,
        referrer: null
    })
}));

export default useLoginDialogStore; 