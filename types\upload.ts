import { GenerationType } from "./video";

export type VideoSource = "fal.ai" | "reelmind";

export interface UploadMetadata {
    title: string;
    description: string;
    tags: string[];
    prompt: string;
    negativePrompt: string;
    guidanceScale: string;
    steps: string;
    seed: string;
    generationType: GenerationType;
    modelResources: string;
    selectedTools: string[];
    videoSource: VideoSource;
}

export interface UploadState {
    videoFile: File | null;
    videoPreviewUrl: string | null;
    uploadProgress: number;
    isUploading: boolean;
    uploadComplete: boolean;
    uploadError: string | null;
}

export interface UploadFormState extends UploadMetadata {
    isPublishing: boolean;
}

// 生图工具列表
export const GENERATION_TOOLS = [
    "Kling", "MiniMax / Hailuo", "Hedra", "Realdreams", "neural frames",
    "Comfy Community Summit / Way To AGI", "Tripo 3D", "LTX Studio", "Haiper",
    "Mochi", "CogVideoX", "Machine Cinema", "zero1cine", "SAGA", "Domo AI",
    "Morph Studio", "Banodoco", "10zebra", "AI Korea Community", "AICU",
    "AIHUB", "AnimateDiff", "Artlist", "Boximator", "DeepMake", "Deforum Studio",
    "Dreamina", "EBSynth", "Eden.art", "Escape.ai", "Fable",
    "Filamen & Zhonk Vision", "Genmo", "Gooey AI", "HunYuan", "iKHOR Labs",
    "Imagine.art", "Invideo", "Kaiber", "LensGo", "Lightricks LTXV",
    "Luma Dream Machine", "Luma Genie", "Magic Animate", "Mago", "Nim",
    "Olivio Sarikas", "Parseq", "Pika", "Pixverse", "Prism", "Ray 2",
    "Runway", "SadTalker", "Showrunner AI", "Silmu", "Sora", "Stable Artisan",
    "Storyteller.ai", "Toon Crafter", "Veo", "VidProc", "Vidu", "Vimeo",
    "Voia", "Warp Video"
]; 