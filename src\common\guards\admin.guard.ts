import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../providers/supabase.provider';
import { Inject } from '@nestjs/common';
import { CustomLogger } from '../services/logger.service';

@Injectable()
export class AdminGuard implements CanActivate {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {
        this.logger.setContext(AdminGuard.name);
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);

        if (!token) {
            throw new UnauthorizedException('未提供认证令牌');
        }

        try {
            // 设置Supabase客户端的认证令牌
            this.supabase.auth.setSession({
                access_token: token,
                refresh_token: '',
            });

            // 获取当前用户
            const { data: { user }, error: userError } = await this.supabase.auth.getUser();

            if (userError || !user) {
                this.logger.error(`获取用户信息失败: ${userError?.message}`, userError);
                throw new UnauthorizedException('无效的认证令牌');
            }

            // 检查用户是否为管理员
            const { data: roleData, error: roleError } = await this.supabase
                .from('user_roles')
                .select('role')
                .eq('user_id', user.id)
                .eq('role', 'admin')
                .single();

            if (roleError || !roleData) {
                this.logger.warn(`用户 ${user.id} 尝试访问管理员资源但没有权限`);
                throw new UnauthorizedException('您没有管理员权限');
            }

            // 将用户信息添加到请求对象中
            request.user = user;
            return true;
        } catch (error) {
            this.logger.error(`管理员认证失败: ${error.message}`, error);
            throw new UnauthorizedException('认证失败');
        }
    }

    private extractTokenFromHeader(request: any): string | undefined {
        const [type, token] = request.headers.authorization?.split(' ') ?? [];
        return type === 'Bearer' ? token : undefined;
    }
} 