import { Logger } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { ICreditStrategy } from './credit-strategy.interface';
import { CreditTransactionStatus } from '../constant';

/**
 * 积分策略基类
 * 
 * 提供积分策略的通用实现，具体策略可以继承此类并重写特定方法
 */
export abstract class BaseCreditStrategy implements ICreditStrategy {
    protected readonly logger = new Logger(this.constructor.name);
    
    /**
     * 构造函数
     * @param supabase Supabase客户端
     * @param type 策略类型
     */
    constructor(
        protected readonly supabase: SupabaseClient,
        public readonly type: string
    ) {}
    
    /**
     * 计算应发放的积分数量
     * 子类必须实现此方法
     */
    abstract calculateAmount(userId: string, params?: any): Promise<number>;
    
    /**
     * 检查是否可以发放积分
     * 默认实现，子类可以重写
     */
    async canGrant(userId: string, params?: any): Promise<boolean> {
        try {
            // 检查用户是否存在
            const { data, error } = await this.supabase
                .from('auth.users')
                .select('id')
                .eq('id', userId)
                .single();
                
            if (error || !data) {
                this.logger.warn(`用户 ${userId} 不存在，无法发放积分`);
                return false;
            }
            
            return true;
        } catch (error) {
            this.logger.error(`检查用户是否可以发放积分时出错: ${error.message}`, error.stack);
            return false;
        }
    }
    
    /**
     * 执行积分发放
     * 子类可以重写此方法以提供特定的发放逻辑
     */
    async execute(userId: string, params?: any): Promise<any> {
        try {
            // 检查是否可以发放积分
            const canGrant = await this.canGrant(userId, params);
            if (!canGrant) {
                return {
                    success: false,
                    message: '不满足发放条件'
                };
            }
            
            // 计算积分数量
            const amount = await this.calculateAmount(userId, params);
            if (amount <= 0) {
                this.logger.log(`用户 ${userId} 计算得到的积分为 ${amount}，不执行发放`);
                return {
                    success: false,
                    message: '计算得到的积分数量不大于0'
                };
            }
            
            // 执行积分发放
            const { data, error } = await this.supabase.rpc('add_credits', {
                p_user_id: userId,
                p_amount: amount,
                p_type: this.type,
                p_description: params?.description || `${this.type} 类型积分发放 ${amount} 积分`,
                p_payment_id: params?.paymentId || null,
                p_status: CreditTransactionStatus.COMPLETED
            });
            
            if (error) {
                this.logger.error(`发放积分失败: ${error.message}`, error.stack);
                throw error;
            }
            
            this.logger.log(`成功为用户 ${userId} 发放 ${this.type} 类型积分 ${amount} 点`);
            
            return {
                success: true,
                transaction: data.transaction,
                newBalance: data.new_balance,
                amount
            };
        } catch (error) {
            this.logger.error(`执行积分发放失败: ${error.message}`, error.stack);
            throw error;
        }
    }
}
