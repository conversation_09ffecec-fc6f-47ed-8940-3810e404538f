"use client"

import { <PERSON><PERSON><PERSON><PERSON>, Spark<PERSON> } from "lucide-react"
import { useEffect, useRef } from "react"

interface ComingSoonOverlayProps {
  title?: string
  description?: string
  badgeText?: string
}

export function ComingSoonOverlay({
  title = "Coming Soon",
  description = "We're preparing something amazing for you. Stay tuned!",
  badgeText = "Grand Opening"
}: ComingSoonOverlayProps) {
  const overlayRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Add this class to prevent body scrolling when overlay is active
    document.body.classList.add('overflow-hidden')

    // 使用自定义滚动函数实现更慢的滚动
    const slowScroll = () => {
      const scrollHeight = document.documentElement.scrollHeight
      const targetPosition = scrollHeight * 0.4 // 滚动到页面的40%位置
      const startPosition = window.pageYOffset
      const distance = targetPosition - startPosition
      const duration = 3000 // 3秒的滚动时间，比默认慢约三倍
      let startTime: number | null = null

      // 动画函数
      const animation = (currentTime: number) => {
        if (startTime === null) startTime = currentTime
        const timeElapsed = currentTime - startTime
        const progress = Math.min(timeElapsed / duration, 1)

        // 使用缓动函数使滚动更平滑
        const easeInOutQuad = (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t

        window.scrollTo(0, startPosition + distance * easeInOutQuad(progress))

        if (timeElapsed < duration) {
          requestAnimationFrame(animation)
        }
      }

      // 开始动画
      requestAnimationFrame(animation)
    }

    // 延迟执行滚动，确保页面已完全加载
    const timer = setTimeout(slowScroll, 800)

    return () => {
      document.body.classList.remove('overflow-hidden')
      clearTimeout(timer)
    }
  }, []) // 空依赖数组确保只执行一次

  return (
    <div
      ref={overlayRef}
      className="fixed top-0 bottom-16 lg:bottom-0 right-0 left-0 lg:left-56 z-[100] backdrop-blur-sm bg-background/50 flex flex-col items-center justify-center overflow-hidden px-6 lg:px-0"
    >
      {/* Transparent Peek-through Areas */}
      <div className="absolute top-[20%] left-[15%] w-32 h-32 rounded-full bg-transparent"></div>
      <div className="absolute bottom-[30%] right-[20%] w-40 h-40 rounded-full bg-transparent"></div>
      <div className="absolute top-[40%] right-[30%] w-24 h-24 rounded-full bg-transparent"></div>

      <div className="relative max-w-md w-full mx-auto px-5 sm:px-8 py-8 sm:py-12 rounded-2xl bg-gradient-to-b from-background/70 to-background/30 border border-primary/20 shadow-xl backdrop-blur-md md:max-w-lg">
        {/* Confetti and Ribbons */}
        <div className="absolute -top-10 -left-10">
          <div className="w-20 h-20 bg-gradient-to-br from-pink-500 to-purple-500 opacity-70 rounded-full blur-xl"></div>
        </div>
        <div className="absolute -bottom-10 -right-10">
          <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-500 opacity-70 rounded-full blur-xl"></div>
        </div>

        {/* Ribbons */}
        <div className="absolute -top-4 -left-4 w-8 h-32 bg-gradient-to-b from-yellow-400 to-orange-500 rotate-45 opacity-80"></div>
        <div className="absolute -top-4 -right-4 w-8 h-32 bg-gradient-to-b from-green-400 to-emerald-500 -rotate-45 opacity-80"></div>

        {/* Content */}
        <div className="text-center relative z-10">
          <div className="inline-flex items-center justify-center mb-4">
            <PartyPopper className="h-8 w-8 text-amber-500 mr-2" />
            <Sparkles className="h-8 w-8 text-primary-foreground" />
          </div>
          <h2 className="text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-500">
            {title}
          </h2>
          <p className="text-lg text-muted-foreground mb-6">
            {description}
          </p>
          <div className="inline-block relative">
            <div className="px-4 py-2 rounded-full bg-primary/10 text-primary-foreground font-medium">
              {badgeText}
            </div>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full animate-ping"></div>
          </div>
        </div>
      </div>

      {/* Additional Confetti for Festive Look */}
      {Array.from({ length: 20 }).map((_, index) => (
        <div key={`confetti-${index}`} className={`absolute top-0 left-[${(index * 5) % 100}%] w-3 h-8 bg-${['red', 'green', 'blue', 'purple', 'yellow', 'pink', 'cyan', 'amber', 'emerald', 'indigo'][index % 10]}-400 animate-fall-${['slow', 'medium', 'fast'][index % 3]}`}></div>
      ))}

      {/* Floating Ribbons */}
      {Array.from({ length: 12 }).map((_, index) => (
        <div key={`ribbon-${index}`} className={`absolute ${['top-[15%]', 'top-[25%]', 'top-[35%]', 'top-[45%]', 'top-[55%]', 'top-[65%]', 'bottom-[15%]', 'bottom-[25%]', 'bottom-[35%]', 'bottom-[45%]', 'bottom-[55%]', 'bottom-[65%]'][index]
          } ${['left-[15%]', 'left-[25%]', 'left-[35%]', 'left-[45%]', 'left-[55%]', 'left-[65%]', 'left-[75%]', 'right-[15%]', 'right-[25%]', 'right-[35%]', 'right-[45%]', 'right-[55%]'][index]
          } w-${[16, 20, 24, 28][index % 4]} h-3 bg-gradient-to-r from-${['pink', 'blue', 'amber', 'green', 'purple', 'red', 'cyan', 'indigo', 'emerald', 'rose', 'violet', 'teal'][index]
          }-500 to-${['purple', 'cyan', 'orange', 'emerald', 'pink', 'amber', 'blue', 'violet', 'lime', 'fuchsia', 'sky', 'yellow'][index]
          }-500 rotate-${[-45, -30, -15, 0, 15, 30, 45][index % 7]} opacity-60 animate-float-${['slow', 'medium', 'fast'][index % 3]}`}></div>
      ))}

      {/* Ribbon Explosions */}
      {Array.from({ length: 8 }).map((_, explosionIndex) => (
        <div key={`explosion-${explosionIndex}`} className={`absolute ${['top-[20%] left-[30%]', 'top-[40%] left-[70%]', 'top-[60%] left-[20%]', 'top-[30%] left-[80%]',
          'top-[70%] left-[40%]', 'top-[50%] left-[60%]', 'top-[25%] left-[50%]', 'top-[65%] left-[75%]'][explosionIndex]
          }`}>
          <div className="ribbon-explosion" style={{ animationDelay: `${explosionIndex * 1.5}s` }}>
            {Array.from({ length: 12 }).map((_, i) => (
              <div key={i} className={`ribbon-strand bg-gradient-to-r from-${['pink', 'blue', 'yellow', 'green', 'purple', 'red', 'indigo', 'amber', 'cyan', 'rose', 'emerald', 'violet'][i]
                }-500 to-${['purple', 'cyan', 'orange', 'emerald', 'pink', 'amber', 'blue', 'violet', 'lime', 'fuchsia', 'sky', 'yellow'][i]
                }-500 animate-ribbon-explosion-${i + 1}`}></div>
            ))}
          </div>
        </div>
      ))}

      {/* Spiral Ribbons */}
      {Array.from({ length: 6 }).map((_, spiralIndex) => (
        <div key={`spiral-${spiralIndex}`} className={`absolute ${['top-[35%] left-[25%]', 'top-[45%] left-[75%]', 'top-[65%] left-[35%]',
          'top-[25%] left-[65%]', 'top-[55%] left-[45%]', 'top-[75%] left-[55%]'][spiralIndex]
          }`}>
          <div className="ribbon-spiral" style={{ animationDelay: `${spiralIndex * 2}s` }}>
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className={`ribbon-spiral-strand bg-gradient-to-r from-${['pink', 'blue', 'yellow', 'green', 'purple', 'red', 'indigo', 'amber'][i]
                }-400 to-${['purple', 'cyan', 'orange', 'emerald', 'pink', 'amber', 'blue', 'violet'][i]
                }-400 animate-ribbon-spiral-${i + 1}`}></div>
            ))}
          </div>
        </div>
      ))}

      {/* Enhanced Firework Confetti Effect */}
      <div className="firework-container">
        {/* Original First Burst */}
        <div className="absolute top-[20%] left-[30%]">
          <div className="firework-burst">
            {Array.from({ length: 12 }).map((_, i) => (
              <div key={i} className={`firework-particle bg-${['pink', 'blue', 'yellow', 'green', 'purple', 'red', 'indigo', 'amber', 'cyan', 'rose', 'emerald', 'violet'][i]}-500 animate-firework-${i + 1}`}></div>
            ))}
          </div>
        </div>

        {/* Additional Burst Sets */}
        {Array.from({ length: 8 }).map((_, setIndex) => (
          <div key={`set-${setIndex}`} className={`absolute ${['top-[15%] left-[20%]', 'top-[25%] left-[80%]', 'top-[40%] left-[50%]', 'top-[60%] left-[30%]',
            'top-[35%] left-[70%]', 'top-[45%] left-[25%]', 'top-[55%] left-[75%]', 'top-[30%] left-[40%]'][setIndex]
            }`}>
            <div className="firework-burst" style={{ animationDelay: `${setIndex * 0.8}s` }}>
              {Array.from({ length: 12 }).map((_, i) => (
                <div key={i} className={`firework-particle bg-${['pink', 'blue', 'yellow', 'green', 'purple', 'red', 'indigo', 'amber', 'cyan', 'rose', 'emerald', 'violet'][i]}-500 animate-firework-${i + 1}`}></div>
              ))}
            </div>
          </div>
        ))}

        {/* Large Firework Bursts */}
        {Array.from({ length: 6 }).map((_, setIndex) => (
          <div key={`large-${setIndex}`} className={`absolute ${['top-[30%] left-[50%]', 'top-[20%] left-[70%]', 'top-[40%] left-[30%]',
            'top-[50%] left-[60%]', 'top-[25%] left-[40%]', 'top-[35%] left-[80%]'][setIndex]
            }`}>
            <div className="firework-burst firework-large" style={{ animationDelay: `${setIndex * 1.2 + 0.5}s` }}>
              {Array.from({ length: 16 }).map((_, i) => (
                <div key={i} className={`firework-particle bg-${['red', 'orange', 'yellow', 'green', 'blue', 'indigo', 'violet', 'pink', 'purple', 'cyan', 'amber', 'emerald', 'rose', 'fuchsia', 'lime', 'teal'][i]}-500 animate-firework-large-${i + 1}`}></div>
              ))}
            </div>
          </div>
        ))}

        {/* Small Quick Fireworks */}
        {Array.from({ length: 12 }).map((_, setIndex) => (
          <div key={`small-${setIndex}`} className={`absolute ${['top-[15%] left-[15%]', 'top-[25%] left-[85%]', 'top-[35%] left-[25%]', 'top-[45%] left-[75%]',
            'top-[55%] left-[35%]', 'top-[20%] left-[65%]', 'top-[30%] left-[45%]', 'top-[40%] left-[85%]',
            'top-[50%] left-[25%]', 'top-[15%] left-[55%]', 'top-[25%] left-[35%]', 'top-[35%] left-[65%]'][setIndex]
            }`}>
            <div className="firework-burst firework-small" style={{ animationDelay: `${setIndex * 0.4 + 1}s`, animationDuration: "3s" }}>
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className={`firework-particle bg-${['pink', 'blue', 'green', 'yellow', 'purple', 'cyan'][i]}-300 animate-firework-small-${i + 1}`}></div>
              ))}
            </div>
          </div>
        ))}

        {/* Sparkle Effects */}
        {Array.from({ length: 10 }).map((_, setIndex) => (
          <div key={`sparkle-${setIndex}`} className={`absolute ${['top-[70%] left-[60%]', 'top-[65%] left-[30%]', 'top-[75%] left-[45%]', 'top-[60%] left-[75%]',
            'top-[80%] left-[25%]', 'top-[55%] left-[65%]', 'top-[85%] left-[40%]', 'top-[50%] left-[80%]',
            'top-[45%] left-[35%]', 'top-[40%] left-[70%]'][setIndex]
            }`}>
            <div className="sparkle-container" style={{ animationDelay: `${setIndex * 0.5}s` }}>
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className={`sparkle bg-white animate-sparkle-${i + 1}`}></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
} 