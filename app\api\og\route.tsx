import { ImageResponse } from 'next/og'
import { NextRequest } from 'next/server'

export const runtime = 'edge'

export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url)

        // 动态参数
        const title = searchParams.get('title') || 'ReelMind - Open Source AI Video Models Community'
        const description = searchParams.get('description') || 'The platform for AI creators to generate and share AIGC videos'
        const copyright = `ReelMind © ${new Date().getFullYear()}`

        // 构建响应
        return new ImageResponse(
            (
                <div
                    style={{
                        height: '100%',
                        width: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#111111',
                        color: 'white',
                        padding: '50px',
                        position: 'relative',
                    }}
                >
                    {/* 渐变背景 */}
                    <div
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            background: 'radial-gradient(circle at top right, rgba(67, 56, 202, 0.4), transparent 70%)',
                            zIndex: 0,
                        }}
                    />

                    {/* 内容区域 */}
                    <div style={{ zIndex: 1, maxWidth: '90%', textAlign: 'center' }}>
                        <h1 style={{ fontSize: 70, fontWeight: 'bold', marginBottom: 20 }}>
                            {title}
                        </h1>
                        <p style={{ fontSize: 36, opacity: 0.9 }}>
                            {description}
                        </p>
                    </div>

                    {/* 底部徽标 */}
                    <div
                        style={{
                            position: 'absolute',
                            bottom: 50,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: 28,
                            opacity: 0.8,
                        }}
                    >
                        {copyright}
                    </div>
                </div>
            ),
            {
                width: 1200,
                height: 630,
            }
        )
    } catch (error) {
        console.error(error)
        return new Response('Failed to generate OG image', { status: 500 })
    }
} 