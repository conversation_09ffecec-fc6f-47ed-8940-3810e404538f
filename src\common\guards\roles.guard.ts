import { Injectable, CanActivate, ExecutionContext, ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { Inject } from '@nestjs/common';
import { SUPABASE_CLIENT } from '../providers/supabase.provider';
import { SupabaseClient } from '@supabase/supabase-js';
import { CustomLogger } from '../services/logger.service';

@Injectable()
export class RolesGuard implements CanActivate {
    private guardLogger: CustomLogger;

    constructor(
        private reflector: Reflector,
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {
        this.guardLogger = this.logger.createLoggerWithContext(RolesGuard.name);
    }

    private extractTokenFromHeader(request: Request): string | undefined {
        const authHeader = request.headers['authorization'];
        if (!authHeader) return undefined;

        const [type, token] = authHeader.split(' ');
        return type === 'Bearer' ? token : undefined;
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        if (!requiredRoles) {
            // 如果没有设置角色要求，则允许访问
            return true;
        }

        const request = context.switchToHttp().getRequest();
        // 从请求头中获取JWT
        const token = this.extractTokenFromHeader(request);
        if (!token) {
            throw new UnauthorizedException('缺少访问令牌');
        }
        // 使用Supabase验证JWT
        const { data, error } = await this.supabase.auth.getUser(token);

        if (error || !data?.user) {
            this.guardLogger.error('JWT验证失败', error?.message);
            throw new UnauthorizedException('无效的访问令牌');
        }

        // 将用户信息附加到请求对象上
        request.user = data.user;

        // 检查用户角色
        try {
            const { data: userRolesData, error } = await this.supabase
                .from('user_roles')
                .select('role')
                .eq('user_id', data.user.id);

            if (error) {
                this.guardLogger.error('获取用户角色失败', error.message);
                return false;
            }

            // 如果没有角色记录，用户不是管理员
            if (!userRolesData || userRolesData.length === 0) {
                return false;
            }

            // 检查用户是否拥有所需角色
            const userRoles = userRolesData.map(r => r.role);
            return requiredRoles.some(role => userRoles.includes(role));
        } catch (error) {
            this.guardLogger.error('角色验证异常', error.message);
            return false;
        }
    }
} 