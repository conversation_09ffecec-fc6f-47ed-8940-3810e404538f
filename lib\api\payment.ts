import { apiClient, ApiResponse } from './client';
import { API_CONFIG } from '@/lib/config';

export interface CreateMembershipPaymentRequest {
    price_id: string; // 会员计划ID，替代之前的level
    billing_cycle: string; // 计费周期
    coupon_id?: string; // 优惠券ID（可选）
}

export interface StripePaymentResponse {
    payment: {
        id: string; // 支付记录ID
        user_id: string;
        type: string;
        amount: number; // 支付金额
        status: string; // 支付状态
        // 其他支付信息...
    };
    checkoutUrl: string; // Stripe结账页面URL
}

/**
 * 支付系统API
 */
export const paymentApi = {
    /**
     * 创建支付订单
     */
    createMembershipPayment: async (data: CreateMembershipPaymentRequest): Promise<StripePaymentResponse> => {
        try {
            console.log('发送Stripe支付请求:', data);

            const response = await apiClient.post<ApiResponse<StripePaymentResponse>>(
                API_CONFIG.ENDPOINTS.MEMBERSHIP.SUBSCRIBE_MEMBERSHIP,
                data
            );

            console.log('Stripe支付响应:', response.data);
            return response.data;
        } catch (error) {
            console.error('Stripe支付请求失败:', error);
            throw error;
        }
    },
    cancelPayment: async (paymentId: string): Promise<void> => {
        try {
            await apiClient.post(API_CONFIG.ENDPOINTS.PAYMENT.CANCEL_PAYMENT, {
                payment_id: paymentId
            });
        } catch (error) {
            console.error('取消支付失败:', error);
            throw error;
        }
    }
};