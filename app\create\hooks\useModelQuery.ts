import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { modelApi } from '@/lib/api/model';
import type { Model } from '@/types/model';
import { useMemo } from 'react';

// 准备模型数据，适配前端组件需要的格式
const prepareModelData = (model: Model): Model => {
    return {
        ...model,
        // 使用默认封面图片替代null值
        cover_img: model.cover_img || '/placeholder.svg?height=400&width=300&text=Model',
        // 将model_type复制到type属性，并转为小写(兼容前端现有代码)
        type: model.model_type.toLowerCase(),
        // 为新模型添加标签
        tags: ['AI'],
    };
};

// 获取单个模型的查询hook
export const useModelById = (modelId: string | null) => {
    return useQuery({
        queryKey: ['model', modelId],
        queryFn: async () => {
            if (!modelId) {
                throw new Error('模型ID不能为空');
            }
            const model = await modelApi.getModelById(modelId);
            return prepareModelData(model);
        },
        enabled: !!modelId,
        staleTime: 1000 * 60 * 5, // 5分钟
    });
};

// 获取分页模型列表的查询hook
export const useInfiniteModels = (limit: number = 20) => {
    return useInfiniteQuery({
        queryKey: ['models', 'infinite', limit],
        queryFn: async ({ pageParam = 1 }) => {
            const modelData = await modelApi.getModels(pageParam, limit);
            return {
                models: modelData.models.map(prepareModelData),
                total: modelData.total,
                nextPage: pageParam + 1,
                hasMore: pageParam * limit < modelData.total
            };
        },
        initialPageParam: 1,
        getNextPageParam: (lastPage) => {
            return lastPage.hasMore ? lastPage.nextPage : undefined;
        },
        staleTime: 1000 * 60 * 5, // 5分钟
    });
};

// 按类型获取分页模型列表的查询hook
export const useInfiniteModelsByType = (type: string, limit: number = 20) => {
    return useInfiniteQuery({
        queryKey: ['models', 'by-type', type, limit],
        queryFn: async ({ pageParam = 1 }) => {
            // 如果没有指定类型，则获取所有模型
            if (!type) {
                const modelData = await modelApi.getModels(pageParam, limit);
                return {
                    models: modelData.models.map(prepareModelData),
                    total: modelData.total,
                    nextPage: pageParam + 1,
                    hasMore: pageParam * limit < modelData.total
                };
            }

            // 否则按类型获取模型
            const modelData = await modelApi.searchModels('', { type: [type] }, pageParam, limit);
            return {
                models: modelData.models.map(prepareModelData),
                total: modelData.total,
                nextPage: pageParam + 1,
                hasMore: pageParam * limit < modelData.total
            };
        },
        initialPageParam: 1,
        getNextPageParam: (lastPage) => {
            return lastPage.hasMore ? lastPage.nextPage : undefined;
        },
        staleTime: 1000 * 60 * 5, // 5分钟
        enabled: true, // 始终启用查询，即使type为空
    });
};

// 过滤器类型
export type ModelFilter = {
    type?: string[];
    features?: string[];
    source?: string[];
};

// 本地搜索和过滤模型的hook (仅用于已加载的模型)
export const useLocalSearchModels = (
    searchTerm: string,
    allModels: Model[],
    filters: ModelFilter = {}
) => {
    return useMemo(() => {
        // 首先应用搜索词过滤
        let filteredModels = allModels;

        if (searchTerm.trim()) {
            const lowerSearchTerm = searchTerm.toLowerCase();
            filteredModels = allModels.filter(model =>
                model.name.toLowerCase().includes(lowerSearchTerm) ||
                (model.description && model.description.toLowerCase().includes(lowerSearchTerm)) ||
                (model.type && model.type.toLowerCase().includes(lowerSearchTerm))
            );
        }

        // 然后应用其他过滤器
        if (filters.type && filters.type.length > 0) {
            filteredModels = filteredModels.filter(model => {
                const modelType = model.type?.toLowerCase();
                return modelType && filters.type?.includes(modelType);
            });
        }

        if (filters.features && filters.features.length > 0) {
            filteredModels = filteredModels.filter(model => {
                if (!model.supported_features) return false;
                return filters.features?.some(feature =>
                    model.supported_features?.includes(feature)
                ) || false;
            });
        }

        if (filters.source && filters.source.length > 0) {
            filteredModels = filteredModels.filter(model => {
                const modelSource = model.source;
                return modelSource && filters.source?.includes(modelSource);
            });
        }

        return filteredModels;
    }, [searchTerm, allModels, filters]);
};

// 使用后端API搜索模型的hook
export const useServerSearchModels = (
    searchTerm: string,
    filters: ModelFilter = {},
    limit: number = 20,
    enabled: boolean = true
) => {

    // 使用react-query进行搜索
    const {
        data,
        isLoading,
        isError,
        error,
        isFetching,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage
    } = useInfiniteQuery({
        queryKey: ['models', 'search', searchTerm, filters, limit],
        queryFn: async ({ pageParam = 1 }) => {
            const result = await modelApi.searchModels(searchTerm, filters, pageParam, limit);
            return {
                models: result.models.map(prepareModelData),
                total: result.total,
                nextPage: pageParam + 1,
                hasMore: pageParam * limit < result.total
            };
        },
        initialPageParam: 1,
        getNextPageParam: (lastPage) => {
            return lastPage.hasMore ? lastPage.nextPage : undefined;
        },
        enabled: enabled && (!!searchTerm.trim() ||
            (filters.type && filters.type.length > 0) ||
            (filters.features && filters.features.length > 0) ||
            (filters.source && filters.source.length > 0)),
        staleTime: 1000 * 60 * 5, // 5分钟
    });

    // 合并所有页面的模型数据
    const models = useMemo(() => {
        if (!data) return [];
        return data.pages.flatMap(page => page.models);
    }, [data]);

    // 计算总数
    const totalCount = data?.pages[0]?.total || 0;

    return {
        models,
        totalCount,
        isLoading,
        isError,
        error,
        isFetching,
        fetchNextPage,
        hasNextPage: !!hasNextPage,
        isFetchingNextPage
    };
}
