"use client"

import { Search } from "lucide-react"
import { useState } from "react"
import { cn } from "@/lib/utils"

export function SearchBar() {
  const [isFocused, setIsFocused] = useState(false)

  return (
    <div
      className={cn(
        "relative flex-1 max-w-xl transition-all duration-200",
        isFocused ? "scale-[1.01]" : ""
      )}
    >
      <div className={cn(
        "relative flex items-center w-full overflow-hidden rounded-full",
        "transition-all duration-200 ease-in-out",
        "dark:bg-gray-800 backdrop-blur-sm shadow-sm",
        isFocused
          ? "border-primary/50 ring-2 ring-primary/20 shadow-md"
          : "border-gray-200/80 hover:border-gray-300"
      )}>
        <div className="absolute left-3 flex items-center justify-center text-muted-foreground">
          <Search size={18} className="transition-all duration-200" />
        </div>
        {/* <input
          type="text"
          placeholder="Search models/videos/creators and collections"
          className="w-full pl-10 pr-24 py-2.5 bg-transparent text-foreground placeholder:text-gray-400/80 focus:outline-none"
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        /> */}
        <button
          className={cn(
            "absolute right-1.5 top-1/2 -translate-y-1/2 px-5 py-1.5",
            "rounded-full bg-primary hover:bg-primary/90 text-white font-medium",
            "transition-all duration-200 text-sm",
            "shadow-sm hover:shadow"
          )}
        >
          Search
        </button>
      </div>
    </div>
  )
}

