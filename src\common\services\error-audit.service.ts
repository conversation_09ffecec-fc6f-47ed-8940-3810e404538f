import { Injectable, Inject } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../providers/supabase.provider';
import { CustomLogger } from './logger.service';

export enum ErrorSeverity {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical',
}

export enum ErrorCategory {
    PAYMENT = 'payment',
    REFUND = 'refund',
    CREDIT = 'credit',
    TASK = 'task',
    OTHER = 'other',
}

export interface ErrorAuditRecord {
    id?: string;
    user_id?: string;
    task_id?: string;
    category: ErrorCategory;
    severity: ErrorSeverity;
    error_message: string;
    error_details: any;
    context_data: any;
    created_at?: Date;
    resolved?: boolean;
    resolved_at?: Date;
    resolution_notes?: string;
}

@Injectable()
export class ErrorAuditService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {
        this.logger.setContext(ErrorAuditService.name);
        this.initializeErrorAuditTable();
    }

    /**
     * 初始化错误审计表（如果不存在）
     * 注意：这通常应该通过数据库迁移脚本完成，这里仅作为示例
     */
    private async initializeErrorAuditTable(): Promise<void> {
        try {
            // 检查表是否存在
            const { error } = await this.supabase.from('error_audit_logs').select('id').limit(1);

            if (error && error.code === '42P01') { // 表不存在的PostgreSQL错误代码
                this.logger.warn('错误审计表不存在，请通过数据库迁移创建表');
            }
        } catch (error) {
            this.logger.error('检查错误审计表时出错', error);
        }
    }

    /**
     * 记录错误到审计日志
     * @param errorRecord 错误记录
     * @returns 创建的记录ID
     */
    async logError(errorRecord: ErrorAuditRecord): Promise<string | null> {
        try {
            // 添加创建时间
            const recordToInsert = {
                ...errorRecord,
                created_at: new Date(),
                resolved: false,
            };

            const { data, error } = await this.supabase
                .from('error_audit_logs')
                .insert(recordToInsert)
                .select('id')
                .single();

            if (error) {
                this.logger.error(`记录错误到审计日志失败: ${error.message}`, error);
                return null;
            }

            this.logger.log(`已记录错误到审计日志，ID: ${data.id}, 类别: ${errorRecord.category}, 严重性: ${errorRecord.severity}`);
            return data.id;
        } catch (error) {
            this.logger.error(`记录错误到审计日志时发生异常: ${error.message}`, error);
            return null;
        }
    }

    /**
     * 记录支付相关错误
     * @param userId 用户ID
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     * @param contextData 上下文数据
     * @param severity 严重性
     * @returns 创建的记录ID
     */
    async logPaymentError(
        userId: string,
        errorMessage: string,
        errorDetails: any,
        contextData: any,
        severity: ErrorSeverity = ErrorSeverity.HIGH,
    ): Promise<string | null> {
        return this.logError({
            user_id: userId,
            category: ErrorCategory.PAYMENT,
            severity,
            error_message: errorMessage,
            error_details: errorDetails,
            context_data: contextData,
        });
    }

    /**
     * 记录退款相关错误
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     * @param contextData 上下文数据
     * @param severity 严重性
     * @returns 创建的记录ID
     */
    async logRefundError(
        userId: string,
        taskId: string,
        errorMessage: string,
        errorDetails: any,
        contextData: any,
        severity: ErrorSeverity = ErrorSeverity.HIGH,
    ): Promise<string | null> {
        return this.logError({
            user_id: userId,
            task_id: taskId,
            category: ErrorCategory.REFUND,
            severity,
            error_message: errorMessage,
            error_details: errorDetails,
            context_data: contextData,
        });
    }

    /**
     * 记录积分相关错误
     * @param userId 用户ID
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     * @param contextData 上下文数据
     * @param severity 严重性
     * @returns 创建的记录ID
     */
    async logCreditError(
        userId: string,
        errorMessage: string,
        errorDetails: any,
        contextData: any,
        severity: ErrorSeverity = ErrorSeverity.HIGH,
    ): Promise<string | null> {
        return this.logError({
            user_id: userId,
            category: ErrorCategory.CREDIT,
            severity,
            error_message: errorMessage,
            error_details: errorDetails,
            context_data: contextData,
        });
    }

    /**
     * 记录任务相关错误
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     * @param contextData 上下文数据
     * @param severity 严重性
     * @returns 创建的记录ID
     */
    async logTaskError(
        userId: string,
        taskId: string,
        errorMessage: string,
        errorDetails: any,
        contextData: any,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    ): Promise<string | null> {
        return this.logError({
            user_id: userId,
            task_id: taskId,
            category: ErrorCategory.TASK,
            severity,
            error_message: errorMessage,
            error_details: errorDetails,
            context_data: contextData,
        });
    }

    /**
     * 将错误标记为已解决
     * @param errorId 错误ID
     * @param resolutionNotes 解决说明
     * @returns 是否成功
     */
    async markAsResolved(errorId: string, resolutionNotes: string): Promise<boolean> {
        try {
            const { error } = await this.supabase
                .from('error_audit_logs')
                .update({
                    resolved: true,
                    resolved_at: new Date(),
                    resolution_notes: resolutionNotes,
                })
                .eq('id', errorId);

            if (error) {
                this.logger.error(`标记错误为已解决失败: ${error.message}`, error);
                return false;
            }

            this.logger.log(`已将错误 ${errorId} 标记为已解决`);
            return true;
        } catch (error) {
            this.logger.error(`标记错误为已解决时发生异常: ${error.message}`, error);
            return false;
        }
    }

    /**
     * 获取未解决的错误列表
     * @param category 可选的类别过滤
     * @param severity 可选的严重性过滤
     * @param limit 分页限制
     * @param offset 分页偏移
     * @returns 错误列表及总数
     */
    async getUnresolvedErrors(
        category?: ErrorCategory,
        severity?: ErrorSeverity,
        limit: number = 50,
        offset: number = 0,
    ): Promise<{ errors: ErrorAuditRecord[]; total: number }> {
        try {
            let query = this.supabase
                .from('error_audit_logs')
                .select('*', { count: 'exact' })
                .eq('resolved', false)
                .order('created_at', { ascending: false });

            if (category) {
                query = query.eq('category', category);
            }

            if (severity) {
                query = query.eq('severity', severity);
            }

            const { data, error, count } = await query.range(offset, offset + limit - 1);

            if (error) {
                this.logger.error(`获取未解决的错误列表失败: ${error.message}`, error);
                return { errors: [], total: 0 };
            }

            return {
                errors: data as ErrorAuditRecord[],
                total: count || 0,
            };
        } catch (error) {
            this.logger.error(`获取未解决的错误列表时发生异常: ${error.message}`, error);
            return { errors: [], total: 0 };
        }
    }
} 