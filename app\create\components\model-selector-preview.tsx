import { Model } from "@/types/model";
import { ChevronDown, ChevronRight, Zap } from "lucide-react";
import Image from "next/image";
import { useSelectedModelFromStore } from "../hooks/useSelectedModelFromStore";
import { useInfiniteModels, useInfiniteModelsByType } from "../hooks/useModelQuery";
import { useMemo } from "react";
import useModelSelectorStore from "@/store/useModelSelectorStore";
import { ModelPreviewCard } from "./model-preview-card";

interface ModelSelectorPreviewProps {
    selectedModel?: Model | null; // 可选，如果提供则直接使用，否则从store获取
    onClick: () => void;
    isLoading?: boolean; // 可选，如果提供则直接使用，否则从store获取
    filteredModels?: Model[]; // 可选，如果提供则使用过滤后的模型
    modelType?: string; // 可选，当前选中的模型类型
}

export function ModelSelectorPreview({
    selectedModel: propSelectedModel,
    onClick,
    isLoading: propIsLoading,
    filteredModels,
    modelType
}: ModelSelectorPreviewProps) {
    // 如果没有提供selectedModel，则从store获取
    const { selectedModel: storeSelectedModel, isLoading: storeIsLoading } = useSelectedModelFromStore();

    // 从store获取activeTab
    const { activeTab } = useModelSelectorStore();

    // 确定当前使用的Tab类型
    const currentTabType = modelType || activeTab;
    const shouldFetchByType = currentTabType !== "all" && currentTabType !== "effect";

    // 获取所有模型列表
    const { data: modelsData, isLoading: isLoadingModels } = useInfiniteModels();

    // 根据当前Tab类型获取特定类型的模型
    const {
        data: modelsByTypeData,
        isLoading: isLoadingModelsByType,
        isFetching: isFetchingModelsByType
    } = useInfiniteModelsByType(
        shouldFetchByType ? currentTabType : '',
        20 // 每页20个模型
    );

    // 合并加载状态 - 包括isFetching状态，确保在数据刷新时也显示加载状态
    const combinedIsLoading = propIsLoading || storeIsLoading || isLoadingModels || isLoadingModelsByType || isFetchingModelsByType;

    // 获取要显示的模型
    const topModels = useMemo(() => {
        // 如果正在加载，返回空数组
        if (combinedIsLoading) {
            return [];
        }

        // 如果提供了过滤后的模型，优先使用
        if (filteredModels && filteredModels.length > 0) {
            return filteredModels.slice(0, 3) as Model[];
        }

        // 如果是特定类型的Tab且有对应的模型数据
        if (shouldFetchByType && modelsByTypeData) {
            // 使用按类型获取的模型数据
            const typeFilteredModels = modelsByTypeData.pages.flatMap(page => page.models);

            // 确保始终返回3个模型，如果不足则从所有模型中补充
            if (typeFilteredModels.length >= 3) {
                return typeFilteredModels.slice(0, 3) as Model[];
            } else if (modelsData && modelsData.pages.length > 0) {
                // 如果特定类型的模型不足3个，从所有模型中补充
                const remainingCount = 3 - typeFilteredModels.length;
                const allModels = modelsData.pages.flatMap(page => page.models);

                // 过滤掉已经包含的模型
                const additionalModels = allModels
                    .filter(model => !typeFilteredModels.some(m => m.id === model.id))
                    .slice(0, remainingCount);

                return [...typeFilteredModels, ...additionalModels].slice(0, 3) as Model[];
            }

            // 如果没有足够的模型，返回现有的模型
            return typeFilteredModels;
        }

        // 否则使用所有模型中的前3个
        if (!modelsData || modelsData.pages.length === 0) {
            return [];
        }

        return modelsData.pages[0].models.slice(0, 3) as Model[];
    }, [
        modelsData,
        modelsByTypeData,
        filteredModels,
        modelType,
        activeTab,
        combinedIsLoading,
        currentTabType,
        shouldFetchByType
    ]);

    // 优先使用props中的值，如果没有则使用store中的值
    const selectedModel = propSelectedModel !== undefined ? propSelectedModel : storeSelectedModel;
    const isLoading = propIsLoading !== undefined ? propIsLoading : combinedIsLoading;

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-3">
                <div className="animate-spin w-6 h-6 mr-3 rounded-full border-2 border-t-blue-500 border-blue-500/30"></div>
                <span className="text-gray-400">Loading models...</span>
            </div>
        );
    }

    // 如果已选择模型，显示选定的模型
    if (selectedModel) {
        return (
            <div
                className="p-2.5 rounded-xl w-full ring-1 ring-white/10 hover:bg-card/20 transition-colors cursor-pointer"
                onClick={onClick}
            >
                <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1 min-w-0">
                        <div className="w-28 h-28 relative rounded-lg overflow-hidden flex-shrink-0 bg-background/50">
                            <Image
                                src={selectedModel.cover_img || "/placeholder.svg"}
                                alt={selectedModel.name}
                                fill
                                className="object-cover"
                            />

                            {/* 视频封面 - 如果有视频封面，使用video标签 */}
                            {selectedModel.cover_video && (
                                <video
                                    src={selectedModel.cover_video}
                                    preload="metadata"
                                    muted
                                    playsInline
                                    autoPlay
                                    loop
                                    className="absolute inset-0 w-full h-full object-cover"
                                    poster={selectedModel.cover_img || "/placeholder.svg"}
                                    data-silent="true"
                                    disablePictureInPicture
                                />
                            )}
                        </div>
                        <div className="ml-3 flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                                <div className="font-medium truncate">{selectedModel.name}</div>
                            </div>
                            <p className="text-xs text-muted-foreground mt-0.5 line-clamp-1">
                                {selectedModel.description || (selectedModel.type === "checkpoint" ? "Professional video generation" : "Creative style model")}
                            </p>
                        </div>
                    </div>
                    <div
                        className="ml-2 p-1.5 rounded-md bg-card/20 hover:bg-card/30 transition-colors flex-shrink-0"
                    >
                        <ChevronDown size={16} className="text-muted-foreground" />
                    </div>
                </div>
            </div>
        );
    }

    // 如果没有选择模型但有模型数据，显示前3个模型的预览
    if (topModels.length > 0) {
        return (
            <div className="rounded-xl w-full">
                <div className="flex flex-col">
                    {/* 模型卡片网格 */}
                    <div className="grid grid-cols-3 gap-3">
                        {topModels.map((model, index) => (
                            <ModelPreviewCard
                                key={model.id}
                                model={model}
                                index={index}
                            />
                        ))}
                    </div>

                    {/* 展开更多按钮和101 more models信息 */}
                    <div
                        className="flex items-center justify-center mt-4 cursor-pointer hover:bg-card/30 py-2 rounded-lg transition-colors"
                        onClick={onClick}
                    >
                        <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm font-medium px-3 py-1 rounded-full flex items-center">
                            <span className="mr-1.5">✨</span>
                            <span>Explore 101 more models</span>
                            <ChevronRight size={16} className="ml-1.5 text-white" />
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // 如果没有模型数据，显示默认的选择器
    return (
        <div
            className="p-2.5 rounded-xl w-full bg-card/20 ring-1 ring-white/10 hover:bg-card/30 transition-colors cursor-pointer"
            onClick={onClick}
        >
            <div className="flex items-center justify-between">
                <div className="flex items-center">
                    <div className="w-14 h-14 relative rounded-lg flex-shrink-0 bg-muted flex items-center justify-center">
                        <Zap size={20} className="text-muted-foreground" />
                    </div>
                    <div className="ml-3">
                        <div className="flex items-center mt-1.5">
                            <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs font-medium px-2.5 py-0.5 rounded-full flex items-center">
                                <span className="mr-1">✨</span>
                                <span>101 more models</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    className="ml-2 p-1.5 rounded-md bg-card/20 hover:bg-card/30 transition-colors flex-shrink-0"
                >
                    <ChevronDown size={16} className="text-muted-foreground" />
                </div>
            </div>
        </div>
    );
}
