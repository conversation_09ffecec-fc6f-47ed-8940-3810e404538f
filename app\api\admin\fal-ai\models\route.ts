import { NextRequest, NextResponse } from 'next/server';
export interface FalAiModel {
    id: string;
    title: string;
    category: string;
    shortDescription: string;
    thumbnailUrl: string;
    thumbnailAnimatedUrl?: string;
    modelUrl: string;
    licenseType: string;
    creditsRequired: number;
    group?: {
        key: string;
        label: string;
    };
    tags: string[];
    pricingInfoOverride?: string;
    durationEstimate?: number;
    pinned: boolean;
    highlighted: boolean;
    deprecated: boolean;
    kind: string;
    date: string;
    machineType?: string;
    examples: string[];
    description: string;
    streamUrl: string;
    minimumUnits?: number;
    authSkippable: boolean;
    unlisted: boolean;
    resultComparison: boolean;
    hidePricing: boolean;
    private: boolean;
    removed: boolean;
    adminOnly: boolean;
    trainingEndpoints: any[];
}

export async function GET(request: NextRequest) {
    try {
        // 获取查询参数
        const { searchParams } = new URL(request.url);
        const categories = searchParams.get('categories') || '';
        const tags = searchParams.get('tags') || '';
        const type = searchParams.get('type') || '';
        const deprecated = searchParams.get('deprecated') || 'false';
        const pendingEnterprise = searchParams.get('pendingEnterprise') || 'false';
        const keywords = searchParams.get('keywords') || 'video';
        const sort = searchParams.get('sort') || 'relevant';

        // 构建fal.ai API URL
        const params = new URLSearchParams({
            categories,
            tags,
            type,
            deprecated,
            pendingEnterprise,
            keywords,
            sort,
        });

        const falAiUrl = `https://fal.ai/api/models?${params.toString()}`;

        console.log('Fetching fal.ai models:', falAiUrl);

        // 调用fal.ai API
        const response = await fetch(falAiUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'ReelMind-Admin/1.0',
            },
            // 设置30秒超时
            signal: AbortSignal.timeout(30000),
        });

        if (!response.ok) {
            console.error('fal.ai API error:', response.status, response.statusText);
            return NextResponse.json(
                { error: `fal.ai API error: ${response.status} ${response.statusText}` },
                { status: 502 }
            );
        }

        const models: FalAiModel[] = await response.json();

        // 过滤出视频相关的模型
        const videoModels = models.filter((model) =>
            model.category === 'video-to-video' ||
            model.category === 'image-to-video' ||
            model.category === 'text-to-video'
        );

        console.log(`Successfully fetched ${models.length} models, ${videoModels.length} video models`);

        return NextResponse.json({
            success: true,
            data: videoModels,
            total: videoModels.length,
            timestamp: new Date().toISOString(),
        });

    } catch (error) {
        console.error('Error fetching fal.ai models:', error);

        // 处理超时错误
        if (error instanceof Error && error.name === 'AbortError') {
            return NextResponse.json(
                { error: 'Request timeout. fal.ai API is not responding.' },
                { status: 408 }
            );
        }

        return NextResponse.json(
            { error: 'Internal server error while fetching fal.ai models' },
            { status: 500 }
        );
    }
}
