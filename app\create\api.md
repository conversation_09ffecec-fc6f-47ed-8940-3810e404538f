# ReelMind API 文档

## 鉴权机制

请求 Header 里需要有 Authorization: Bearer ${supabase auth token}

## 视频生成接口

### 创建视频生成任务

用户在视频生成页面发起视频生成请求，创建一个新的视频生成任务。

- **URL**: `/generation/gen-video`
- **方法**: `POST`
- **权限**: 需要用户登录
- **请求体**:
  ```json
  {
    "prompt": "一只猫在草地上奔跑",
    "gen_type": "text2video",
    "negative_prompt": "模糊, 低质量",
    "guidance_scale": 7.5,
    "steps": 25,
    "seed": 12345,
    "definition": "480P",
    "duration": "5s",
    "ratio": "9:16"
  }
  ```
- **参数说明**:
  - `prompt`: 创意描述，文本类型，必填
  - `gen_type`: 生成类型，枚举值: "text2video" 或 "img2video"，必填
  - `negative_prompt`: 不希望视频中出现的内容，文本类型，可选
  - `guidance_scale`: 创意相关性，数值类型(1-20)，可选
  - `steps`: 迭代步数，数值类型(1-100)，可选
  - `seed`: 初始值，数值类型，可选
  - `definition`: 视频清晰度，枚举值: "480P" 或 "720P"，可选
  - `duration`: 视频时长，枚举值: "5s" 或 "10s"，可选
  - `ratio`: 视频比例，枚举值: "9:16", "16:9" 或 "1:1"，可选
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": "uuid-task-id",
      "status": "pending",
      "progress": 0,
      "queued_at": null,
      "started_at": null,
      "completed_at": null,
      "last_activity_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

### 获取用户任务列表

获取当前用户的所有视频生成任务，支持按状态过滤和分页。

- **URL**: `/generation/user-tasks`
- **方法**: `GET`
- **权限**: 需要用户登录
- **查询参数**:
  - `status`: (可选) 任务状态，可选值: `pending`, `queued`, `processing`, `completed`, `failed`
  - `limit`: (可选) 每页记录数，默认 10
  - `offset`: (可选) 分页偏移，默认 0
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "tasks": [
        {
          "id": "uuid-task-id",
          "user_id": "uuid-user-id",
          "status": "completed",
          "progress": 100,
          "input_params": {
            "prompt": "一只猫在草地上奔跑",
            "gen_type": "text2video",
            "negative_prompt": "模糊, 低质量",
            "guidance_scale": 7.5,
            "steps": 25,
            "seed": 12345,
            "definition": "480P",
            "duration": "5s",
            "ratio": "9:16"
          },
          "output_result": {
            "video_url": "https://example.com/videos/cat.mp4",
            "thumbnail_url": "https://example.com/videos/cat_thumbnail.jpg"
          },
          "created_at": "2023-01-01T00:00:00Z",
          "completed_at": "2023-01-01T00:05:00Z"
        }
      ],
      "total": 23
    }
  }
  ```

### 获取任务详情

获取特定任务的详情信息。

- **URL**: `/generation/task/:taskId`
- **方法**: `GET`
- **权限**: 需要用户登录
- **路径参数**:
  - `taskId`: 任务 ID
- **响应**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": "uuid-task-id",
      "user_id": "uuid-user-id",
      "status": "completed",
      "progress": 100,
      "input_params": {
        "prompt": "一只猫在草地上奔跑",
        "gen_type": "text2video",
        "negative_prompt": "模糊, 低质量",
        "guidance_scale": 7.5,
        "steps": 25,
        "seed": 12345,
        "definition": "480P",
        "duration": "5s",
        "ratio": "9:16"
      },
      "output_result": {
        "video_url": "https://example.com/videos/cat.mp4",
        "thumbnail_url": "https://example.com/videos/cat_thumbnail.jpg",
        "duration_seconds": 5,
        "file_size_mb": 2.5,
        "format": "mp4"
      },
      "created_at": "2023-01-01T00:00:00Z",
      "queued_at": "2023-01-01T00:01:00Z",
      "started_at": "2023-01-01T00:02:00Z",
      "completed_at": "2023-01-01T00:05:00Z",
      "last_activity_at": "2023-01-01T00:05:00Z",
      "storage_path": "user_videos/uuid-user-id/uuid-task-id.mp4"
    }
  }
  ```
