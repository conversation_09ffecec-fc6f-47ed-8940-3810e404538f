"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import useAuthStore from '@/store/useAuthStore';
import { userApi } from '@/lib/api/user';
import { useToast } from '@/components/ui/toast';

interface AdminLayoutProps {
    children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
    const router = useRouter();
    const { isAuthenticated, isLoading, checkAuthStatus } = useAuthStore();
    const [isAdmin, setIsAdmin] = React.useState<boolean | null>(null);
    const [isChecking, setIsChecking] = React.useState(true);
    const { error } = useToast();

    // 检查用户是否为管理员
    useEffect(() => {
        const checkAdminStatus = async () => {
            if (!isAuthenticated) {
                await checkAuthStatus();
            };

            setIsChecking(true);
            try {
                // 获取用户角色
                const { role } = await userApi.getUserRole();
                const hasAdminRole = role === 'admin';
                setIsAdmin(hasAdminRole);

                if (!hasAdminRole) {
                    error('访问被拒绝', '无权访问管理页面');
                    router.push('/');
                }
            } catch (err) {
                console.error('检查管理员权限失败:', err);
                error('权限验证', '权限验证失败');
                router.push('/');
            } finally {
                setIsChecking(false);
            }
        };

        checkAdminStatus();
    }, [isAuthenticated]);

    // 显示加载状态
    if (isLoading || isChecking) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600 dark:text-gray-300">加载中...</p>
                </div>
            </div>
        );
    }

    // 如果用户未登录或非管理员，路由中间件会处理重定向
    // 这里只是额外的客户端保护
    if (!isAuthenticated || isAdmin === false) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="container mx-auto px-4 py-8">
                <div className="mb-6 border-b pb-4 border-gray-200 dark:border-gray-700">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">管理控制台</h1>
                </div>
                <main>{children}</main>
            </div>
        </div>
    );
}