"use client";

import { ControlPanel } from "./control-panel";
import HistoryList from "./HistoryList";
import { Sidebar } from "./sidebar";
import { useLegoStore } from "../store";

export function LegoPageContent() {
    const { selectedModel } = useLegoStore();
    
    // 检查是否为FLUX模型
    const isFluxModel = selectedModel?.name.toLowerCase().includes('flux') || false;

    return (
        <div className="relative z-10 md:flex h-full overflow-hidden dark:bg-radial from-gray-900 via-gray-950 to-black">
            {/* Desktop View - Only visible on medium screens and larger */}
            {/* 主内容区 - Now on the left side */}
            <div className="flex-1 flex flex-col translate-y-0">
                <HistoryList />
                {/* 控制面板 - 固定在底部 */}
                <ControlPanel />
            </div>
            
            {/* 右侧侧边栏 - 只在非FLUX模型时显示 */}
            {!isFluxModel && <Sidebar />}
        </div>
    );
}
