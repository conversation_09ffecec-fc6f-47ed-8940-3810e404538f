import React, { useState } from 'react';
import useCreditsStore from '@/store/useCreditsStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

interface PackageOption {
    id: string;
    amountUsd: number;
    credits: number;
    label: string;
}

// Credits packages as defined in the documentation
const CREDIT_PACKAGES: PackageOption[] = [
    { id: 'basic', amountUsd: 5, credits: 500, label: 'Basic Pack' },
    { id: 'standard', amountUsd: 20, credits: 2000, label: 'Standard Pack' },
    { id: 'premium', amountUsd: 50, credits: 5000, label: 'Premium Pack' },
    { id: 'pro', amountUsd: 100, credits: 10000, label: 'Pro Pack' },
];

export function CreditsCard() {
    const { isLoading, purchaseCredits } = useCreditsStore();
    const [selectedPackage, setSelectedPackage] = useState<string>(CREDIT_PACKAGES[1].id);
    const [isPurchasing, setIsPurchasing] = useState(false);

    const handlePurchase = async () => {
        const selectedPkg = CREDIT_PACKAGES.find(pkg => pkg.id === selectedPackage);
        if (!selectedPkg) return;

        setIsPurchasing(true);
        try {
            const checkoutUrl = await purchaseCredits(selectedPkg.amountUsd);
            if (checkoutUrl) {
                window.location.href = checkoutUrl;
            }
        } finally {
            setIsPurchasing(false);
        }
    };

    return (
        <Card className="w-full">
            <CardHeader>
                <CardDescription>
                    Select a credit package to enhance your AI video generation capabilities
                </CardDescription>
            </CardHeader>

            <CardContent>
                <div className="space-y-4">
                    {/* Credit Package Options */}
                    <div className="grid grid-cols-2 gap-3">
                        {CREDIT_PACKAGES.map((pkg) => (
                            <div
                                key={pkg.id}
                                className={`border rounded-lg p-3 cursor-pointer transition-colors
                                     ${selectedPackage === pkg.id
                                        ? 'border-foreground bg-muted-foreground/10'
                                        : 'hover:border-primary/50'
                                    }`}
                                onClick={() => setSelectedPackage(pkg.id)}
                            >
                                <div className="font-medium">{pkg.label}</div>
                                <div className="text-2xl font-bold">{pkg.credits}</div>
                                <div className="text-muted-foreground">${pkg.amountUsd}</div>
                            </div>
                        ))}
                    </div>

                    {/* Credits Usage Info */}
                    <div className="text-sm text-muted-foreground space-y-1 mt-4">
                        <p>• 1 USD = 100 credits</p>
                        <p>• Credits never expire</p>
                        <p>• Instant delivery after payment</p>
                    </div>
                </div>
            </CardContent>

            <CardFooter>
                <Button
                    className="text-base text-medium text-primary-foreground w-full dark:text-primary dark:bg-primary-foreground"
                    onClick={handlePurchase}
                    disabled={isPurchasing || isLoading}
                >
                    {isPurchasing ? (
                        <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                        </>
                    ) : (
                        `Buy ${CREDIT_PACKAGES.find(pkg => pkg.id === selectedPackage)?.credits} Credits`
                    )}
                </Button>
            </CardFooter>
        </Card>
    );
} 