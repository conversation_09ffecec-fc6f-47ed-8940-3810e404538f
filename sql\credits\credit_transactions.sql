create table public.credit_transactions (
  id uuid not null default extensions.uuid_generate_v4 (),
  user_id uuid not null,
  type text not null,
  amount integer not null,
  description text null,
  status character varying(20) not null default 'completed'::character varying,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  payment_id uuid null,
  constraint credit_transactions_pkey primary key (id),
  constraint credit_transactions_payment_id_fkey foreign KEY (payment_id) references payments (id),
  constraint credit_transactions_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_credit_transactions_user_id on public.credit_transactions using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_credit_transactions_status on public.credit_transactions using btree (status) TABLESPACE pg_default;

create index IF not exists idx_credit_transactions_created_at on public.credit_transactions using btree (created_at) TABLESPACE pg_default;

create index IF not exists idx_credit_transactions_type on public.credit_transactions using btree (type) TABLESPACE pg_default;