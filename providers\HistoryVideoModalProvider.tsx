"use client"

import { ReactNode } from "react"
import { HistoryVideoModal } from "../app/create/components/HistoryVideoModal"
import { useHistoryVideoModalStore } from "@/store/useHistoryVideoModalStore"

interface HistoryVideoModalProviderProps {
    children: ReactNode
}

export function HistoryVideoModalProvider({ children }: HistoryVideoModalProviderProps) {
    const { taskId, isOpen, closeModal } = useHistoryVideoModalStore()

    return (
        <>
            {children}
            <HistoryVideoModal
                taskId={taskId}
                isOpen={isOpen}
                onClose={closeModal}
            />
        </>
    )
} 