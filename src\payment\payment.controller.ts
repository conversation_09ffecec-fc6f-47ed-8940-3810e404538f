import { Controller, Post, Req, Headers, Get, Query, BadRequestException, NotFoundException, Body, UseGuards } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { CustomLogger } from '../common/services/logger.service';
import { Request } from 'express';
import { StripeService } from './stripe.service';
import { STRIPE_WEBHOOK_ROUTE } from 'src/common/constants/route';
import { JwtGuard } from 'src/common/guards/jwt.guard';

@Controller('payment')
export class PaymentController {
    constructor(
        private readonly paymentService: PaymentService,
        private readonly logger: CustomLogger,
        private readonly stripeService: StripeService,
    ) {
        this.logger.setContext(PaymentController.name);
    }

    @Post(STRIPE_WEBHOOK_ROUTE)
    async handleStripeWebhook(
        @Req() request: Request & { rawBody?: Buffer },
        @Headers('stripe-signature') signature: string
    ) {
        // 获取原始请求体 - 直接访问request.body (应该是Buffer类型)
        const payload = request.body;

        if (!payload || !signature) {
            this.logger.error('Stripe Webhook: 缺少payload或signature');
            return { success: false, message: '无效的请求' };
        }

        // 验证Stripe签名并构造事件对象
        try {
            const event = await this.stripeService.constructEventFromWebhook(
                payload,
                signature
            );
            // 处理Stripe事件
            const success = await this.paymentService.handleStripeWebhookEvent(event);

            if (!success) {
                this.logger.error(`处理Stripe事件失败: ${event.type}, ID: ${event.id}`);
            }

            return { success };
        } catch (error) {
            this.logger.error(`Stripe Webhook处理失败: ${error.message}`, error);
            return { success: false, message: error.message };
        }
    }

    @Post('cancel')
    @UseGuards(JwtGuard)
    async cancelPayment(@Req() req: Request & { user: { id: string } }, @Body() body: { payment_id: string }) {
        const userId = req.user.id;
        return this.paymentService.cancelPayment(userId, body.payment_id);
    }

    /**
     * 创建Stripe客户门户会话
     * @param userId 用户ID
     * @param returnUrl 返回URL
     * @returns 客户门户URL
     */
    @Get('customer-portal')
    @UseGuards(JwtGuard)
    async createCustomerPortalSession(
        @Req() req: Request & { user: { id: string } },
        @Query('return_url') returnUrl: string,
    ): Promise<{ portalUrl: string }> {
        if (!returnUrl) {
            throw new BadRequestException('必须提供返回URL');
        }

        const userId = req.user.id;

        // 获取用户会员信息（包含customer_id）
        const membership = await this.paymentService.getUserSubscription(userId);

        if (!membership || !membership.customer_id) {
            throw new NotFoundException('未找到用户的订阅信息或客户ID');
        }

        // 创建客户门户会话
        const portalUrl = await this.stripeService.createCustomerPortalSession(
            membership.customer_id,
            returnUrl
        );

        return { portalUrl };
    }
} 