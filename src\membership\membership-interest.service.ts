import { Injectable, Inject, OnModuleInit } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';

// 权益类型枚举
export enum InterestType {
    PRIORITY = 'priority',
    QUOTA = 'quota',
    FEATURE = 'feature'
}

// 权益键名枚举
export enum InterestKey {
    VIDEO_GEN_PRIORITY = 'video_gen_priority',
    MONTHLY_GEN_LIMIT = 'monthly_gen_limit',
    WATERMARK_FREE = 'watermark_free',
    ADVANCED_EDITING = 'advanced_editing',
    TEAM_COLLABORATION = 'team_collaboration',
    MAX_VIDEO_DURATION = 'max_video_duration',
    MAX_RESOLUTION = 'max_resolution'
}

// 会员权益接口
export interface MembershipInterest {
    id: string;
    plan_id: string;
    interest_type: string;
    interest_key: string;
    interest_value: any;
    description: string;
    is_active: boolean;
}

@Injectable()
export class MembershipInterestService implements OnModuleInit {
    // 缓存会员权益
    private interestCache: Map<string, MembershipInterest[]> = new Map();
    // 缓存更新时间
    private lastCacheUpdate: number = 0;
    // 缓存有效期（毫秒）
    private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟

    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {
        this.logger.setContext(MembershipInterestService.name);
        // 初始化移至onModuleInit中，不再在构造函数中加载数据
    }

    /**
     * 在模块初始化时同步加载所有权益配置
     * 如果加载失败，抛出异常终止服务启动
     */
    async onModuleInit(): Promise<void> {
        try {
            this.logger.log('正在初始化会员权益服务...');
            await this.loadAllInterestsOrFail();
            this.logger.log('会员权益服务初始化完成');
        } catch (error) {
            this.logger.error('会员权益服务初始化失败，终止服务启动', error);
            throw new Error('会员权益服务初始化失败: ' + (error instanceof Error ? error.message : String(error)));
        }
    }

    /**
     * 获取指定计划的所有权益
     * @param planId 会员计划ID
     * @returns 权益列表
     */
    async getPlanInterests(planId: string): Promise<MembershipInterest[]> {
        await this.refreshCacheIfNeeded();
        return this.interestCache.get(planId) || [];
    }

    /**
     * 获取指定计划的特定类型权益
     * @param planId 会员计划ID
     * @param interestType 权益类型
     * @returns 权益列表
     */
    async getPlanInterestsByType(planId: string, interestType: string): Promise<MembershipInterest[]> {
        const interests = await this.getPlanInterests(planId);
        return interests.filter(interest => interest.interest_type === interestType);
    }

    /**
     * 获取指定计划的特定权益键值
     * @param planId 会员计划ID
     * @param interestKey 权益键名
     * @returns 权益对象
     */
    async getPlanInterestByKey(planId: string, interestKey: string): Promise<MembershipInterest | null> {
        const interests = await this.getPlanInterests(planId);
        return interests.find(interest => interest.interest_key === interestKey) || null;
    }

    /**
     * 获取用户的视频生成优先级
     * @param planId 会员计划ID
     * @returns 优先级数值
     */
    async getVideoGenPriority(planId: string | null): Promise<number> {
        if (!planId) return 0; // 非会员默认优先级

        const priorityInterest = await this.getPlanInterestByKey(
            planId,
            InterestKey.VIDEO_GEN_PRIORITY
        );

        if (!priorityInterest || !priorityInterest.is_active) {
            return 0;
        }

        const priorityValue = priorityInterest.interest_value;
        return typeof priorityValue === 'number' ? priorityValue :
            (parseInt(priorityValue?.toString() || '0', 10) || 0);
    }

    /**
     * 获取用户的月度生成配额
     * @param planId 会员计划ID
     * @returns 配额数量，null表示无限制
     */
    async getMonthlyGenLimit(planId: string | null): Promise<number | null> {
        if (!planId) return 10; // 非会员默认配额

        const quotaInterest = await this.getPlanInterestByKey(
            planId,
            InterestKey.MONTHLY_GEN_LIMIT
        );

        if (!quotaInterest || !quotaInterest.is_active) {
            return 10;
        }

        const quotaValue = quotaInterest.interest_value;
        if (quotaValue === null || quotaValue === 'null') {
            return null; // 无限制
        }

        return typeof quotaValue === 'number' ? quotaValue :
            (parseInt(quotaValue?.toString() || '10', 10) || 10);
    }

    /**
     * 检查用户是否拥有特定权益
     * @param planId 会员计划ID
     * @param interestKey 权益键名
     * @returns 是否拥有权益
     */
    async hasInterest(planId: string | null, interestKey: string): Promise<boolean> {
        if (!planId) return false;

        const interest = await this.getPlanInterestByKey(planId, interestKey);
        return interest !== null && interest.is_active;
    }

    /**
     * 添加或更新会员权益
     * @param planId 会员计划ID
     * @param interestType 权益类型
     * @param interestKey 权益键名
     * @param interestValue 权益值
     * @param description 权益描述
     * @returns 是否成功
     */
    async upsertInterest(
        planId: string,
        interestType: string,
        interestKey: string,
        interestValue: any,
        description: string
    ): Promise<boolean> {
        try {
            const { error } = await this.supabase
                .from('membership_interests')
                .upsert({
                    plan_id: planId,
                    interest_type: interestType,
                    interest_key: interestKey,
                    interest_value: typeof interestValue === 'object' ? interestValue : JSON.parse(JSON.stringify(interestValue)),
                    description,
                    is_active: true,
                    updated_at: new Date().toISOString()
                }, {
                    onConflict: 'plan_id,interest_key'
                });

            if (error) {
                this.logger.error(`更新会员权益失败: ${planId} - ${interestKey}`);
                return false;
            }

            // 刷新缓存
            await this.loadAllInterests();
            return true;
        } catch (error) {
            this.logger.error(`更新会员权益异常: ${planId} - ${interestKey}`, error);
            return false;
        }
    }

    /**
     * 禁用会员权益
     * @param interestId 权益ID
     * @returns 是否成功
     */
    async deactivateInterest(interestId: string): Promise<boolean> {
        try {
            const { error } = await this.supabase
                .from('membership_interests')
                .update({
                    is_active: false,
                    updated_at: new Date().toISOString()
                })
                .eq('id', interestId);

            if (error) {
                this.logger.error(`禁用会员权益失败: ${interestId}`);
                return false;
            }

            // 刷新缓存
            await this.loadAllInterests();
            return true;
        } catch (error) {
            this.logger.error(`禁用会员权益异常: ${interestId}`, error);
            return false;
        }
    }

    /**
     * 检查是否需要刷新缓存
     */
    private async refreshCacheIfNeeded(): Promise<void> {
        const now = Date.now();
        if (now - this.lastCacheUpdate > this.CACHE_TTL) {
            await this.loadAllInterests();
        }
    }

    /**
     * 从数据库加载所有会员权益
     * 不再捕获异常，而是向上传播
     */
    private async loadAllInterestsOrFail(): Promise<void> {
        const { data, error } = await this.supabase
            .from('membership_interests')
            .select('*')
            .eq('is_active', true);

        if (error) {
            throw new Error(`加载会员权益失败: ${error.message}`);
        }

        // 清空缓存
        this.interestCache.clear();

        // 按计划ID分组
        for (const interest of data) {
            if (!this.interestCache.has(interest.plan_id)) {
                this.interestCache.set(interest.plan_id, []);
            }
            this.interestCache.get(interest.plan_id)?.push(interest);
        }

        this.lastCacheUpdate = Date.now();
        this.logger.log(`已从数据库加载${data.length}条会员权益配置`);
    }

    /**
     * 从数据库加载所有会员权益
     * 保持原有行为，仅用于缓存刷新
     */
    private async loadAllInterests(): Promise<void> {
        try {
            await this.loadAllInterestsOrFail();
        } catch (error) {
            this.logger.error('加载会员权益异常', error);
        }
    }

    /**
     * 获取特定权益的值
     * @param planId 会员计划ID
     * @param interestKey 权益键名
     * @returns 权益值，如果不存在返回null
     */
    async getMembershipInterestValue(planId: string, interestKey: string): Promise<any> {
        try {
            const { data, error } = await this.supabase
                .from('membership_interests')
                .select('interest_value')
                .eq('plan_id', planId)
                .eq('interest_key', interestKey)
                .eq('is_active', true)
                .single();

            if (error || !data) {
                return null;
            }

            return data.interest_value;
        } catch (error) {
            this.logger.error(`获取会员权益值异常: ${planId}, ${interestKey}`, error);
            return null;
        }
    }
} 