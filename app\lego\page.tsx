import { Suspense } from "react";
import PromptInitializer from "./components/PromptInitializer";
import { ResponsiveLegoLayout } from "./components/responsive-lego-layout";

// Force dynamic rendering because we use headers()
export const dynamic = 'force-dynamic'

export default async function LegoPage() {
    return (
        <>
            {/* 处理URL参数 - 包裹在Suspense中 */}
            <Suspense fallback={null}>
                <PromptInitializer />
            </Suspense>

            {/* 使用响应式布局组件，确保只有一个HistoryList实例 */}
            <ResponsiveLegoLayout />
        </>
    );
}