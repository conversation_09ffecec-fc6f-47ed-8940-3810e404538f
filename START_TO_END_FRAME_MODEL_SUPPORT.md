# Start-to-End Frame Model Support

## Overview

Added support for start-to-end frame models in the create page. These models allow users to upload two images (start frame and end frame) to generate videos that transition between the two frames.

## Detection Logic

Models are identified as start-to-end frame models when:

- `model.default_config.start_to_end === true`

## Implementation Details

### 1. Type Definitions Updated

#### `types/model.ts`

- Added `start_to_end?: boolean` to `ModelDefaultConfig` interface

#### `lib/api/generation.ts`

- Added `start_image_url?: string | null` and `end_image_url?: string | null` to `VideoGenerationRequest` interface

### 2. New Hook

#### `hooks/use-start-to-end-model.ts`

- Custom hook that encapsulates all start-to-end model logic
- Manages state for start and end images
- Handles image upload, validation, and API calls
- Provides clean interface for components to use
- Includes error handling and loading states

### 3. New Components

#### `StartToEndImageUploader.tsx`

- New component for uploading start and end frame images
- Side-by-side layout with clear labels
- Uses the custom hook for all logic
- Clean separation of UI and business logic

### 4. Context Updates

#### `ControlPanelContext.tsx`

- Uses the new `useStartToEndModel` hook
- Added `isStartToEndModel` detection logic
- Delegates all start-to-end logic to the hook
- Maintains clean separation of concerns

### 5. UI Updates

#### `ControlPanel/index.tsx`

- Conditionally renders `StartToEndImageUploader` for start-to-end models
- Falls back to regular `ReferenceImageUploader` for other models

#### `GenerateButton.tsx`

- Updated validation logic to check for both start and end images when using start-to-end models
- Updated disabled state logic

### 6. Store Updates

#### `useVideoGeneratorStore.ts`

- Added detection logic for start-to-end models
- Preserved existing logic for other model types

## API Integration

When a start-to-end model is used, the generation request includes:

```typescript
{
  // ... other parameters
  start_image_url: "https://...", // URL of the start frame image
  end_image_url: "https://...",   // URL of the end frame image
}
```

## User Experience

1. **Model Selection**: When a start-to-end model is selected, the UI automatically switches to the dual image upload interface
2. **Image Upload**: Users can upload start and end frame images separately with clear labels
3. **Validation**: Both images are required before generation can proceed
4. **Error Handling**: Clear error messages for missing images or validation failures

## Backward Compatibility

- All existing model types continue to work as before
- No breaking changes to existing APIs
- Graceful fallback for models without start_to_end configuration

## Testing

To test the functionality:

1. Create a model with `default_config.start_to_end = true`
2. Select the model in the create page
3. Verify the dual image upload interface appears
4. Upload start and end frame images
5. Verify generation works with both image URLs passed to the API

## Architecture Benefits

The new hook-based architecture provides several advantages:

1. **Separation of Concerns**: Business logic is separated from UI components
2. **Reusability**: The hook can be used by other components if needed
3. **Testability**: Hook logic can be tested independently
4. **Maintainability**: Easier to modify and extend functionality
5. **Clean API**: Components have a simple interface to work with

## Files Modified

- `types/model.ts`
- `lib/api/generation.ts`
- `app/create/components/ControlPanel/ControlPanelContext.tsx`
- `app/create/components/ControlPanel/index.tsx`
- `app/create/components/ControlPanel/GenerateButton.tsx`
- `store/useVideoGeneratorStore.ts`

## Files Added

- `hooks/use-start-to-end-model.ts`
- `app/create/components/ControlPanel/StartToEndImageUploader.tsx`
