user www-data;
worker_processes auto;
pid /run/nginx.pid;

events {
	use epoll;
	worker_connections 65535;
	multi_accept on;
}

http {

	##
	# Basic Settings
	##
	sendfile on;
	tcp_nopush on;
	tcp_nodelay on;
	keepalive_timeout 65;
	types_hash_max_size 2048;
	server_tokens off;

	server_names_hash_bucket_size 64;
	client_header_buffer_size 512k;
  	large_client_header_buffers 4 512k;  
	client_max_body_size 100m;
	# server_name_in_redirect off;

	proxy_connect_timeout 10;  
	proxy_read_timeout 120;  
	proxy_send_timeout 10;  
	proxy_buffer_size 16k;  
   	proxy_buffers 4 64k;  
	proxy_busy_buffers_size 128k;  
	proxy_temp_file_write_size 128k;
	proxy_temp_path  /nginxCache/;  
	proxy_cache_path /nginxCache/temp levels=1:2 keys_zone=cache_one:200m inactive=1d max_size=10g; 
	proxy_cache_valid 200 206 304 301 302 10d;
	proxy_cache_valid any 1m;
	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	##
	# SSL Settings
	##

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
	ssl_prefer_server_ciphers on;

	##
	# Logging Settings
	##

	access_log /var/log/nginx/access.log;
	error_log /var/log/nginx/error.log;

	##
	# Gzip Settings
	##

	gzip on;
	gzip_disable "msie6";

	gzip_vary on;
	gzip_proxied any;
	gzip_comp_level 2;
	gzip_http_version 1.0;
	gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/png;

	##
	#limit
	##
	limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
	limit_conn_zone $binary_remote_addr zone=addr:10m;
	

	##
	# Virtual Host Configs
	##

	include /etc/nginx/conf.d/*.conf;
	#include /etc/nginx/sites-enabled/*;

    # 安全头
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; img-src 'self' https: data:; style-src 'self' 'unsafe-inline' https:; font-src 'self' https: data:;" always;
}
