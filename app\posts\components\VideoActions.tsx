"use client";

import { Arrow<PERSON><PERSON><PERSON>, Heart, Download } from "lucide-react";
import { useRouter } from "next/navigation";
import { PostItemDto } from "@/types/posts";
import { useState, useEffect } from "react";
import { useVideoDetailStore } from "@/store/useVideoDetailStore";
import { useVideoModalStore } from "@/store/useVideoModalStore";
import { useAuthProtectedCallback } from "@/components/auth/with-auth";

interface VideoActionsProps {
    post: PostItemDto;
    isModal?: boolean;
    checkingFavorite?: boolean;
}

export function VideoActions({
    post,
    isModal = false,
}: VideoActionsProps) {
    const router = useRouter();
    const {
        likePost,
        unlikePost,
        isLiking,
        isLiked,
    } = useVideoDetailStore();
    const { closeModal } = useVideoModalStore();

    // 使用可选链和默认值
    const [likeCount, setLikeCount] = useState<number>(post.like_count || 0);
    // 下载状态
    const [isDownloading, setIsDownloading] = useState(false);

    // 当post更新时，更新本地状态
    useEffect(() => {
        setLikeCount(post.like_count || 0);
    }, [post]);

    // 使用useAuthProtectedCallback钩子包装点赞操作
    const handleLike = useAuthProtectedCallback(async () => {
        if (isLiking) return;

        try {
            if (isLiked) {
                await unlikePost(post.id);
            } else {
                await likePost(post.id);
            }
        } catch (error) {
            console.error("Error toggling like:", error);
        }
    }, "like_post");

    // 处理下载视频的逻辑
    const handleDownload = useAuthProtectedCallback(async () => {
        if (isDownloading) return;

        setIsDownloading(true);
        // 这里实现下载视频的逻辑
        // 如果视频URL在post对象中可用，可以直接使用
        if (post.video_url) {
            const link = document.createElement('a');
            link.href = post.video_url;
            link.download = `video-${post.id}.mp4`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        setIsDownloading(false);
    }, "download_video");

    const handleBack = () => {
        if (isModal) {
            // 如果是弹窗模式，则关闭弹窗并恢复原始URL
            const state = window.history.state;
            if (state && state.previousUrl) {
                window.history.pushState({}, "", state.previousUrl);
            } else {
                window.history.pushState({}, "", "/");
            }
            closeModal();
        } else {
            // 如果不是弹窗模式，则使用router.back()
            router.back();
        }
    };

    return (
        <div className="flex items-center justify-between px-6 py-4 z-10">
            <button
                onClick={handleBack}
                className="text-white p-2 bg-white/10 rounded-full"
                aria-label={isModal ? "Close video" : "Go back"}
            >
                <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="flex items-center gap-3">
                <button
                    className={`flex items-center gap-2 px-4 py-2 text-sm rounded-full transition-all duration-200 ${isLiked
                        ? "bg-gradient-to-r from-pink-500 to-red-500 text-white"
                        : "bg-slate-200/70 hover:bg-slate-300/70 text-slate-700 dark:bg-white/10 dark:hover:bg-white/20 dark:text-white"
                        } backdrop-blur-sm`}
                    onClick={handleLike}
                    disabled={isLiking}
                    aria-label={isLiked ? "Unlike video" : "Like video"}
                >
                    <Heart
                        className={`w-4 h-4 ${isLiked ? "fill-white" : ""
                            } transition-all duration-200`}
                    />
                    <span>{isLiked ? "Liked" : "Like"}</span>
                    {likeCount > 0 && (
                        <span className="px-1.5 py-0.5 bg-black/20 rounded-full text-xs">
                            {likeCount}
                        </span>
                    )}
                </button>

                {/* 下载按钮 */}
                <button
                    className="flex items-center gap-2 px-4 py-2 text-sm rounded-full transition-all duration-200 bg-slate-200/70 hover:bg-slate-300/70 text-slate-700 dark:bg-white/10 dark:hover:bg-white/20 dark:text-white backdrop-blur-sm"
                    onClick={handleDownload}
                    disabled={isDownloading}
                    aria-label="Download video"
                >
                    <Download className="w-4 h-4 transition-all duration-200" />
                    <span>Download</span>
                </button>
            </div>
        </div>
    );
}
