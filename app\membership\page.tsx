"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { BillingToggle } from "./components/billing-toggle"
import { PricingCard } from "./components/pricing-card"
import { FAQSection } from "./components/faq-section"
import Placeholder from "./components/placeholder"
import { CreditsCard } from "./components/credits-card"
import { CreditsHistory } from "./components/credits-history"
import { CouponStatusCard } from "./components/coupon-status-card"
import { membershipApi } from "@/lib/api/membership"
import { creditsApi } from "@/lib/api/credits"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2, CreditCard, Crown, ChevronDown, ChevronUp } from "lucide-react"
import useAuthStore from "@/store/useAuthStore"

export default function MembershipPage() {
    const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly")
    const [showCreditsHistory, setShowCreditsHistory] = useState(false)
    const { isAuthenticated: isAuth } = useAuthStore()

    // 使用 React Query 获取会员信息 - 复用 user-nav 中已加载的数据
    const {
        data: userMembership,
        isLoading: isLoadingMembership
    } = useQuery({
        queryKey: ["membership", "user"],
        queryFn: async () => {
            const data = await membershipApi.getUserMembership();
            return data;
        },
        enabled: !!isAuth,
    });

    // 使用 React Query 获取积分余额 - 复用 user-nav 中已加载的数据
    const {
        data: balance,
        isLoading: isLoadingCredits
    } = useQuery({
        queryKey: ["credits", "balance"],
        queryFn: async () => {
            const data = await creditsApi.getUserBalance();
            return data.data.balance;
        },
        enabled: !!isAuth,
    });

    // 使用 React Query 获取会员计划列表
    const {
        data: membershipPlans,
        isLoading: isLoadingPlans,
        error
    } = useQuery({
        queryKey: ["membership", "plans"],
        queryFn: async () => {
            const plans = await membershipApi.getMembershipPlans();
            return plans;
        },
        staleTime: 1000 * 60 * 5, // 5分钟内不重新请求
    });

    // 检查是否有 UNLIMITED 计划
    const hasUnlimitedPlan = membershipPlans?.some(plan => plan.plan_name === "UNLIMITED");

    // 如果没有 UNLIMITED 计划，手动添加一个
    const plansWithUnlimited = membershipPlans ? [...membershipPlans] : [];

    if (!hasUnlimitedPlan && plansWithUnlimited.length > 0) {
        // 复制 MAX 计划并修改为 UNLIMITED 计划
        const maxPlan = plansWithUnlimited.find(plan => plan.plan_name === "MAX");
        if (maxPlan) {
            const unlimitedPlan = {
                ...maxPlan,
                id: "unlimited-plan-id", // 临时 ID
                level: 3, // UNLIMITED 级别
                plan_name: "UNLIMITED",
                monthly_price: 89.00,
                yearly_price: 890.00,
                features: [
                    "Unlimited video generation",
                    "Free credits for all generations",
                    "All effects available",
                    "High resolution up to 1080P",
                    "Priority support",
                    "Download videos with no watermark",
                    "Commercial use"
                ],
                is_popular: false,
                stripe_price_id_month: maxPlan.stripe_price_id_month, // 临时使用 MAX 的价格 ID
                stripe_price_id_year: maxPlan.stripe_price_id_year // 临时使用 MAX 的价格 ID
            };
            plansWithUnlimited.push(unlimitedPlan);
        }
    }

    // 对会员计划进行排序
    const sortedPlans = plansWithUnlimited.length > 0
        ? [...plansWithUnlimited].sort((a, b) => a.level - b.level)
        : [];

    // 判断用户是否有活跃会员
    const hasActiveMembership = userMembership && userMembership.is_active;

    // 合并加载状态
    const isLoading = isLoadingMembership || isLoadingPlans;

    // 渲染会员订阅部分
    const renderMembershipSection = () => (
        <section className="mb-12 md:mb-16">
            <div className="flex flex-col items-center mb-8 md:mb-12">
                <div className="text-center mb-8">
                    <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                        Membership Plans
                    </h2>
                </div>

                {/* Billing Toggle */}
                <BillingToggle billingCycle={billingCycle} onChange={(cycle) => setBillingCycle(cycle)} />
            </div>

            {/* Pricing Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
                {isLoading
                    ? <Placeholder />
                    : sortedPlans.length > 0
                        ? (
                            // 显示会员计划
                            sortedPlans.map((plan) => (
                                <PricingCard
                                    key={`plan-${plan.level}`}
                                    plan={plan}
                                    billingCycle={billingCycle}
                                    userMembership={userMembership || undefined}
                                    isLoadingUserMembership={isLoading}
                                />
                            ))
                        ) : error
                            ? (
                                // 获取会员计划失败时显示错误信息
                                <div className="col-span-4 text-center p-8 border border-destructive/30 rounded-xl bg-destructive/10">
                                    <p className="text-destructive">Failed to get membership plans, please try again later.</p>
                                </div>
                            )
                            : <Placeholder />
                }
            </div>
        </section>
    );

    // 渲染积分购买部分
    const renderCreditsSection = () => (
        <section className="mb-12 md:mb-16">
            <div className="flex flex-col items-center mb-8 md:mb-10">
                <div className="text-center">
                    <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                        Purchase Credits
                    </h2>
                    <p className="text-base md:text-lg text-muted-foreground max-w-2xl mx-auto">
                        Get instant access to AI video generation with our flexible credit packages. Perfect for occasional use or trying out our platform.
                    </p>
                </div>
            </div>

            <div className="mx-auto">
                <CreditsCard />

                <div className="mt-6">
                    <button
                        type="button"
                        onClick={() => setShowCreditsHistory(!showCreditsHistory)}
                        className="flex items-center justify-center w-full py-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                        {showCreditsHistory ? (
                            <>
                                Hide Transaction History <ChevronUp className="ml-1 h-4 w-4" />
                            </>
                        ) : (
                            <>
                                Show Transaction History <ChevronDown className="ml-1 h-4 w-4" />
                            </>
                        )}
                    </button>

                    {showCreditsHistory && (
                        <div className="mt-4">
                            <CreditsHistory />
                        </div>
                    )}
                </div>
            </div>
        </section>
    );

    return (
        <div className="relative min-h-screen bg-background text-foreground">
            {/* Main Content */}
            <main className="px-4 py-8 pb-20 lg:pb-8">
                <div className="text-center mb-12">
                    <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full px-4 py-2 mb-6">
                        <Crown className="h-5 w-5 text-blue-400" />
                        <span className="text-sm font-medium text-blue-400">Premium Membership</span>
                    </div>
                    <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
                        Choose Your Perfect Plan
                    </h1>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
                        Unlock the full potential of AI video generation with our flexible subscription plans and credit packages
                    </p>

                    {/* 优惠政策说明 */}
                    <div className="max-w-4xl mx-auto mb-8">
                        <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/30 rounded-xl p-6 md:p-8">
                            <div className="flex items-center justify-center gap-2 mb-4">
                                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                                <span className="text-sm font-semibold text-orange-400 uppercase tracking-wider">Special Offer</span>
                                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-center">
                                <div className="space-y-2">
                                    <h3 className="text-xl md:text-2xl font-bold text-orange-400">Annual Subscription</h3>
                                    <p className="text-2xl md:text-3xl font-bold text-white">First Month FREE</p>
                                    <p className="text-sm text-muted-foreground">Save more with yearly billing</p>
                                </div>
                                <div className="space-y-2">
                                    <h3 className="text-xl md:text-2xl font-bold text-orange-400">Monthly Subscription</h3>
                                    <p className="text-2xl md:text-3xl font-bold text-white">90% OFF First Month</p>
                                    <p className="text-sm text-muted-foreground">Perfect for trying our service</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* User Status Dashboard - 显示会员、积分和优惠券信息 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-8 md:mb-12">
                    {/* 会员信息卡片 */}
                    <Card className="bg-card shadow-sm">
                        <CardContent className="p-4 md:p-6">
                            <div className="flex items-center gap-3 mb-4">
                                <Crown className="h-5 w-5 md:h-6 md:w-6" />
                                <h3 className="text-lg md:text-xl font-bold">Membership Status</h3>
                            </div>

                            {isLoading ? (
                                <div className="flex items-center space-x-2 py-4">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Loading membership info...</span>
                                </div>
                            ) : userMembership && userMembership.is_active ? (
                                <div className="space-y-2">
                                    <div className="flex items-baseline justify-between">
                                        <span className="text-muted-foreground">Current Plan:</span>
                                        <span className="text-lg md:text-xl font-bold">{userMembership.plan_name}</span>
                                    </div>
                                    {userMembership.expires_at && (
                                        <div className="flex items-baseline justify-between">
                                            <span className="text-muted-foreground">Expires on:</span>
                                            <span>{new Date(userMembership.expires_at).toLocaleDateString()}</span>
                                        </div>
                                    )}
                                    <div className="mt-4 pt-4 border-t border-border">
                                        <p className="text-xs md:text-sm text-muted-foreground">
                                            You have access to all {userMembership.plan_name} features and benefits.
                                        </p>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-2">
                                    <p className="text-base md:text-lg">You don't have an active membership.</p>
                                    <p className="text-xs md:text-sm text-muted-foreground">
                                        Subscribe to a plan below to unlock premium features.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* 积分信息卡片 */}
                    <Card className="bg-card shadow-sm">
                        <CardContent className="p-4 md:p-6">
                            <div className="flex items-center gap-3 mb-4">
                                <CreditCard className="h-5 w-5 md:h-6 md:w-6" />
                                <h3 className="text-lg md:text-xl font-bold">Credits Balance</h3>
                            </div>

                            {isLoadingCredits ? (
                                <div className="flex items-center space-x-2 py-4">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Loading credits info...</span>
                                </div>
                            ) : (
                                <div className="space-y-2">
                                    <div className="flex items-baseline justify-between">
                                        <span className="text-muted-foreground">Available Credits:</span>
                                        <span className="text-2xl md:text-3xl font-bold">{balance !== null ? balance : '—'}</span>
                                    </div>
                                    <div className="mt-4 pt-4 border-t border-border">
                                        <div className="text-xs md:text-sm text-muted-foreground space-y-1">
                                            <p>• Standard video (480p): 50 credits</p>
                                            <p>• HD video (720p): 80 credits</p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* 优惠券状态卡片 */}
                    <CouponStatusCard />
                </div>

                {/* 根据用户会员状态决定显示顺序 */}
                {hasActiveMembership ? (
                    <>
                        {/* 当用户已订阅会员时，先显示积分部分 */}
                        {renderCreditsSection()}
                        <hr className="my-8 md:my-12 border-t border-border" />
                        {renderMembershipSection()}
                    </>
                ) : (
                    <>
                        {/* 当用户未订阅会员时，保持原有顺序 */}
                        {renderMembershipSection()}
                        <hr className="my-8 md:my-12 border-t border-border" />
                        {renderCreditsSection()}
                    </>
                )}

                {/* FAQ Section */}
                <FAQSection />
            </main>

            {/* Space to account for mobile navigation bar */}
            <div className="h-16 lg:h-0"></div>

            {/* Footer */}
            <footer className="border-t border-border py-6 md:py-8 text-center text-xs md:text-sm text-muted-foreground">
                <p>© {new Date().getFullYear()} ReelMind. All rights reserved.</p>
            </footer>
        </div>
    )
}

