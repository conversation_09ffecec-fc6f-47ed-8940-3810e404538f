"use client"

import { Gift, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CouponStatus } from "@/app/membership/types/coupon"
import { useCouponLogic } from "@/hooks/useCouponLogic"
import useCouponStore from "@/store/useCouponStore"

export function CouponStatusCard() {
    const {
        timeLeft,
        activeCoupon,
        isLoading,
        isAuthenticated,
        isEligibleForFirstMonth,
        hasCheckedEligibility,
        handleClaimCoupon,
        isActiveMember
    } = useCouponLogic()

    const { error } = useCouponStore()

    // 如果用户已经是活跃会员，不显示优惠券状态卡片
    if (isActiveMember) {
        return null;
    }

    // 渲染优惠券状态
    const renderCouponStatus = () => {
        // 有活跃优惠券
        if (activeCoupon && activeCoupon.status === CouponStatus.ACTIVE) {
            const isExpired = new Date(activeCoupon.expires_at) <= new Date()

            return (
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <CheckCircle className="w-5 h-5 text-green-500" />
                            <span className="font-medium text-green-500">Coupon Activated</span>
                        </div>
                        <Badge
                            variant="secondary"
                            className="bg-green-500/20 text-green-400 border-green-500/30"
                        >
                            {activeCoupon.discount_percentage}% OFF
                        </Badge>
                    </div>

                    <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Claimed:</span>
                            <span>{new Date(activeCoupon.claimed_at).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Expires:</span>
                            <span>{new Date(activeCoupon.expires_at).toLocaleDateString()}</span>
                        </div>
                        {!isExpired && (
                            <div className="flex items-center justify-between text-sm">
                                <span className="text-muted-foreground">Time Left:</span>
                                <div className="flex items-center gap-1">
                                    <Clock className="w-3 h-3" />
                                    <span className="font-mono">{timeLeft}</span>
                                </div>
                            </div>
                        )}
                    </div>

                    {isExpired && (
                        <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                            <div className="flex items-center gap-2 text-destructive">
                                <XCircle className="w-4 h-4" />
                                <span className="text-sm font-medium">Coupon Expired</span>
                            </div>
                        </div>
                    )}
                </div>
            )
        }

        // 已使用的优惠券
        if (activeCoupon && activeCoupon.status === CouponStatus.USED) {
            return (
                <div className="space-y-4">
                    <div className="flex items-center gap-2">
                        <CheckCircle className="w-5 h-5 text-blue-500" />
                        <span className="font-medium text-blue-500">Coupon Used</span>
                    </div>

                    <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Used At:</span>
                            <span>{activeCoupon.used_at ? new Date(activeCoupon.used_at).toLocaleDateString() : '-'}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Discount:</span>
                            <span>{activeCoupon.discount_percentage}% OFF</span>
                        </div>
                    </div>
                </div>
            )
        }

        // 可以领取优惠券
        if (isEligibleForFirstMonth) {
            return (
                <div className="space-y-4">
                    <div className="flex items-center gap-2">
                        <Gift className="w-5 h-5 text-orange-500" />
                        <span className="font-medium">First Month 90% OFF Available</span>
                    </div>

                    <div className="p-4 bg-gradient-to-r from-orange-500/10 to-yellow-500/10 border border-orange-500/20 rounded-lg">
                        <div className="space-y-3">
                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">🎉 New User Exclusive</span>
                                <Badge variant="secondary" className="text-xs bg-orange-500/20 text-orange-400 border-orange-500/30">
                                    Limited Time
                                </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">
                                First-time membership subscribers get 90% OFF, coupon valid for 24 hours
                            </p>
                            <Button
                                onClick={handleClaimCoupon}
                                disabled={isLoading}
                                size="sm"
                                className="w-full bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white border-0"
                            >
                                Claim Coupon Now
                            </Button>
                        </div>
                    </div>
                </div>
            )
        }

        // 不符合条件
        return (
            <div className="space-y-4">
                <div className="flex items-center gap-2">
                    <AlertCircle className="w-5 h-5 text-muted-foreground" />
                    <span className="font-medium text-muted-foreground">No Available Coupons</span>
                </div>

                <div className="p-3 bg-muted/50 border border-border rounded-lg">
                    <p className="text-sm text-muted-foreground">
                        First month 90% OFF coupon is only available for new users who have never purchased a membership
                    </p>
                </div>
            </div>
        )
    }

    if (!isAuthenticated) {
        return null
    }

    return (
        <Card className="bg-card shadow-sm">
            <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                    <Gift className="w-5 h-5" />
                    Coupon Status
                </CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="pt-4">
                {isLoading && !hasCheckedEligibility ? (
                    <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                ) : error ? (
                    <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                        <div className="flex items-center gap-2 text-destructive">
                            <XCircle className="w-4 h-4" />
                            <span className="text-sm">{error}</span>
                        </div>
                    </div>
                ) : (
                    renderCouponStatus()
                )}
            </CardContent>
        </Card>
    )
}
