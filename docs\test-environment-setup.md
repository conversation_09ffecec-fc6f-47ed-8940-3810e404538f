# 测试环境配置指南

本文档介绍如何配置和使用测试环境进行支付功能测试。

## 环境配置

### 1. 环境变量配置

复制 `.env.example` 文件为 `.env` 并配置以下变量：

```bash
# 基础配置
NODE_ENV=development
PORT=3000

# Supabase配置
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key

# Stripe环境控制
STRIPE_ENVIRONMENT=test  # 强制使用测试环境

# Stripe测试环境配置
STRIPE_API_VERSION=2023-10-16
STRIPE_TEST_SECRET_KEY=sk_test_your_test_secret_key
STRIPE_TEST_PUBLIC_KEY=pk_test_your_test_public_key
STRIPE_TEST_WEBHOOK_SECRET=whsec_your_test_webhook_secret

# API配置
API_KEY=your_api_key
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_APP_URL=http://localhost:3001

# 前端Stripe公钥（测试环境）
NEXT_PUBLIC_STRIPE_PUBLIC_KEY=${STRIPE_TEST_PUBLIC_KEY}
```

### 2. Stripe 测试环境设置

#### 2.1 创建 Stripe 测试产品和价格

在 Stripe Dashboard 的测试模式下：

1. **创建产品**：

   - Pro Plan (Test)
   - Max Plan (Test)

2. **创建价格**：

   ```
   Pro Plan:
   - Monthly: price_test_pro_monthly ($0.99)
   - Yearly: price_test_pro_yearly ($9.99)

   Max Plan:
   - Monthly: price_test_max_monthly ($1.99)
   - Yearly: price_test_max_yearly ($19.99)
   ```

#### 2.2 创建测试优惠券

创建固定的优惠券 ID：

**生产环境优惠券：**

- `Grvt44lb` - 30% 折扣，用于首月优惠

**测试环境优惠券：**

- `uYY1w0UZ` - 30% 折扣，用于首月优惠（测试环境）

> 注意：系统会根据当前环境（测试/生产）自动选择对应的优惠券 ID

**首月折扣优惠券领取条件：**

1. 用户从未购买过会员
2. 用户从未领取过首月折扣优惠券

> 重要：测试时请确保测试用户满足以上条件，否则无法领取优惠券

#### 2.3 配置 Webhook

1. 在 Stripe Dashboard 中创建 Webhook 端点
2. URL: `https://your-domain.com/payment/webhook`
3. 监听事件：
   - `checkout.session.completed`
   - `invoice.payment_succeeded`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`

### 3. 数据库配置

#### 3.1 添加 env 字段到 membership_plans 表

```sql
-- 添加env字段
ALTER TABLE membership_plans ADD COLUMN env VARCHAR(20) DEFAULT 'production';

-- 创建索引
CREATE INDEX idx_membership_plans_env ON membership_plans(env);

-- 更新现有数据为生产环境
UPDATE membership_plans SET env = 'production' WHERE env IS NULL;
```

#### 3.2 创建测试用户

```sql
-- 创建测试用户资料
INSERT INTO user_profiles (user_id, email, username) VALUES
('test-user-id-123', '<EMAIL>', 'testuser1'),
('test-user-coupon-123', '<EMAIL>', 'testuser2'),
('test-user-credits-123', '<EMAIL>', 'testuser3');

-- 创建测试用户积分余额
INSERT INTO user_credit_balances (user_id, balance) VALUES
('test-user-id-123', 100),
('test-user-coupon-123', 50),
('test-user-credits-123', 200);
```

## 测试数据管理

### 1. 创建测试数据

```bash
# 创建测试环境的会员计划
node scripts/test-data/create-test-membership-plans.js
```

### 2. 清理测试数据

```bash
# 基础清理（清理测试用户数据）
node scripts/test-data/cleanup-test-data.js

# 完整清理（包括测试计划）
node scripts/test-data/cleanup-test-data.js --all --plans
```

## 运行测试

### 1. 单独测试

```bash
# 会员订阅测试
node scripts/test-scripts/test-membership-subscription.js

# 带优惠券的会员订阅测试
node scripts/test-scripts/test-membership-with-coupon.js

# 积分购买测试
node scripts/test-scripts/test-credits-purchase.js
```

### 2. 综合测试

```bash
# 运行所有测试
node scripts/test-scripts/run-all-tests.js

# 包含数据设置和清理
node scripts/test-scripts/run-all-tests.js --setup --cleanup
```

## 环境切换

### 自动环境检测

系统会根据以下规则自动选择 Stripe 环境：

1. 如果设置了 `STRIPE_ENVIRONMENT`，使用该值
2. 否则根据 `NODE_ENV` 推断：
   - `production` → `live`
   - 其他 → `test`

### 手动环境控制

```bash
# 强制使用测试环境
STRIPE_ENVIRONMENT=test

# 强制使用生产环境
STRIPE_ENVIRONMENT=live
```

## 测试场景

### 1. 会员订阅测试

- ✅ 获取会员计划列表
- ✅ 创建月付订阅
- ✅ 创建年付订阅
- ✅ 验证支付链接格式
- ✅ 检查用户会员状态

### 2. 优惠券测试

- ✅ 检查优惠券资格
- ✅ 领取优惠券
- ✅ 计算优惠后价格
- ✅ 创建带优惠券的订阅
- ✅ 验证 Stripe 优惠券应用

### 3. 积分购买测试

- ✅ 获取用户积分余额
- ✅ 创建不同金额的积分购买
- ✅ 验证兑换率计算
- ✅ 测试边界情况
- ✅ 检查交易历史

## 故障排除

### 常见问题

1. **Stripe 密钥错误**

   - 检查环境变量配置
   - 确认使用正确的测试/生产密钥

2. **会员计划不存在**

   - 运行测试数据创建脚本
   - 检查 env 字段过滤

3. **优惠券无法领取**

   - 确认用户没有会员历史
   - 检查优惠券是否已过期

4. **支付链接无法创建**
   - 检查 Stripe Price ID 配置
   - 确认用户资料存在

### 调试技巧

1. **查看日志**

   ```bash
   # 查看应用日志
   tail -f logs/info-$(date +%Y-%m-%d).log
   ```

2. **检查数据库状态**

   ```sql
   -- 检查会员计划
   SELECT * FROM membership_plans WHERE env = 'test';

   -- 检查用户数据
   SELECT * FROM user_profiles WHERE user_id LIKE 'test-%';
   ```

3. **验证 Stripe 配置**
   - 在 Stripe Dashboard 中检查产品和价格
   - 确认 Webhook 配置正确

## 安全注意事项

1. **测试环境隔离**

   - 使用独立的数据库
   - 使用 Stripe 测试密钥
   - 不要在生产环境运行测试脚本

2. **敏感信息保护**

   - 不要提交 `.env` 文件
   - 定期轮换 API 密钥
   - 限制测试用户权限

3. **数据清理**
   - 定期清理测试数据
   - 避免测试数据污染生产环境

## 持续集成

### GitHub Actions 示例

```yaml
name: Payment Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: "18"

      - name: Install dependencies
        run: npm install

      - name: Setup test data
        run: node scripts/test-data/create-test-membership-plans.js
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_KEY: ${{ secrets.SUPABASE_KEY }}

      - name: Run tests
        run: node scripts/test-scripts/run-all-tests.js
        env:
          STRIPE_ENVIRONMENT: test
          STRIPE_TEST_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}
          # ... 其他环境变量

      - name: Cleanup test data
        run: node scripts/test-data/cleanup-test-data.js --all
        if: always()
```

## 总结

通过以上配置，你可以：

1. 🔧 **安全地测试支付功能** - 使用 Stripe 测试环境
2. 🎯 **隔离测试数据** - 通过 env 字段区分环境
3. 🚀 **自动化测试流程** - 使用测试脚本
4. 🧹 **便捷的数据管理** - 创建和清理测试数据
5. 📊 **全面的测试覆盖** - 会员订阅、优惠券、积分购买

这样的测试环境配置确保了开发和测试的安全性，同时提供了便捷的测试工具。
