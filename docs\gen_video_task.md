## 数据表定义

### video_gen_tasks

```sql
create table public.video_gen_tasks (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  client_info jsonb null,
  status character varying(20) not null default 'pending'::character varying,
  progress smallint not null default 0,
  retry_count smallint null default 0,
  input_params jsonb not null,
  output_result jsonb null,
  error_log jsonb null,
  created_at timestamp with time zone not null default now(),
  queued_at timestamp with time zone null,
  started_at timestamp with time zone null,
  completed_at timestamp with time zone null,
  last_activity_at timestamp with time zone null,
  storage_path text null,
  notification_sent boolean null default false,
  priority smallint not null default 0,
  handler text null,
  request_id uuid null,
  constraint video_generation_tasks_pkey primary key (id),
  constraint video_generation_tasks_user_id_fkey foreign KEY (user_id) references auth.users (id),
  constraint video_generation_tasks_progress_check check (
    (
      (progress >= 0)
      and (progress <= 100)
    )
  ),
  constraint video_generation_tasks_status_check check (
    (
      (status)::text = any (
        (
          array[
            'pending'::character varying,
            'queued'::character varying,
            'processing'::character varying,
            'completed'::character varying,
            'failed'::character varying,
            'canceled'::character varying
          ]
        )::text[]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_tasks_user on public.video_gen_tasks using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_tasks_status on public.video_gen_tasks using btree (status) TABLESPACE pg_default;

create index IF not exists idx_tasks_created on public.video_gen_tasks using btree (created_at) TABLESPACE pg_default;

create index IF not exists idx_tasks_priority on public.video_gen_tasks using btree (priority desc) TABLESPACE pg_default;
```
