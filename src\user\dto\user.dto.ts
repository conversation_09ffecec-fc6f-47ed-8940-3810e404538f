import { IsBoolean, IsOptional, IsString, IsUrl, <PERSON><PERSON><PERSON><PERSON>, Val<PERSON>teNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

export enum NsfwLevel {
    PG = 1,
    PG13 = 2,
    R = 4,
    X = 8,
    XXX = 16,
    Blocked = 32,
}

export class UserLinkDto {
    @IsString()
    @MaxLength(50)
    title: string;

    @IsUrl()
    @MaxLength(200)
    url: string;
}

export class UpdateProfileDto {
    @IsOptional()
    @IsString()
    @MaxLength(50)
    nickname?: string;

    @IsOptional()
    @IsString()
    @MaxLength(200)
    bio?: string;

    @IsOptional()
    @IsString()
    avatar?: string;

    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => UserLinkDto)
    links?: UserLinkDto[];
}

export class UpdateNsfwSettingDto {
    @IsBoolean()
    nsfwEnabled: boolean;
}

export class DeleteAccountDto {
    @IsString()
    confirmation: string;
}

export class FavoriteItemDto {
    @IsString()
    @IsOptional()
    post_id?: string;

    @IsString()
    @IsOptional()
    model_id?: string;

    @IsString()
    item_type: 'model' | 'post';
}

/**
 * 检查收藏状态DTO
 */
export class CheckFavoriteDto {
    @IsString()
    @IsOptional()
    post_id?: string;

    @IsString()
    @IsOptional()
    model_id?: string;

    @IsString()
    item_type: 'model' | 'post';
}

/**
 * 检查收藏状态响应DTO
 */
export class CheckFavoriteResponseDto {
    favorite: boolean;
} 