/**
 * 积分策略接口
 * 
 * 定义了积分发放策略的通用接口，遵循策略模式设计
 * 不同类型的积分发放可以实现此接口，提供特定的积分计算和发放逻辑
 */
export interface ICreditStrategy {
    /**
     * 策略类型标识
     */
    readonly type: string;
    
    /**
     * 计算应发放的积分数量
     * @param userId 用户ID
     * @param params 计算所需的参数
     * @returns 计算得到的积分数量
     */
    calculateAmount(userId: string, params?: any): Promise<number>;
    
    /**
     * 检查是否可以发放积分
     * @param userId 用户ID
     * @param params 检查所需的参数
     * @returns 是否可以发放积分
     */
    canGrant(userId: string, params?: any): Promise<boolean>;
    
    /**
     * 执行积分发放
     * @param userId 用户ID
     * @param params 发放所需的参数
     * @returns 发放结果
     */
    execute(userId: string, params?: any): Promise<any>;
}

/**
 * 积分策略工厂接口
 * 
 * 用于创建和管理不同类型的积分策略
 */
export interface ICreditStrategyFactory {
    /**
     * 获取指定类型的积分策略
     * @param type 策略类型
     * @returns 对应的积分策略实例
     */
    getStrategy(type: string): ICreditStrategy;
    
    /**
     * 注册新的积分策略
     * @param strategy 积分策略实例
     */
    registerStrategy(strategy: ICreditStrategy): void;
}
