import { useEffect, useMemo, useState } from "react";
import { useInfiniteModels, useServerSearchModels } from "../hooks/useModelQuery";
import useModelSelectorStore from "@/store/useModelSelectorStore";
import { ModelSelectorPreview } from "./model-selector-preview";
import { ModelSelectorModal } from "./model-selector-modal";

interface ModelSelectorProps {
    selectedModelId: string;
    setSelectedModelId: (id: string) => void;
    filteredModels?: any[]; // 可选的已过滤模型列表
    modelType?: string; // 可选的模型类型
}

export function ModelSelector({ selectedModelId, setSelectedModelId, filteredModels, modelType }: ModelSelectorProps) {
    // 从store获取状态和方法
    const {
        isModalOpen,
        searchTerm,
        filters,
        activeTab,
        openModal,
        closeModal,
        setSearchTerm,
        resetPage,
        setSelectedModelId: storeSetSelectedModelId,
        setActiveTab,
        addTypeFilter,
        removeTypeFilter,
        addFeatureFilter,
        removeFeatureFilter,
        addSourceFilter,
        removeSourceFilter,
        resetFilters
    } = useModelSelectorStore();

    // 使用状态来跟踪是否应该使用后端搜索
    const [useServerSearch, setUseServerSearch] = useState(false);

    // 使用react-query获取所有模型数据（用于初始显示和无搜索时）
    const {
        data: modelsData,
        isLoading: isLoadingModels,
        fetchNextPage: fetchNextPageAllModels,
        hasNextPage: hasNextPageAllModels,
        isFetchingNextPage: isFetchingNextPageAllModels
    } = useInfiniteModels();

    // 使用后端API搜索模型（当有搜索词或过滤器时）
    const {
        models: searchResults,
        totalCount: searchTotalCount,
        isLoading: isSearching,
        fetchNextPage: fetchNextPageSearch,
        hasNextPage: hasNextPageSearch,
        isFetchingNextPage: isFetchingNextPageSearch
    } = useServerSearchModels(searchTerm, filters, 20, useServerSearch);

    // 不再需要单独获取选中的模型详情，因为现在从store中获取

    // 合并所有页面的模型数据
    const allModels = useMemo(() => {
        if (!modelsData) return [];
        return modelsData.pages.flatMap(page => page.models);
    }, [modelsData]);

    // 决定使用哪个数据源
    const shouldUseServerSearch = useServerSearch && (!!searchTerm.trim() ||
        (filters.type && filters.type.length > 0) ||
        (filters.features && filters.features.length > 0) ||
        (filters.source && filters.source.length > 0));

    // 直接计算结果，不使用复杂的缓存逻辑
    // 对于模态框，我们总是显示所有模型，不使用filteredModels
    // 只有在预览组件中才使用filteredModels
    const useFilteredModelsForPreview = !!filteredModels && filteredModels.length > 0;

    // 根据当前状态选择正确的数据源
    // 模态框总是显示所有模型或搜索结果
    const displayModels = shouldUseServerSearch ? searchResults : allModels;
    const previewModels = useFilteredModelsForPreview ? filteredModels : displayModels;

    const modelCount = shouldUseServerSearch ? searchTotalCount : (modelsData?.pages[0]?.total || 0);

    const isLoadingCurrentModels = shouldUseServerSearch ? isSearching : isLoadingModels;

    const hasMoreModels = shouldUseServerSearch ? hasNextPageSearch : hasNextPageAllModels;

    const isLoadingMoreModels = shouldUseServerSearch ? isFetchingNextPageSearch : isFetchingNextPageAllModels;

    // 选择正确的fetchNextPage函数
    const fetchMoreModels = shouldUseServerSearch ? fetchNextPageSearch : fetchNextPageAllModels;

    // 当搜索词或过滤器变化时，切换到服务器搜索
    useEffect(() => {
        const hasSearchOrFilters = !!searchTerm.trim() ||
            (filters.type && filters.type.length > 0) ||
            (filters.features && filters.features.length > 0) ||
            (filters.source && filters.source.length > 0);

        setUseServerSearch(!!hasSearchOrFilters);
    }, [searchTerm, filters]);

    // 同步selectedModelId到store
    useEffect(() => {
        storeSetSelectedModelId(selectedModelId);
    }, [selectedModelId, storeSetSelectedModelId]);

    // 处理模型选择
    const handleSelectModel = (id: string) => {
        setSelectedModelId(id);
        storeSetSelectedModelId(id);
    };

    // 处理加载更多模型
    const handleLoadMore = () => {
        if (hasMoreModels && !isLoadingMoreModels) {
            // React Query 的 fetchNextPage 会自动处理分页逻辑
            fetchMoreModels();
        }
    };

    // 处理打开模态框
    const handleOpenModal = () => {
        resetPage();
        setSearchTerm('');

        // 如果当前有激活的tab，则设置对应的类型过滤器
        if (activeTab && activeTab !== 'all' && activeTab !== 'effect') {
            // 重置过滤器，然后添加当前tab对应的类型过滤器
            resetFilters();
            addTypeFilter(activeTab);
            // 当有类型过滤器时，使用服务器搜索
            setUseServerSearch(true);
        } else {
            // 重置所有过滤器，让模态框显示所有模型
            resetFilters();
            // 没有过滤器时，使用所有模型数据源
            setUseServerSearch(false);
        }

        openModal();
    };

    // 处理过滤器变化
    const handleFilterChange = (filterType: string, value: string, isActive: boolean) => {
        if (isActive) {
            switch (filterType) {
                case 'type':
                    addTypeFilter(value);
                    break;
                case 'features':
                    addFeatureFilter(value);
                    break;
                case 'source':
                    addSourceFilter(value);
                    break;
            }
        } else {
            switch (filterType) {
                case 'type':
                    removeTypeFilter(value);
                    break;
                case 'features':
                    removeFeatureFilter(value);
                    break;
                case 'source':
                    removeSourceFilter(value);
                    break;
            }
        }
    };

    // 处理模型类型变化
    const handleModelTypeChange = (type: string) => {
        // 更新 activeTab 状态（这会自动触发 setFiltersByTab 来更新过滤器）
        setActiveTab(type);

        // 重置页面
        resetPage();

        // 根据类型决定是否使用服务器搜索
        if (type && type !== 'all' && type !== 'effect') {
            // 当有类型过滤器时，使用服务器搜索
            setUseServerSearch(true);
        } else {
            // 没有类型过滤器时，使用所有模型数据源
            setUseServerSearch(false);
        }
    };

    return (
        <div className="rounded-xl relative w-full">
            {/* 模型选择器预览 - 传递过滤后的模型和模型类型 */}
            <ModelSelectorPreview
                onClick={handleOpenModal}
                filteredModels={previewModels}
                modelType={modelType}
            />

            {/* 模型选择器模态框 */}
            <ModelSelectorModal
                isOpen={isModalOpen}
                onClose={closeModal}
                models={displayModels}
                selectedModelId={selectedModelId}
                onSelectModel={handleSelectModel}
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                isLoading={isLoadingCurrentModels || isLoadingMoreModels}
                hasMore={hasMoreModels}
                onLoadMore={handleLoadMore}
                totalCount={modelCount}
                filters={filters}
                onFilterChange={handleFilterChange}
                onResetFilters={resetFilters}
                activeModelType={activeTab}
                onModelTypeChange={handleModelTypeChange}
            />
        </div>
    );
}
