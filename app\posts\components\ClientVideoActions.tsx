"use client";

import { useEffect, useState } from "react";
import { postApi } from "@/lib/api/post";
import { PostItemDto } from "@/types/posts";
import { useAuth } from "@/contexts/auth-context";
import { CommentSection } from "./CommentSection";
import { VideoActions } from "./VideoActions";

interface ClientVideoActionsProps {
    post: PostItemDto;
    initialComments: any[];
    container: string;
}

export function ClientVideoActions({
    post,
    initialComments,
    container
}: ClientVideoActionsProps) {
    const { isAuthenticated } = useAuth();
    const [checkingFavorite, setCheckingFavorite] = useState(false);
    const [commentContent, setCommentContent] = useState("");
    const [submittingComment, setSubmittingComment] = useState(false);
    const [comments, setComments] = useState(initialComments || []);

    // 检查收藏状态
    useEffect(() => {
        if (isAuthenticated) {
            // 这里可以添加实际的收藏状态检查逻辑
            setCheckingFavorite(true);
            setTimeout(() => {
                setCheckingFavorite(false);
            }, 500);
        }
    }, [isAuthenticated, post.id]);

    // 提交评论
    const submitComment = async (postId: string, content: string) => {
        if (!content.trim() || submittingComment) return;

        setSubmittingComment(true);
        try {
            const response = await postApi.createComment({
                content,
                targetType: 'post',
                targetId: postId
            });

            if (response) {
                // 更新评论列表
                const { data: updatedComments } = await postApi.getPostComments(postId);
                setComments(updatedComments.comments || []);
                setCommentContent("");
            }
        } catch (error) {
            console.error("Failed to submit comment:", error);
        } finally {
            setSubmittingComment(false);
        }
    };

    // 使用useEffect在客户端渲染后挂载交互组件
    useEffect(() => {
        // 找到评论容器，并在其中渲染客户端评论组件
        const commentsContainer = document.getElementById(container);
        if (commentsContainer) {
            // 清空容器，替换为客户端渲染的评论组件
            commentsContainer.innerHTML = '';

            // 通过DOM操作注入按钮到视频操作栏
            const actionsContainer = document.querySelector('[data-video-actions]');
            if (actionsContainer) {
                // 在这里可以通过DOM操作添加交互按钮
            }
        }
    }, [container]);

    return (
        <>
            {/* 视频操作按钮 */}
            <VideoActions post={post} isModal={false} checkingFavorite={checkingFavorite} />

            {/* 评论区 */}
            <div id={`${container}-interactive`}>
                <CommentSection
                    videoId={post.id}
                    comments={comments}
                    loadingComments={false}
                    commentContent={commentContent}
                    submittingComment={submittingComment}
                    setCommentContent={setCommentContent}
                    submitComment={submitComment}
                />
            </div>
        </>
    );
} 