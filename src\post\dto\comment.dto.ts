import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateCommentDto {
    @IsNotEmpty()
    @IsString()
    content: string; // 评论内容

    @IsOptional()
    @IsUUID()
    parentId?: string; // 父评论ID，如果是对评论的回复则需要提供

    @IsNotEmpty()
    @IsString()
    targetType: 'post' | 'model'; // 评论目标类型：帖子或模型

    @IsNotEmpty()
    @IsUUID()
    targetId: string; // 评论目标ID：帖子ID或模型ID
}

export class CommentResponseDto {
    id: string;
    content: string;
    userId: string;
    username: string;
    userAvatar?: string;
    parentId?: string;
    targetType: string;
    targetId: string;
    createdAt: Date;
    childrenCount?: number;
} 