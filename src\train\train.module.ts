import { Module } from '@nestjs/common';
import { TrainController } from './train.controller';
import { TrainService } from './train.service';
import { ModelsModule } from '../models/models.module';
import { MembershipModule } from '../membership/membership.module';
import { CreditsModule } from '../credits/credits.module';
@Module({
    imports: [
        ModelsModule,
        MembershipModule,
        CreditsModule
    ],
    controllers: [TrainController],
    providers: [TrainService],
    exports: [TrainService]
})
export class TrainModule {} 