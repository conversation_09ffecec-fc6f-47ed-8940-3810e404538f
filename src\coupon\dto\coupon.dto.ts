import { IsEnum, <PERSON>NotEmpty, IsOptional, IsString, IsUUID, <PERSON>N<PERSON><PERSON>, Min, Max } from 'class-validator';

export enum CouponType {
    FIRST_MONTH_DISCOUNT = 'first_month_discount'
}

export enum CouponStatus {
    ACTIVE = 'active',
    USED = 'used',
    EXPIRED = 'expired'
}

/**
 * 领取优惠券请求DTO
 */
export class ClaimCouponDto {
    @IsEnum(CouponType)
    @IsNotEmpty()
    coupon_type: CouponType;
}

/**
 * 优惠券响应DTO
 */
export class CouponResponseDto {
    @IsUUID()
    id: string;

    @IsUUID()
    user_id: string;

    @IsEnum(CouponType)
    coupon_type: CouponType;

    @IsNumber()
    @Min(1)
    @Max(100)
    discount_percentage: number;

    @IsEnum(CouponStatus)
    status: CouponStatus;

    claimed_at: Date;
    expires_at: Date;

    @IsOptional()
    used_at?: Date;

    @IsOptional()
    @IsUUID()
    payment_id?: string;

    created_at: Date;
    updated_at: Date;
}

/**
 * 优惠券状态检查响应DTO
 */
export class CouponStatusResponseDto {
    has_coupon: boolean;

    @IsOptional()
    coupon?: CouponResponseDto;

    is_eligible: boolean;

    @IsOptional()
    @IsString()
    message?: string;
}

/**
 * 价格计算请求DTO
 */
export class CalculatePriceDto {
    @IsNumber()
    @Min(0)
    original_price: number;

    @IsOptional()
    @IsUUID()
    coupon_id?: string;
}

/**
 * 价格计算响应DTO
 */
export class PriceCalculationResponseDto {
    @IsNumber()
    original_price: number;

    @IsNumber()
    @Min(0)
    @Max(100)
    discount_percentage: number;

    @IsNumber()
    @Min(0)
    discount_amount: number;

    @IsNumber()
    @Min(0)
    final_price: number;

    has_coupon: boolean;
}

/**
 * 应用优惠券到支付请求DTO
 */
export class ApplyCouponToPaymentDto {
    @IsUUID()
    @IsNotEmpty()
    coupon_id: string;

    @IsUUID()
    @IsNotEmpty()
    payment_id: string;
}

/**
 * 应用优惠券到支付响应DTO
 */
export class ApplyCouponToPaymentResponseDto {
    success: boolean;

    @IsNumber()
    @Min(0)
    original_amount: number;

    @IsNumber()
    @Min(0)
    discount_amount: number;

    @IsNumber()
    @Min(0)
    final_amount: number;

    @IsString()
    message: string;
}
