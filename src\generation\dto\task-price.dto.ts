import { IsNotEmpty, IsString, IsOptional, IsEnum } from 'class-validator';
import { VideoDuration } from 'src/common/constants/video';

/**
 * 任务价格查询请求DTO
 */
export class TaskPriceRequestDto {
    @IsString()
    @IsNotEmpty()
    model_id: string;

    @IsString()
    @IsOptional()
    duration?: string = VideoDuration.SHORT; // 默认为5秒
}

/**
 * 任务价格查询响应DTO
 */
export class TaskPriceResponseDto {
    @IsString()
    model_id: string;

    price: number;

    @IsString()
    duration: string;

    @IsString()
    @IsOptional()
    model_name?: string;

    @IsString()
    @IsOptional()
    currency?: string = 'credits';
}
