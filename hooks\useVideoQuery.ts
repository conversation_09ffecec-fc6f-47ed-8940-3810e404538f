import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { postApi } from '@/lib/api/post';
import { favoriteApi, FavoriteTargetType } from '@/lib/api/favorite';
import { useAuth } from '@/contexts/auth-context';

// 查询键前缀
const VIDEO_QUERY_KEY = 'videos';
const FAVORITE_QUERY_KEY = 'favorites';

/**
 * 获取视频详情的Hook
 * @param id 视频ID
 */
export function useVideoQuery(id?: string) {
    return useQuery({
        queryKey: [VIDEO_QUERY_KEY, id],
        queryFn: async () => {
            if (!id) throw new Error('Video ID is required');
            const { data } = await postApi.getPost(id);
            return data;
        },
        enabled: !!id, // 仅在id存在时执行查询
    });
}

/**
 * 获取视频评论的Hook
 * @param videoId 视频ID 
 * @param parentId 父评论ID（可选）
 */
export function useVideoCommentsQuery(videoId?: string, parentId?: string) {
    return useQuery({
        queryKey: [VIDEO_QUERY_KEY, videoId, 'comments', parentId],
        queryFn: async () => {
            if (!videoId) throw new Error('Video ID is required');
            const { data } = await postApi.getPostComments(videoId, parentId);
            return data.comments;
        },
        enabled: !!videoId,
    });
}

/**
 * 获取用户收藏状态的Hook
 * @param targetId 目标ID
 * @param targetType 目标类型
 */
export function useFavoriteStatusQuery(targetId?: string, targetType = FavoriteTargetType.POST) {
    const { isAuthenticated } = useAuth();

    return useQuery({
        queryKey: [FAVORITE_QUERY_KEY, 'status', targetId, targetType],
        queryFn: async () => {
            if (!targetId) throw new Error('Target ID is required');
            if (!isAuthenticated) return false;

            const params = {
                item_type: targetType,
                ...(targetType === FavoriteTargetType.POST
                    ? { post_id: targetId }
                    : { model_id: targetId })
            };

            const result = await favoriteApi.checkFavorite(params);
            return result.favorite;
        },
        enabled: !!targetId && isAuthenticated,
    });
}

/**
 * 获取用户收藏列表的Hook
 * @param itemType 收藏类型
 */
export function useFavoritesQuery(itemType?: string) {
    return useQuery({
        queryKey: [FAVORITE_QUERY_KEY, 'list', itemType],
        queryFn: async () => {
            const result = await favoriteApi.getFavorites({ item_type: itemType });
            return result;
        },
    });
}

/**
 * 收藏/取消收藏的Hook
 */
export function useFavoriteMutation() {
    const queryClient = useQueryClient();

    // 添加收藏
    const addFavorite = useMutation({
        mutationFn: async ({ id, type = FavoriteTargetType.POST }: { id: string; type?: FavoriteTargetType }) => {
            return favoriteApi.addFavorite({
                post_id: type === FavoriteTargetType.POST ? id : undefined,
                model_id: type === FavoriteTargetType.MODEL ? id : undefined,
                item_type: type,
            });
        },
        onSuccess: (_, variables) => {
            // 更新收藏状态查询
            queryClient.invalidateQueries({
                queryKey: [FAVORITE_QUERY_KEY, 'status', variables.id, variables.type],
            });
            // 更新收藏列表查询
            queryClient.invalidateQueries({
                queryKey: [FAVORITE_QUERY_KEY, 'list'],
            });
        },
    });

    // 移除收藏
    const removeFavorite = useMutation({
        mutationFn: async ({ id, type = FavoriteTargetType.POST }: { id: string; type?: FavoriteTargetType }) => {
            return favoriteApi.removeFavorite({
                post_id: type === FavoriteTargetType.POST ? id : undefined,
                model_id: type === FavoriteTargetType.MODEL ? id : undefined,
                item_type: type,
            });
        },
        onSuccess: (_, variables) => {
            // 更新收藏状态查询
            queryClient.invalidateQueries({
                queryKey: [FAVORITE_QUERY_KEY, 'status', variables.id, variables.type],
            });
            // 更新收藏列表查询
            queryClient.invalidateQueries({
                queryKey: [FAVORITE_QUERY_KEY, 'list'],
            });
        },
    });

    // 切换收藏状态
    const toggleFavorite = async (id: string, isFavorite: boolean, type = FavoriteTargetType.POST) => {
        if (isFavorite) {
            return removeFavorite.mutateAsync({ id, type });
        } else {
            return addFavorite.mutateAsync({ id, type });
        }
    };

    return {
        addFavorite,
        removeFavorite,
        toggleFavorite,
        isLoading: addFavorite.isPending || removeFavorite.isPending,
    };
} 