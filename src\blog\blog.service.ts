import { Injectable, Inject, BadRequestException } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import {
    CreateBlogPostDto,
    BlogPostResponseDto
} from './dto/blog-post.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class BlogService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {
        this.logger.setContext(BlogService.name);
    }

    /**
     * 创建博客文章
     * @param authorId 作者ID (不使用，因为表结构中没有author_id字段)
     * @param createBlogPostDto 创建博客文章DTO
     * @returns 创建的博客文章信息
     */
    async createBlogPost(authorId: string, createBlogPostDto: CreateBlogPostDto): Promise<BlogPostResponseDto> {
        try {
            const {
                title,
                slug,
                content,
                excerpt,
                cover_image,
                published_at,
                seo_title,
                seo_description,
                seo_keywords,
                category,
                tags,
                cover_img_prompt
            } = createBlogPostDto;

            // 生成UUID作为ID
            const id = uuidv4();

            // 如果没有提供发布时间，则使用当前时间
            const actualPublishedAt = published_at || new Date().toISOString();

            // 创建博客文章
            const { data, error } = await this.supabase
                .from('blog_posts')
                .insert({
                    id,
                    title,
                    slug,
                    content,
                    excerpt,
                    cover_image,
                    published_at: actualPublishedAt,
                    seo_title,
                    seo_description,
                    seo_keywords,
                    category,
                    tags,
                    cover_img_prompt
                })
                .select('*')
                .single();

            if (error) {
                this.logger.error('创建博客文章失败', error);
                throw new BadRequestException('创建博客文章失败: ' + error.message);
            }

            return data;
        } catch (error) {
            this.logger.error(`创建博客文章时发生错误: ${error.message}`, error.stack);
            throw error;
        }
    }
}
