import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 获取用户的优惠券列表
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // 验证用户会话
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }
    
    // 转发请求到后端 API
    const response = await fetch(`${process.env.REELAPI_SERVER_URL}/api/coupon/user-coupons`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }
    
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('获取用户优惠券列表失败:', error);
    return NextResponse.json(
      { error: '获取用户优惠券列表失败' }, 
      { status: 500 }
    );
  }
}
