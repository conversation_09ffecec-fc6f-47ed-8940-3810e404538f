/**
 * 环境配置
 */

// 判断当前环境
const isDevelopment = process.env.NODE_ENV === 'development';

// 统计数据配置
export const STATS_CONFIG = {
    // 是否启用统计数据上报
    // 默认在开发环境下禁用，生产环境启用
    // 也可以通过环境变量 NEXT_PUBLIC_ENABLE_STATS 手动控制
    ENABLE_STATS: process.env.NEXT_PUBLIC_ENABLE_STATS
        ? process.env.NEXT_PUBLIC_ENABLE_STATS === 'true'
        : !isDevelopment,
};

// API配置
export const API_CONFIG = {
    // 根据环境选择API基础URL
    BASE_URL: isDevelopment
        ? process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080'
        : 'https://nestapi.reelmind.ai',

    // API路径
    ENDPOINTS: {
        // 视频生成相关
        GENERATION: {
            CREATE_TASK: '/generation/gen-video',
            GET_USER_TASKS: '/generation/user-tasks',
            GET_TASK_DETAIL: '/generation/task',
            GET_TASK_PRICE: '/generation/task/price',
        },

        // 模型相关
        MODEL: {
            GET_MODELS: '/models',
            GET_MODEL: '/models', // 具体ID会在调用时拼接
        },

        // 会员系统相关
        MEMBERSHIP: {
            GET_USER_MEMBERSHIP: '/membership/info',
            GET_PLANS: '/membership/plans',
            CREATE_MEMBERSHIP: '/membership/create',
            SUBSCRIBE_MEMBERSHIP: '/membership/subscribe',
        },
        PAYMENT: {
            CANCEL_PAYMENT: '/payment/cancel',
        },

        // 优惠券相关
        COUPON: {
            GET_STATUS: '/coupon/status',
            CLAIM: '/coupon/claim',
            GET_USER_COUPONS: '/coupon/user-coupons',
            GET_USAGE_HISTORY: '/coupon/usage-history',
            CALCULATE_PRICE: '/coupon/calculate-price',
            APPLY_TO_PAYMENT: '/coupon/apply-to-payment',
            CHECK_ELIGIBILITY: '/coupon/check-eligibility',
        },

        // 队列信息相关
        QUEUE: {
            GET_QUEUE_INFO: '/generation/task/queue-info',
        },

        // 反馈相关
        FEEDBACK: {
            CREATE: '/feedback',
            GET_USER_FEEDBACKS: '/feedback/user',
            GET_FEEDBACK: '/feedback', // 具体ID会在调用时拼接
            UPDATE_STATUS: '/feedback', // 具体ID和/status会在调用时拼接
            DELETE: '/feedback', // 具体ID会在调用时拼接
        },
    },

    // API超时设置（毫秒）
    TIMEOUT: 30000,
};