"use client"

import { useRef, useState, useEffect } from "react"
import { ChevronRight } from "lucide-react"
import { Effect } from "@/types/model"
import { EffectCard } from "@/app/models/components/effect-card"
import Link from "next/link"

interface EffectsSliderProps {
    effects: Effect[]
    title: string
    isLoading?: boolean
}

export function EffectsSlider({ effects, title, isLoading = false }: EffectsSliderProps) {
    const sliderRef = useRef<HTMLDivElement>(null)
    const [showRightButton, setShowRightButton] = useState(true)
    const [scrollPosition, setScrollPosition] = useState(0)
    const [maxScroll, setMaxScroll] = useState(0)

    // 初始化和更新最大滚动距离
    useEffect(() => {
        if (sliderRef.current) {
            const container = sliderRef.current
            const scrollableWidth = container.scrollWidth - container.clientWidth
            setMaxScroll(scrollableWidth)

            // 确保按钮状态正确
            if (scrollableWidth <= 0) {
                setShowRightButton(false)
            } else {
                setShowRightButton(true)
            }
        }
    }, [effects])

    const handleScrollRight = () => {
        if (sliderRef.current) {
            const container = sliderRef.current
            const scrollAmount = container.clientWidth * 0.8
            const newScrollPosition = Math.min(container.scrollLeft + scrollAmount, maxScroll)

            // 使用scrollTo而不是scrollBy确保更精确的滚动
            container.scrollTo({
                left: newScrollPosition,
                behavior: 'smooth'
            })

            // 更新滚动位置
            setScrollPosition(newScrollPosition)

            // 如果已经滚动到接近末尾，隐藏按钮
            if (newScrollPosition >= maxScroll - 10) {
                setShowRightButton(false)
            }
        }
    }

    // 当滚动事件发生时
    const handleScroll = () => {
        if (sliderRef.current) {
            const container = sliderRef.current
            const currentPosition = container.scrollLeft
            setScrollPosition(currentPosition)

            // 更新按钮状态
            if (currentPosition >= maxScroll - 10) {
                setShowRightButton(false)
            } else {
                setShowRightButton(true)
            }
        }
    }

    if (effects.length === 0 && !isLoading) {
        return null
    }

    return (
        <section className="mb-6">
            <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-medium">{title}</h2>
                <Link
                    href="/create"
                    className="text-sm text-blue-500 hover:text-blue-400 transition-colors"
                >
                    View all
                </Link>
            </div>

            <div className="relative">
                <div
                    ref={sliderRef}
                    className="flex overflow-x-auto pb-2 scrollbar-hide snap-x snap-mandatory"
                    onScroll={handleScroll}
                >
                    {!effects.length ? (
                        // 加载骨架屏
                        Array(5).fill(0).map((_, i) => (
                            <div
                                key={`skeleton-${i}`}
                                className="min-w-[200px] h-[250px] bg-card/20 animate-pulse rounded-xl mx-2 flex-shrink-0 snap-start"
                            />
                        ))
                    ) : (
                        effects.map((effect) => (
                            <div
                                key={effect.id}
                                className="min-w-[200px] mx-2 flex-shrink-0 snap-start hover:scale-[1.02] transition-transform"
                            >
                                <EffectCard
                                    autoPlay={false}
                                    effect={{
                                        id: effect.id,
                                        name: effect.name,
                                        desc: effect.desc || "",
                                        cover_img: effect.cover_img || "/placeholder.svg",
                                        created_at: effect.created_at,
                                        trigger_words: effect.trigger_words || [],
                                    }}
                                    onSelectEffect={() => {
                                        window.location.href = `/create?effect=${effect.name}`;
                                    }}
                                />
                            </div>
                        ))
                    )}
                </div>

                {showRightButton && effects.length > 0 && (
                    <button
                        className="absolute right-2 top-1/2 -translate-y-1/2 w-10 h-10 bg-background/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-xl hover:bg-background transition-colors z-20 border border-white/10 ring-4 ring-background/20"
                        onClick={handleScrollRight}
                        aria-label="Scroll right"
                    >
                        <ChevronRight className="w-5 h-5" />
                    </button>
                )}
            </div>
        </section>
    )
} 