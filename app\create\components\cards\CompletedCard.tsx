"use client"

import { Download, Sparkles } from "lucide-react";
import type { VideoTask } from "@/types/video-task";
import { cn } from "@/lib/utils";
import { CardInfoSection } from "./CardInfoSection";
import { useRef, useCallback } from "react";

interface CompletedCardProps {
    video: VideoTask;
    onPublishClick: (video: VideoTask, e: React.MouseEvent) => void;
    onShareClick?: (video: VideoTask, e: React.MouseEvent) => void;
    onDownloadClick: (video: VideoTask) => void;
    onRemixClick?: (videoId: string) => void;
    isDownloading?: boolean;
}

/**
 * CompletedCard Component - 显示已完成状态的视频任务
 * 显示视频预览，支持hover自动播放
 */
export const CompletedCard = ({
    video,
    onPublishClick,
    onDownloadClick,
    onRemixClick,
    isDownloading = false
}: CompletedCardProps) => {
    const videoRef = useRef<HTMLVideoElement>(null);

    // 处理视频hover播放
    const handleVideoHover = useCallback(() => {
        const videoElement = videoRef.current;
        if (videoElement && videoElement.paused) {
            videoElement.play().catch(() => {
                // 静默处理错误，避免控制台警告
            });
        }
    }, []);

    // 处理视频离开暂停
    const handleVideoLeave = useCallback(() => {
        const videoElement = videoRef.current;
        if (videoElement && !videoElement.paused) {
            videoElement.pause();
            videoElement.currentTime = 0;
        }
    }, []);

    return (
        <>
            {/* Video preview area */}
            <div 
                className="relative w-full overflow-hidden bg-muted/60 cursor-pointer"
                onMouseEnter={handleVideoHover}
                onMouseLeave={handleVideoLeave}
            >
                {/* Video preview (auto-plays on hover for completed videos) */}
                {video.output_result?.video_url && (
                    <div className="aspect-video cursor-pointer">
                        <video
                            ref={videoRef}
                            src={video.output_result.video_url}
                            className={cn(
                                "h-full w-full",
                                video.input_params.ratio === "9:16" ? "object-contain" : "object-cover"
                            )}
                            muted
                            loop
                            playsInline
                            preload="metadata"
                        />
                    </div>
                )}

                {/* Video info overlay - only show info, no buttons */}
                <div className={cn(
                    "absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent",
                    "transition-opacity duration-300 flex items-end p-3",
                    "hover:opacity-100 opacity-0"
                )}>
                    <div className="text-xs text-white/90 font-medium">
                        {video.input_params.ratio} · {video.input_params.definition || "SD"}
                    </div>
                </div>
            </div>

            {/* Information area */}
            <CardInfoSection video={video}>
                {/* Action buttons row */}
                <div className="flex items-center gap-2 h-11">
                    {/* Remix button */}
                    <button
                        className={cn(
                            "flex-1 flex items-center justify-center gap-2 px-4 py-2.5",
                            "text-sm font-medium rounded-md transition-all",
                            "border border-black/5 dark:border-gray-800/30",
                            "bg-white/10 dark:bg-gray-900/30 hover:bg-white/20 dark:hover:bg-gray-900/40"
                        )}
                        onClick={(e) => {
                            e.stopPropagation();
                            if (onRemixClick) onRemixClick(video.id);
                        }}
                        title="Create a new video using this prompt"
                    >
                        <Sparkles size={16} />
                        Remix
                    </button>

                    {/* Publish button */}
                    <button
                        className="px-4 h-full
                            bg-white/10 dark:bg-gray-900/30
                            hover:bg-white/20 dark:hover:bg-gray-900/40
                            backdrop-blur-md
                            text-xs font-medium rounded-md transition-all
                            border border-black/5 dark:border-gray-800/30"
                        onClick={(e) => {
                            e.stopPropagation();
                            onPublishClick(video, e);
                        }}
                        title="Publish to community"
                    >
                        Publish
                    </button>

                    {/* Download button */}
                    <button
                        className="flex items-center justify-center w-10 h-full
                            bg-white/10 dark:bg-gray-900/30
                            hover:bg-white/20 dark:hover:bg-gray-900/40
                            backdrop-blur-md
                            text-gray-700 dark:text-white
                            rounded-md transition-all
                            border border-black/5 dark:border-gray-800/30"
                        onClick={(e) => {
                            e.stopPropagation();
                            onDownloadClick(video);
                        }}
                        title="Download this video"
                    >
                        <Download size={16} />
                    </button>
                </div>
            </CardInfoSection>
        </>
    );
};
