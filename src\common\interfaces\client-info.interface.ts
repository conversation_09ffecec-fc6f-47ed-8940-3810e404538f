/**
 * 客户端信息接口
 * 用于统一收集客户端请求信息，便于异常监控和风控逻辑
 */
export interface ClientInfo {
    // 客户端IP地址
    ip: string;

    // 用户代理字符串
    userAgent: string;

    // 请求ID，用于追踪整个请求流程
    requestId: string;

    // 设备信息
    device?: {
        type?: string;       // 设备类型: mobile, tablet, desktop
        platform?: string;   // 操作系统平台: ios, android, windows, macos, linux
        browser?: string;    // 浏览器类型: chrome, safari, firefox, edge
        version?: string;    // 浏览器版本
    };

    // 位置信息
    location?: {
        country?: string;    // 国家
        region?: string;     // 地区
        city?: string;       // 城市
        timezone?: string;   // 时区
    };

    // 网络信息
    network?: {
        isp?: string;        // 互联网服务提供商
        type?: string;       // 网络类型: wifi, cellular, ethernet
        asn?: string;        // 自治系统号码
    };

    // 会话信息
    session?: {
        referrer?: string;   // 来源页面
        entryPath?: string;  // 入口路径
        language?: string;   // 语言偏好
    };

    // 行为信息
    behavior?: {
        previousRequests?: number; // 之前请求次数
        lastActivityAt?: Date;    // 最后活动时间
        unusualPatterns?: string[]; // 不寻常的行为模式
    };

    // 扩展字段，用于存储其他可能需要的信息
    extraData?: Record<string, any>;
} 