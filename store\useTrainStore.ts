import { create } from "zustand"
import { persist } from "zustand/middleware"
import { type Video, type TrainingSettings, defaultSettings } from "../app/train/types"
import { TrainTask, TrainTaskStatus } from "@/lib/api/train"

interface TrainState {
    videos: Video[]
    totalSize: number
    settings: TrainingSettings
    userTasks: TrainTask[]
    userTasksTotal: number
    userTasksFilter: {
        status?: TrainTaskStatus
        limit: number
        offset: number
    }

    // Actions
    setVideos: (videos: Video[]) => void
    addVideos: (newVideos: Video[]) => void
    removeVideo: (id: string) => void
    updateTotalSize: (size: number) => void
    updateVideoUrl: (id: string, url: string) => void
    updateSetting: <K extends keyof TrainingSettings>(key: K, value: TrainingSettings[K]) => void
    resetSettings: () => void
    reset: () => void

    // 训练记录相关
    setUserTasks: (tasks: TrainTask[], total: number) => void
    updateUserTasksFilter: (filter: Partial<TrainState['userTasksFilter']>) => void
}

export const useTrainStore = create<TrainState>()(
    persist(
        (set) => ({
            videos: [],
            totalSize: 0,
            settings: defaultSettings,
            userTasks: [],
            userTasksTotal: 0,
            userTasksFilter: {
                limit: 10,
                offset: 0
            },

            setVideos: (videos) => set({ videos }),
            addVideos: (newVideos) =>
                set((state) => ({
                    videos: [...state.videos, ...newVideos],
                })),
            removeVideo: (id) =>
                set((state) => ({
                    videos: state.videos.filter((v) => v.id !== id),
                })),
            updateTotalSize: (size) => set({ totalSize: size }),
            updateVideoUrl: (id, url) =>
                set((state) => ({
                    videos: state.videos.map((v) =>
                        v.id === id ? { ...v, url } : v
                    ),
                })),
            updateSetting: (key, value) =>
                set((state) => ({
                    settings: { ...state.settings, [key]: value },
                })),
            resetSettings: () => set({ settings: defaultSettings }),
            reset: () => set({ videos: [], totalSize: 0, settings: defaultSettings }),

            // 训练记录相关
            setUserTasks: (tasks, total) => set({
                userTasks: tasks,
                userTasksTotal: total
            }),
            updateUserTasksFilter: (filter) => set((state) => ({
                userTasksFilter: { ...state.userTasksFilter, ...filter }
            })),
        }),
        {
            name: "train-storage",
            partialize: (state) => ({
                settings: state.settings,
                userTasksFilter: state.userTasksFilter
            }),
            merge: (persistedState: any, currentState: TrainState) => ({
                ...currentState,
                ...persistedState,
                settings: {
                    ...defaultSettings,
                    ...persistedState.settings,
                },
                userTasksFilter: {
                    ...currentState.userTasksFilter,
                    ...persistedState.userTasksFilter,
                }
            }),
        }
    )
)

