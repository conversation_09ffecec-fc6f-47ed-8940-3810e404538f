-- 创建错误审计日志表
CREATE TABLE IF NOT EXISTS error_audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    task_id UUID,
    category VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    error_message TEXT NOT NULL,
    error_details JSONB,
    context_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    
    -- 添加索引以提高查询性能
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL,
    CONSTRAINT fk_task FOREIGN KEY (task_id) REFERENCES video_gen_tasks(id) ON DELETE SET NULL
);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_error_audit_logs_user_id ON error_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_error_audit_logs_task_id ON error_audit_logs(task_id);
CREATE INDEX IF NOT EXISTS idx_error_audit_logs_category ON error_audit_logs(category);
CREATE INDEX IF NOT EXISTS idx_error_audit_logs_severity ON error_audit_logs(severity);
CREATE INDEX IF NOT EXISTS idx_error_audit_logs_resolved ON error_audit_logs(resolved);
CREATE INDEX IF NOT EXISTS idx_error_audit_logs_created_at ON error_audit_logs(created_at);
