import { PostItemDto } from "@/types/posts"
import { JsonLd } from "@/components/seo/JsonLd"

interface VideoJsonLdProps {
    post: PostItemDto
}

/**
 * 视频结构化数据组件 - 生成视频的JsonLd结构化数据
 */
export function VideoJsonLd({ post }: VideoJsonLdProps) {
    // 视频的结构化数据
    const videoJsonLd = {
        "@context": "https://schema.org",
        "@type": "VideoObject",
        "name": post.title || `AI Generated Video #${post.id}`,
        "description": post.description || "This is an AI-generated video showcasing the latest in video generation technology.",
        "thumbnailUrl": post.videos?.cover_img || post.video_url,
        "uploadDate": post.created_at,
        "contentUrl": post.video_url,
        "embedUrl": `https://reelmind.ai/posts/embed/${post.id}`,
        "interactionStatistic": [
            {
                "@type": "InteractionCounter",
                "interactionType": "https://schema.org/WatchAction",
                "userInteractionCount": 1024
            },
            {
                "@type": "InteractionCounter",
                "interactionType": "https://schema.org/LikeAction",
                "userInteractionCount": post.like_count || 0
            }
        ],
        "author": {
            "@type": "Person",
            "name": post.username
        },
        "keywords": "AI,text-to-video,AIGC",
        "creator": {
            "@type": "Person",
            "name": post.username
        }
    };

    return <JsonLd data={videoJsonLd} />
} 