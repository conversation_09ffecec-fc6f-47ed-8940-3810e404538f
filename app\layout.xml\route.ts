import { NextResponse } from 'next/server';
import { postApi } from '@/lib/api/post';
import { getBlogPostsCount } from '@/lib/blog';
import { modelApi } from '@/lib/api/model';
import type { Model } from '@/types/model';
import { MetadataRoute } from 'next';

type ChangeFrequency = 'daily' | 'weekly' | 'monthly' | 'always' | 'hourly' | 'yearly' | 'never';

// 验证日期并返回有效的日期对象
const getValidDate = (dateInput: string | Date | undefined | null): Date => {
    if (!dateInput) return new Date();

    try {
        const date = dateInput instanceof Date ? dateInput : new Date(dateInput);
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            return new Date(); // 如果无效则返回当前日期
        }
        return date;
    } catch (error) {
        return new Date(); // 出错时返回当前日期
    }
};

// 生成URL sitemap XML
function generateUrlSitemapXML(entries: Array<{
    url: string;
    lastModified: Date;
    changeFrequency?: ChangeFrequency;
    priority?: number;
}>): string {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

    for (const entry of entries) {
        xml += '  <url>\n';
        xml += `    <loc>${entry.url}</loc>\n`;
        xml += `    <lastmod>${entry.lastModified.toISOString()}</lastmod>\n`;
        if (entry.changeFrequency) {
            xml += `    <changefreq>${entry.changeFrequency}</changefreq>\n`;
        }
        if (entry.priority !== undefined) {
            xml += `    <priority>${entry.priority}</priority>\n`;
        }
        xml += '  </url>\n';
    }

    xml += '</urlset>';
    return xml;
}

// 处理sitemap请求
export async function GET() {
    try {
        // 网站基本URL
        const baseUrl = 'https://reelmind.ai';

        // 创建URL函数
        const createSitemapEntry = (path: string, changeFreq: ChangeFrequency = 'weekly', priority: number = 0.8) => ({
            url: `${baseUrl}${path}`,
            lastModified: new Date(), // 使用当前日期
            changeFrequency: changeFreq,
            priority: priority,
        });

        // 主要页面路径 - 按重要性排序
        const mainPages = [
            { path: '', changeFreq: 'daily' as ChangeFrequency, priority: 1.0 },
            { path: '/explore', changeFreq: 'daily' as ChangeFrequency, priority: 1.0 },
            { path: '/models', changeFreq: 'weekly' as ChangeFrequency, priority: 0.8 },
            { path: '/create', changeFreq: 'monthly' as ChangeFrequency, priority: 0.8 },
            { path: '/train', changeFreq: 'monthly' as ChangeFrequency, priority: 0.7 },
            { path: '/blog', changeFreq: 'daily' as ChangeFrequency, priority: 0.8 },
        ];

        // 创建静态页面URL
        const staticPages = mainPages.map(page => createSitemapEntry(page.path, page.changeFreq, page.priority));

        // 动态内容 - 从API获取最新内容
        let videoPages: MetadataRoute.Sitemap = [];

        try {
            // 获取最新视频内容
            const feedResponse = await postApi.getFeed({
                limit: 100, // 获取较多的视频数据用于生成站点地图
                offset: 0
            });

            if (feedResponse && feedResponse.data && feedResponse.data.posts) {
                // 将视频帖子添加到站点地图
                videoPages = feedResponse.data.posts.map(post => ({
                    url: `${baseUrl}/posts/${post.id}`,
                    lastModified: getValidDate(post.created_at), // 使用验证后的日期
                    changeFrequency: 'weekly' as ChangeFrequency,
                    priority: 0.6,
                }));
            }
        } catch (error) {
            console.error('生成Sitemap时获取视频数据失败:', error);
            // 如果获取失败，不添加视频页面，但不影响其他页面的生成
        }

        // 模型页面的路径也可以动态获取
        let modelPages: MetadataRoute.Sitemap = [];
        try {
            // 假设我们想要获取第一页的前100个模型用于sitemap
            const modelsResponse = await modelApi.getModels(1, 100);
            if (modelsResponse?.models) {
                modelPages = modelsResponse.models.map((model: Model) => ({
                    url: `${baseUrl}/models/${model.id}`,
                    lastModified: getValidDate(model.updated_at || model.created_at), // 使用下划线命名
                    changeFrequency: 'weekly' as ChangeFrequency,
                    priority: 0.7,
                }));
            }
        } catch (error) {
            console.error('生成Sitemap时获取模型数据失败:', error);
            // 获取失败不影响其他页面生成
        }

        // 博客分页页面
        let blogPaginationPages: MetadataRoute.Sitemap = [];

        const totalPosts = await getBlogPostsCount();

        const pageSize = 21; // 与blog/page.tsx中保持一致
        const totalPages = Math.ceil(totalPosts / pageSize);

        // 添加博客分页页面到sitemap
        // 第一页是/blog，已经在主页中包含了，所以从第2页开始
        if (totalPages > 1) {
            blogPaginationPages = Array.from({ length: totalPages - 1 }, (_, i) => ({
                url: `${baseUrl}/blog?page=${i + 2}`,
                lastModified: new Date(), // 使用当前日期
                changeFrequency: 'weekly' as ChangeFrequency,
                priority: 0.6, // 略低于文章页面
            }));
        }

        // 合并所有页面到一个数组
        const urlEntries = [
            ...staticPages,
            ...videoPages,
            ...modelPages,
            ...blogPaginationPages,
        ];

        // 生成URL sitemap XML
        const urlSitemapXML = generateUrlSitemapXML(urlEntries as any);

        // 返回XML响应
        return new NextResponse(urlSitemapXML, {
            headers: {
                'Content-Type': 'application/xml',
                'Content-Length': Buffer.byteLength(urlSitemapXML).toString(),
            },
        });
    } catch (error) {
        console.error(`生成主Sitemap时出错:`, error);
        return NextResponse.json(
            { error: '生成Sitemap时出错' },
            { status: 500 }
        );
    }
}
