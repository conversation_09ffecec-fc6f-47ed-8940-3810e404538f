import { NSFWLevel } from "src/common/constants/video";

enum SupportedFeatures {
    TEXT_TO_VIDEO = 'text_to_video',
    IMAGE_TO_VIDEO = 'image_to_video',
    VIDEO_TO_VIDEO = 'video_to_video',
}

export type Model = {
    id: string; // 模型ID
    user_id: string; // 用户ID
    name: string; // 模型名称
    description: string; // 模型描述
    source: string; // 模型来源
    model_type: string; // 模型类型
    storage_path: string; // 模型存储路径
    is_public: boolean; // 是否公开
    nsfw_level: NSFWLevel; // NSFW分级
    created_at: Date; // 创建时间
    updated_at: Date; // 更新时间
    metadata: any; // 元数据
    supported_features: SupportedFeatures[]; // 支持的功能
    default_config: {
        seed?: number; // 种子
        ratio?: string; // 比例
        steps?: number; // 步数
        prompt?: string; // 提示词
        duration?: string; // 时长
        gen_type?: string; // 生成类型
        model_id?: string; // 模型ID
        definition?: string; // 清晰度
        refer_img_url?: string; // 参考图片URL
        guidance_scale?: number; // 引导尺度
        negative_prompt?: string; // 负向提示词
    };
    size?: string; // 大小
    cover_img?: string; // 封面图片
    price: number; // 价格
    trainable: boolean; // 是否可训练
    category: string; // 分类
    group: string; // 分组
}