import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export enum Environment {
    DEVELOPMENT = 'development',
    PRODUCTION = 'production',
    TEST = 'test'
}

export enum StripeEnvironment {
    TEST = 'test',
    LIVE = 'live'
}

@Injectable()
export class EnvironmentConfig {
    constructor(private readonly configService: ConfigService) {}

    /**
     * 获取当前运行环境
     */
    getEnvironment(): Environment {
        const env = this.configService.get<string>('NODE_ENV', Environment.DEVELOPMENT);
        return env as Environment;
    }

    /**
     * 判断是否为开发环境
     */
    isDevelopment(): boolean {
        return this.getEnvironment() === Environment.DEVELOPMENT;
    }

    /**
     * 判断是否为生产环境
     */
    isProduction(): boolean {
        return this.getEnvironment() === Environment.PRODUCTION;
    }

    /**
     * 判断是否为测试环境
     */
    isTest(): boolean {
        return this.getEnvironment() === Environment.TEST;
    }

    /**
     * 获取Stripe环境
     */
    getStripeEnvironment(): StripeEnvironment {
        // 如果明确设置了STRIPE_ENVIRONMENT，使用该值
        const stripeEnv = this.configService.get<string>('STRIPE_ENVIRONMENT') as StripeEnvironment;
        if (stripeEnv) {
            return stripeEnv;
        }

        // 否则根据NODE_ENV推断
        return this.isProduction() ? StripeEnvironment.LIVE : StripeEnvironment.TEST;
    }

    /**
     * 判断是否使用Stripe测试模式
     */
    isStripeTestMode(): boolean {
        return this.getStripeEnvironment() === StripeEnvironment.TEST;
    }

    /**
     * 获取数据库环境标识
     * 用于membership_plans表的env字段过滤
     */
    getDatabaseEnvironment(): StripeEnvironment {
        const stripeEnv = this.getStripeEnvironment();
        return stripeEnv === StripeEnvironment.LIVE ? StripeEnvironment.LIVE : StripeEnvironment.TEST;
    }

    /**
     * 获取环境相关的配置
     */
    getEnvironmentConfig() {
        return {
            nodeEnv: this.getEnvironment(),
            stripeEnv: this.getStripeEnvironment(),
            dbEnv: this.getDatabaseEnvironment(),
            isDevelopment: this.isDevelopment(),
            isProduction: this.isProduction(),
            isTest: this.isTest(),
            isStripeTestMode: this.isStripeTestMode(),
        };
    }

    /**
     * 获取Stripe相关配置
     */
    getStripeConfig() {
        const isTestMode = this.isStripeTestMode();

        return {
            secretKey: this.configService.get<string>(
                isTestMode ? 'STRIPE_TEST_SECRET_KEY' : 'STRIPE_SECRET_KEY'
            ),
            webhookSecret: this.configService.get<string>(
                isTestMode ? 'STRIPE_TEST_WEBHOOK_SECRET' : 'STRIPE_WEBHOOK_SECRET'
            ),
            apiVersion: this.configService.get<string>('STRIPE_API_VERSION'),
            isTestMode,
        };
    }

    /**
     * 验证必要的环境变量是否已配置
     */
    validateEnvironmentConfig(): { isValid: boolean; missingVars: string[] } {
        const missingVars: string[] = [];
        const stripeConfig = this.getStripeConfig();

        if (!stripeConfig.secretKey) {
            missingVars.push(this.isStripeTestMode() ? 'STRIPE_TEST_SECRET_KEY' : 'STRIPE_SECRET_KEY');
        }

        if (!stripeConfig.webhookSecret) {
            missingVars.push(this.isStripeTestMode() ? 'STRIPE_TEST_WEBHOOK_SECRET' : 'STRIPE_WEBHOOK_SECRET');
        }

        if (!stripeConfig.apiVersion) {
            missingVars.push('STRIPE_API_VERSION');
        }

        // 检查Supabase配置
        if (!this.configService.get<string>('SUPABASE_URL')) {
            missingVars.push('SUPABASE_URL');
        }

        if (!this.configService.get<string>('SUPABASE_KEY')) {
            missingVars.push('SUPABASE_KEY');
        }

        return {
            isValid: missingVars.length === 0,
            missingVars,
        };
    }
}
