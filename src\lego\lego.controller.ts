import { Controller, Post, Get, Body, Param, Query, Req, UseGuards, HttpCode, ParseUUIDPipe, BadRequestException } from '@nestjs/common';
import { LegoService } from './lego.service';
import { GenPicRequestDto, TickProgressDto } from './dto/gen-pic.dto';
import { PicTaskDto, FinishTaskDto, PicTaskResponseDto, PopTaskDto } from './dto/pic-task.dto';
import { JwtGuard } from '../common/guards/jwt.guard';
import { Request } from 'express';
import { CancelTaskDto } from './dto/cancel.dto';
import { PicTaskStatus } from 'src/common/constants/pic';
import { QueueInfoResponseDto } from './dto/queue-info.dto';
import { EnhancePromptRequestDto, EnhancePromptResponseDto } from './dto/enhance-prompt.dto';

@Controller('lego')
export class LegoController {
    constructor(private readonly legoService: LegoService) {}

    /**
     * 获取图片生成可用的模型列表
     */
    @Get('models')
    async getPicModels() {
        return this.legoService.getPicModels();
    }

    /**
     * 检查用户FLUX模型使用限制
     */
    @Get('flux-usage-limit')
    @UseGuards(JwtGuard)
    async checkFluxUsageLimit(@Req() req: Request): Promise<{ canUse: boolean; usageCount: number; limit: number }> {
        const userId = req.user.id;
        if (!userId) {
            throw new BadRequestException('无效的用户信息');
        }

        return this.legoService.getFluxUsageLimitInfo(userId);
    }

    /**
     * 面向内部人员的图片生成任务接口
     */
    @Post('inter-pic')
    async interPic(
        @Body() genPicDto: GenPicRequestDto,
    ): Promise<PicTaskResponseDto> {
        const userId = '406df7a4-aaa1-48e7-a62a-a4a8b2e82012';
        return this.legoService.createPicGenerationTask(userId, genPicDto, -1);
    }

    /**
     * 创建图片生成任务
     * @param req 请求对象，包含用户信息
     * @param genPicDto 图片生成参数
     */
    @Post('gen-pic')
    @UseGuards(JwtGuard)
    async createPicGenerationTask(
        @Req() req: Request,
        @Body() genPicDto: GenPicRequestDto,
    ): Promise<PicTaskResponseDto> {
        const userId = req.user.id;
        if (!userId) {
            throw new BadRequestException('无效的用户信息');
        }

        return this.legoService.createPicGenerationTask(userId, genPicDto);
    }

    /**
     * 增强图片生成提示词
     * @param enhancePromptDto 原始提示词
     */
    @Post('enhance-prompt/pic')
    @UseGuards(JwtGuard)
    async enhancePrompt(
        @Body() enhancePromptDto: EnhancePromptRequestDto,
    ): Promise<EnhancePromptResponseDto> {
        return this.legoService.enhancePrompt(enhancePromptDto);
    }

    /**
     * 获取用户的图片生成任务列表
     * @param req 请求对象，包含用户信息
     * @param status 可选的状态过滤
     * @param limit 分页限制
     * @param offset 分页偏移
     */
    @Get('user-tasks')
    @UseGuards(JwtGuard)
    async getUserTasks(
        @Req() req: Request,
        @Query('status') status?: PicTaskStatus,
        @Query('limit') limit?: number,
        @Query('offset') offset?: number,
    ): Promise<{ tasks: PicTaskDto[]; total: number }> {
        const userId = req.user.id;
        return this.legoService.getUserTasks(
            userId,
            status,
            limit ? parseInt(limit.toString(), 10) : undefined,
            offset ? parseInt(offset.toString(), 10) : undefined
        );
    }

    /**
     * 获取任务详情
     * @param taskId 任务ID
     */
    @Get('task/:taskId')
    @UseGuards(JwtGuard)
    async getTaskById(
        @Param('taskId', ParseUUIDPipe) taskId: string,
    ): Promise<PicTaskDto> {
        return this.legoService.getTaskById(taskId);
    }

    /**
     * 取消任务
     * @param req 请求对象，包含用户信息
     * @param taskId 任务ID
     */
    @Post('task/cancel')
    @UseGuards(JwtGuard)
    async cancelTask(
        @Req() req: Request,
        @Body() cancelTaskDto: CancelTaskDto,
    ): Promise<PicTaskDto> {
        const userId = req.user.id;
        const { task_id } = cancelTaskDto;
        return this.legoService.cancelTask(task_id, userId);
    }

    // 以下接口由Python图片生成服务调用，需要单独的API密钥验证

    /**
     * 获取下一个待处理任务
     */
    @Post('gen_pic_task/pop')
    @HttpCode(200)
    async popNextPendingTask(
        @Body() body: PopTaskDto,
    ): Promise<PicTaskDto | null> {
        return this.legoService.popNextPendingTask(body);
    }

    /**
     * 完成图片生成任务
     * @param finishTaskDto 任务完成信息
     */
    @Post('gen_pic_task/finish')
    @HttpCode(200)
    async finishPicGenerationTask(
        @Body() finishTaskDto: FinishTaskDto,
    ): Promise<PicTaskDto> {
        return this.legoService.finishPicGenerationTask(finishTaskDto);
    }

    /**
     * 获取任务的队列信息
     * @param taskId 任务ID
     */
    @Get('task/queue-info/:taskId')
    @UseGuards(JwtGuard)
    async getTaskQueueInfo(
        @Param('taskId', ParseUUIDPipe) taskId: string,
    ): Promise<QueueInfoResponseDto> {
        return this.legoService.getQueueInfo(taskId);
    }

    @Post('gen_pic_task/tick-progress')
    @HttpCode(200)
    async tickProgress(
        @Body() body: TickProgressDto,
    ): Promise<boolean> {
        return this.legoService.tickProgress(body);
    }

    /**
     * fal.ai图片生成webhook回调
     * @param body webhook请求体
     */
    @Post('webhook/fal-ai')
    @HttpCode(200)
    async handleFalAiPicWebhook(@Body() body: any) {
        return this.legoService.handleFalAiPicWebhook(body);
    }
}