import { Controller, Get, Post, Body, Param, Query, UseGuards, Req, Delete } from '@nestjs/common';
import { TagsService } from './tags.service';
import { TagResponseDto } from './dto/tag-response.dto';
import { TagFilterDto } from './dto/tag-filter.dto';
import { CustomLogger } from '../common/services/logger.service';
import { JwtGuard } from '../common/guards/jwt.guard';
import { Request } from 'express';
import { CreateTagDto } from './dto/create-tag.dto';
import { Roles } from 'src/common/decorators/roles.decorator';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Tag } from './constants';

@Controller('tags')
export class TagsController {
    constructor(
        private readonly tagsService: TagsService,
        private readonly logger: CustomLogger,
    ) {
        this.logger.setContext(TagsController.name);
    }

    /**
     * 获取所有标签
     */
    @Get()
    async getAllTags(@Query() filter: TagFilterDto): Promise<TagResponseDto[]> {
        return this.tagsService.getAllTags(filter);
    }

    /**
     * 获取视频的标签
     */
    @Get('video/:videoId')
    async getVideoTags(@Param('videoId') videoId: string): Promise<any> {
        return this.tagsService.getVideoTags(videoId);
    }

    /**
     * 获取模型的标签
     */
    // @Get('model/:modelId')
    // async getModelTags(@Param('modelId') modelId: string): Promise<TagResponseDto[]> {
    //     return this.tagsService.getModelTags(modelId);
    // }

    /**
     * 为模型添加标签
     */
    @Post('model/:modelId')
    @UseGuards(JwtGuard)
    async addTagsToModel(
        @Param('modelId') modelId: string,
        @Body('tags') tags: string[],
        @Req() request: Request,
    ): Promise<string[]> {
        this.logger.log(`用户 ${request['user'].id} 为模型 ${modelId} 添加标签`);
        return this.tagsService.addTagsToModel(modelId, tags);
    }

    /**
     * 根据标签获取视频
     */
    @Get('videos/by-tag/:tagName')
    async getVideosByTag(
        @Param('tagName') tagName: string,
        @Query('limit') limit: number,
        @Query('offset') offset: number,
    ): Promise<any[]> {
        return this.tagsService.getVideosByTag(tagName, limit, offset);
    }

    /**
     * 根据标签获取模型
     */
    @Get('models/by-tag/:tagName')
    async getModelsByTag(
        @Param('tagName') tagName: string,
        @Query('limit') limit: number,
        @Query('offset') offset: number,
    ): Promise<any[]> {
        return this.tagsService.getModelsByTag(tagName, limit, offset);
    }

    /**
     * 健康检查接口
     */
    @Get('health')
    healthCheck(): string {
        return 'Tags服务运行正常';
    }

    /**
     * 管理员标签管理接口
     */

    /**
     * 创建新标签
     */
    @Post('admin/create')
    @UseGuards(RolesGuard)
    @Roles('admin')
    async createTag(
        @Body() createTagDto: CreateTagDto,
        @Req() request: Request,
    ): Promise<TagResponseDto> {
        this.logger.log(`管理员 ${request['user']?.id || 'unknown'} 创建标签: ${createTagDto.name}`);
        return this.tagsService.createTag(createTagDto);
    }

    /**
     * 删除标签
     */
    @Delete('admin/:tagId')
    @UseGuards(RolesGuard)
    @Roles('admin')
    async deleteTag(
        @Param('tagId') tagId: string,
        @Req() request: Request,
    ): Promise<{ success: boolean; message: string }> {
        this.logger.log(`管理员 ${request['user']?.id || 'unknown'} 删除标签: ${tagId}`);
        await this.tagsService.deleteTag(tagId);
        return { success: true, message: '标签删除成功' };
    }

    /**
     * 为视频添加标签 (管理员)
     */
    @Post('admin/video/addTags')
    @UseGuards(RolesGuard)
    @Roles('admin')
    async adminAddTagsToVideo(
        @Body('video_id') video_id: string,
        @Body('tags') tags: Tag[],
        @Req() request: Request,
    ): Promise<Tag[]> {
        this.logger.log(`管理员 ${request['user']?.id || 'unknown'} 为视频 ${video_id} 添加标签`);
        return this.tagsService.saveVideoTags(video_id, tags);
    }
} 