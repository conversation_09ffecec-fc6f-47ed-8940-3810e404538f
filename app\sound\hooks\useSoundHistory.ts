import { useCallback, useEffect } from 'react';
import { useSoundStore, SoundTask } from '@/store/useSoundStore';
import { soundApi, handleApiError } from '@/lib/api/sound';

// Mock history data for development
const mockHistory: SoundTask[] = [
    {
        id: "1",
        text: "Welcome to our AI voice generation platform. This is a sample of high-quality text-to-speech technology.",
        voice_model_id: "brian-multilingual",
        voice_model_name: "<PERSON> (Multilingual)",
        speed: 1.0,
        stability: 0.5,
        similarity_boost: 0.5,
        style_exaggeration: 0.0,
        audio_url: "/sample-audio.mp3",
        status: "completed",
        created_at: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
    },
    {
        id: "2",
        text: "This is another example of AI-generated speech with different voice settings.",
        voice_model_id: "sarah-english",
        voice_model_name: "<PERSON> (English)",
        speed: 1.2,
        stability: 0.7,
        similarity_boost: 0.6,
        style_exaggeration: 0.2,
        status: "processing",
        created_at: new Date(Date.now() - 1000 * 60 * 10).toISOString(), // 10 minutes ago
    },
    {
        id: "3",
        text: "Failed generation example to show error handling.",
        voice_model_id: "james-narrative",
        voice_model_name: "<PERSON> (Narrative)",
        speed: 0.8,
        stability: 0.3,
        similarity_boost: 0.4,
        style_exaggeration: 0.1,
        status: "failed",
        error_message: "Voice model temporarily unavailable",
        created_at: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
    },
];

export function useSoundHistory() {
    const {
        soundHistory,
        isLoadingHistory,
        hasMoreHistory,
        setSoundHistory,
        setIsLoadingHistory,
        setHasMoreHistory,
    } = useSoundStore();

    const loadHistory = useCallback(async (append: boolean = false) => {
        if (isLoadingHistory) return;

        setIsLoadingHistory(true);

        try {
            const offset = append ? soundHistory.length : 0;
            const response = await soundApi.getSoundHistory(20, offset);

            if (!response.success || !response.data) {
                throw new Error(response.error || 'Failed to load sound history');
            }

            const { tasks, has_more } = response.data;

            if (append) {
                setSoundHistory([...soundHistory, ...tasks]);
            } else {
                setSoundHistory(tasks);
            }

            setHasMoreHistory(has_more);
        } catch (error) {
            console.error('Failed to load sound history:', error);
            // Fallback to mock data if API fails
            if (!append && soundHistory.length === 0) {
                setSoundHistory(mockHistory);
                setHasMoreHistory(false);
            }
        } finally {
            setIsLoadingHistory(false);
        }
    }, [
        isLoadingHistory,
        soundHistory,
        setSoundHistory,
        setIsLoadingHistory,
        setHasMoreHistory,
    ]);

    const loadMoreHistory = useCallback(() => {
        if (hasMoreHistory && !isLoadingHistory) {
            loadHistory(true);
        }
    }, [hasMoreHistory, isLoadingHistory, loadHistory]);

    const refreshHistory = useCallback(() => {
        loadHistory(false);
    }, [loadHistory]);

    // Load initial history
    useEffect(() => {
        if (soundHistory.length === 0 && !isLoadingHistory) {
            loadHistory(false);
        }
    }, [soundHistory.length, isLoadingHistory, loadHistory]);

    return {
        soundHistory,
        isLoadingHistory,
        hasMoreHistory,
        loadMoreHistory,
        refreshHistory,
    };
}
