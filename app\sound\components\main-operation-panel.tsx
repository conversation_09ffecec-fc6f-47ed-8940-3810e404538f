"use client";

import { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useSoundStore } from "@/store/useSoundStore";
import { useSoundGeneration } from "../hooks/useSoundGeneration";
import { Loader2, Play, AudioWaveform, Volume2 } from "lucide-react";
import { cn } from "@/lib/utils";

export function MainOperationPanel() {
    const {
        soundType,
        text,
        characterCount,
        maxCharacters,
        selectedVoiceModel,
        error,
        setText,
    } = useSoundStore();

    const { generateSpeech, canGenerate, isGenerating } = useSoundGeneration();
    const [isPlaying, setIsPlaying] = useState(false);

    const handleTextChange = useCallback((value: string) => {
        setText(value);
    }, [setText]);

    const getPlaceholderText = () => {
        return soundType === 'text-to-speech'
            ? "Enter text you want to convert to speech..."
            : "Describe the sound effect you want to generate...";
    };

    const getButtonText = () => {
        return soundType === 'text-to-speech' ? "Generate Speech" : "Generate Sound";
    };

    return (
        <div className="flex flex-col h-full px-8 py-8">
            {/* Main Text Input Area */}
            <div className="flex-1 flex flex-col max-w-4xl mx-auto w-full">
                <div className="flex-1 mb-6">
                    <Textarea
                        value={text}
                        onChange={(e) => handleTextChange(e.target.value)}
                        placeholder={getPlaceholderText()}
                        className={cn(
                            "w-full h-full resize-none text-base leading-relaxed",
                            "bg-transparent border border-gray-200 dark:border-gray-800",
                            "text-black dark:text-white placeholder:text-gray-500",
                            "focus:border-gray-400 dark:focus:border-gray-600 focus:ring-0",
                            "rounded-lg p-6 font-mono",
                            error && characterCount > maxCharacters && "border-red-400 dark:border-red-600"
                        )}
                        style={{ minHeight: "320px" }}
                    />
                </div>

                {/* Bottom Controls */}
                <div className="flex items-center justify-between">
                    {/* Character Count */}
                    <div className="flex items-center space-x-6 text-sm">
                        <span className={cn(
                            "font-mono text-xs",
                            characterCount > maxCharacters
                                ? "text-red-500"
                                : "text-gray-500"
                        )}>
                            {characterCount.toLocaleString()} / {maxCharacters.toLocaleString()}
                        </span>
                        {selectedVoiceModel && (
                            <div className="flex items-center space-x-2 text-gray-500 text-xs">
                                <span>{selectedVoiceModel.name}</span>
                            </div>
                        )}
                    </div>

                    {/* Generate Button */}
                    <Button
                        onClick={generateSpeech}
                        disabled={!canGenerate}
                        className={cn(
                            "px-8 py-3 font-medium text-sm",
                            "bg-black dark:bg-white text-white dark:text-black",
                            "hover:bg-gray-800 dark:hover:bg-gray-100",
                            "disabled:opacity-50 disabled:cursor-not-allowed",
                            "transition-all duration-200 rounded-lg border-0"
                        )}
                    >
                        {isGenerating ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Generating...
                            </>
                        ) : (
                            <>
                                {soundType === 'text-to-speech' ? (
                                    <Play className="w-4 h-4 mr-2" />
                                ) : (
                                    <AudioWaveform className="w-4 h-4 mr-2" />
                                )}
                                {getButtonText()}
                            </>
                        )}
                    </Button>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="mt-4 p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
                    </div>
                )}
            </div>
        </div>
    );
}
