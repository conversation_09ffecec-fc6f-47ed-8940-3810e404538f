import { Upload, X } from "lucide-react"
import Image from "next/image"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface ResourceUploaderProps {
    title: string
    description: string
    textValue: string
    onTextChange: (value: string) => void
    imageValue: string | null
    onImageChange: (value: string | null) => void
    placeholder: string
    className?: string
    hideTextInput?: boolean
}

export function ResourceUploader({
    title,
    description,
    textValue,
    onTextChange,
    imageValue,
    onImageChange,
    placeholder,
    className,
    hideTextInput = false
}: ResourceUploaderProps) {
    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (file) {
            const reader = new FileReader()
            reader.onload = (event) => {
                const result = event.target?.result as string
                onImageChange(result)
            }
            reader.readAsDataURL(file)
        }
    }

    return (
        <div className={cn("rounded-lg border border-border/50 p-3 bg-card/10", className)}>
            {!hideTextInput && (
                <div className="flex justify-between items-center mb-2">
                    <div>
                        <h4 className="text-sm font-medium">{title}</h4>
                        <p className="text-xs text-muted-foreground">{description}</p>
                    </div>
                </div>
            )}

            <div className="space-y-3">
                {/* Text Input */}
                {!hideTextInput && (
                    <div>
                        <Input
                            value={textValue}
                            onChange={(e) => onTextChange(e.target.value)}
                            placeholder={placeholder}
                            className="bg-card/30 border-border/50 text-sm"
                        />
                    </div>
                )}

                {/* Image Upload */}
                <div>
                    {imageValue ? (
                        <div className="relative h-20 w-full rounded-md overflow-hidden">
                            <Image
                                src={imageValue}
                                alt={`${title || "Image"}`}
                                fill
                                className="object-cover"
                            />
                            <button
                                onClick={() => onImageChange(null)}
                                className="absolute top-2 right-2 p-1 bg-black/60 hover:bg-black/80 rounded-full text-white"
                                aria-label={`Remove ${title || "image"}`}
                            >
                                <X size={14} />
                            </button>
                        </div>
                    ) : (
                        <div
                            onClick={() => document.getElementById(`${title ? title.toLowerCase() : "image"}-upload-${Math.random().toString(36).substr(2, 9)}`)?.click()}
                            className="h-20 w-full rounded-md border border-dashed border-border/50 flex flex-col items-center justify-center bg-card/20 cursor-pointer hover:bg-card/30 transition-colors"
                        >
                            <Upload size={16} className="text-muted-foreground mb-1" />
                            <p className="text-xs text-muted-foreground">Upload image</p>
                            <input
                                type="file"
                                id={`${title ? title.toLowerCase() : "image"}-upload-${Math.random().toString(36).substr(2, 9)}`}
                                className="hidden"
                                onChange={handleImageUpload}
                                accept="image/*"
                            />
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
} 