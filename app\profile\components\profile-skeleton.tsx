export function ProfileSkeleton() {
    return (
        <div className="w-full max-w-4xl mx-auto animate-pulse">
            <div className="flex flex-col md:flex-row gap-8 items-start">
                {/* 头像骨架 */}
                <div className="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700"></div>

                <div className="flex-1 space-y-6 w-full">
                    {/* 姓名骨架 */}
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded-md w-2/5"></div>

                    {/* 邮箱骨架 */}
                    <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-md w-3/5"></div>

                    {/* 表单骨架 */}
                    <div className="space-y-6 mt-8">
                        <div className="space-y-2">
                            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-md w-1/6"></div>
                            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-md w-full"></div>
                        </div>

                        <div className="space-y-2">
                            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-md w-1/6"></div>
                            <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded-md w-full"></div>
                        </div>

                        <div className="space-y-2">
                            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-md w-1/6"></div>
                            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-md w-full"></div>
                        </div>

                        <div className="space-y-2">
                            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-md w-1/6"></div>
                            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-md w-full"></div>
                        </div>

                        {/* 按钮骨架 */}
                        <div className="flex justify-end mt-8">
                            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-md w-32"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
} 