"use client";

import { useEffect } from "react";
import { ProfileForm } from "./components/profile-form";
import { ProfileStats } from "./components/profile-stats";
import { ProfileSkeleton } from "./components/profile-skeleton";
import { useProfile } from "@/hooks/use-profile";
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { UserCheck } from "lucide-react";

export default function ProfilePage() {
    const { user, isLoading } = useAuth();
    const { isLoading: profileLoading } = useProfile();
    const router = useRouter();

    // 如果用户未登录且加载完成，重定向到首页
    useEffect(() => {
        if (!isLoading && !user) {
            router.push("/");
        }
    }, [user, isLoading, router]);

    // 加载状态处理
    if (isLoading || profileLoading) {
        return (
            <div className="relative">
                <div className="container px-4 py-8">
                    <h1 className="text-3xl font-bold mb-6">Profile</h1>
                    <ProfileSkeleton />
                </div>
            </div>
        );
    }

    // 未登录状态
    if (!user) {
        return null; // 重定向会处理，这里不需要额外渲染
    }

    return (
        <div className="relative">
            <div className="container px-4 py-8">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-3xl font-bold">Profile</h1>
                </div>

                <Alert className="mb-8">
                    <UserCheck className="h-4 w-4" />
                    <AlertTitle>Personalize your account</AlertTitle>
                    <AlertDescription>
                        Update your profile information and manage your account preferences.
                    </AlertDescription>
                </Alert>

                <ProfileStats />
                <ProfileForm />
            </div>

            {/* Space to account for mobile navigation bar */}
            <div className="h-16 lg:h-0"></div>
        </div>
    );
} 