"use client"

import { useEffect, useState } from "react"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { effectApi } from "@/lib/api/effect"
import { Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import type { Effect } from "@/app/create/components/effect-selector"

export default function EffectDetailPage() {
    const params = useParams<{ id: string }>()
    const router = useRouter()
    const [effect, setEffect] = useState<Effect | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    const effectId = params?.id || ""

    useEffect(() => {
        async function loadEffect() {
            setLoading(true)
            try {
                // 获取所有效果并过滤当前效果
                // 后续可以替换为单个效果的API
                const allEffects = await effectApi.getEffects()
                const currentEffect = allEffects.find(e => e.id === effectId)

                if (!currentEffect) {
                    throw new Error("Effect not found")
                }

                // 处理数据，适配前端显示
                const processedEffect = {
                    ...currentEffect,
                    cover_img: currentEffect.cover_img || '/placeholder.svg'
                }

                setEffect(processedEffect)
                setError(null)
            } catch (err) {
                console.error("Failed to load effect:", err)
                setError("Failed to load effect details")
            } finally {
                setLoading(false)
            }
        }

        if (effectId) {
            loadEffect()
        }
    }, [effectId])

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-[60vh]">
                <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
            </div>
        )
    }

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-4">
                <p className="text-lg text-red-500">{error}</p>
                <Button variant="outline" onClick={() => router.back()}>Go Back</Button>
            </div>
        )
    }

    if (!effect) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-4">
                <p className="text-lg">Effect not found</p>
                <Button variant="outline" onClick={() => router.back()}>Go Back</Button>
            </div>
        )
    }

    return (
        <main className="container mx-auto px-4 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Effect Preview */}
                <div className="relative aspect-video rounded-xl overflow-hidden">
                    <Image
                        src={effect.cover_img || '/placeholder.svg'}
                        alt={effect.name}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, 50vw"
                        priority
                    />
                </div>

                {/* Effect Details */}
                <div className="space-y-6">
                    <Button
                        variant="outline"
                        size="sm"
                        className="mb-4"
                        onClick={() => router.back()}
                    >
                        Back to Effects
                    </Button>

                    <h1 className="text-3xl font-bold">{effect.name}</h1>

                    <div className="flex flex-wrap gap-2">
                        <div className="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm">
                            Effect
                        </div>
                    </div>

                    {effect.desc && (
                        <p className="text-lg text-muted-foreground">{effect.desc}</p>
                    )}

                    {/* Created Date */}
                    <div className="text-sm text-muted-foreground">
                        Added on {new Date(effect.created_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        })}
                    </div>

                    {/* Call to Action */}
                    <div className="pt-4">
                        <Button size="lg" className="w-full sm:w-auto">
                            Apply This Effect
                        </Button>
                    </div>
                </div>
            </div>
        </main>
    )
} 