import { PostItemDto } from "@/types/posts"

/**
 * 根据视频数据获取视频的宽高比
 * @param post 视频帖子数据
 * @returns 视频比例标识："16:9", "9:16", 或 "1:1"(默认)
 */
export function getVideoRatio(post: PostItemDto): string {
    const ratio = post.videos?.input_params?.ratio;

    if (!ratio) return "1:1";

    // 将 Ratio 枚举值直接作为字符串返回
    return ratio.toString();
}

/**
 * 格式化视频时长为可读字符串
 * @param duration 时长（秒）
 * @returns 格式化后的时长字符串，如 "01:25"
 */
export function formatDuration(duration?: number): string {
    if (!duration) return "00:00";

    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);

    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * 格式化发布时间为相对时间
 * @param dateString ISO日期字符串
 * @returns 相对时间字符串，如 "3 hours ago"
 */
export function formatRelativeTime(dateString?: string): string {
    if (!dateString) return "Unknown time";

    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;

    return `${Math.floor(diffInSeconds / 31536000)} years ago`;
}

/**
 * 安全获取视频封面图片URL
 * @param post 视频帖子数据
 * @returns 封面图URL或占位图URL
 */
export function getVideoCoverUrl(post: PostItemDto): string {
    return post.videos?.cover_img || '/placeholders/video-placeholder.jpg';
}

/**
 * 安全获取视频URL
 * @param post 视频帖子数据
 * @returns 视频URL或undefined
 */
export function getVideoUrl(post: PostItemDto): string | undefined {
    return post.video_url;
}

/**
 * 检查视频是否支持自动播放
 * @param post 视频帖子数据
 * @returns 是否支持自动播放
 */
export function canAutoPlay(post: PostItemDto): boolean {
    const videoUrl = getVideoUrl(post);
    // 检查视频格式是否支持自动播放（例如MP4）
    return !!videoUrl && videoUrl.toLowerCase().endsWith('.mp4');
}

// 根据视频比例获取合适的样式
export const getVideoStyle = (post: PostItemDto, isModal: boolean): string => {
    const ratio = getVideoRatio(post);

    if (isModal) {
        return "w-full h-full object-cover";
    }

    if (ratio === "9:16") {
        return "w-full h-full object-cover";
    } else if (ratio === "16:9") {
        return "w-full h-full object-cover";
    } else {
        return "w-full h-full object-cover";
    }
}; 