import React from "react";
import Link from "next/link";
import Image from "next/image";
import { formatDate } from "../../lib/utils";
import type { BlogPost } from "../../types/blog";

// 定义一个不需要content字段的博客文章类型，用于列表展示
type BlogPostListItem = Omit<BlogPost, 'content'> & { content?: string };

interface BlogPostCardProps {
    post: BlogPostListItem;
    className?: string;
}

export function BlogPostCard({ post, className = "" }: BlogPostCardProps) {
    const formattedDate = formatDate(post.created_at);

    return (
        <div
            className={`group relative flex flex-col overflow-hidden rounded-xl border border-gray-100 bg-white shadow-sm hover:shadow-lg transition-all duration-300 ease-in-out dark:bg-gray-900 dark:border-gray-800 hover:translate-y-[-4px] ${className}`}
        >
            <div className="relative w-full aspect-[5/3] overflow-hidden">
                <Link target="_blank" href={`/blog/${post.slug}`} aria-label={post.title}>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent z-10 opacity-60 group-hover:opacity-40 transition-opacity" />
                    <Image
                        src={post.cover_image}
                        alt={post.title}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-110"
                        sizes="(max-width: 768px) 100vw, 33vw"
                    />
                </Link>
            </div>

            <div className="flex flex-col p-6 flex-1 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-950">
                <div>
                    <div className="mb-3 flex items-center text-base font-medium">
                        <span className="text-gray-500 dark:text-gray-400 flex items-center">
                            <svg
                                className="w-5 h-5 mr-1.5 text-gray-400"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                />
                            </svg>
                            {formattedDate}
                        </span>
                    </div>

                    <Link
                        href={`/blog/${post.slug}`}
                        className="group-hover:text-primary-600 transition block"
                    >
                        <h3 className="font-bold text-gray-900 dark:text-gray-100 text-2xl mb-4 leading-tight">
                            {post.title}
                        </h3>
                    </Link>

                    <p className="text-gray-600 dark:text-gray-300 text-base leading-relaxed line-clamp-3">
                        {post.excerpt}
                    </p>
                </div>
            </div>
        </div>
    );
}
