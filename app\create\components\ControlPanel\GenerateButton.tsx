"use client"

import React from 'react';
import { Sparkles, Gift } from "lucide-react";
import { cn } from "@/lib/utils";
import { useControlPanel } from './ControlPanelContext';
import { useQuery } from "@tanstack/react-query";
import { creditsApi } from "@/lib/api/credits";
import useAuthStore from "@/store/useAuthStore";
import { useRouter } from 'next/navigation';
import { GiftIcon } from "@/components/credits/gift-icon";
import useCreditsStore from "@/store/useCreditsStore";

export const GenerateButton = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();
  const {
    canClaimBonus,
    isClaimingBonus,
    checkClaimStatus,
    hasCheckedClaimStatus
  } = useCreditsStore();

  const {
    isGenerating,
    isLoadingModels,
    isLoadingPresignedUrl,
    isLoadingTaskPrice,
    selectedModelId,
    modelType,
    globalPrompt,
    images,
    videoCost,
    handleGenerateVideo,
    isStartToEndModel,
    startImages,
    endImages
  } = useControlPanel();

  // 使用 react-query 获取积分余额（与UserNav保持一致）
  const { data: currentBalance, isLoading: isLoadingCredits } = useQuery({
    queryKey: ["credits", "balance"],
    queryFn: async () => {
      const data = await creditsApi.getUserBalance();
      return data.data.balance;
    },
    enabled: !!isAuthenticated,
  });

  // 检查新用户积分领取状态
  React.useEffect(() => {
    if (isAuthenticated && !hasCheckedClaimStatus && !isClaimingBonus) {
      console.log('[GenerateButton] Checking claim status');
      checkClaimStatus();
    }
  }, [isAuthenticated, hasCheckedClaimStatus, isClaimingBonus, checkClaimStatus]);

  // Check if user has insufficient credits
  const hasInsufficientCredits = currentBalance !== undefined && videoCost !== undefined && currentBalance < videoCost;

  // 判断是否应该显示领取按钮：用户积分为0且有领取资格
  const shouldShowClaimButton = currentBalance === 0 && canClaimBonus;

  // Determine if the button should be disabled - 仅在不是积分不足的情况下应用其他禁用条件
  const isDisabled = hasInsufficientCredits
    ? false // 余额不足时按钮始终可点击
    : (
      isGenerating ||
      !selectedModelId ||
      isLoadingModels ||
      isLoadingPresignedUrl ||
      (isStartToEndModel && (startImages.length === 0 || endImages.length === 0)) ||
      (!isStartToEndModel && ((!modelType || modelType === 'text-to-video') && !globalPrompt.trim())) ||
      (!isStartToEndModel && modelType === 'image-to-video' && images.length === 0) ||
      (!isStartToEndModel && modelType === 'video-to-video' && images.length === 0)
    );

  // Determine the button's visual style
  const buttonStyle = cn(
    "w-full py-3 rounded-xl font-medium flex items-center justify-center transition-all relative z-10",
    // 当余额不足时，按钮始终有活跃的样式
    hasInsufficientCredits
      ? "bg-gradient-to-r from-amber-500 to-orange-600 hover:brightness-110 text-white"
      : isGenerating || isLoadingModels || isLoadingPresignedUrl
        ? "bg-gray-500/50 cursor-wait"
        : !selectedModelId
          ? "bg-gray-500/30 cursor-not-allowed"
          : (isStartToEndModel && (startImages.length === 0 || endImages.length === 0)) ||
            (!isStartToEndModel && ((!modelType || modelType === 'text-to-video') && !globalPrompt.trim())) ||
            (!isStartToEndModel && modelType === 'image-to-video' && images.length === 0) ||
            (!isStartToEndModel && modelType === 'video-to-video' && images.length === 0)
            ? "bg-gray-500/30 cursor-not-allowed"
            : "text-white animate-gradient-bg bg-gradient-to-r from-red-600 via-orange-500 via-blue-500 to-pink-600"
  );

  const handleButtonClick = () => {
    if (hasInsufficientCredits) {
      // 直接跳转到会员页面，不显示弹框
      router.push('/membership');
    } else {
      handleGenerateVideo();
    }
  };

  return (
    <div className="p-2.5">
      <div className="mb-2 text-sm flex justify-between">
        {/* 只有在有视频成本数据时才显示 */}
        {!isLoadingTaskPrice && videoCost !== undefined && (
          <div>
            <span className="text-muted-foreground">Cost:</span>
            <span className="font-bold ml-2">{videoCost} Credits</span>
          </div>
        )}

        {/* 只有在有余额数据时才显示 */}
        {!isLoadingCredits && currentBalance !== undefined && (
          <div>
            <span className="text-muted-foreground">Balance:</span>
            <span className={`font-bold ml-2 ${hasInsufficientCredits ? 'text-red-500' : ''}`}>
              {currentBalance} Credits
            </span>
          </div>
        )}
      </div>

      {/* 隐藏的GiftIcon组件用于触发模态框 */}
      <div className="hidden">
        <GiftIcon variant="button" size="sm" />
      </div>

      {/* 根据条件显示不同的按钮 */}
      {shouldShowClaimButton ? (
        // 显示领取积分按钮 - 使用与Generate按钮相同的样式
        <button
          onClick={() => {
            // 触发礼品图标的点击事件
            const giftButton = document.querySelector('[data-gift-button]') as HTMLButtonElement;
            if (giftButton) {
              giftButton.click();
            }
          }}
          disabled={isClaimingBonus}
          className={cn(
            "w-full py-3 rounded-xl font-medium flex items-center justify-center transition-all relative z-10",
            isClaimingBonus
              ? "bg-gray-500/50 cursor-wait"
              : "bg-gradient-to-r from-amber-500 to-orange-600 hover:brightness-110 text-white"
          )}
        >
          {isClaimingBonus ? (
            <>
              <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
              Getting credits...
            </>
          ) : (
            <>
              <Gift className="mr-2 h-4 w-4" />
              Get 88 Credits
            </>
          )}
        </button>
      ) : (
        <>
          {/* 只有在不显示领取按钮时才显示礼品图标 */}
          {canClaimBonus && (
            <div className="mb-2">
              <GiftIcon variant="button" size="sm" />
            </div>
          )}

          <button
            onClick={handleButtonClick}
            disabled={isDisabled}
            className={buttonStyle}
          >
            {isGenerating ? (
              <>
                <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
                Generating...
              </>
            ) : isLoadingModels ? (
              <>
                <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
                Loading Models...
              </>
            ) : isLoadingPresignedUrl ? (
              <>
                <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
                Uploading Image...
              </>
            ) : isClaimingBonus ? (
              <>
                <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
                Claiming Credits...
              </>
            ) : hasInsufficientCredits ? (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Get More Credits
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Generate Video
              </>
            )}
          </button>
        </>
      )}
    </div>
  );
};
