import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath } from 'next/cache';

export async function POST(request: NextRequest) {
    // 获取需要重新验证的路径
    const path = request.nextUrl.searchParams.get('path') || '/';

    try {
        // 重新验证指定路径
        revalidatePath(path);
        return NextResponse.json({
            revalidated: true,
            message: `Path "${path}" revalidated successfully`,
            now: Date.now()
        });
    } catch (err) {
        // 返回错误信息
        return NextResponse.json(
            {
                revalidated: false,
                message: `Error revalidating path "${path}"`,
                error: err instanceof Error ? err.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
} 