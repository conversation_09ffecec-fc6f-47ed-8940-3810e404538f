'use client'

import { useState, useEffect } from 'react'
import Image, { ImageProps } from 'next/image'

interface OptimizedImageProps extends Omit<ImageProps, 'onLoad'> {
  isLCP?: boolean // 是否为LCP图片
  onLoadingComplete?: (result: { naturalWidth: number; naturalHeight: number }) => void
}

// 优化的图片组件 - 专门处理LCP图片
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  isLCP = false,
  priority = false,
  onLoadingComplete,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)

  // 使用useEffect监听图片加载
  useEffect(() => {
    // 如果是LCP图片，记录加载时间
    if (isLCP) {
      // 使用Performance API记录LCP图片加载时间
      const markName = `lcp-image-${typeof src === 'string' ? src.split('/').pop() : 'unknown'}`
      performance.mark(markName)

      // 清理函数
      return () => {
        // 尝试清除性能标记
        try {
          performance.clearMarks(markName)
        } catch (e) {
          console.error('Failed to clear performance mark:', e)
        }
      }
    }
  }, [src, isLCP])

  // 处理图片加载完成事件
  const handleLoadingComplete = (result: { naturalWidth: number; naturalHeight: number }) => {
    setIsLoaded(true)
    
    // 如果是LCP图片，记录加载完成时间
    if (isLCP) {
      const markName = `lcp-image-loaded-${typeof src === 'string' ? src.split('/').pop() : 'unknown'}`
      performance.mark(markName)
      
      // 测量加载时间
      try {
        performance.measure(
          `lcp-image-load-time-${typeof src === 'string' ? src.split('/').pop() : 'unknown'}`,
          `lcp-image-${typeof src === 'string' ? src.split('/').pop() : 'unknown'}`,
          markName
        )
      } catch (e) {
        console.error('Failed to measure performance:', e)
      }
    }
    
    // 调用原始的onLoadingComplete回调
    if (onLoadingComplete) {
      onLoadingComplete(result)
    }
  }

  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      // LCP图片或明确设置priority的图片使用高优先级加载
      priority={isLCP || priority}
      // 使用fetchPriority属性
      fetchPriority={isLCP ? "high" : (priority ? "high" : "auto")}
      // 使用loading属性
      loading={isLCP || priority ? "eager" : "lazy"}
      // 使用decoding属性
      decoding={isLCP ? "sync" : "async"}
      // 处理加载完成事件
      onLoadingComplete={handleLoadingComplete}
      // 传递其他属性
      {...props}
      // 添加data属性用于性能监控
      data-lcp={isLCP ? "true" : undefined}
    />
  )
}
