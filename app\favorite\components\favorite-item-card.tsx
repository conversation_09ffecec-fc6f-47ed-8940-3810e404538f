"use client"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Heart, MessageCircle, Play, Video, Bookmark } from "lucide-react"
import { Card } from "@/components/ui/card"
import { FavoritePostResponse, FavoriteModelResponse, FavoriteTargetType } from "@/lib/api/favorite"
import { formatTimeAgo } from "@/lib/utils"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"

interface FavoriteItemCardProps {
    item: FavoritePostResponse | FavoriteModelResponse
    type: FavoriteTargetType
    onRemove: (id: string, type: FavoriteTargetType) => void
}

export function FavoriteItemCard({ item, type, onRemove }: FavoriteItemCardProps) {
    const [isHovered, setIsHovered] = useState(false)
    const videoRef = useRef<HTMLVideoElement>(null)
    const router = useRouter()
    const isPost = type === FavoriteTargetType.POST
    const post = isPost ? item as FavoritePostResponse : null
    const model = !isPost ? item as FavoriteModelResponse : null

    // 获取创建时间
    const createdAt = new Date(isPost ? post!.createdAt : model!.createdAt)
    const timeAgo = formatTimeAgo(createdAt.toISOString());

    // 处理移除收藏
    const handleRemove = (e: React.MouseEvent) => {
        e.preventDefault()
        e.stopPropagation()

        const targetId = isPost ? post!.postId : model!.modelId
        onRemove(targetId, type)
    }

    // 处理点击卡片
    const handleClick = () => {
        if (isPost) {
            // 导航到视频详情页，与首页保持一致
            router.push(`/posts/${post!.postId}`)
        } else if (model) {
            // 导航到模型详情页
            router.push(`/models/${model.modelId}`)
        }
    }

    // 自动播放视频
    useEffect(() => {
        if (isPost && videoRef.current) {
            // 尝试自动播放视频
            videoRef.current.play().catch(() => {
                // 自动播放可能被浏览器阻止，这里忽略错误
                console.log("视频自动播放被阻止")
            })
        }
    }, [isPost])

    return (
        <motion.div
            className={cn(
                "group relative overflow-hidden rounded-xl transition-all duration-300",
                isHovered && "shadow-lg"
            )}
            whileHover={{ scale: 1.02 }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={handleClick}
        >
            {/* 卡片主体 */}
            <div className="relative cursor-pointer">
                {/* 视频/图片区域 */}
                <div className="relative aspect-video overflow-hidden rounded-t-xl">
                    {isPost ? (
                        <>
                            {/* 视频预览 - 始终显示并自动播放 */}
                            <video
                                ref={videoRef}
                                src={post!.videoUrl}
                                className="absolute inset-0 h-full w-full object-cover"
                                muted
                                loop
                                playsInline
                                autoPlay
                            />

                            {/* 封面图作为备用（如果视频无法播放） */}

                            {/* 播放图标 */}
                            <div className={cn(
                                "absolute inset-0 flex items-center justify-center bg-black/30 transition-opacity duration-300",
                                "hover:opacity-100 opacity-0"
                            )}>
                                <motion.div
                                    initial={{ scale: 0.8, opacity: 0 }}
                                    animate={isHovered ? { scale: 1, opacity: 1 } : { scale: 0.8, opacity: 0 }}
                                    className="rounded-full bg-white/20 p-3 backdrop-blur-sm"
                                >
                                    <Play className="h-8 w-8 text-white" />
                                </motion.div>
                            </div>
                        </>
                    ) : (
                        <div className="h-full w-full bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center">
                            <Bookmark className="h-12 w-12 text-primary/40" />
                        </div>
                    )}

                    {/* 类型标签 */}
                    <div className="absolute left-3 top-3 rounded-full bg-background/80 px-2.5 py-1 text-xs font-medium backdrop-blur-sm">
                        {isPost ? (
                            <div className="flex items-center gap-1">
                                <Video className="h-3 w-3 text-primary-foreground" />
                                <span>视频</span>
                            </div>
                        ) : (
                            <div className="flex items-center gap-1">
                                <Bookmark className="h-3 w-3 text-blue-500" />
                                <span>模型</span>
                            </div>
                        )}
                    </div>
                </div>

                {/* 内容区域 */}
                <div className="bg-background p-4 rounded-b-xl">
                    <h3 className="font-semibold text-lg line-clamp-1 mb-1">
                        {isPost ? post!.title : model!.name}
                    </h3>

                    <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                        {isPost ? post!.description : model!.description}
                    </p>

                    {!isPost && model!.modelType && (
                        <div className="mb-2">
                            <Badge variant="secondary" className="text-xs">
                                {model!.modelType}
                            </Badge>
                        </div>
                    )}

                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{timeAgo}</span>

                        {isPost && (
                            <div className="flex items-center space-x-3">
                                <div className="flex items-center">
                                    <Heart className="w-3.5 h-3.5 mr-1 text-muted-foreground" />
                                    <span>{post!.likeCount}</span>
                                </div>
                                <div className="flex items-center">
                                    <MessageCircle className="w-3.5 h-3.5 mr-1 text-muted-foreground" />
                                    <span>{post!.commentCount}</span>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* 收藏按钮 - 悬停时显示 */}
            <motion.button
                className="absolute right-3 top-3 rounded-full bg-background/80 p-2 text-destructive backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                initial={{ opacity: 0 }}
                animate={isHovered ? { opacity: 1 } : { opacity: 0 }}
                onClick={handleRemove}
            >
                <Heart className="h-4 w-4 fill-current" />
            </motion.button>
        </motion.div>
    )
} 