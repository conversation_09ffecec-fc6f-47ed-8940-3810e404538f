"use client"

import { useEffectList } from "@/app/models/queries"
import { EffectCard } from "@/app/models/components/effect-card"
import { Loader2 } from "lucide-react"

export default function EffectsPage() {
    const { data: effectData, isLoading } = useEffectList()

    return (
        <main className="container mx-auto px-4 py-8">
            <div className="space-y-2 mb-8">
                <h1 className="text-3xl font-bold tracking-tight">AI Effects</h1>
                <p className="text-muted-foreground">Browse our collection of AI effects for enhancing your videos</p>
            </div>

            {/* Effects Grid */}
            {isLoading ? (
                <div className="flex justify-center py-12">
                    <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
                    {effectData?.effects?.map((effect) => (
                        <EffectCard key={effect.id} effect={effect} />
                    ))}
                </div>
            )}

            {/* Space to account for mobile navigation bar */}
            <div className="h-16 lg:h-0"></div>
        </main>
    )
} 