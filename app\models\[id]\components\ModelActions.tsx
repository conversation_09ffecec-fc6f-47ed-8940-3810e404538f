"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

interface ModelActionsProps {
    modelId: string
}

export default function ModelActions({ modelId }: ModelActionsProps) {
    const router = useRouter()

    return (
        <div className="pt-4">
            <Button
                size="lg"
                className="w-full sm:w-auto"
                onClick={() => router.push(`/create?modelId=${modelId}`)}
            >
                Use This Model
            </Button>
        </div>
    )
} 