import { cn } from "@/lib/utils"
import { ChevronDown, Wand2, X } from "lucide-react"
import Image from "next/image"
import { useState, useEffect, Suspense } from "react"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { EffectCard } from "@/app/models/components/effect-card"
import { VideoPlayer } from "@/app/models/components/video-player"
import type { Effect } from "@/types/model"

interface EffectSelectorProps {
    effects: Effect[]
    selectedEffect: string
    setSelectedEffect: (id: string) => void
    isLoading: boolean
}

export function EffectSelector({ effects, selectedEffect, setSelectedEffect, isLoading }: EffectSelectorProps) {
    const [showEffectModal, setShowEffectModal] = useState(false)

    // 获取选中的效果信息
    const selectedEffectInfo = effects.find((effect) => effect.name === selectedEffect)

    // 当点击效果选择器时的处理函数
    const handleEffectSelectorClick = () => {
        // 打开模态框
        setShowEffectModal(true)
    }

    // 处理对话框关闭
    const handleOpenChange = (open: boolean) => {
        setShowEffectModal(open);
    }

    return (
        <div className="rounded-xl relative">
            {isLoading ? (
                <div className="flex items-center justify-center p-3">
                    <div className="animate-spin w-6 h-6 mr-3 rounded-full border-2 border-t-blue-500 border-blue-500/30"></div>
                    <span className="text-gray-400">Loading effects...</span>
                </div>
            ) : (
                <div className="relative w-full">
                    <div
                        className="cursor-pointer w-full"
                        onClick={handleEffectSelectorClick}
                    >
                        {selectedEffectInfo ? (
                            <div
                                className="p-2.5 rounded-xl w-full ring-1 ring-white/10 hover:bg-card/20 transition-colors"
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center flex-1 min-w-0">
                                        <div className="w-28 h-28 relative rounded-lg overflow-hidden flex-shrink-0 bg-background/50">
                                            <Suspense fallback={
                                                <Image
                                                    src={"/placeholder.svg"}
                                                    alt={selectedEffectInfo.name}
                                                    fill
                                                    className="object-cover"
                                                />
                                            }>
                                                <VideoPlayer
                                                    autoPlay={true}
                                                    src={selectedEffectInfo.cover_img || ""}
                                                    poster="/placeholder.svg"
                                                />
                                            </Suspense>
                                        </div>
                                        <div className="ml-3 flex-1 min-w-0">
                                            <div className="flex items-center gap-2">
                                                <div className="font-medium truncate">{selectedEffectInfo.name}</div>
                                            </div>
                                            <p className="text-xs text-muted-foreground mt-0.5 line-clamp-1">
                                                {selectedEffectInfo.desc || "Apply special effects to your video"}
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        className="ml-2 p-1.5 rounded-md bg-card/20 hover:bg-card/30 transition-colors flex-shrink-0"
                                    >
                                        <ChevronDown size={16} className="text-muted-foreground" />
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <div
                                className="p-2.5 rounded-xl w-full bg-card/20 ring-1 ring-white/10 hover:bg-card/30 transition-colors"
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        <div className="w-14 h-14 relative rounded-lg flex-shrink-0 bg-muted flex items-center justify-center">
                                            <Wand2 size={20} className="text-muted-foreground" />
                                        </div>
                                        <div className="ml-3">
                                            <div className="font-medium text-sm">Select an effect</div>
                                            <p className="text-[10px] text-muted-foreground mt-0.5">Click to choose a video effect</p>
                                        </div>
                                    </div>
                                    <div
                                        className="ml-2 p-1.5 rounded-md bg-card/20 hover:bg-card/30 transition-colors flex-shrink-0"
                                    >
                                        <ChevronDown size={16} className="text-muted-foreground" />
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* 效果选择模态框 */}
                    <Dialog open={showEffectModal} onOpenChange={handleOpenChange}>
                        <DialogContent className="max-w-[90vw] w-[90vw] h-[90vh] sm:max-w-[90vw] p-0 rounded-lg overflow-hidden">
                            <div className="flex flex-col h-full">
                                {/* 模态框头部 */}
                                <div className="bg-card/20 p-4 border-b border-foreground/10 flex items-center justify-between">
                                    <h2 className="text-xl font-medium">Select Effect</h2>
                                    <button
                                        onClick={() => setShowEffectModal(false)}
                                        className="p-2 rounded-full hover:bg-card/50"
                                        aria-label="Close effect selector"
                                        title="Close effect selector"
                                    >
                                        <X className="h-5 w-5" />
                                    </button>
                                </div>

                                {/* 模态框内容 - 效果网格 */}
                                <div className="flex-1 overflow-y-auto p-6">
                                    <div className="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                                        {effects.map((effect) => (
                                            <div
                                                key={effect.id}
                                                className={cn(
                                                    "cursor-pointer transition-all hover:scale-[1.02]",
                                                    selectedEffect === effect.name
                                                        ? "ring-2 ring-white/50 shadow-lg scale-[1.02]"
                                                        : ""
                                                )}
                                            >
                                                <EffectCard
                                                    effect={{
                                                        id: effect.id,
                                                        name: effect.name,
                                                        desc: effect.desc || "",
                                                        cover_img: effect.cover_img || "/placeholder.svg",
                                                        created_at: effect.created_at,
                                                        trigger_words: effect.trigger_words || [],
                                                    }}
                                                    onSelectEffect={() => {
                                                        setSelectedEffect(effect.name);
                                                        setShowEffectModal(false);
                                                    }}
                                                />
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>
            )}
        </div>
    )
} 