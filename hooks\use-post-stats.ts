import { useCallback, useEffect, useRef } from 'react';
import { postStatsApi, PostStatsEventType } from '@/lib/api/post-stats';
import { usePathname } from 'next/navigation';
import { STATS_CONFIG } from '@/lib/config';

/**
 * 帖子统计Hook
 * 用于记录帖子的浏览量和点击量
 * 可通过配置控制是否启用统计功能
 */
export function usePostStats() {
  const pathname = usePathname();
  const recordedPostIds = useRef<Set<string>>(new Set());
  const recordedClickIds = useRef<Set<string>>(new Set());

  // 检查统计功能是否启用
  const isStatsEnabled = STATS_CONFIG.ENABLE_STATS;

  /**
   * 记录帖子统计事件
   * @param postId 帖子ID
   * @param eventType 事件类型，view表示浏览，click表示点击
   */
  const recordPostEvent = useCallback(async (postId: string, eventType: PostStatsEventType) => {
    // 如果统计功能被禁用，直接返回
    if (!isStatsEnabled) {
      return;
    }

    try {
      // 获取客户端信息
      const clientInfo = {
        userAgent: navigator.userAgent,
        language: navigator.language,
        screenWidth: window.screen.width,
        screenHeight: window.screen.height,
        url: window.location.href,
        referrer: document.referrer || '',
        pathname,
      };

      // 发送统计事件
      await postStatsApi.recordEvent({
        post_id: postId,
        event_type: eventType,
        client_info: clientInfo,
      });
    } catch (error) {
      // 捕获错误但不抛出，避免影响用户体验
      console.error(`记录帖子${eventType}统计失败:`, error);
    }
  }, [pathname, isStatsEnabled]);

  /**
   * 记录帖子浏览量
   * 确保每个帖子在一次页面加载中只记录一次浏览量
   * @param postId 帖子ID
   */
  const recordView = useCallback((postId: string) => {
    if (!postId || recordedPostIds.current.has(postId)) return;

    // 标记为已记录
    recordedPostIds.current.add(postId);

    // 记录浏览事件
    recordPostEvent(postId, 'view');
  }, [recordPostEvent]);

  /**
   * 记录帖子点击量
   * 确保每个帖子在一次页面加载中只记录一次点击量
   * @param postId 帖子ID
   */
  const recordClick = useCallback((postId: string) => {
    if (!postId) return;

    // 记录点击事件（点击可以多次记录）
    recordPostEvent(postId, 'click');
  }, [recordPostEvent]);

  // 页面卸载时清除记录
  useEffect(() => {
    return () => {
      recordedPostIds.current.clear();
      recordedClickIds.current.clear();
    };
  }, []);

  return {
    recordView,
    recordClick,
    isStatsEnabled
  };
}
