import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, Min } from 'class-validator';
import { MembershipLevel } from '../constant';

/**
 * 会员计划信息DTO
 */
export class MembershipPlanDto {
    @IsUUID()
    @IsOptional()
    id?: string;

    @IsEnum(MembershipLevel)
    @IsNotEmpty()
    level: MembershipLevel;

    @IsString()
    @IsNotEmpty()
    plan_name: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsNumber()
    @IsNotEmpty()
    monthly_price: number;

    @IsNumber()
    @IsNotEmpty()
    yearly_price: number;

    @IsArray()
    @IsOptional()
    features?: string[];

    @IsBoolean()
    @IsOptional()
    is_popular?: boolean;

    @IsNumber()
    @IsOptional()
    priority?: number;

    @IsBoolean()
    @IsOptional()
    is_active?: boolean;
}

/**
 * 创建会员计划DTO
 */
export class CreateMembershipPlanDto {
    @IsEnum(MembershipLevel)
    @IsNotEmpty()
    level: MembershipLevel;

    @IsString()
    @IsNotEmpty()
    plan_name: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsNumber()
    @IsNotEmpty()
    monthly_price: number;

    @IsNumber()
    @IsNotEmpty()
    yearly_price: number;

    @IsArray()
    @IsOptional()
    features?: string[];

    @IsBoolean()
    @IsOptional()
    is_popular?: boolean;

    @IsNumber()
    @IsOptional()
    priority?: number;
}

/**
 * 更新会员计划DTO
 */
export class UpdateMembershipPlanDto {
    @IsEnum(MembershipLevel)
    @IsOptional()
    level?: MembershipLevel;

    @IsString()
    @IsOptional()
    plan_name?: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsNumber()
    @IsOptional()
    monthly_price?: number;

    @IsNumber()
    @IsOptional()
    yearly_price?: number;

    @IsArray()
    @IsOptional()
    features?: string[];

    @IsBoolean()
    @IsOptional()
    is_popular?: boolean;

    @IsNumber()
    @IsOptional()
    priority?: number;

    @IsBoolean()
    @IsOptional()
    is_active?: boolean;
}

/**
 * 会员计划查询DTO
 */
export class MembershipPlanQueryDto {
    @IsEnum(MembershipLevel)
    @IsOptional()
    level?: MembershipLevel;

    @IsBoolean()
    @IsOptional()
    is_active?: boolean;

    @IsBoolean()
    @IsOptional()
    is_popular?: boolean;

    @IsNumber()
    @IsOptional()
    limit?: number;

    @IsNumber()
    @IsOptional()
    offset?: number;
}

/**
 * 会员计划摘要DTO（用于前端展示）
 */
export class MembershipPlanSummaryDto {
    @IsEnum(MembershipLevel)
    level: MembershipLevel;

    @IsString()
    name: string;

    @IsBoolean()
    is_popular: boolean;

    @IsBoolean()
    is_active: boolean;

    @IsArray()
    price_options: {
        months: number;
        price: number;
        effective_price: number;
        discount_percent?: number | null;
    }[];
}
