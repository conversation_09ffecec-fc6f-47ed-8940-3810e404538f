'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface PaginationProps {
    currentPage: number;
    totalPages: number;
    totalItems: number;
}

export default function BlogPagination({ currentPage, totalPages, totalItems }: PaginationProps) {
    const pathname = usePathname();

    // 如果只有一页，不显示分页组件
    if (totalPages <= 1) {
        return null;
    }

    // 生成页码数组，总是显示第一页和最后一页，以及当前页附近的页码
    const generatePageNumbers = () => {
        const pages = [];
        const showEllipsis = totalPages > 7;

        if (!showEllipsis) {
            // 如果总页数少于7，显示所有页码
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            // 总是显示第一页
            pages.push(1);

            if (currentPage > 3) {
                // 如果当前页大于3，显示省略号
                pages.push('ellipsis1');
            }

            // 显示当前页附近的页码
            const startPage = Math.max(2, currentPage - 1);
            const endPage = Math.min(totalPages - 1, currentPage + 1);

            for (let i = startPage; i <= endPage; i++) {
                pages.push(i);
            }

            if (currentPage < totalPages - 2) {
                // 如果当前页小于倒数第3页，显示省略号
                pages.push('ellipsis2');
            }

            // 总是显示最后一页
            if (totalPages > 1) {
                pages.push(totalPages);
            }
        }

        return pages;
    };

    const pageNumbers = generatePageNumbers();

    // 生成分页链接
    const createPageUrl = (page: number) => {
        return `${pathname}?page=${page}`;
    };

    return (
        <div className="mt-12 mb-8">
            <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
                <nav aria-label="Pagination" className="flex items-center space-x-1 ml-auto">
                    {/* 上一页按钮 */}
                    {currentPage > 1 ? (
                        <Link
                            href={createPageUrl(currentPage - 1)}
                            className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                        >
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                            Previous
                        </Link>
                    ) : (
                        <span className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg bg-gray-100 text-gray-400 opacity-50 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600">
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                            Previous
                        </span>
                    )}

                    {/* 页码按钮 */}
                    <div className="hidden md:flex items-center space-x-1">
                        {pageNumbers.map((page, index) => {
                            // 处理省略号
                            if (page === 'ellipsis1' || page === 'ellipsis2') {
                                return (
                                    <span key={`ellipsis-${index}`} className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                                        ...
                                    </span>
                                );
                            }

                            // 处理页码按钮
                            return (
                                <Link
                                    key={`page-${page}`}
                                    href={createPageUrl(page as number)}
                                    className={`px-4 py-2 text-sm rounded-lg ${currentPage === page
                                        ? 'bg-primary-600 text-white font-medium hover:bg-primary-700'
                                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                                        }`}
                                >
                                    {page}
                                </Link>
                            );
                        })}
                    </div>

                    {/* 下一页按钮 */}
                    {currentPage < totalPages ? (
                        <Link
                            href={createPageUrl(currentPage + 1)}
                            className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                        >
                            Next
                            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                        </Link>
                    ) : (
                        <span className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg bg-gray-100 text-gray-400 opacity-50 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600">
                            Next
                            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                        </span>
                    )}
                </nav>
            </div>
        </div>
    );
} 