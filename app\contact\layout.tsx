import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us - ReelMind AI Video Generation',
  description: 'Get in touch with ReelMind team. We provide support for AI video generation, technical assistance, and answer all your questions about our platform.',
  keywords: [
    'contact reelmind',
    'ai video support',
    'technical support',
    'customer service',
    'video generation help',
    'reelmind contact',
    'ai video assistance'
  ],
  openGraph: {
    title: 'Contact Us - ReelMind AI Video Generation',
    description: 'Get in touch with ReelMind team. We provide support for AI video generation, technical assistance, and answer all your questions.',
    type: 'website',
    url: 'https://reelmind.ai/contact',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Us - ReelMind AI Video Generation',
    description: 'Get in touch with ReelMind team. We provide support for AI video generation, technical assistance, and answer all your questions.',
  },
  alternates: {
    canonical: 'https://reelmind.ai/contact',
  },
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
} 