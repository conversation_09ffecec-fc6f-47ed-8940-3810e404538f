"use client"

import { useEffect } from "react"
import { Database, Zap, Cpu, ChevronDown, Terminal } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CyberpunkSlider } from "@/components/ui/slider"
import { useTrainStore } from "@/store/useTrainStore"
import { useBaseModels } from "../queries"

export function TrainingSettingsSection() {
  const { settings, updateSetting } = useTrainStore()
  const { data: baseModels = [], isLoading } = useBaseModels()

  // Set default base model when data is loaded
  useEffect(() => {
    if (baseModels.length > 0 && !settings.model_id) {
      updateSetting("model_id", baseModels[0].id)
    }
  }, [baseModels, settings.model_id, updateSetting])

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-3">
        <div className="p-2 rounded-lg bg-[#F5EFFF]/10 text-[#F5EFFF]">
          <Cpu className="h-5 w-5" />
        </div>
        <h2 className="font-mono uppercase tracking-wider text-[#F5EFFF] text-shadow-[0_0_5px_#F5EFFF]">MODEL CONFIG</h2>
      </div>

      <div className="cyber-card pixel-border rounded-xl p-4 shadow-xl">
        {/* 基本训练参数区域 - 使用2列布局 */}
        <div className="grid grid-cols-2 gap-6 mb-4">
          {/* 左列 */}
          <div className="space-y-4">
            {/* Trigger word */}
            <div className="space-y-2">
              <Label htmlFor="triggerWord" className="text-[#F5EFFF] flex items-center space-x-2 font-mono uppercase text-xs tracking-wider">
                <Zap className="h-4 w-4 text-[#F5EFFF]" />
                <span>Trigger Word</span>
              </Label>
              <Input
                id="triggerWord"
                value={settings.trigger_word}
                onChange={(e) => updateSetting("trigger_word", e.target.value)}
                placeholder="ENTER_TRIGGER"
                className="bg-black border-[#F5EFFF] focus:ring-[#F5EFFF] focus:border-[#F5EFFF] text-[#F5EFFF] font-mono placeholder:text-[#F5EFFF]/50"
              />
            </div>
          </div>

          {/* 右列 */}
          <div className="space-y-4">
            {/* Base model selection */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-[#F5EFFF]" />
                  <Label htmlFor="baseModel" className="text-[#F5EFFF] font-mono uppercase text-xs tracking-wider">
                    Base Model
                  </Label>
                </div>
                <Terminal className="h-4 w-4 text-[#F5EFFF]/50" />
              </div>
              <Select
                value={settings.model_id}
                onValueChange={(value) => updateSetting("model_id", value)}
                disabled={isLoading}
              >
                <SelectTrigger className="bg-black border-[#F5EFFF] focus:ring-[#F5EFFF] focus:border-[#F5EFFF] text-[#F5EFFF] font-mono">
                  <SelectValue placeholder={isLoading ? "SCANNING..." : "SELECT_MODEL"} />
                </SelectTrigger>
                <SelectContent className="bg-black border-[#F5EFFF] text-[#F5EFFF] font-mono">
                  {baseModels.map((model) => (
                    <SelectItem key={model.id} value={model.id} className="focus:bg-[#F5EFFF]/20 focus:text-[#F5EFFF]">
                      {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Advanced settings section with cyberpunk styling */}
        <div className="border-t border-[#F5EFFF]/30 pt-2">
          <details className="group">
            <summary className="flex items-center justify-between cursor-pointer text-[#F5EFFF]/70 hover:text-[#F5EFFF] transition-colors">
              <span className="text-sm font-mono uppercase">ADVANCED_CONFIG</span>
              <ChevronDown className="w-4 h-4 transition-transform duration-300 group-open:rotate-180" />
            </summary>

            <div className="mt-3 border-l border-[#F5EFFF]/30 pl-3">
              {/* 高级设置使用3列网格布局 */}
              <div className="grid grid-cols-3 gap-4">
                {/* Learning rate with cyberpunk styling */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="learningRate" className="text-[#F5EFFF] font-mono uppercase text-xs tracking-wider">
                      Learning Rate
                    </Label>
                    <span className="text-xs text-[#F5EFFF] font-mono bg-[#F5EFFF]/10 px-2 py-0.5 rounded border border-[#F5EFFF]/30">
                      {settings.learning_rate.toExponential(6)}
                    </span>
                  </div>
                  <div className="relative">
                    <CyberpunkSlider
                      id="learningRate"
                      min={1e-6}
                      max={1e-4}
                      step={1e-6}
                      value={[settings.learning_rate]}
                      onValueChange={(value: number[]) => updateSetting("learning_rate", value[0])}
                      className="py-2"
                    />
                  </div>
                </div>
                {/* Accumulate grad batches */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="accumulateGradBatches" className="text-[#F5EFFF] font-mono uppercase text-xs tracking-wider">
                      Gradient Acc.
                    </Label>
                    <span className="text-xs text-[#F5EFFF] font-mono bg-[#F5EFFF]/10 px-2 py-0.5 rounded border border-[#F5EFFF]/30">
                      {settings.accumulate_grad_batches}
                    </span>
                  </div>
                  <div className="relative">
                    <CyberpunkSlider
                      id="accumulateGradBatches"
                      min={1}
                      max={8}
                      step={1}
                      value={[settings.accumulate_grad_batches]}
                      onValueChange={(value: number[]) => updateSetting("accumulate_grad_batches", value[0])}
                      className="py-2"
                    />
                  </div>
                </div>
                {/* Max epochs */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="maxEpochs" className="text-[#F5EFFF] font-mono uppercase text-xs tracking-wider">
                      Max Epochs
                    </Label>
                    <span className="text-xs text-[#F5EFFF]/70 font-mono">{settings.max_epochs}</span>
                  </div>
                  <CyberpunkSlider
                    id="maxEpochs"
                    min={1}
                    max={10}
                    step={1}
                    value={[settings.max_epochs]}
                    onValueChange={(value: number[]) => updateSetting("max_epochs", value[0])}
                    className="py-2"
                  />
                </div>

                {/* LoRA rank */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="loraRank" className="text-[#F5EFFF] font-mono uppercase text-xs tracking-wider">
                      LoRA Rank
                    </Label>
                    <span className="text-xs text-[#F5EFFF]/70 font-mono">{settings.lora_rank}</span>
                  </div>
                  <CyberpunkSlider
                    id="loraRank"
                    min={1}
                    max={16}
                    step={1}
                    value={[settings.lora_rank]}
                    onValueChange={(value: number[]) => updateSetting("lora_rank", value[0])}
                    className="py-2"
                  />
                </div>

                {/* LoRA alpha */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="loraAlpha" className="text-[#F5EFFF] font-mono uppercase text-xs tracking-wider">
                      LoRA Alpha
                    </Label>
                    <span className="text-xs text-[#F5EFFF]/70 font-mono">{settings.lora_alpha.toFixed(1)}</span>
                  </div>
                  <CyberpunkSlider
                    id="loraAlpha"
                    min={1}
                    max={16}
                    step={0.1}
                    value={[settings.lora_alpha]}
                    onValueChange={(value: number[]) => updateSetting("lora_alpha", value[0])}
                    className="py-2"
                  />
                </div>

                {/* Init LoRA weights */}
                <div className="space-y-2">
                  <Label htmlFor="initLoraWeights" className="text-[#F5EFFF] font-mono uppercase text-xs tracking-wider">
                    Init LoRA Weights
                  </Label>
                  <Select
                    value={settings.init_lora_weights}
                    onValueChange={(value: "gaussian" | "kaiming") => updateSetting("init_lora_weights", value)}
                  >
                    <SelectTrigger className="bg-black border-[#F5EFFF]/50 focus:ring-[#F5EFFF] focus:border-[#F5EFFF] text-[#F5EFFF] font-mono">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-black border-[#F5EFFF] text-[#F5EFFF] font-mono">
                      <SelectItem value="gaussian" className="focus:bg-[#F5EFFF]/20 focus:text-[#F5EFFF]">
                        gaussian
                      </SelectItem>
                      <SelectItem value="kaiming" className="focus:bg-[#F5EFFF]/20 focus:text-[#F5EFFF]">
                        kaiming
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </details>
        </div>
      </div>
    </div>
  )
}

