import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { CustomLogger } from '../services/logger.service';
import { ClientInfo } from '../interfaces/client-info.interface';
import { getRealClientIP } from '../utils/ip.utils';
import * as useragent from 'useragent';

@Injectable()
export class ClientInfoMiddleware implements NestMiddleware {
    constructor(private readonly logger: CustomLogger) {
        this.logger.setContext('ClientInfoMiddleware');
    }

    use(req: Request, res: Response, next: NextFunction) {
        try {
            // 为请求生成唯一的requestId
            const requestId = req['requestId'] || uuidv4();

            // 如果没有在前一个中间件设置requestId，则设置它
            if (!req['requestId']) {
                req['requestId'] = requestId;
            }

            // 解析用户代理信息
            const userAgentString = req.get('user-agent') || '';
            const agent = useragent.parse(userAgentString);

            // 构建基本的客户端信息
            const clientInfo: ClientInfo = {
                ip: getRealClientIP(req),
                userAgent: userAgentString,
                requestId: requestId,
                device: {
                    type: this.getDeviceType(agent),
                    platform: agent.os.family.toLowerCase(),
                    browser: agent.family.toLowerCase(),
                    version: agent.toVersion()
                },
                session: {
                    referrer: req.get('referer') || '',
                    entryPath: req.originalUrl,
                    language: req.get('accept-language') || ''
                },
                extraData: {
                    headers: this.sanitizeHeaders(req.headers),
                    method: req.method,
                    path: req.path,
                    query: req.query
                }
            };

            // 将客户端信息添加到请求对象中
            req['clientInfo'] = clientInfo;

            // 记录客户端信息
            this.logger.debug(`Client info collected: ${JSON.stringify({
                requestId: clientInfo.requestId,
                ip: clientInfo.ip,
                userAgent: clientInfo.userAgent?.substring(0, 100), // 避免日志过大
                path: req.path
            })}`);
        } catch (error) {
            // 出错时记录日志，但不阻止请求继续
            this.logger.error('Error collecting client info', error);

            // 确保至少有基本的客户端信息
            req['clientInfo'] = {
                ip: getRealClientIP(req),
                userAgent: req.get('user-agent') || '',
                requestId: req['requestId'] || uuidv4()
            };
        }

        next();
    }

    /**
     * 获取设备类型
     */
    private getDeviceType(agent: useragent.Agent): string {
        const ua = agent.source.toLowerCase();

        // 简单的设备类型检测逻辑
        if (/mobile|android|iphone|ipod|symbian|blackberry|windows phone/i.test(ua)) {
            return 'mobile';
        } else if (/tablet|ipad/i.test(ua)) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }

    /**
     * 清理请求头信息，移除敏感信息
     */
    private sanitizeHeaders(headers: any): Record<string, any> {
        const sanitized = { ...headers };

        // 删除可能包含敏感信息的头
        const sensitiveHeaders = [
            'authorization',
            'cookie',
            'set-cookie',
            'x-auth-token',
            'x-api-key'
        ];

        for (const header of sensitiveHeaders) {
            if (sanitized[header]) {
                sanitized[header] = '[REDACTED]';
            }
        }

        return sanitized;
    }
} 