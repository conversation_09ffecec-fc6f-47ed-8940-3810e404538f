'use client'

import { useEffect, useState, ReactNode } from 'react'

interface OptimizedLayoutProps {
  children: ReactNode
  priority?: boolean // 是否为高优先级内容
}

// 优化的布局组件 - 用于控制内容渲染优先级
export function OptimizedLayout({ children, priority = false }: OptimizedLayoutProps) {
  const [isVisible, setIsVisible] = useState(priority) // 高优先级内容立即可见

  useEffect(() => {
    if (priority) return // 高优先级内容已经可见，无需处理

    // 使用requestIdleCallback延迟渲染非关键内容
    if ('requestIdleCallback' in window) {
      // @ts-ignore - TypeScript可能不认识requestIdleCallback
      const idleId = window.requestIdleCallback(
        () => {
          setIsVisible(true)
        },
        { timeout: 2000 } // 最长等待2秒
      )

      return () => {
        // @ts-ignore
        window.cancelIdleCallback(idleId)
      }
    } else {
      // 回退到setTimeout
      const timeoutId = setTimeout(() => {
        setIsVisible(true)
      }, 200)

      return () => clearTimeout(timeoutId)
    }
  }, [priority])

  // 使用content-visibility优化渲染
  const visibilityStyle = {
    contentVisibility: priority ? 'visible' : (isVisible ? 'visible' : 'auto'),
    containIntrinsicSize: '0 500px'
  }

  return (
    <div style={visibilityStyle}>
      {isVisible ? children : null}
    </div>
  )
}
