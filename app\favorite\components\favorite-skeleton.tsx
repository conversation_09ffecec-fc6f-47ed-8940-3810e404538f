"use client"

import React from 'react';
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

interface FavoriteSkeletonProps {
    count?: number;
    type?: "post" | "model";
}

export function FavoriteSkeleton({
    count = 6,
    type = "post"
}: FavoriteSkeletonProps) {
    const renderPostSkeleton = (index: number) => (
        <motion.div
            key={`post-${index}`}
            className="overflow-hidden rounded-xl bg-background"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
        >
            <div className="relative aspect-video overflow-hidden rounded-t-xl">
                <Skeleton className="h-full w-full absolute" />
                <div className="absolute left-3 top-3">
                    <Skeleton className="h-6 w-16 rounded-full" />
                </div>
            </div>
            <div className="p-4">
                <Skeleton className="h-6 w-4/5 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-4/5 mb-3" />
                <div className="flex items-center justify-between">
                    <Skeleton className="h-3 w-24" />
                    <div className="flex items-center space-x-3">
                        <div className="flex items-center">
                            <Skeleton className="w-3.5 h-3.5 mr-1" />
                            <Skeleton className="w-6 h-3.5" />
                        </div>
                        <div className="flex items-center">
                            <Skeleton className="w-3.5 h-3.5 mr-1" />
                            <Skeleton className="w-6 h-3.5" />
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    );

    const renderModelSkeleton = (index: number) => (
        <motion.div
            key={`model-${index}`}
            className="overflow-hidden rounded-xl bg-background"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
        >
            <div className="relative aspect-video overflow-hidden rounded-t-xl">
                <Skeleton className="h-full w-full absolute" />
                <div className="absolute left-3 top-3">
                    <Skeleton className="h-6 w-16 rounded-full" />
                </div>
            </div>
            <div className="p-4">
                <Skeleton className="h-6 w-4/5 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-4/5 mb-1" />
                <Skeleton className="h-4 w-3/5 mb-3" />
                <div className="mb-2">
                    <Skeleton className="h-5 w-16 rounded-full" />
                </div>
                <Skeleton className="h-3 w-24" />
            </div>
        </motion.div>
    );

    const renderSkeletons = () => {
        const skeletons = [];
        const renderFunction = type === "post" ? renderPostSkeleton : renderModelSkeleton;

        for (let i = 0; i < count; i++) {
            skeletons.push(renderFunction(i));
        }

        return skeletons;
    };

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {renderSkeletons()}
        </div>
    );
} 