import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Search, X, Filter, SlidersHorizontal } from "lucide-react";
import { useState, useEffect, useRef, ChangeEvent } from "react";
import { cn } from "@/lib/utils";
import {
    Popover,
    PopoverContent,
    PopoverTrigger
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface ModelSearchProps {
    searchTerm: string;
    onSearchChange: (term: string) => void;
    totalCount: number;
    filteredCount: number;
    isSearching: boolean;
    className?: string;

    // 过滤器相关
    activeFilters?: {
        type?: string[];
        features?: string[];
        source?: string[];
    };
    onFilterChange?: (filterType: string, value: string, isActive: boolean) => void;
    onResetFilters?: () => void;
}

export function ModelSearch({
    searchTerm,
    onSearchChange,
    totalCount,
    filteredCount,
    isSearching,
    className,
    activeFilters = {
        type: [],
        features: [],
        source: []
    },
    onFilterChange,
    onResetFilters
}: ModelSearchProps) {
    const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

    // 计算活跃过滤器总数
    const totalActiveFilters =
        (activeFilters.type?.length || 0) +
        (activeFilters.features?.length || 0) +
        (activeFilters.source?.length || 0);

    // 同步外部搜索词到本地状态
    useEffect(() => {
        setLocalSearchTerm(searchTerm);
    }, [searchTerm]);

    // 处理搜索输入变化
    const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setLocalSearchTerm(value);

        // 清除之前的定时器
        if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
        }

        // 设置新的定时器，300ms后触发搜索
        debounceTimerRef.current = setTimeout(() => {
            onSearchChange(value);
        }, 300);
    };

    // 清除搜索
    const handleClearSearch = () => {
        setLocalSearchTerm('');
        onSearchChange('');
        if (searchInputRef.current) {
            searchInputRef.current.focus();
        }
    };

    // 处理过滤器变化
    const handleFilterChange = (filterType: string, value: string, isChecked: boolean) => {
        if (onFilterChange) {
            onFilterChange(filterType, value, isChecked);
        }
    };

    // 重置所有过滤器
    const handleResetFilters = () => {
        if (onResetFilters) {
            onResetFilters();
        }
    };

    // 计算显示文本
    const displayText = isSearching
        ? "Searching..."
        : searchTerm
            ? `Found ${filteredCount} models`
            : `${totalCount} models available`;

    return (
        <div className={cn("flex flex-col space-y-2", className)}>
            <div className="flex items-center gap-2">
                {/* 搜索输入框 */}
                <div className="relative flex-1">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                        <Search className="h-4 w-4" />
                    </div>
                    <Input
                        ref={searchInputRef}
                        type="text"
                        placeholder="Search by name, type, or description..."
                        className={cn(
                            "pl-10 pr-10 py-2 h-10 bg-background/80 border-foreground/10 focus-visible:ring-1 focus-visible:ring-foreground/20",
                            "transition-all duration-200 ease-in-out",
                            localSearchTerm && "pr-8"
                        )}
                        value={localSearchTerm}
                        onChange={handleSearchChange}
                    />
                    {localSearchTerm && (
                        <button
                            onClick={handleClearSearch}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                            aria-label="Clear search"
                        >
                            <X className="h-4 w-4" />
                        </button>
                    )}
                </div>

                {/* 过滤器按钮 */}
                <Popover>
                    <PopoverContent className="w-56 p-3" align="end">
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <h5 className="text-xs font-medium text-muted-foreground">Features</h5>
                                <div className="space-y-1">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="filter-text2video"
                                            checked={activeFilters.features?.includes('text-to-video')}
                                            onCheckedChange={(checked) => handleFilterChange('features', 'text-to-video', !!checked)}
                                        />
                                        <Label htmlFor="filter-text2video" className="text-sm">Text to Video</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="filter-img2video"
                                            checked={activeFilters.features?.includes('image-to-video')}
                                            onCheckedChange={(checked) => handleFilterChange('features', 'image-to-video', !!checked)}
                                        />
                                        <Label htmlFor="filter-img2video" className="text-sm">Image to Video</Label>
                                    </div>
                                </div>
                            </div>

                            <Button
                                className="w-full"
                                size="sm"
                                onClick={handleResetFilters}
                                variant={totalActiveFilters > 0 ? "default" : "outline"}
                            >
                                {totalActiveFilters > 0 ? "Reset Filters" : "No Filters Applied"}
                            </Button>
                        </div>
                    </PopoverContent>
                </Popover>
            </div>

            {/* 搜索状态和结果计数 */}
            <div className="flex items-center justify-between text-xs text-muted-foreground px-1">
                <span>{displayText}</span>
            </div>
        </div>
    );
}
