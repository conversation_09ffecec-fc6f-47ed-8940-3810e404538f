import { ImageIcon, Download, Share2 } from "lucide-react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle
} from "@/components/ui/alert-dialog"

interface ResultPreviewProps {
    generatedImage: string
    showResult: boolean
    setShowResult: (show: boolean) => void
    handleDownload: () => void
    handleShare: () => void
}

export function ResultPreview({
    generatedImage,
    showResult,
    setShowResult,
    handleDownload,
    handleShare
}: ResultPreviewProps) {
    return (
        <>
            <Card className="h-full flex flex-col">
                <CardHeader>
                    <CardTitle>Preview</CardTitle>
                    <CardDescription>
                        Your generated image will appear here
                    </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow flex items-center justify-center p-6">
                    {generatedImage ? (
                        <div className="relative w-full h-full min-h-[300px]">
                            <Image
                                src={generatedImage}
                                alt="Generated image"
                                fill
                                className="object-contain rounded-md"
                            />
                        </div>
                    ) : (
                        <div className="text-center p-8 border-2 border-dashed rounded-lg w-full flex flex-col items-center justify-center min-h-[300px]">
                            <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
                            <p className="text-muted-foreground">Your creation will appear here</p>
                        </div>
                    )}
                </CardContent>
                <CardFooter className="flex justify-between">
                    <Button
                        variant="outline"
                        disabled={!generatedImage}
                        onClick={handleDownload}
                        className="flex items-center space-x-1"
                    >
                        <Download className="h-4 w-4 mr-1" />
                        <span>Download</span>
                    </Button>
                    <Button
                        variant="outline"
                        disabled={!generatedImage}
                        onClick={handleShare}
                        className="flex items-center space-x-1"
                    >
                        <Share2 className="h-4 w-4 mr-1" />
                        <span>Share</span>
                    </Button>
                </CardFooter>
            </Card>

            {/* Results Dialog */}
            <AlertDialog open={showResult} onOpenChange={setShowResult}>
                <AlertDialogContent className="max-w-3xl">
                    <AlertDialogHeader>
                        <AlertDialogTitle>Your Created Image</AlertDialogTitle>
                        <AlertDialogDescription>
                            Here's the image we created based on your description and resources
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    {generatedImage && (
                        <div className="relative w-full h-[400px] my-4">
                            <Image
                                src={generatedImage}
                                alt="Generated image"
                                fill
                                className="object-contain rounded-md"
                            />
                        </div>
                    )}
                    <AlertDialogFooter>
                        <AlertDialogCancel>Close</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDownload}>
                            Download
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    )
} 