import { modelApi } from "@/lib/api/model";
import { effectApi } from "@/lib/api/effect";
import type { Model } from "@/types/model";
import type { Effect } from "@/types/model";

// 准备模型数据，适配前端组件需要的格式
const prepareModelData = (model: Model): Model => {
    return {
        ...model,
        // 使用默认封面图片替代null值
        cover_img: model.cover_img || '/placeholder.svg?height=400&width=300&text=Model',
        // 将model_type复制到type属性，并转为小写(兼容前端现有代码)
        type: model.model_type.toLowerCase(),
        // 添加一些默认信息
        creator: {
            name: 'Reelmind',
            avatar: '/placeholder.svg?height=32&width=32&text=R'
        },
        // 为新模型添加标签
        tags: ['AI'],
    };
}

// 准备效果数据，适配前端组件需要的格式
const prepareEffectData = (effect: Effect): Effect => {
    return {
        ...effect,
        cover_img: effect.cover_img || '/placeholder.svg?height=400&width=300&text=Effect'
    };
}

// 服务端获取模型列表（获取所有模型数据，有利于SEO）
export async function getModels(limit: number = 100) {
    try {
        // 首先获取第一页数据，了解总数
        const firstPageData = await modelApi.getModels(1, limit);
        const totalModels = firstPageData.total;
        const totalPages = Math.ceil(totalModels / limit);

        // 如果只有一页，直接返回
        if (totalPages <= 1) {
            return firstPageData.models.map(prepareModelData);
        }

        // 否则，并行获取所有页面的数据
        const pagePromises = [];
        // 第一页已经获取，从第二页开始
        for (let page = 2; page <= totalPages; page++) {
            pagePromises.push(modelApi.getModels(page, limit));
        }

        // 等待所有请求完成
        const additionalPagesData = await Promise.all(pagePromises);

        // 合并所有页面的数据
        let allModels = [...firstPageData.models];
        additionalPagesData.forEach(pageData => {
            allModels = [...allModels, ...pageData.models];
        });

        // 处理后端返回的数据，适配前端组件需要的格式
        const processedModels = allModels.map(prepareModelData);

        return processedModels;
    } catch (error) {
        console.error("Failed to fetch all models:", error);
        return [];
    }
}

// 服务端获取效果列表
export async function getEffects() {
    try {
        const effectData = await effectApi.getEffects();
        // 处理后端返回的数据，适配前端组件需要的格式
        const processedEffects = effectData.map(prepareEffectData);

        return processedEffects;
    } catch (error) {
        console.error("Failed to fetch effects:", error);
        return [];
    }
}