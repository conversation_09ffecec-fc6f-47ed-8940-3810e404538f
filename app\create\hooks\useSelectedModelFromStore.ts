import { useMemo } from 'react';
import { useInfiniteModels } from './useModelQuery';
import useModelSelectorStore from '@/store/useModelSelectorStore';
import { useModelById } from '../queries';

/**
 * 从store和已加载的模型数据中获取选中的模型
 * 避免重复请求API
 */
export const useSelectedModelFromStore = () => {
    // 从store获取选中的模型ID
    const selectedModelId = useModelSelectorStore(state => state.selectedModelId);

    // 获取所有已加载的模型数据
    const { data: modelsData, isLoading: isLoadingModels, isFetching: isFetchingModels } = useInfiniteModels();

    // 如果在已加载的模型中找不到选中的模型，则直接获取该模型
    const { data: specificModel, isLoading: isLoadingSpecificModel } = useModelById(selectedModelId);

    // 合并加载状态
    const isDataLoading = isLoadingModels || isFetchingModels || isLoadingSpecificModel;

    // 从已加载的模型数据中查找选中的模型，如果找不到则使用直接获取的模型
    const selectedModel = useMemo(() => {
        if (!selectedModelId) return null;

        // 首先尝试从已加载的模型数据中查找
        if (modelsData) {
            // 合并所有页面的模型数据
            const allModels = modelsData.pages.flatMap(page => page.models);

            // 查找选中的模型
            const foundModel = allModels.find(model => model.id === selectedModelId);
            if (foundModel) return foundModel;
        }

        // 如果在已加载的模型中找不到，则使用直接获取的模型
        if (specificModel) {
            console.log(`Using specific model from API: ${specificModel.name}`);
            return specificModel;
        }

        return null;
    }, [modelsData, selectedModelId, specificModel]);

    return {
        selectedModel,
        isLoading: isDataLoading
    };
};
