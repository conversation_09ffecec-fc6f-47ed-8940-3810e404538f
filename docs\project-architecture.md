# ReelMind 服务端项目架构文档

## 项目概览

ReelMind 服务端是一个基于 NestJS 框架开发的 TypeScript 后端应用程序，主要提供视频生成、用户管理、支付处理、模型管理等功能。该项目采用了模块化架构，遵循了依赖注入和控制反转的设计原则，每个功能模块都有明确的边界和职责。

## 技术栈

- **核心框架**：NestJS (v11.x)
- **语言**：TypeScript
- **数据库**：Supabase (PostgreSQL)
- **支付处理**：Stripe
- **日志管理**：Winston
- **监控**: Sentry
- **API文档**：Swagger
- **包管理**：pnpm

## 项目结构

```
src/
├── app.controller.ts      # 主应用控制器
├── app.module.ts          # 主应用模块（连接所有子模块）
├── app.service.ts         # 主应用服务
├── main.ts                # 应用程序入口点
├── instrument.ts          # 性能监控和跟踪配置
├── common/                # 共享模块（中间件、拦截器、过滤器等）
├── credits/               # 积分/信用管理模块
├── generation/            # 内容生成模块
├── membership/            # 会员管理模块
├── models/                # AI模型管理模块
├── payment/               # 支付处理模块
├── post/                  # 帖子/内容模块
├── scheduler/             # 定时任务模块
├── tags/                  # 标签管理模块
├── user/                  # 用户管理模块
└── video/                 # 视频处理模块
```

## 核心模块

### 1. 应用主模块 (app.module.ts)

应用的根模块，负责导入和配置所有子模块，设置全局中间件、拦截器和异常过滤器。

### 2. 公共模块 (common/)

包含可在整个应用程序中重用的组件：

- **constants/**：常量定义
- **controllers/**：共享控制器
- **decorators/**：自定义装饰器
- **filters/**：全局异常过滤器
- **guards/**：认证和授权守卫
- **interceptors/**：响应转换拦截器
- **interfaces/**：通用接口定义
- **middleware/**：请求处理中间件
- **module/**：共享模块定义
- **providers/**：服务提供者（如Supabase客户端）
- **services/**：共享服务

### 3. 用户模块 (user/)

管理用户注册、认证、资料更新等功能。

- **user.controller.ts**：处理用户相关请求
- **user.service.ts**：实现用户业务逻辑
- **dto/**：数据传输对象
- **sql/**：用户相关SQL查询

### 4. 生成模块 (generation/)

处理内容生成请求，如AI生成文本、图像等。

- **generation.controller.ts**：处理生成请求
- **generation.service.ts**：实现生成逻辑
- **dto/**：请求和响应数据定义
- **sql/**：生成相关数据库查询

### 5. 支付模块 (payment/)

管理支付流程，包括Stripe集成和支付处理。

- **payment.controller.ts**：处理支付请求
- **payment.service.ts**：实现支付业务逻辑
- **stripe.service.ts**：Stripe API集成
- **middleware/**：支付相关中间件
- **dto/**：支付相关数据定义
- **sql/**：支付数据库查询

### 6. 模型模块 (models/)

管理AI模型相关功能。

- **models.controller.ts**：处理模型查询请求
- **models.admin.controller.ts**：管理员模型管理接口
- **models.service.ts**：实现模型业务逻辑
- **model.type.ts**：模型相关类型定义
- **dto/**：模型数据传输对象
- **sql/**：模型相关数据库查询

### 7. 视频模块 (video/)

处理视频相关功能。

- **video.controller.ts**：处理视频请求
- **video.service.ts**：实现视频业务逻辑
- **video.module.ts**：视频模块定义

## 数据库交互

项目使用Supabase（基于PostgreSQL）作为数据库。数据库连接通过Supabase客户端管理：

- **src/common/providers/supabase.provider.ts**：提供Supabase客户端实例
- **src/common/providers/supabase.module.ts**：封装Supabase提供者为模块

SQL查询语句主要位于各模块的sql/目录中，例如：

- **src/user/sql/user_tables.sql**：用户相关表结构和查询

## 应用启动流程

1. 从 **main.ts** 启动应用
2. 配置全局中间件、CORS、验证管道
3. 设置Swagger API文档
4. 启动HTTP服务器监听端口

## 认证与授权

用户认证基于Supabase Auth，应用使用守卫（Guards）来保护需要认证的路由。

## 错误处理

应用使用全局异常过滤器处理和转换异常：

- **HttpExceptionFilter**：处理HTTP异常
- **ValidationExceptionFilter**：处理验证异常

## 监控与日志

- 使用Sentry进行错误监控和性能分析
- 使用Winston进行日志记录和管理

## API文档

应用使用Swagger自动生成API文档，可通过 `/api/docs` 路径访问。

## 定时任务

通过 **scheduler/** 模块实现定时任务和计划作业，使用@nestjs/schedule包。

## 依赖关系

主要模块间依赖关系：

1. 所有模块依赖 **common** 模块中的共享服务和组件
2. **payment** 模块被 **membership** 和 **credits** 模块依赖
3. **user** 模块被大多数其他模块引用和依赖
4. **models** 模块被 **generation** 模块依赖
5. **credits** 模块与 **generation** 和 **video** 模块相互依赖

## 关键文件索引

- **src/main.ts**：应用入口点
- **src/app.module.ts**：主模块配置
- **src/common/providers/supabase.provider.ts**：数据库连接
- **src/common/interceptors/transform.interceptor.ts**：响应转换
- **src/common/filters/http-exception.filter.ts**：异常处理
- **src/user/user.service.ts**：用户管理核心逻辑
- **src/payment/stripe.service.ts**：Stripe支付处理
- **src/generation/generation.service.ts**：内容生成核心逻辑
- **src/models/models.service.ts**：模型管理逻辑
- **src/video/video.service.ts**：视频处理逻辑
