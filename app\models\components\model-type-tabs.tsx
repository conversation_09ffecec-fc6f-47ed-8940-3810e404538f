import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Video, Image as ImageIcon, RefreshCcw, Grid } from "lucide-react"
import { ReactNode } from "react"

interface ModelTypeTabsProps {
    activeTab: string
    onTabChange: (value: string) => void
    children: ReactNode
    className?: string
}

export function ModelTypeTabs({ activeTab, onTabChange, children, className }: ModelTypeTabsProps) {
    return (
        <Tabs value={activeTab} onValueChange={onTabChange} className={`w-full ${className || ''}`}>
            <div className="flex overflow-x-auto no-scrollbar scroll-smooth py-1 mb-4">
                <TabsList className="flex-nowrap">
                    <TabsTrigger value="all" variant="default" className="flex items-center whitespace-nowrap">
                        <Grid className="h-4 w-4 mr-2" />
                        All Models
                    </TabsTrigger>
                    <TabsTrigger value="text-to-video" variant="text-to-video" className="flex items-center whitespace-nowrap">
                        <Video className="h-4 w-4 mr-2" />
                        Text to Video
                    </TabsTrigger>
                    <TabsTrigger value="image-to-video" variant="image-to-video" className="flex items-center whitespace-nowrap">
                        <ImageIcon className="h-4 w-4 mr-2" />
                        Image to Video
                    </TabsTrigger>
                    <TabsTrigger value="video-to-video" variant="video-to-video" className="flex items-center whitespace-nowrap">
                        <RefreshCcw className="h-4 w-4 mr-2" />
                        Video to Video
                    </TabsTrigger>
                </TabsList>
            </div>

            {children}
        </Tabs>
    )
} 