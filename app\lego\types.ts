// 卡片类型
export type CardType = "image" | "text" | "empty";

// 区域类型
export type SectionType = "theme" | "scene" | "style";

// 卡片数据接口
export interface CardData {
    id: string;
    type: CardType;
    content: string;
    selected: boolean;
    isUploading?: boolean;
}

export interface StylePreset {
    id: string;
    name: string;
    description: string;
}

export interface GenerateImageParams {
    model_id?: string;
    model?: string; // 新增模型名称参数
    prompt: string;
    negative_prompt?: string;
    guidance_scale?: number;
    steps?: number;
    seed?: string;
    width?: number;
    height?: number;
    refer_img_urls?: string[];
    image_url?: string; // FLUX模型单张图片参数
    image_urls?: string[]; // FLUX模型多张图片参数
    stylePreset?: string;
    aspectRatio?: string;
    aspect_ratio?: string;
}

export interface GenerateImageResponse {
    success: boolean;
    taskId?: string;
    error?: string;
    task?: {
        id: string;
        user_id: string;
        status: string;
        progress: number;
        input_params: any;
        output_result: any;
        storage_path: string | null;
        created_at: string;
        started_at: string | null;
        completed_at: string | null;
        [key: string]: any;
    };
    status?: string;
    estimatedWaitTime?: number;
}

export interface LegoHistoryItem {
    id: string;
    user_id: string;
    progress: number;
    prompt: string;
    input_params: {
        seed: string;
        steps: number;
        width: number;
        height: number;
        prompt: string;
        guidance_scale: number;
        refer_img_urls: string[];
        negative_prompt: string;
    };
    output_result: {
        seed: number;
        width: number;
        format: string;
        height: number;
        image_url: string;
        resolution: string;
        file_size_mb: number;
    },
    storage_path: string;
    created_at: Date;
    started_at: string | Date;
    completed_at?: string | Date;
    status?: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface LegoQueueInfo {
    taskId: string;
    position: number;
    totalInQueue: number;
    estimatedTimeSeconds?: number;
    isProcessing: boolean;
    result?: {
        imageUrl?: string;
        [key: string]: any;
    };
}

export type AspectRatio = "16:9" | "9:16" | "1:1";

// 宽高比映射关系
export const ASPECT_RATIO_DIMENSIONS = {
    "1:1": { width: 512, height: 512 },
    "9:16": { width: 576, height: 1024 },
    "16:9": { width: 1024, height: 576 },
};

// Lego模型接口
export interface LegoModel {
    name: string; // 模型名称，作为唯一标识符
    displayName: string; // 显示名称
    description?: string; // 描述
    price: number; // 模型价格（积分）
    type: string; // 模型类型 (internal/external)
    image_limit?: number; // 最多能上传图片的数量
}
