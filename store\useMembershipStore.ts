import { create } from 'zustand';
import { membershipApi } from '@/lib/api/membership';
import { UserMembership, MembershipPlan } from '@/app/membership/types';

interface MembershipState {
    // 状态
    userMembership: UserMembership | null;
    membershipPlans: MembershipPlan[] | null;
    isLoading: boolean;
    error: string | null;
    isFetchingMembershipPlans: boolean;

    // 操作
    fetchUserMembership: () => Promise<void>;
    fetchMembershipPlans: () => Promise<void>;
    resetMembershipState: () => void;
}

const useMembershipStore = create<MembershipState>((set, get) => ({
    // 初始状态
    userMembership: null,
    membershipPlans: null,
    isLoading: false,
    error: null,
    isFetchingMembershipPlans: false,

    // 获取用户会员信息
    fetchUserMembership: async () => {
        try {
            // 如果已经加载过且不是初始加载，则不重复加载
            if (get().isLoading) return;

            set({ isLoading: true, error: null });
            const membership = await membershipApi.getUserMembership();
            set({ userMembership: membership, isLoading: false });
        } catch (error) {
            console.error('获取会员信息失败:', error);
            set({
                error: error instanceof Error ? error.message : '获取会员信息失败',
                isLoading: false
            });
        }
    },

    // 获取会员计划列表
    fetchMembershipPlans: async () => {
        try {
            // 如果已经有数据或者正在请求中，则不重复加载
            if (get().membershipPlans || get().isFetchingMembershipPlans) return;

            set({ isFetchingMembershipPlans: true, isLoading: true, error: null });
            const plans = await membershipApi.getMembershipPlans();
            set({ membershipPlans: plans, isLoading: false, isFetchingMembershipPlans: false });
        } catch (error) {
            console.error('获取会员计划失败:', error);
            set({
                error: error instanceof Error ? error.message : '获取会员计划失败',
                isLoading: false,
                isFetchingMembershipPlans: false
            });
        }
    },

    // 重置状态
    resetMembershipState: () => {
        set({
            userMembership: null,
            isLoading: false,
            error: null,
            isFetchingMembershipPlans: false
        });
    }
}));

export default useMembershipStore; 