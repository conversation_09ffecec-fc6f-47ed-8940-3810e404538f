import { NextRequest } from "next/server"
import { generatePresignedUrl, generateSecureFilename, validateFileType } from "../../upload/tools"
import { createClient } from '@/lib/supabase/server'

const TRAIN_BUCKET = "train-videos" as const
const MAX_VIDEO_SIZE = 1024 * 1024 * 1024 // 1GB

export async function POST(request: NextRequest) {
    try {
        // 检查认证
        const supabase = await createClient()
        const { data: { user }, error } = await supabase.auth.getUser()

        if (error || !user) {
            return Response.json(
                { message: "Unauthorized" },
                { status: 401 }
            )
        }

        // 解析请求
        const body = await request.json()
        const { filename, contentType } = body

        // 验证文件类型
        if (!validateFileType(contentType)) {
            return Response.json(
                { message: "Invalid file type. Only video files are allowed." },
                { status: 400 }
            )
        }

        // 验证文件大小（如果请求中包含了文件大小信息）
        const fileSize = body.size
        if (fileSize && fileSize > MAX_VIDEO_SIZE) {
            return Response.json(
                { message: "File size exceeds the 1GB limit." },
                { status: 400 }
            )
        }

        // 生成安全的文件名
        const secureFilename = generateSecureFilename(user.id, filename)

        // 生成预签名URL
        const { url, publicUrl } = await generatePresignedUrl(
            secureFilename,
            contentType,
            3600, // 1小时有效期
            TRAIN_BUCKET
        )

        return Response.json({
            url,
            publicUrl,
            key: secureFilename,
        })
    } catch (error) {
        console.error("[TRAIN UPLOAD]", error)
        return Response.json(
            { message: "Internal server error" },
            { status: 500 }
        )
    }
} 