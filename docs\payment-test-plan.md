# 支付模块测试方案

## 1. 测试范围

本测试方案涵盖了支付模块的以下组件：

1. **PaymentService** - 支付服务的核心功能
2. **StripeService** - Stripe支付集成
3. **PaymentController** - API端点

## 2. 测试环境

### 2.1 测试框架和工具

- Jest - 单元测试框架
- NestJS Testing - NestJS测试工具
- Mock对象 - 模拟外部依赖

### 2.2 测试数据

测试中使用的主要测试数据包括：

- 用户ID
- 支付记录
- Stripe会话响应
- 会员等级和价格

## 3. 测试策略

### 3.1 单元测试

对各个组件进行独立测试，使用mock对象模拟外部依赖。

### 3.2 集成测试

测试组件之间的交互，包括控制器、服务和外部API的集成。

### 3.3 端到端测试

测试完整的支付流程，从创建支付订单到处理回调。

## 4. 测试用例

### 4.1 PaymentService测试

#### 4.1.1 创建支付订单

- 测试成功创建支付订单
- 测试创建支付订单失败情况

#### 4.1.2 创建Stripe支付

- 测试成功创建Stripe支付会话
- 测试会员级别无效的情况
- 测试计算价格的逻辑

#### 4.1.3 获取用户支付记录

- 测试获取用户支付列表
- 测试分页功能

#### 4.1.4 获取支付详情

- 测试获取存在的支付详情
- 测试获取不存在的支付详情

#### 4.1.5 取消支付

- 测试成功取消支付
- 测试取消不存在的支付
- 测试取消已完成的支付

#### 4.1.6 处理支付回调

- 测试成功处理支付回调
- 测试处理无效支付ID的回调

#### 4.1.7 处理Stripe Webhook

- 测试处理checkout.session.completed事件
- 测试处理payment_intent.succeeded事件
- 测试处理payment_intent.payment_failed事件

### 4.2 StripeService测试

#### 4.2.1 创建会员订阅支付会话

- 测试每月会员订阅会话创建
- 测试年度会员订阅会话创建
- 测试无效会员级别

#### 4.2.2 创建会员续费支付会话

- 测试会员续费会话创建
- 测试无效会员级别

#### 4.2.3 构建Webhook事件

- 测试从有效签名构建事件
- 测试从无效签名构建事件

#### 4.2.4 获取结账会话

- 测试获取存在的会话
- 测试获取不存在的会话

### 4.3 PaymentController测试

#### 4.3.1 创建支付订单

- 测试调用相应的服务方法
- 测试授权逻辑

#### 4.3.2 创建Stripe支付

- 测试调用相应的服务方法
- 测试返回结果格式

#### 4.3.3 获取用户支付记录

- 测试分页参数处理
- 测试用户授权

#### 4.3.4 获取支付详情

- 测试正确处理支付ID
- 测试处理错误情况

#### 4.3.5 取消支付

- 测试用户授权
- 测试调用相应的服务方法

#### 4.3.6 处理支付回调

- 测试API密钥验证
- 测试调用相应的服务方法

#### 4.3.7 处理Stripe Webhook

- 测试请求签名验证
- 测试调用相应的服务方法

## 5. 模拟策略

### 5.1 外部依赖模拟

- Supabase客户端 - 模拟数据库操作
- Stripe SDK - 模拟Stripe API调用
- 配置服务 - 模拟环境配置
- 会员服务 - 模拟会员相关功能

### 5.2 模拟数据准备

- 准备模拟用户数据
- 准备模拟支付记录
- 准备模拟Stripe响应

## 6. 执行计划

### 6.1 单元测试执行

- 运行PaymentService测试：`npm test payment.service`
- 运行StripeService测试：`npm test stripe.service`
- 运行PaymentController测试：`npm test payment.controller`

### 6.2 集成测试执行

- 运行完整支付模块测试：`npm test payment`

### 6.3 端到端测试执行

- 运行支付端到端测试：`npm run test:e2e`

## 7. 测试覆盖率目标

- 代码行覆盖率 > 85%
- 分支覆盖率 > 80%
- 函数覆盖率 > 90%

## 8. 持续集成

测试将集成到CI/CD流程中：

- 每次提交时运行单元测试
- 每次Pull Request时运行单元测试和集成测试
- 发布前运行端到端测试

## 9. Bug跟踪与修复

- 所有发现的问题将记录到Issue跟踪系统
- 修复后的Bug需要添加对应的测试用例
- 关闭Bug前需要通过所有相关测试

## 10. 测试报告

测试完成后将生成以下报告：

- 测试执行结果报告
- 代码覆盖率报告
- 发现的问题清单

---

这个测试方案将确保支付模块的稳定性和可靠性，减少生产环境中的错误，并为未来的功能扩展提供保障。
