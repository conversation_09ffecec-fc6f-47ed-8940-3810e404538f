# 面向Next.js的接口及接入说明

## 一、概述

本文档提供了如何在Next.js前端应用中集成Stripe支付系统，实现会员计划的订阅和续费功能的详细说明。后端已实现了Stripe支付处理的核心逻辑，前端需要与后端API对接，并提供良好的用户支付体验。

## 二、前置准备

### 1. 安装依赖

```bash
npm install @stripe/stripe-js @stripe/react-stripe-js
```

### 2. 环境变量配置

在Next.js项目根目录创建或编辑`.env.local`文件：

```
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
```

## 三、API接口说明

后端已实现以下API接口用于Stripe支付处理：

### 1. 创建Stripe支付会话

- **URL**: `POST /payment/stripe`
- **鉴权**: 需要用户JWT令牌
- **请求体**:
  ```typescript
  {
    type: "membership", // 支付类型，固定为"membership"
    level: 1, // 会员等级：1=PRO会员，2=MAX会员
    months: 12, // 购买月份
    successUrl: "https://your-site.com/payment/success", // 支付成功跳转URL
    cancelUrl: "https://your-site.com/payment/cancel" // 支付取消跳转URL
  }
  ```
- **响应**:
  ```typescript
  {
    payment: {
      id: "uuid", // 支付记录ID
      user_id: "uuid",
      type: "membership",
      amount: 120.00, // 支付金额
      status: "pending", // 支付状态
      // 其他支付信息...
    },
    checkoutUrl: "https://checkout.stripe.com/..." // Stripe结账页面URL
  }
  ```

### 2. 获取用户支付记录

- **URL**: `GET /payment/user-payments`
- **鉴权**: 需要用户JWT令牌
- **查询参数**: `limit`（条数），`offset`（偏移量）
- **响应**: 支付记录列表和总数

### 3. 获取支付详情

- **URL**: `GET /payment/:paymentId`
- **鉴权**: 需要用户JWT令牌
- **响应**: 支付详情

### 4. 取消支付

- **URL**: `POST /payment/:paymentId/cancel`
- **鉴权**: 需要用户JWT令牌
- **响应**: 取消结果

## 六、会员状态管理

### 1. 会员状态API封装

创建`lib/api/membership.ts`：

```typescript
import { api } from '@/lib/api';
import { MembershipPlan, Membership } from '@/types/membership';

// 获取会员方案列表
export const getMembershipPlans = async (): Promise<MembershipPlan[]> => {
  const response = await api.get('/membership/plans');
  return response.data;
};

// 获取当前用户会员信息
export const getUserMembership = async (): Promise<Membership> => {
  const response = await api.get('/membership');
  return response.data;
};

// 开通会员
export const createMembership = async (
  level: number,
  months: number,
  paymentId?: string,
) => {
  const response = await api.post('/membership/create', {
    level,
    months,
    payment_id: paymentId,
  });
  return response.data;
};

// 续费会员
export const renewMembership = async (
  level: number,
  months: number,
  paymentId?: string,
) => {
  const response = await api.post('/membership/renew', {
    level,
    months,
    payment_id: paymentId,
  });
  return response.data;
};

// 取消会员
export const cancelMembership = async () => {
  const response = await api.post('/membership/cancel');
  return response.data;
};
```

### 2. 会员状态和权益检查

创建`lib/hooks/useMembership.ts`：

```typescript
import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth';
import { getUserMembership } from '@/lib/api/membership';
import { MembershipLevel } from '@/types/membership';

export function useMembership() {
  const { isAuthenticated } = useAuth();
  const [membership, setMembership] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchMembership() {
      if (!isAuthenticated) {
        setMembership(null);
        setLoading(false);
        return;
      }

      try {
        const data = await getUserMembership();
        setMembership(data);
      } catch (err) {
        console.error('获取会员信息失败', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    }

    fetchMembership();
  }, [isAuthenticated]);

  const isMember =
    membership?.is_active && membership?.level > MembershipLevel.FREE;

  const isProMember =
    membership?.is_active && membership?.level === MembershipLevel.PRO;

  const isMaxMember =
    membership?.is_active && membership?.level === MembershipLevel.MAX;

  const daysLeft = membership?.days_left || 0;

  return {
    membership,
    loading,
    error,
    isMember,
    isProMember,
    isMaxMember,
    daysLeft,
  };
}
```

## 七、集成测试

在集成Stripe支付系统时，建议先使用Stripe的测试模式进行测试：

### 1. Stripe测试卡号

| 卡类型       | 卡号                | 到期日       | CVC       | 邮编      |
| ------------ | ------------------- | ------------ | --------- | --------- |
| Visa         | 4242 4242 4242 4242 | 任何未来日期 | 任意3位数 | 任意5位数 |
| 需要验证的卡 | 4000 0000 0000 3220 | 任何未来日期 | 任意3位数 | 任意5位数 |
| 余额不足的卡 | 4000 0000 0000 9995 | 任何未来日期 | 任意3位数 | 任意5位数 |

### 2. 测试流程

1. 使用测试卡号创建支付
2. 验证支付成功回调处理
3. 检查会员状态更新
4. 测试取消支付流程
5. 测试续费流程

## 八、最佳实践与注意事项

1. **安全性考虑**：

   - 敏感信息（如API密钥）不要暴露在前端代码中
   - 使用HTTPS保护API通信
   - 实现防CSRF措施

2. **用户体验**：

   - 提供明确的支付金额和周期信息
   - 显示支付过程中的加载状态
   - 提供清晰的支付结果反馈
   - 显示会员到期时间和剩余天数

3. **错误处理**：

   - 捕获并优雅处理所有API错误
   - 提供用户友好的错误信息
   - 记录详细的错误日志以便调试

4. **响应式设计**：

   - 确保支付界面在移动设备上同样易用
   - 测试不同屏幕尺寸的显示效果

5. **国际化**：
   - 支持多语言显示
   - 考虑不同地区的货币显示

## 九、问题排查

### 常见问题及解决方案

1. **支付创建失败**

   - 检查API请求参数是否正确
   - 检查用户认证状态和Token
   - 检查网络连接

2. **支付成功但会员未激活**

   - 检查支付成功回调URL是否正确
   - 检查后端Webhook处理逻辑
   - 查看后端日志确认支付状态更新

3. **Stripe结账页面不显示**

   - 确认Stripe公钥配置正确
   - 检查checkoutUrl是否正确返回
   - 检查浏览器控制台错误日志

4. **会员权益不生效**
   - 检查会员状态API是否返回正确信息
   - 确认前端权益检查逻辑
   - 验证用户会员级别是否正确设置
