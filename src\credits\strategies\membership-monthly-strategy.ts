import { Injectable } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../../common/providers/supabase.provider';
import { Inject } from '@nestjs/common';
import { BaseCreditStrategy } from './base-credit-strategy';
import { CreditTransactionType } from '../constant';

@Injectable()
export class MembershipMonthlyStrategy extends BaseCreditStrategy {
    constructor(
        @Inject(SUPABASE_CLIENT) protected readonly supabase: SupabaseClient,
    ) {
        super(supabase, CreditTransactionType.MEMBERSHIP_MONTHLY);
    }

    /**
     * 计算会员应发放的月度积分数量
     * @param _userId 用户ID（未使用）
     * @param params 包含planId和可选plan对象的参数对象
     * @returns 计算得到的积分数量
     */
    async calculateAmount(_userId: string, params?: { planId: string; plan?: any }): Promise<number> {
        try {
            if (!params?.planId) {
                this.logger.error(`计算会员月度积分时缺少planId参数`);
                return 0;
            }

            // 查询会员计划对应的积分奖励配置
            const { data: interestData, error: interestError } = await this.supabase
                .from('membership_interests')
                .select('interest_value')
                .eq('interest_type', 'credits')
                .eq('interest_key', 'monthly_grant') // 使用monthly_grant配置项
                .eq('plan_id', params.planId)
                .eq('is_active', true)
                .single();

            if (interestError) {
                if (interestError.code === 'PGRST116') { // 没有找到记录
                    // 优先使用传入的plan对象，避免重复查询
                    let planData = params.plan;

                    // 如果没有传入plan对象，则查询数据库
                    if (!planData) {
                        const { data, error: planError } = await this.supabase
                            .from('membership_plans')
                            .select('plan_name, level')
                            .eq('id', params.planId)
                            .single();

                        if (planError) {
                            this.logger.error(`获取会员计划信息失败: ${planError.message}`);
                            return 0;
                        }

                        planData = data;
                    }

                    // 根据会员计划名称或级别返回默认值
                    if (planData.plan_name) {
                        const planName = planData.plan_name.toUpperCase();
                        if (planName.includes('PRO')) {
                            return 1288; // PRO会员
                        } else if (planName.includes('MAX')) {
                            return 2888; // MAX会员
                        }
                    }

                    // 如果没有通过名称判断，则使用level
                    if (planData.level) {
                        switch (planData.level) {
                            case 1: return 1288; // PRO会员
                            case 2: return 2888; // MAX会员
                        }
                    }

                    return 0; // 默认返回0
                }

                this.logger.error(`获取会员积分配置失败: ${interestError.message}`);
                return 0;
            }

            // 如果找到配置，返回配置的值
            return parseInt(interestData.interest_value, 10) || 0;
        } catch (error) {
            this.logger.error(`计算会员月度积分失败: ${error.message}`, error.stack);
            return 0;
        }
    }

    /**
     * 检查是否可以发放会员月度积分
     * @param userId 用户ID
     * @param params 包含planId和可选的forceGrant标志的参数对象
     * @returns 是否可以发放积分
     */
    async canGrant(userId: string, params?: { planId: string; forceGrant?: boolean; invoiceId?: string }): Promise<boolean> {
        try {
            // 首先检查基本条件
            const baseCanGrant = await super.canGrant(userId, params);
            if (!baseCanGrant || !params?.planId) {
                return false;
            }

            // 如果设置了强制发放标志，则跳过重复检查
            if (params.forceGrant) {
                this.logger.log(`用户 ${userId} 设置了强制发放标志，跳过重复检查`);
                return true;
            }

            // 检查今天是否已经发放过月度积分
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // 构建查询条件
            let query = this.supabase
                .from('credit_transactions')
                .select('id, description, reference_id')
                .eq('user_id', userId)
                .eq('type', this.type)
                .gte('created_at', today.toISOString());

            // 如果提供了发票ID，检查是否已经为该发票发放过积分
            if (params.invoiceId) {
                // 先检查是否已经为这个特定的发票ID发放过积分
                const { data: invoiceCredits, error: invoiceError } = await this.supabase
                    .from('credit_transactions')
                    .select('id')
                    .eq('user_id', userId)
                    .eq('type', this.type)
                    .eq('reference_id', params.invoiceId)
                    .limit(1);

                if (invoiceError) {
                    this.logger.error(`检查发票积分发放记录失败: ${invoiceError.message}`);
                } else if (invoiceCredits && invoiceCredits.length > 0) {
                    this.logger.log(`用户 ${userId} 已经为发票 ${params.invoiceId} 发放过会员月度积分`);
                    return false;
                }
            }

            // 执行查询
            const { data: existingCredits, error: checkError } = await query.limit(1);

            if (checkError) {
                this.logger.error(`检查月度积分发放记录失败: ${checkError.message}`);
                return false;
            }

            // 如果今天已经发放过，则不再发放
            if (existingCredits && existingCredits.length > 0) {
                this.logger.log(`用户 ${userId} 今天已经获得过会员月度积分，交易ID: ${existingCredits[0].id}`);
                return false;
            }

            return true;
        } catch (error) {
            this.logger.error(`检查是否可以发放会员月度积分失败: ${error.message}`, error.stack);
            return false;
        }
    }

    /**
     * 执行会员月度积分刷新（重置）
     * @param userId 用户ID
     * @param params 包含planId、可选plan对象、可选description、可选forceGrant和可选invoiceId的参数对象
     * @returns 刷新结果
     */
    async execute(userId: string, params?: {
        planId: string;
        plan?: any;
        description?: string;
        forceGrant?: boolean;
        invoiceId?: string;
        isRenewal?: boolean;
    }): Promise<any> {
        try {
            if (!params?.planId) {
                return {
                    success: false,
                    message: '缺少会员计划ID参数'
                };
            }

            // 检查是否可以发放积分
            const canGrant = await this.canGrant(userId, params);
            if (!canGrant) {
                return {
                    success: false,
                    message: '不满足发放条件或今天已经发放过'
                };
            }

            // 获取会员计划信息，用于生成描述
            let membershipType = 'unknown';
            let planData = params?.plan; // 优先使用传入的plan对象

            // 如果没有传入plan对象，则查询数据库
            if (!planData && params?.planId) {
                const { data, error: planError } = await this.supabase
                    .from('membership_plans')
                    .select('level, plan_name')
                    .eq('id', params.planId)
                    .single();

                if (!planError && data) {
                    planData = data;
                }
            }

            // 使用plan_name作为会员类型
            if (planData && planData.plan_name) {
                membershipType = planData.plan_name;
            }

            // 计算积分数量 - 传递plan对象避免重复查询
            const amount = await this.calculateAmount(userId, {
                planId: params.planId,
                plan: planData
            });

            if (amount <= 0) {
                this.logger.log(`用户 ${userId} 计算得到的月度积分为 ${amount}，不执行发放`);
                return {
                    success: false,
                    message: '计算得到的积分数量不大于0'
                };
            }

            // 设置描述
            let description = params?.description;
            if (!description) {
                if (params.isRenewal) {
                    description = `${membershipType}会员续费赠送${amount}积分`;
                } else {
                    description = `${membershipType}会员月度赠送${amount}积分`;
                }
            }

            // 准备RPC调用参数 - 使用refresh_membership_credits函数
            const rpcParams: any = {
                p_user_id: userId,
                p_plan_id: params.planId,
                p_description: description
            };

            // 执行积分刷新（重置）
            this.logger.log(`正在为用户 ${userId} 刷新会员积分...`);
            const { data, error } = await this.supabase.rpc('refresh_membership_credits', rpcParams);

            if (error) {
                this.logger.error(`刷新会员积分失败: ${error.message}`, error.stack);
                throw error;
            }

            // 检查SQL函数返回的结果
            if (!data.success) {
                this.logger.error(`刷新会员积分失败: ${data.error || '未知错误'}`);
                return {
                    success: false,
                    message: data.error || '刷新积分失败'
                };
            }

            const logMessage = params.isRenewal
                ? `成功为用户 ${userId} 刷新会员续费积分，新余额: ${data.new_balance}，旧余额: ${data.previous_balance}`
                : `成功为用户 ${userId} 刷新会员月度积分，新余额: ${data.new_balance}，旧余额: ${data.previous_balance}`;

            this.logger.log(logMessage);

            return {
                success: true,
                transaction: data.transaction_id, // SQL函数返回的交易ID字段
                newBalance: data.new_balance,
                previousBalance: data.previous_balance,
                amount: data.new_balance, // 新的余额就是发放的积分数量
                isRenewal: params.isRenewal || false
            };
        } catch (error) {
            this.logger.error(`执行会员月度积分发放失败: ${error.message}`, error.stack);
            throw error;
        }
    }
}
