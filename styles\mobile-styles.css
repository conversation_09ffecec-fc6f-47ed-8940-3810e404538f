/* Mobile-specific animations and styles */

/* Bottom navigation bar animations */
@keyframes slideUpIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.mobile-nav-enter {
  animation: slideUpIn 0.3s ease-out forwards;
}

/* Active nav item indicator */
.mobile-nav-indicator {
  position: absolute;
  bottom: 0;
  height: 3px;
  width: 100%;
  background: linear-gradient(to right, var(--primary-500), var(--primary-400));
  border-radius: 3px 3px 0 0;
  transition: transform 0.3s ease;
}

/* Mobile sheet animations */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mobile-sheet {
  animation: slideInFromRight 0.3s ease-out;
}

.mobile-sheet-overlay {
  animation: fadeIn 0.2s ease-out;
}

/* Mobile card scaling */
.mobile-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.mobile-card:active {
  transform: scale(0.98);
  box-shadow: 0 0 0 1px var(--primary-200);
}

/* Mobile-specific padding and margins */
@media (max-width: 640px) {
  .mobile-content-px {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .mobile-content-py {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  
  .mobile-no-scrollbar {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  
  .mobile-no-scrollbar::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}

/* Mobile tap state */
.mobile-tap-effect {
  position: relative;
  overflow: hidden;
}

.mobile-tap-effect::after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, var(--primary-400) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.5s, opacity 0.8s;
}

.mobile-tap-effect:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

/* Optimized scroll on mobile */
.optimize-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Safe area paddings for modern mobile browsers */
.safe-area-bottom {
  padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 16px);
}

.safe-area-top {
  padding-top: calc(env(safe-area-inset-top, 0px) + 16px);
}

/* Slide in animations for sheet components */
.animate-slide-in-from-right {
  animation: slideInFromRight 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-slide-in-from-left {
  animation: slideInFromLeft 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-slide-in-from-bottom {
  animation: slideInFromBottom 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-slide-in-from-top {
  animation: slideInFromTop 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
} 