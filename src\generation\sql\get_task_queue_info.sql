-- 创建获取任务队列信息的SQL函数
CREATE OR REPLACE FUNCTION public.get_task_queue_info(p_task_id UUID)
RETURNS TABLE (
    task_id UUID,
    queue_position INTEGER,
    total_tasks_in_queue BIGINT,
    task_priority INTEGER,
    ahead_tasks_json JSONB,
    avg_processing_time_seconds NUMERIC,
    is_processing BOOLEAN,
    started_at TIMESTAMP WITH TIME ZONE,
    debug_task_status TEXT -- 调试字段
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    task_status TEXT;
    task_priority INTEGER;
    task_started_at TIMESTAMP WITH TIME ZONE;
    default_avg_processing_time NUMERIC := 300; -- 默认平均处理时间为5分钟
    avg_time NUMERIC;
    task_jsonb JSONB;
BEGIN
    -- 检查任务是否存在并获取状态和优先级
    SELECT vgt.status, COALESCE(vgt.priority, 1), vgt.started_at 
    INTO task_status, task_priority, task_started_at
    FROM public.video_gen_tasks vgt
    WHERE vgt.id = p_task_id;

    -- 如果任务不存在，返回空结果
    IF task_status IS NULL THEN
        RETURN;
    END IF;
    
    -- 预先获取平均处理时间，避免在每个查询中重复获取
    SELECT COALESCE(
        (SELECT stat_value 
         FROM public.task_processing_stats 
         WHERE stat_type = 'avg_processing_time_seconds' 
         ORDER BY updated_at DESC LIMIT 1),
        default_avg_processing_time
    ) INTO avg_time;

    -- 全部使用统一的RETURN QUERY结构，避免多个RETURN
    RETURN QUERY
    SELECT 
        p_task_id::UUID,
        CASE WHEN task_status ILIKE 'processing' THEN 0 
             ELSE (
                SELECT position FROM (
                    SELECT vgt.id, ROW_NUMBER() OVER(
                        ORDER BY COALESCE(vgt.priority, 1) DESC, vgt.created_at ASC
                    ) AS position 
                    FROM public.video_gen_tasks vgt
                    WHERE vgt.status IN ('pending', 'queued')
                ) AS queue_pos
                WHERE queue_pos.id = p_task_id
             )
        END::INTEGER,
        
        CASE WHEN task_status ILIKE 'processing' THEN 0::BIGINT
             ELSE (
                SELECT COUNT(*)::BIGINT 
                FROM public.video_gen_tasks vgt
                WHERE vgt.status IN ('pending', 'queued')
             )
        END,
        
        task_priority::INTEGER,
        
        CASE WHEN task_status ILIKE 'processing' THEN '[]'::JSONB
             ELSE (
                SELECT COALESCE(jsonb_agg(
                    jsonb_build_object(
                        'id', qt.id,
                        'priority', COALESCE(qt.priority, 1)
                    )
                ), '[]'::JSONB)
                FROM public.video_gen_tasks qt
                WHERE qt.status IN ('pending', 'queued')
                AND (
                    SELECT position 
                    FROM (
                        SELECT vgt.id, ROW_NUMBER() OVER(
                            ORDER BY COALESCE(vgt.priority, 1) DESC, vgt.created_at ASC
                        ) AS position
                        FROM public.video_gen_tasks vgt
                        WHERE vgt.status IN ('pending', 'queued')
                    ) AS q
                    WHERE q.id = qt.id
                ) < (
                    SELECT position 
                    FROM (
                        SELECT vgt.id, ROW_NUMBER() OVER(
                            ORDER BY COALESCE(vgt.priority, 1) DESC, vgt.created_at ASC
                        ) AS position
                        FROM public.video_gen_tasks vgt
                        WHERE vgt.status IN ('pending', 'queued')
                    ) AS q
                    WHERE q.id = p_task_id
                )
             )
        END,
        
        avg_time::NUMERIC,
        
        (task_status ILIKE 'processing')::BOOLEAN,
        
        CASE WHEN task_status ILIKE 'processing' THEN 
                COALESCE(task_started_at, NOW() - INTERVAL '1 minute')
             ELSE NULL
        END,
        
        task_status;
END;
$$;

-- 创建统计缓存表
CREATE TABLE public.task_processing_stats (
    id SERIAL PRIMARY KEY,
    stat_type VARCHAR(50) NOT NULL,
    stat_value NUMERIC NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 创建更新统计数据的函数
CREATE OR REPLACE FUNCTION update_task_processing_stats()
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
    -- 更新或插入平均处理时间
    INSERT INTO public.task_processing_stats (stat_type, stat_value, updated_at)
    VALUES ('avg_processing_time_seconds', 
           (SELECT COALESCE(
                AVG(EXTRACT(EPOCH FROM (completed_at - started_at))),
                300) AS avg_seconds
            FROM public.video_gen_tasks
            WHERE 
                status = 'completed' 
                AND started_at IS NOT NULL 
                AND completed_at IS NOT NULL
                AND completed_at > NOW() - INTERVAL '7 days'),
           NOW())
    ON CONFLICT (stat_type) 
    DO UPDATE SET 
        stat_value = EXCLUDED.stat_value,
        updated_at = NOW();
END;
$$;

-- 创建一个触发器或使用cron job定期调用此函数 