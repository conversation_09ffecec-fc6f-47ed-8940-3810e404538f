import { apiClient, ApiResponse } from './client';

// 统计事件类型
export type PostStatsEventType = 'view' | 'click';

// 统计事件请求参数
export interface PostStatsEventParams {
  post_id: string;
  event_type: PostStatsEventType;
  client_info?: Record<string, any>;
}

// 统计事件响应
export interface PostStatsResponse {
  success: boolean;
}

// 统计数据响应
export interface PostStatsData {
  view_count: number;
  click_count: number;
}

/**
 * 帖子统计API服务
 */
export const postStatsApi = {
  /**
   * 记录帖子统计事件（浏览量或点击量）
   * @param params 统计事件参数
   * @returns 操作结果
   */
  recordEvent: async (params: PostStatsEventParams): Promise<ApiResponse<PostStatsResponse>> => {
    try {
      return await apiClient.post<ApiResponse<PostStatsResponse>>('/post-stats', params);
    } catch (error) {
      // 捕获错误但不抛出，避免影响用户体验
      console.error('记录帖子统计失败:', error);
      return {
        code: -1,
        message: '记录统计失败',
        data: { success: false }
      };
    }
  },

  /**
   * 获取帖子统计数据
   * @param postId 帖子ID
   * @returns 帖子统计数据
   */
  getStats: async (postId: string): Promise<ApiResponse<PostStatsData>> => {
    try {
      return await apiClient.get<ApiResponse<PostStatsData>>(`/post-stats/${postId}`);
    } catch (error) {
      console.error('获取帖子统计数据失败:', error);
      return {
        code: -1,
        message: '获取统计数据失败',
        data: { view_count: 0, click_count: 0 }
      };
    }
  }
};
