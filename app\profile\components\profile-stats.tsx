"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Heart, UserCircle, Clock, Medal } from "lucide-react";
import { useAuth } from "@/contexts/auth-context";
import useMembershipStore from "@/store/useMembershipStore";
import useCreditsStore from "@/store/useCreditsStore";
import { MembershipPlanName } from "@/app/membership/types";
import { useEffect } from "react";

export function ProfileStats() {
    const { user } = useAuth();
    const { userMembership, fetchUserMembership } = useMembershipStore();
    const { balance, fetchBalance, hasLoadedBalance, isLoading } = useCreditsStore();

    // 确保会员信息已加载
    useEffect(() => {
        if (user && !userMembership) {
            fetchUserMembership();
        }
    }, [user, userMembership, fetchUserMembership]);

    // 确保积分余额已加载
    useEffect(() => {
        if (user && !hasLoadedBalance && !isLoading) {
            fetchBalance();
        }
    }, [user, hasLoadedBalance, isLoading, fetchBalance]);

    // 计算注册时间
    const getAccountAge = () => {
        if (!user?.created_at) return "N/A";

        const created = new Date(user.created_at);
        const now = new Date();
        const diffMs = now.getTime() - created.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays < 30) {
            return `${diffDays} days`;
        } else if (diffDays < 365) {
            const months = Math.floor(diffDays / 30);
            return `${months} ${months === 1 ? 'month' : 'months'}`;
        } else {
            const years = Math.floor(diffDays / 365);
            const remainingMonths = Math.floor((diffDays % 365) / 30);
            return `${years} ${years === 1 ? 'year' : 'years'}${remainingMonths > 0 ? ` ${remainingMonths} ${remainingMonths === 1 ? 'month' : 'months'}` : ''}`;
        }
    };

    // 获取会员状态显示信息
    const getMembershipInfo = () => {
        if (!userMembership) return "Free";

        if (userMembership.plan_name === MembershipPlanName.MAX) {
            return "MAX";
        } else if (userMembership.plan_name === MembershipPlanName.PRO) {
            return "PRO";
        } else {
            return "Free";
        }
    };

    // 获取会员状态样式
    const getMembershipStyle = () => {
        if (!userMembership) return "text-muted-foreground";

        if (userMembership.plan_name === MembershipPlanName.MAX) {
            return "text-amber-500";
        } else if (userMembership.plan_name === MembershipPlanName.PRO) {
            return "text-blue-500";
        } else {
            return "text-muted-foreground";
        }
    };

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8 mb-12">
            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center">
                        <UserCircle className="mr-2 h-4 w-4" />
                        Membership Level
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <p className={`text-2xl font-bold ${getMembershipStyle()}`}>
                        {getMembershipInfo()}
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center">
                        <Medal className="mr-2 h-4 w-4" />
                        Credits
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-2xl font-bold">
                        {isLoading ? "..." : balance}
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center">
                        <Heart className="mr-2 h-4 w-4" />
                        Favorites
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-2xl font-bold">
                        0
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center">
                        <Clock className="mr-2 h-4 w-4" />
                        Member Since
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-2xl font-bold">
                        {getAccountAge()}
                    </p>
                </CardContent>
            </Card>
        </div>
    );
} 