-- 超级简单的反馈系统数据库迁移脚本
-- 描述：创建简单的用户反馈表

BEGIN;

-- 1. 创建反馈表 - 超级简化版
CREATE TABLE IF NOT EXISTS public.feedbacks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL,
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),

    -- 简化的约束检查
    CONSTRAINT feedbacks_type_check CHECK (
        type IN ('bug', 'suggestion', 'other')
    ),
    CONSTRAINT feedbacks_subject_not_empty CHECK (
        char_length(trim(subject)) > 0
    ),
    CONSTRAINT feedbacks_message_not_empty CHECK (
        char_length(trim(message)) > 0
    )
);

-- 2. 创建基本索引
CREATE INDEX IF NOT EXISTS feedbacks_user_id_idx ON public.feedbacks(user_id);
CREATE INDEX IF NOT EXISTS feedbacks_created_at_idx ON public.feedbacks(created_at DESC);

-- 3. 添加简单注释
COMMENT ON TABLE public.feedbacks IS '用户反馈表';
COMMENT ON COLUMN public.feedbacks.type IS '反馈类型：bug, suggestion, other';
COMMENT ON COLUMN public.feedbacks.subject IS '反馈主题';
COMMENT ON COLUMN public.feedbacks.message IS '反馈内容';

-- 4. 启用行级安全策略（RLS）
ALTER TABLE public.feedbacks ENABLE ROW LEVEL SECURITY;

-- 5. 创建简单的RLS策略
-- 用户只能创建自己的反馈
CREATE POLICY "Users can create own feedbacks" ON public.feedbacks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

COMMIT;

-- 验证迁移
DO $$
BEGIN
    -- 检查表是否创建成功
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'feedbacks' AND table_schema = 'public') THEN
        RAISE EXCEPTION 'Migration failed: feedbacks table was not created';
    END IF;

    RAISE NOTICE 'Migration completed successfully: simple feedback system is ready';
END $$;
