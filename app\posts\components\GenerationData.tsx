"use client";

import { Download, <PERSON>rk<PERSON>, Wand2, <PERSON><PERSON>, Hash, Scale, MessageSquare, ImageIcon } from "lucide-react";
import { PostItemDto } from "@/types/posts";
import { RemixButton } from "./RemixButton";
import { useRouter } from "next/navigation";
import { useVideoModalStore } from "@/store/useVideoModalStore";
import { ActionButton } from "./ActionButton";
import { FavoriteButton } from "@/components/shared/favorite-button";
import { FavoriteTargetType } from "@/lib/api/favorite";
import Image from "next/image";

interface GenerationDataProps {
    post: PostItemDto;
}

export function GenerationData({ post }: GenerationDataProps) {
    const router = useRouter();
    const { closeModal } = useVideoModalStore();

    const inputParams = post.videos?.input_params;
    if (!inputParams) return null;

    const handleRemixClick = () => {
        if (!post.task_id && !post.videos) return;
        const taskId = post.task_id || "";
        closeModal();
        router.push(`/create?remix=${taskId}`);
    };

    const handleDownloadModel = () => {
        console.log("Downloading model...");
        // 下载模型逻辑
    };

    return (
        <div className="px-6 py-5 text-gray-200">
            <div className="flex items-center gap-2 mb-3">
                <Wand2 size={16} className="text-purple-400" />
                <h3 className="text-sm font-medium text-white">Generation Model</h3>
            </div>

            <div className="flex items-center justify-between bg-gray-900/50 rounded-lg px-4 py-3">
                <div className="flex flex-col">
                    <span className="text-white font-medium">
                        {post.videos?.input_params.model_name}
                    </span>

                    {inputParams.effect_name && (
                        <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-gray-400">
                                with {inputParams.effect_name}
                            </span>
                            <span className="px-2 py-0.5 text-xs bg-purple-900/50 text-purple-300 rounded-full">
                                EFFECT
                            </span>
                        </div>
                    )}
                </div>

                <div className="flex items-center justify-center h-8 w-8 rounded-full bg-purple-500/10 text-purple-400">
                    <Sparkles size={14} />
                </div>
            </div>

            {/* Prompt */}
            {inputParams.prompt && (
                <div
                    className="my-6"
                >
                    <div className="flex items-center gap-2 mb-3">
                        <MessageSquare size={16} className="text-blue-400" />
                        <h3 className="text-sm font-medium text-white">Prompt</h3>
                        {inputParams.gen_type && (
                            <span className="ml-auto px-2 py-0.5 text-xs bg-blue-900/30 text-blue-300 rounded-full uppercase">
                                {inputParams.gen_type}
                            </span>
                        )}
                    </div>
                    <div className="text-sm text-gray-300 bg-gray-900/50 p-4 rounded-lg backdrop-blur-sm">
                        {inputParams.prompt}
                    </div>
                </div>
            )}

            {/* Reference Image */}
            {post.videos?.input_params.refer_img_url && (
                <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                        <ImageIcon size={16} className="text-blue-400" />
                        <h3 className="text-sm font-medium text-white">Reference Image</h3>
                    </div>
                    <div className="relative w-full h-48 bg-gray-900/50 rounded-lg overflow-hidden">
                        <Image
                            src={post.videos?.input_params.refer_img_url}
                            alt="Reference image for video generation"
                            fill
                            sizes="(max-width: 768px) 100vw, 50vw"
                            className="object-contain"
                        />
                    </div>
                </div>
            )}

            {/* Negative Prompt */}
            {inputParams.negative_prompt && (
                <div
                    className="mb-6"
                >
                    <div className="flex items-center gap-2 mb-3">
                        <MessageSquare size={16} className="text-red-400" />
                        <h3 className="text-sm font-medium text-white">Negative Prompt</h3>
                    </div>
                    <div className="text-sm text-gray-300 bg-gray-900/50 p-4 rounded-lg backdrop-blur-sm">
                        {inputParams.negative_prompt}
                    </div>
                </div>
            )}

            {/* Parameters */}
            <div
                className="mb-6"
            >
                <div className="flex items-center gap-2 mb-3">
                    <Layers size={16} className="text-emerald-400" />
                    <h3 className="text-sm font-medium text-white">Parameters</h3>
                </div>
                <div className="grid grid-cols-2 gap-3">
                    {inputParams.guidance_scale !== undefined && (
                        <div className="px-3 py-2 bg-gray-900/50 rounded-lg flex items-center gap-3">
                            <Scale size={14} className="text-emerald-400" />
                            <div>
                                <div className="text-xs text-gray-400">Guidance</div>
                                <div className="text-sm font-medium">{inputParams.guidance_scale}</div>
                            </div>
                        </div>
                    )}

                    {inputParams.steps !== undefined && (
                        <div className="px-3 py-2 bg-gray-900/50 rounded-lg flex items-center gap-3">
                            <Layers size={14} className="text-emerald-400" />
                            <div>
                                <div className="text-xs text-gray-400">Steps</div>
                                <div className="text-sm font-medium">{inputParams.steps}</div>
                            </div>
                        </div>
                    )}

                    {inputParams.seed !== undefined && (
                        <div className="px-3 py-2 bg-gray-900/50 rounded-lg flex items-center gap-3">
                            <Hash size={14} className="text-emerald-400" />
                            <div>
                                <div className="text-xs text-gray-400">Seed</div>
                                <div className="text-sm font-medium">{inputParams.seed}</div>
                            </div>
                        </div>
                    )}

                    {inputParams.ratio && (
                        <div className="px-3 py-2 bg-gray-900/50 rounded-lg flex items-center gap-3">
                            <Layers size={14} className="text-emerald-400" />
                            <div>
                                <div className="text-xs text-gray-400">Ratio</div>
                                <div className="text-sm font-medium">{inputParams.ratio}</div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Action Buttons */}
            <div
                className="flex flex-col gap-3 mt-8"
            >
                <RemixButton
                    onClick={handleRemixClick}
                    className="w-full py-2.5 text-sm font-medium bg-gray-800 hover:bg-gray-700 text-white"
                    shimmerColor="rgba(255, 255, 255, 0.1)"
                    background="rgba(17, 24, 39, 0.9)"
                >
                    Remix
                </RemixButton>

                <div className="flex gap-3">
                    <ActionButton
                        icon={Download}
                        label="Download"
                        onClick={handleDownloadModel}
                        className="w-1/2 bg-gray-800/80 hover:bg-gray-700 text-white rounded-lg"
                    />
                    <FavoriteButton
                        targetId={post.id}
                        targetType={FavoriteTargetType.POST}
                        className="w-1/2 flex justify-center bg-gray-800/80 hover:bg-gray-700 text-white rounded-lg"
                        showText={true}
                    />
                </div>
            </div>
        </div>
    );
}
