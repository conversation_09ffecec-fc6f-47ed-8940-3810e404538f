create table public.membership_interests (
  id uuid not null default extensions.uuid_generate_v4 (),
  plan_id uuid not null,
  interest_type character varying(50) not null,
  interest_key character varying(50) not null,
  interest_value jsonb not null,
  description text null,
  is_active boolean null default true,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint membership_interests_pkey primary key (id),
  constraint membership_interests_plan_id_fkey foreign KEY (plan_id) references membership_plans (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_membership_interests_plan_id on public.membership_interests using btree (plan_id) TABLESPACE pg_default;