# 简化博客系统设计 (SEO 导向)

本文档提供了一个简化的博客系统设计，专注于 SEO 优化功能，去除了不必要的复杂性。

## 简化数据模型

### 1. 文章表 (blog_posts)

```sql
CREATE TABLE blog_posts (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL UNIQUE,
  content TEXT NOT NULL,
  excerpt TEXT NOT NULL,
  cover_image VARCHAR(255),
  published_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- SEO 相关字段
  seo_title VARCHAR(255) NULL,
  seo_description TEXT NULL,
  seo_keywords VARCHAR(255) NULL,

  -- 分类和标签 (简化为字符串字段)
  category VARCHAR(100) NULL,
  tags TEXT NULL, -- 以逗号分隔的标签列表

  -- 索引
  INDEX idx_blog_posts_slug (slug),
  INDEX idx_blog_posts_published_at (published_at),
  INDEX idx_blog_posts_category (category)
);
```

### 2. 简化版博客配置表 (blog_config)

```sql
CREATE TABLE blog_config (
  id INT PRIMARY KEY DEFAULT 1, -- 只有一行数据
  site_name VARCHAR(100) NOT NULL,
  site_description TEXT NULL,
  default_meta_title VARCHAR(255) NULL,
  default_meta_description TEXT NULL,
  default_meta_keywords VARCHAR(255) NULL,
  social_image VARCHAR(255) NULL, -- 默认社交媒体分享图片
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 简化 API 端点

### 文章 API

#### 获取文章列表

```
GET /api/blog/posts
```

查询参数:

- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `category`: 按分类筛选
- `tag`: 按标签筛选
- `search`: 搜索关键词

响应:

```json
{
  "posts": [
    {
      "id": "uuid",
      "title": "文章标题",
      "slug": "article-slug",
      "excerpt": "文章摘要...",
      "cover_image": "https://example.com/image.jpg",
      "published_at": "2023-06-15T10:00:00Z",
      "updated_at": "2023-06-16T10:00:00Z",
      "category": "技术",
      "tags": ["前端", "React", "JavaScript"],
      "seo": {
        "title": "SEO 标题",
        "description": "SEO 描述",
        "keywords": "关键词1,关键词2"
      }
    }
  ],
  "pagination": {
    "total": 100,
    "pages": 10,
    "page": 1,
    "limit": 10
  }
}
```

#### 获取单篇文章

```
GET /api/blog/posts/{slug}
```

响应:

```json
{
  "post": {
    "id": "uuid",
    "title": "文章标题",
    "slug": "article-slug",
    "content": "完整的文章内容，支持 Markdown",
    "excerpt": "文章摘要...",
    "cover_image": "https://example.com/image.jpg",
    "published_at": "2023-06-15T10:00:00Z",
    "updated_at": "2023-06-16T10:00:00Z",
    "category": "技术",
    "tags": ["前端", "React", "JavaScript"],
    "seo": {
      "title": "SEO 标题",
      "description": "SEO 描述",
      "keywords": "关键词1,关键词2"
    }
  },
  "related_posts": [
    // 可选的相关文章
    {
      "id": "uuid",
      "title": "相关文章",
      "slug": "related-article",
      "excerpt": "摘要..."
    }
  ]
}
```

#### 创建/更新文章

```
POST /api/blog/posts
PUT /api/blog/posts/{id}
```

请求体:

```json
{
  "title": "文章标题",
  "slug": "article-slug", // 可选，自动生成
  "content": "文章内容",
  "excerpt": "文章摘要", // 可选，自动从内容生成
  "cover_image": "https://example.com/image.jpg",
  "category": "技术",
  "tags": ["前端", "React", "JavaScript"],
  "seo": {
    "title": "SEO 标题", // 可选，默认使用文章标题
    "description": "SEO 描述", // 可选，默认使用摘要
    "keywords": "关键词1,关键词2"
  }
}
```

#### 删除文章

```
DELETE /api/blog/posts/{id}
```

### 分类和标签 API

#### 获取所有分类和标签

```
GET /api/blog/taxonomy
```

响应:

```json
{
  "categories": [
    {
      "name": "技术",
      "count": 25
    },
    {
      "name": "设计",
      "count": 10
    }
  ],
  "tags": [
    {
      "name": "React",
      "count": 15
    },
    {
      "name": "JavaScript",
      "count": 30
    }
  ]
}
```

## 前端考虑

### SEO 优化关键点

1. **服务器端渲染 (SSR)**

   - 使用 Next.js 的 `getStaticProps` 和 `getStaticPaths` 预渲染页面
   - 博客首页、文章详情页、分类页、标签页都应预渲染

2. **元数据优化**

   - 每个页面自定义 title、description 和 keywords
   - 使用 Next.js 的 Metadata API 设置元数据

3. **结构化数据**

   - 添加 JSON-LD 结构化数据，特别是 Article 类型
   - 包含作者、发布日期、标题、描述等信息

4. **语义化 HTML**

   - 使用适当的 HTML5 标签 (article, section, header 等)
   - 清晰的内容层次结构 (h1, h2, h3)

5. **URL 结构**
   - 使用 `/blog/[slug]` 格式的简洁 URL
   - 分类和标签使用 `/blog/category/[name]` 和 `/blog/tag/[name]`

## 实现方案

### 前端组件

1. **博客列表页**

   - 文章卡片组件 (BlogCard)
   - 分页组件 (Pagination)
   - 分类/标签筛选器 (FilterBar)
   - 搜索组件 (SearchBox)

2. **文章详情页**

   - 文章头部 (标题、日期、分类)
   - 文章内容 (Markdown 渲染)
   - 相关文章推荐
   - 分享链接
   - SEO 元数据组件

3. **布局组件**
   - 博客页面布局
   - 边栏组件 (可显示最近文章、分类、标签)

### 业务层

1. **Markdown 处理**

   - 使用 remark/rehype 处理 Markdown
   - 自动生成目录
   - 代码高亮

2. **SEO 工具函数**
   - 生成 sitemap.xml
   - 生成 robots.txt
   - 生成规范化链接 (canonical URLs)

## 部署注意事项

1. **静态生成**

   - 使用 Next.js 的静态导出功能生成静态站点
   - 配置增量静态再生成 (ISR)，定期更新内容

2. **图片优化**

   - 使用 Next.js 的图片组件优化图片
   - 实现响应式图片和延迟加载

3. **性能优化**
   - 利用 CDN 分发内容
   - 设置适当的缓存策略

## 数据填充

为了测试和开发，可以创建一些示例博客文章，包括不同的分类和标签，以验证 SEO 功能是否正常工作。
