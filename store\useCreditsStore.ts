import { create } from 'zustand';
import { creditsApi, CreditTransaction } from '@/lib/api/credits';

interface CreditsState {
    // 状态
    balance: number;
    transactions: CreditTransaction[];
    totalTransactions: number;
    isLoading: boolean;
    isLoadingTransactions: boolean;
    error: string | null;
    hasLoadedBalance: boolean;     // 新增：标记是否已加载余额
    hasLoadedTransactions: boolean; // 新增：标记是否已加载交易记录

    // 新用户奖励相关状态
    canClaimBonus: boolean;
    isCheckingClaimStatus: boolean;
    isClaimingBonus: boolean;
    hasCheckedClaimStatus: boolean;

    // 操作
    fetchBalance: () => Promise<number | null>;
    fetchTransactions: (params?: {
        type?: string;
        status?: string;
        page?: number | string;
        limit?: number | string;
        fromDate?: string;
        toDate?: string;
    }, forceReload?: boolean) => Promise<void>; // 新增forceReload参数
    purchaseCredits: (amount: number) => Promise<string | null>;

    // 新用户奖励操作
    checkClaimStatus: () => Promise<boolean>;
    claimBonus: (deviceFingerprint: any) => Promise<{ success: boolean; amount?: number; reason?: string }>;

    resetState: () => void;
}

const useCreditsStore = create<CreditsState>((set, get) => ({
    // 初始状态
    balance: 0,
    transactions: [],
    totalTransactions: 0,
    isLoading: false,
    isLoadingTransactions: false,
    error: null,
    hasLoadedBalance: false,
    hasLoadedTransactions: false,

    // 新用户奖励初始状态
    canClaimBonus: false,
    isCheckingClaimStatus: false,
    isClaimingBonus: false,
    hasCheckedClaimStatus: false,

    // 获取用户积分余额
    fetchBalance: async () => {
        try {
            // 检查用户是否已登录
            const { createClient } = await import('@/lib/supabase/client');
            const supabase = createClient();
            const { data: { session } } = await supabase.auth.getSession();

            // 如果没有会话，则直接返回null
            if (!session || !session.user) {
                return null;
            }

            // 如果已经加载过余额且不是初始加载，则跳过
            if (get().hasLoadedBalance && !get().isLoading) {
                return get().balance;
            }

            set({ isLoading: true, error: null });
            const response = await creditsApi.getUserBalance();
            set({
                balance: response.data.balance,
                isLoading: false,
                hasLoadedBalance: true  // 标记为已加载
            });
            return response.data.balance;
        } catch (error) {
            console.error('Failed to fetch credits balance:', error);
            set({
                error: error instanceof Error ? error.message : 'Failed to fetch credits balance',
                isLoading: false
            });
            return null;
        }
    },

    // 获取交易记录
    fetchTransactions: async (params = { page: 1, limit: 10 }, forceReload = false) => {
        try {
            // 如果正在加载中且不是强制刷新，则跳过
            if (get().isLoadingTransactions && !forceReload) {
                return;
            }

            // 如果页码为1，且已经加载过记录，且不是强制刷新，则跳过
            if (params.page === 1 && get().hasLoadedTransactions && !forceReload) {
                return;
            }

            set({ isLoadingTransactions: true, error: null });

            // 将数字转换为字符串
            const stringParams: Record<string, string> = {};
            if (params) {
                Object.entries(params).forEach(([key, value]) => {
                    if (value !== undefined) {
                        stringParams[key] = String(value);
                    }
                });
            }

            const response = await creditsApi.getTransactions(params);

            // 如果是第一页或强制刷新，直接替换整个列表
            if (params.page === 1 || forceReload) {
                set({
                    transactions: response.data.data,
                    totalTransactions: response.data.total,
                    isLoadingTransactions: false,
                    hasLoadedTransactions: true // 标记为已加载
                });
            } else {
                // 如果是加载更多，则将新数据追加到现有数据后面
                set({
                    transactions: [...get().transactions, ...response.data.data],
                    totalTransactions: response.data.total,
                    isLoadingTransactions: false,
                    hasLoadedTransactions: true
                });
            }
        } catch (error) {
            console.error('Failed to fetch credit transactions:', error);
            set({
                error: error instanceof Error ? error.message : 'Failed to fetch credit transactions',
                isLoadingTransactions: false
            });
        }
    },

    // 购买积分
    purchaseCredits: async (amount: number) => {
        try {
            set({ isLoading: true, error: null });
            const response = await creditsApi.purchaseCredits(amount);
            set({ isLoading: false });
            return response.data.checkoutUrl;
        } catch (error) {
            console.error('Failed to purchase credits:', error);
            set({
                error: error instanceof Error ? error.message : 'Failed to purchase credits',
                isLoading: false
            });
            return null;
        }
    },

    // 检查新用户奖励领取状态
    checkClaimStatus: async () => {
        const state = get();

        // 如果正在检查或已经检查过，直接返回当前状态
        if (state.isCheckingClaimStatus || state.hasCheckedClaimStatus) {
            console.log('[Credits] Skipping claim status check - already checking or checked');
            return state.canClaimBonus;
        }

        try {
            console.log('[Credits] Starting claim status check');
            set({ isCheckingClaimStatus: true, error: null });
            const response = await creditsApi.checkClaimStatus();
            const canClaim = response.code === 200 && response.data.canClaim && !response.data.alreadyClaimed;
            console.log('[Credits] Claim status check result:', { canClaim, response: response.data });
            set({
                canClaimBonus: canClaim,
                isCheckingClaimStatus: false,
                hasCheckedClaimStatus: true
            });
            return canClaim;
        } catch (error) {
            console.error('Failed to check claim status:', error);
            set({
                error: error instanceof Error ? error.message : 'Failed to check claim status',
                isCheckingClaimStatus: false,
                hasCheckedClaimStatus: true,
                canClaimBonus: false
            });
            return false;
        }
    },

    // 领取新用户奖励
    claimBonus: async (deviceFingerprint: any) => {
        try {
            set({ isClaimingBonus: true, error: null });
            const response = await creditsApi.claimBonus(deviceFingerprint);

            // 根据实际后端返回格式：response.data.success 和 response.data.newBalance
            if (response.data.success) {
                // 领取成功，更新状态并刷新余额
                set({
                    canClaimBonus: false,
                    isClaimingBonus: false
                });

                // 刷新余额
                const { fetchBalance } = get();
                await fetchBalance();

                return {
                    success: true,
                    amount: response.data.newBalance,
                    reason: 'Credits claimed successfully'
                };
            } else {
                set({
                    canClaimBonus: false,
                    isClaimingBonus: false
                });
                return {
                    success: false,
                    reason: response.data.reason || 'Failed to claim credits'
                };
            }
        } catch (error) {
            console.error('Failed to claim bonus:', error);
            set({
                error: error instanceof Error ? error.message : 'Failed to claim bonus',
                isClaimingBonus: false
            });
            return {
                success: false,
                reason: error instanceof Error ? error.message : 'Failed to claim bonus'
            };
        }
    },

    // 重置状态
    resetState: () => {
        set({
            balance: 0,
            transactions: [],
            totalTransactions: 0,
            isLoading: false,
            isLoadingTransactions: false,
            error: null,
            hasLoadedBalance: false,
            hasLoadedTransactions: false,
            canClaimBonus: false,
            isCheckingClaimStatus: false,
            isClaimingBonus: false,
            hasCheckedClaimStatus: false
        });
    }
}));

export default useCreditsStore;