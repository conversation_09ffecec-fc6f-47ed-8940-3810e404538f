"use client";

import { useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import useAuthStore from "@/store/useAuthStore";
import useCreditsStore from "@/store/useCreditsStore";
import useMembershipStore from "@/store/useMembershipStore";
import useCouponStore from "@/store/useCouponStore";

/**
 * 认证监听器组件
 * 专门负责监听Supabase认证状态变化，并更新全局认证状态
 * 不渲染任何UI元素，仅处理逻辑
 */
export function AuthListener() {
    const { setAuthState, isAuthenticated } = useAuthStore();
    const resetCredits = useCreditsStore((state) => state.resetState);
    const resetMembership = useMembershipStore((state) => state.resetMembershipState);
    const resetCoupon = useCouponStore((state) => state.resetCouponState);

    // 设置Supabase认证状态监听
    useEffect(() => {
        const supabase = createClient();
        let isInitialized = false;

        // 监听认证状态变化
        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
            console.log(`[AuthListener] Auth state change: ${event}`);

            // 如果是初始化事件且还没有初始化过，或者是其他认证事件
            if (!isInitialized || event !== 'INITIAL_SESSION') {
                setAuthState(session?.user || null, session || null);
                isInitialized = true;
            }
        });

        return () => {
            subscription.unsubscribe();
        };
    }, [setAuthState]);

    // 当认证状态变为未认证时，重置其他状态存储
    useEffect(() => {
        if (!isAuthenticated) {
            resetCredits();
            resetMembership();
            resetCoupon();
        }
    }, [isAuthenticated, resetCredits, resetMembership, resetCoupon]);

    // 定期检查认证状态
    useEffect(() => {
        const checkAuthInterval = setInterval(() => {
            useAuthStore.getState().checkAuthStatus();
        }, 10 * 60 * 1000); // 每10分钟检查一次

        return () => clearInterval(checkAuthInterval);
    }, []);

    // 不渲染任何内容
    return null;
}