# Generation 模块

## 概述

Generation 模块是 ReelMind 服务的核心功能之一，提供视频生成和任务管理功能。此模块允许用户创建视频生成任务、查询任务状态、取消任务，并支持队列信息查询。

## API 端点

### 1. 创建视频生成任务

- **URL**: `/generation/gen-video`
- **方法**: `POST`
- **认证**: JWT
- **请求体**: `GenVideoRequestDto`
- **响应**: `VideoTaskResponseDto`

示例请求:

```json
{
  "gen_type": "text2video",
  "prompt": "一只猫在沙滩上奔跑",
  "negative_prompt": "模糊，低质量",
  "guidance_scale": 7.5,
  "steps": 30,
  "seed": 42,
  "definition": "720P",
  "duration": "5s",
  "ratio": "16:9"
}
```

### 2. 获取用户任务列表

- **URL**: `/generation/user-tasks`
- **方法**: `GET`
- **认证**: JWT
- **参数**:
  - `status`: 可选，根据状态筛选任务
  - `limit`: 可选，分页限制
  - `offset`: 可选，分页偏移
- **响应**: 任务列表和总数

### 3. 获取任务详情

- **URL**: `/generation/task/:taskId`
- **方法**: `GET`
- **认证**: JWT
- **参数**: `taskId`
- **响应**: `VideoTaskDto`

### 4. 取消任务

- **URL**: `/generation/task/cancel`
- **方法**: `POST`
- **认证**: JWT
- **请求体**: `CancelTaskDto`
- **响应**: 更新后的任务

### 5. 获取任务队列信息（GET方式）

- **URL**: `/generation/task/:taskId/queue-info`
- **方法**: `GET`
- **认证**: JWT
- **参数**: `taskId`
- **响应**: `QueueInfoResponseDto`

此接口提供任务在队列中的位置、总队列长度、预计等待时间和预计完成时间。

### 6. 获取任务队列信息（POST方式）

- **URL**: `/generation/task/queue-info`
- **方法**: `POST`
- **认证**: JWT
- **请求体**: `QueueInfoRequestDto`
- **响应**: `QueueInfoResponseDto`

示例请求:

```json
{
  "task_id": "123e4567-e89b-12d3-a456-426614174000"
}
```

示例响应:

```json
{
  "task_id": "123e4567-e89b-12d3-a456-426614174000",
  "queue_position": 3,
  "total_tasks_in_queue": 10,
  "estimated_wait_time_seconds": 720,
  "estimated_start_time": "2023-05-01T12:30:00Z",
  "estimated_completion_time": "2023-05-01T12:35:00Z"
}
```

## 数据结构

### QueueInfoResponseDto

队列信息响应对象，包含以下字段：

- `task_id`: 任务ID
- `queue_position`: 任务在队列中的位置
- `total_tasks_in_queue`: 队列中的总任务数
- `estimated_wait_time_seconds`: 预计等待时间（秒）
- `estimated_start_time`: 预计开始时间
- `estimated_completion_time`: 预计完成时间

## 数据库函数

### get_task_queue_info

此存储过程用于高效获取任务在队列中的位置和相关信息。

参数:

- `p_task_id`: 任务ID

返回:

- `task_id`: 任务ID
- `queue_position`: 队列位置
- `total_tasks_in_queue`: 队列总长度
- `task_priority`: 任务优先级
- `ahead_tasks_json`: 前面任务的JSON数据
- `avg_processing_time_seconds`: 平均处理时间（秒）

## 注意事项

1. 队列位置和预计时间是基于当前任务状态的估计，实际处理时间可能因系统负载和任务复杂度而变化。
2. 任务优先级基于用户的会员等级，高级会员的任务将获得更高的优先级。
3. 只有处于 `pending` 或 `queued` 状态的任务才能查询队列信息。

## 关于内部模型和外部模型

### 区分标志

models表里有一个字段source，如果是fal.ai，则表明是外部的模型。

### 处理策略

1. generation controller收到/gen-video用户的请求;
2. generation service处理请求，首先判断用户是否有足够的积分来调用，如果积分不够，中止流程，返回错误；
3. 执行扣款逻辑，如果扣款失败，中止流程，返回错误；
4. 根据source判断是内部模型还是fal.ai模型；
5. 如果是内部模型，创建video_gen_tasks记录，handler为NULL，返回任务id，等待生成服务调用gen_video_task/pop接口处理，生成服务调用gen_video_task/finish接口更新任务状态；
6. 如果是fal.ai模型，创建video_gen_tasks记录，handler为fal.ai，调用fal.ai的API开始生成任务，返回任务id，等待fal.ai的webhook回调更新任务状态；
