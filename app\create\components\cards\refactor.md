请你重构 history-card： 1.分别实现四种状态类型的 card 组件：pending\processing\completed\failed，history-card.tsx 只负责根据状态渲染对应的卡片组件；
2.pending 类型的卡片，没有进度条，参照现在的样式，每 10 秒查询一次状态更新，如果状态变更为 processing，则渲染 processing 类型的卡片；
3.processing 类型的卡片，有进度条，参照现在的样式。因为后端目前暂不支持进度查询，所以需要前端根据任务创建时间来估算进度，一般按照 model.metadata.duration_estimate（如果这个字段为空则默认 3 分钟）时间来定。每 10 秒查询一次状态更新，如果状态变更为 completed，则渲染 completed 类型的卡片，如果状态变更为 failed，则渲染 failed 类型的卡片；
4.completed 类型的卡片，参照现在的样式，不再查询状态更新；
5.failed 类型的卡片，参照现在的样式，不再查询状态更新；
