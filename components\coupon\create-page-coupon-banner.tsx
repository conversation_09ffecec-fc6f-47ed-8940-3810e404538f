"use client"
import { <PERSON>, <PERSON>, <PERSON>rk<PERSON>, Zap } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { useCouponLogic } from "@/hooks/useCouponLogic"
import { useRouter } from "next/navigation"
import useLoginDialogStore from "@/store/useLoginDialogStore"

interface CreatePageCouponBannerProps {
    className?: string;
}

export function CreatePageCouponBanner({ className }: CreatePageCouponBannerProps) {
    const router = useRouter()
    const { openLoginDialog } = useLoginDialogStore()

    const {
        timeLeft,
        activeCoupon,
        isLoading,
        isAuthenticated,
        canClaim,
        handleClaimCoupon
    } = useCouponLogic()

    // Handle click to membership page when coupon is active
    const handleMembershipRedirect = () => {
        if (!isAuthenticated) {
            openLoginDialog({
                pendingAction: {
                    type: 'membership_redirect',
                    payload: {},
                    onSuccess: () => {
                        router.push('/membership')
                    }
                }
            })
            return
        }
        router.push('/membership')
    }

    // Check if should show
    // For authenticated users: show if eligible and not dismissed
    // For unauthenticated users: always show if not dismissed (to encourage login)
    const shouldShow = isAuthenticated ? canClaim : true;

    // Don't show if conditions not met
    if (!shouldShow && !activeCoupon) return null

    // Active coupon display - clickable to go to membership page
    if (activeCoupon) {
        return (
            <div className={cn("relative overflow-hidden mb-4 cursor-pointer", className)} onClick={handleMembershipRedirect}>
                <div className="relative bg-gradient-to-r from-emerald-500/10 via-cyan-500/10 to-blue-500/10 border border-emerald-500/20 rounded-xl p-4 backdrop-blur-sm hover:border-emerald-500/30 transition-all duration-300">
                    {/* Animated background */}
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-cyan-500/5 to-blue-500/5 animate-pulse rounded-xl" />
                    <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-emerald-500/50 to-transparent" />

                    <div className="relative z-10">
                        <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center">
                                    <Sparkles className="w-4 h-4 text-emerald-400" />
                                </div>
                                <div>
                                    <span className="text-sm font-semibold text-emerald-400">Coupon Active</span>
                                    <Badge variant="secondary" className="ml-2 text-xs bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
                                        {activeCoupon.discount_percentage}% OFF
                                    </Badge>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center justify-between">
                            <p className="text-sm text-muted-foreground">
                                Click to use your first month membership discount
                            </p>
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <Clock className="w-4 h-4" />
                                <span>{timeLeft}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    // Claim coupon display
    return (
        <div className={cn("relative overflow-hidden mb-4", className)}>
            <div className="relative bg-gradient-to-r from-orange-500/10 via-pink-500/10 to-purple-500/10 border border-orange-500/20 rounded-xl p-4 backdrop-blur-sm hover:border-orange-500/30 transition-all duration-300">
                {/* Animated background effects */}
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 via-pink-500/5 to-purple-500/5 animate-pulse rounded-xl" />
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/50 to-transparent" />
                <div className="absolute bottom-0 right-0 w-20 h-20 bg-gradient-to-tl from-orange-500/10 to-transparent rounded-full blur-xl" />

                <div className="relative z-10">
                    <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-orange-500/20 to-pink-500/20 rounded-lg flex items-center justify-center">
                                <Gift className="w-4 h-4 text-orange-400" />
                            </div>
                            <div>
                                <div className="flex items-center gap-2">
                                    <span className="text-sm font-semibold">🎉 First Month 90% OFF</span>
                                    <Badge variant="secondary" className="text-xs bg-orange-500/20 text-orange-400 border-orange-500/30">
                                        Limited Time
                                    </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    Exclusive discount for new users
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="w-3 h-3" />
                            <span>Valid for 24 hours</span>
                        </div>
                        <Button
                            onClick={handleClaimCoupon}
                            disabled={isLoading}
                            size="sm"
                            className="bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white border-0 text-sm px-4 h-8"
                        >
                            <Zap className="mr-1 w-3 h-3" />
                            Claim Now
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}
