import { Injectable, Inject, NotFoundException, BadRequestException } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import { ErrorAuditService, ErrorSeverity } from '../common/services/error-audit.service';

export interface VideoDto {
    id: string;
    user_id: string;
    created_at: Date;
    prompt?: string;
    url: string;
    task_id?: string;
    input_params?: any;
    source?: string;
}

export interface CreateVideoDto {
    user_id: string;
    prompt?: string;
    url: string;
    task_id?: string;
    input_params?: any;
    source?: string;
}

export interface VideoQueryParams {
    userId?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

@Injectable()
export class VideoService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
        private readonly errorAuditService: ErrorAuditService,
    ) {
        this.logger.setContext(VideoService.name);
    }

    /**
     * 创建新视频记录
     * @param createVideoDto 视频创建参数
     * @returns 创建的视频记录
     */
    async createVideo(createVideoDto: CreateVideoDto): Promise<VideoDto> {
        try {
            const { data, error } = await this.supabase
                .from('videos')
                .insert({
                    ...createVideoDto,
                    created_at: new Date(),
                })
                .select('*')
                .single();

            if (error) {
                this.logger.error(`创建视频记录失败: ${error.message}`, error);

                // 记录到错误审计系统
                await this.errorAuditService.logTaskError(
                    createVideoDto.user_id,
                    createVideoDto.task_id || '',
                    `创建视频记录失败: ${error.message}`,
                    error,
                    {
                        createVideoDto,
                        operation: 'createVideo'
                    },
                    ErrorSeverity.HIGH
                );

                throw new BadRequestException('创建视频记录失败');
            }

            this.logger.log(`成功创建视频记录，ID: ${data.id}`);
            return data as VideoDto;
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }

            this.logger.error(`创建视频记录时发生异常: ${error.message}`, error);
            throw new BadRequestException('创建视频记录失败');
        }
    }

    /**
     * 根据ID获取视频
     * @param videoId 视频ID
     * @returns 视频记录
     */
    async getVideoById(videoId: string): Promise<VideoDto> {
        try {
            const { data, error } = await this.supabase
                .from('videos')
                .select('*')
                .eq('id', videoId)
                .single();

            if (error || !data) {
                this.logger.error(`获取视频记录失败: ${error?.message}`, error);
                throw new NotFoundException('视频不存在');
            }

            return data as VideoDto;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }

            this.logger.error(`获取视频记录时发生异常: ${error.message}`, error);
            throw new BadRequestException('获取视频记录失败');
        }
    }

    /**
     * 获取用户的视频列表
     * @param queryParams 查询参数
     * @returns 视频列表及总数
     */
    async getVideos(queryParams: VideoQueryParams): Promise<{ videos: VideoDto[]; total: number }> {
        try {
            const {
                userId,
                limit = 10,
                offset = 0,
                sortBy = 'created_at',
                sortOrder = 'desc',
            } = queryParams;

            let query = this.supabase
                .from('videos')
                .select('*', { count: 'exact' });

            if (userId) {
                query = query.eq('user_id', userId);
            }

            const { data, error, count } = await query
                .order(sortBy, { ascending: sortOrder === 'asc' })
                .range(offset, offset + limit - 1);

            if (error) {
                this.logger.error(`获取视频列表失败: ${error.message}`, error);
                throw new BadRequestException('获取视频列表失败');
            }

            return {
                videos: data as VideoDto[],
                total: count || 0,
            };
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }

            this.logger.error(`获取视频列表时发生异常: ${error.message}`, error);
            throw new BadRequestException('获取视频列表失败');
        }
    }

    /**
     * 删除视频
     * @param videoId 视频ID
     * @param userId 用户ID（用于权限验证）
     * @returns 是否成功
     */
    async deleteVideo(videoId: string, userId: string): Promise<boolean> {
        try {
            // 检查视频是否存在并属于该用户
            const { data: existingVideo, error: checkError } = await this.supabase
                .from('videos')
                .select('*')
                .eq('id', videoId)
                .eq('user_id', userId)
                .single();

            if (checkError || !existingVideo) {
                this.logger.error(`视频不存在或无权限: ${videoId}`, checkError);
                throw new NotFoundException('视频不存在或无权限');
            }

            // 删除视频
            const { error } = await this.supabase
                .from('videos')
                .delete()
                .eq('id', videoId);

            if (error) {
                this.logger.error(`删除视频失败: ${error.message}`, error);

                // 记录到错误审计系统
                await this.errorAuditService.logTaskError(
                    userId,
                    existingVideo.task_id || '',
                    `删除视频失败: ${error.message}`,
                    error,
                    {
                        videoId,
                        operation: 'deleteVideo'
                    },
                    ErrorSeverity.MEDIUM
                );

                throw new BadRequestException('删除视频失败');
            }

            this.logger.log(`成功删除视频，ID: ${videoId}`);
            return true;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof BadRequestException) {
                throw error;
            }

            this.logger.error(`删除视频时发生异常: ${error.message}`, error);
            throw new BadRequestException('删除视频失败');
        }
    }

    /**
     * 根据任务ID获取视频
     * @param taskId 任务ID
     * @returns 视频记录
     */
    async getVideoByTaskId(taskId: string): Promise<VideoDto | null> {
        try {
            const { data, error } = await this.supabase
                .from('videos')
                .select('*')
                .eq('task_id', taskId)
                .single();

            if (error) {
                if (error.code === 'PGRST116') { // 没有找到记录
                    return null;
                }

                this.logger.error(`根据任务ID获取视频失败: ${error.message}`, error);
                throw new BadRequestException('获取视频记录失败');
            }

            return data as VideoDto;
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }

            this.logger.error(`根据任务ID获取视频时发生异常: ${error.message}`, error);
            throw new BadRequestException('获取视频记录失败');
        }
    }
} 