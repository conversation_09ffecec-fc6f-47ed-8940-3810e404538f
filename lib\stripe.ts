import { loadStripe } from '@stripe/stripe-js';

// 根据环境变量加载Stripe实例
let stripePromise: ReturnType<typeof loadStripe> | null = null;

// 获取应用基础URL，用于构建完整的回调URL
const getBaseUrl = () => {
    // 优先使用环境变量中配置的API URL
    if (process.env.NEXT_PUBLIC_APP_URL) {
        return process.env.NEXT_PUBLIC_APP_URL;
    }

    // 否则检测当前运行环境
    if (typeof window !== 'undefined') {
        // 确保URL包含协议部分
        const origin = window.location.origin;
        return origin;
    }

    // 默认使用生产环境URL
    return 'https://reelmind.ai';
};

/**
 * 获取Stripe实例
 */
export const getStripe = () => {
    if (!stripePromise) {
        const key = process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY;
        if (!key) {
            throw new Error('缺少NEXT_PUBLIC_STRIPE_PUBLIC_KEY');
        }
        stripePromise = loadStripe(key);
    }
    return stripePromise;
};

/**
 * 生成支付成功URL
 */
export const generateSuccessUrl = (planId: string, months: number) => {
    const baseUrl = getBaseUrl();
    // 确保完整URL格式
    return `${baseUrl}/payment/success?plan_id=${planId}&months=${months}`;
};

/**
 * 生成支付取消URL
 */
export const generateCancelUrl = () => {
    const baseUrl = getBaseUrl();
    // 确保完整URL格式
    return `${baseUrl}/payment/cancel`;
}; 