# 会员首月订阅7折促销功能

## 功能概述

本功能实现了一个完整的会员首月订阅7折促销系统，包括：

- 全局优惠券提示系统
- 优惠券有效期管理（24小时）
- 会员页面集成
- 折扣应用规则
- 用户资格检查

## 核心特性

### 1. 用户资格检查
- 仅限从未购买过会员的新用户
- 每个用户只能领取一次首月折扣优惠券
- 自动检查用户的会员购买历史

### 2. 优惠券管理
- 优惠券领取后有效期为24小时
- 自动过期处理
- 状态跟踪（活跃/已使用/已过期）

### 3. 价格计算
- 7折优惠（30% OFF）
- 仅适用于首月订阅
- 原价和折扣价清晰展示

### 4. UI/UX设计
- 全局非侵入式横幅提示
- 会员页面优惠券状态卡片
- Dark模式适配
- 响应式设计

## 技术架构

### 前端组件

#### 1. 全局优惠券横幅 (`GlobalCouponBanner`)
```typescript
// 位置: components/coupon/global-coupon-banner.tsx
// 功能: 在网站顶部显示优惠券领取提示
// 特性: 
// - 自动检查用户资格
// - 可手动关闭
// - 倒计时显示
```

#### 2. 优惠券状态卡片 (`CouponStatusCard`)
```typescript
// 位置: app/membership/components/coupon-status-card.tsx
// 功能: 在会员页面显示优惠券状态
// 特性:
// - 实时状态更新
// - 倒计时显示
// - 领取按钮
```

#### 3. 价格显示组件 (`CouponPriceDisplay`)
```typescript
// 位置: app/membership/components/coupon-price-display.tsx
// 功能: 显示应用优惠券后的价格
// 特性:
// - 原价和折扣价对比
// - 折扣详情展示
// - 说明文字
```

### 状态管理

#### 优惠券Store (`useCouponStore`)
```typescript
// 位置: store/useCouponStore.ts
// 功能: 管理优惠券相关状态
// 包含:
// - 优惠券列表
// - 活跃优惠券
// - 资格检查
// - 价格计算
```

### 后端服务

#### 1. 数据库表结构
```sql
-- 用户优惠券表
user_coupons (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    coupon_type VARCHAR(50) DEFAULT 'first_month_discount',
    discount_percentage INTEGER DEFAULT 30,
    status VARCHAR(20) DEFAULT 'active',
    claimed_at TIMESTAMP,
    expires_at TIMESTAMP,
    used_at TIMESTAMP,
    payment_id UUID
)
```

#### 2. API端点
```
GET  /api/coupon/status              - 获取优惠券状态
POST /api/coupon/claim               - 领取优惠券
GET  /api/coupon/user-coupons        - 获取用户优惠券列表
POST /api/coupon/calculate-price     - 计算折扣价格
GET  /api/coupon/check-eligibility   - 检查领取资格
```

#### 3. 服务类
```typescript
// CouponService - 核心业务逻辑
// - 优惠券创建和管理
// - 资格检查
// - 价格计算
// - 过期处理
```

## 使用流程

### 1. 用户访问网站
1. 系统检查用户是否已登录
2. 检查用户是否有资格领取优惠券
3. 如果符合条件，显示全局横幅提示

### 2. 领取优惠券
1. 用户点击"立即领取"按钮
2. 系统验证用户资格
3. 创建优惠券记录
4. 设置24小时过期时间
5. 更新UI状态

### 3. 查看优惠券状态
1. 在会员页面显示优惠券状态卡片
2. 显示剩余时间倒计时
3. 提供优惠券详细信息

### 4. 应用优惠券
1. 在价格显示组件中自动应用折扣
2. 显示原价和折扣价对比
3. 突出显示优惠幅度

## 配置说明

### 环境变量
```env
REELAPI_SERVER_URL=your_backend_api_url
```

### 优惠券配置
```typescript
// 折扣百分比（可在数据库中配置）
const DISCOUNT_PERCENTAGE = 30; // 30% = 7折

// 有效期（24小时）
const VALIDITY_HOURS = 24;

// 优惠券类型
const COUPON_TYPE = 'first_month_discount';
```

## 部署注意事项

### 1. 数据库迁移
```bash
# 执行SQL脚本创建表结构
psql -d your_database -f sql/coupons/user_coupons.sql
```

### 2. 后端模块注册
```typescript
// 在主模块中导入CouponModule
@Module({
  imports: [
    // ... 其他模块
    CouponModule,
  ],
})
export class AppModule {}
```

### 3. 前端依赖
```bash
# 安装必要的依赖
npm install zustand @tanstack/react-query lucide-react sonner
```

## 监控和维护

### 1. 定期清理过期优惠券
```sql
-- 可以设置定时任务执行
UPDATE user_coupons 
SET status = 'expired', updated_at = NOW()
WHERE status = 'active' AND expires_at < NOW();
```

### 2. 监控指标
- 优惠券领取率
- 优惠券使用率
- 转化率（领取到购买）
- 过期率

### 3. 日志记录
- 优惠券领取事件
- 优惠券使用事件
- 错误和异常情况

## 扩展功能

### 1. 多种优惠券类型
- 可以扩展支持不同类型的优惠券
- 不同的折扣规则
- 不同的有效期

### 2. 优惠券分享
- 推荐奖励机制
- 社交分享功能

### 3. 高级规则引擎
- 复杂的资格检查规则
- 动态折扣计算
- A/B测试支持

## 故障排除

### 常见问题

1. **优惠券不显示**
   - 检查用户登录状态
   - 验证用户资格
   - 检查API连接

2. **价格计算错误**
   - 验证优惠券状态
   - 检查过期时间
   - 确认折扣百分比

3. **横幅不消失**
   - 清除localStorage
   - 检查状态同步
   - 验证组件逻辑

### 调试工具
- 浏览器开发者工具
- React DevTools
- Zustand DevTools
- 网络请求监控
