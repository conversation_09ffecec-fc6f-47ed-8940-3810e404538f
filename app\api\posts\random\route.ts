import { NextRequest, NextResponse } from 'next/server';
import { postApi } from '@/lib/api';
import { postToContentItem } from '@/lib/api/post';

/**
 * 随机获取指定数量的帖子
 * 由于接口不支持随机获取，使用获取较多帖子后随机抽取的方式实现
 */
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const limit = parseInt(searchParams.get('limit') || '4');

        // 获取一批帖子（数量更多，提高随机性）
        const response = await postApi.getFeed({ limit: 50, offset: 0 });

        if (!response.data || !response.data.posts || response.data.posts.length === 0) {
            return NextResponse.json({ posts: [] });
        }

        // 将帖子随机排序并选取前limit个
        const shuffled = [...response.data.posts]
            .sort(() => 0.5 - Math.random())
            .slice(0, Math.min(limit, response.data.posts.length));

        // 转换为前端使用的格式
        const postItems = shuffled.map(post => postToContentItem(post));

        return NextResponse.json({ posts: postItems });
    } catch (error) {
        console.error('Error fetching random posts:', error);
        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    }
} 