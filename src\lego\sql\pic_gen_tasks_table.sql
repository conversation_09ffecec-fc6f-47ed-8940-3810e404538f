-- 创建图片生成任务表
CREATE TABLE IF NOT EXISTS public.pic_gen_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    progress INTEGER NOT NULL DEFAULT 0,
    input_params JSONB NOT NULL DEFAULT '{}'::jsonb,
    output_result JSONB DEFAULT NULL,
    storage_path TEXT DEFAULT NULL,
    error_log JSONB DEFAULT NULL,
    priority INTEGER NOT NULL DEFAULT 1,
    model VARCHAR(50) DEFAULT 'UNO',
    request_id text null,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    queued_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS pic_gen_tasks_user_id_idx ON public.pic_gen_tasks(user_id);
CREATE INDEX IF NOT EXISTS pic_gen_tasks_status_idx ON public.pic_gen_tasks(status);
CREATE INDEX IF NOT EXISTS pic_gen_tasks_priority_idx ON public.pic_gen_tasks(priority);
CREATE INDEX IF NOT EXISTS pic_gen_tasks_created_at_idx ON public.pic_gen_tasks(created_at);

-- 添加外键约束
ALTER TABLE public.pic_gen_tasks 
    ADD CONSTRAINT fk_pic_gen_tasks_user_id 
    FOREIGN KEY (user_id) 
    REFERENCES auth.users(id)
    ON DELETE CASCADE;

-- 添加权限
ALTER TABLE public.pic_gen_tasks ENABLE ROW LEVEL SECURITY;

-- 创建策略
CREATE POLICY "Users can view their own pic tasks"
    ON public.pic_gen_tasks
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all pic tasks"
    ON public.pic_gen_tasks
    FOR SELECT
    TO authenticated
    USING (auth.jwt() ->> 'app_role' = 'admin');

CREATE POLICY "Service role can do everything"
    ON public.pic_gen_tasks
    FOR ALL
    TO service_role
    USING (true); 