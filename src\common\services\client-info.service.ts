import { Injectable, Inject } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../providers/supabase.provider';
import { ClientInfo } from '../interfaces/client-info.interface';
import { CustomLogger } from './logger.service';
import * as geoip from 'geoip-lite';

@Injectable()
export class ClientInfoService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {
        this.logger.setContext('ClientInfoService');
    }

    /**
     * 丰富客户端信息，添加地理位置等额外数据
     * @param clientInfo 基本客户端信息
     * @returns 丰富后的客户端信息
     */
    async enrichClientInfo(clientInfo: ClientInfo): Promise<ClientInfo> {
        try {
            // 添加地理位置信息
            if (clientInfo.ip && clientInfo.ip !== '0.0.0.0' && clientInfo.ip !== '127.0.0.1') {
                try {
                    const geo = geoip.lookup(clientInfo.ip);
                    if (geo) {
                        clientInfo.location = {
                            country: geo.country,
                            region: geo.region,
                            city: geo.city,
                            timezone: geo.timezone,
                        };
                    }
                } catch (err) {
                    this.logger.error(`获取地理位置信息失败: ${clientInfo.ip}`, err);
                }
            }

            // 可以在这里添加更多数据丰富逻辑
            // 例如从IP信息服务查询ISP信息等

            return clientInfo;
        } catch (error) {
            this.logger.error('丰富客户端信息失败', error);
            return clientInfo; // 返回原始信息，不阻止流程
        }
    }

    /**
     * 存储客户端信息
     * 可以用于后续的分析和审计
     * @param clientInfo 客户端信息
     * @param userId 用户ID（如果有）
     * @param actionType 操作类型
     */
    async storeClientInfo(clientInfo: ClientInfo, userId?: string, actionType?: string): Promise<void> {
        try {
            // 将客户端信息存储到数据库中
            const { error } = await this.supabase
                .from('client_access_logs')
                .insert({
                    request_id: clientInfo.requestId,
                    user_id: userId,
                    ip_address: clientInfo.ip,
                    user_agent: clientInfo.userAgent,
                    device_info: clientInfo.device,
                    location_info: clientInfo.location,
                    action_type: actionType,
                    session_data: clientInfo.session,
                    extra_data: clientInfo.extraData,
                    created_at: new Date(),
                });

            if (error) {
                this.logger.error('存储客户端信息失败', error);
            }
        } catch (error) {
            this.logger.error('存储客户端信息出错', error);
        }
    }

    /**
     * 风控检查
     * 检查客户端访问是否存在风险
     * @param clientInfo 客户端信息
     * @param userId 用户ID（如果有）
     * @returns 风险评分和风险原因
     */
    async riskCheck(clientInfo: ClientInfo, userId?: string): Promise<{ score: number; reasons: string[] }> {
        try {
            const reasons: string[] = [];
            let score = 0;

            // 检查IP是否在黑名单中
            const { data: ipBlacklist, error: ipError } = await this.supabase
                .from('ip_blacklist')
                .select('*')
                .eq('ip_address', clientInfo.ip)
                .single();

            if (!ipError && ipBlacklist) {
                score += 100;
                reasons.push('IP地址在黑名单中');
            }

            // 检查是否短时间内多次访问
            if (userId) {
                const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
                const { count, error: countError } = await this.supabase
                    .from('client_access_logs')
                    .select('*', { count: 'exact', head: true })
                    .eq('user_id', userId)
                    .gte('created_at', fiveMinutesAgo.toISOString());

                if (!countError && count && count > 50) {
                    score += 30;
                    reasons.push('短时间内请求频率过高');
                }
            }

            // 检查设备和位置异常
            if (userId) {
                // 获取用户最近的登录记录
                const { data: recentLogins, error: loginError } = await this.supabase
                    .from('client_access_logs')
                    .select('location_info, device_info')
                    .eq('user_id', userId)
                    .eq('action_type', 'login')
                    .order('created_at', { ascending: false })
                    .limit(5);

                if (!loginError && recentLogins && recentLogins.length > 0) {
                    // 检查是否存在位置突变
                    const uniqueCountries = new Set(recentLogins.map(log => log.location_info?.country).filter(Boolean));
                    if (uniqueCountries.size > 2 && clientInfo.location?.country) {
                        score += 20;
                        reasons.push('短时间内多国家登录');
                    }

                    // 检查设备变化
                    const uniqueDevices = new Set(recentLogins.map(log => log.device_info?.platform).filter(Boolean));
                    if (uniqueDevices.size > 2 && clientInfo.device?.platform) {
                        score += 10;
                        reasons.push('短时间内多设备登录');
                    }
                }
            }

            // 可以添加更多风控规则

            return { score, reasons };
        } catch (error) {
            this.logger.error('风控检查失败', error);
            return { score: 0, reasons: ['风控检查过程出错'] };
        }
    }
} 