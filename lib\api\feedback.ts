import { apiClient, ApiResponse } from './client';

// 反馈类型枚举 - 简化为3种
export enum FeedbackType {
    BUG = 'bug',
    SUGGESTION = 'suggestion',
    OTHER = 'other'
}

// 创建反馈请求参数 - 只要3个字段
export interface CreateFeedbackParams {
    type: FeedbackType;       // 反馈类型
    subject: string;          // 反馈主题
    message: string;          // 反馈内容
}

// 反馈响应数据 - 简化响应
export interface FeedbackResponse {
    id: string;
    type: FeedbackType;
    subject: string;
    message: string;
    created_at: string;
}

/**
 * 反馈API服务 - 超级简化版
 */
export const feedbackApi = {
    /**
     * 创建用户反馈 - 只需要3个字段
     * @param params 反馈参数
     * @returns 创建的反馈信息
     */
    createFeedback: async (params: CreateFeedbackParams): Promise<FeedbackResponse> => {
        const response = await apiClient.post<ApiResponse<FeedbackResponse>>(
            '/feedback',
            params
        );
        return response.data;
    },
};
