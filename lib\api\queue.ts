import { apiClient, ApiResponse } from './client';
import { API_CONFIG } from '@/lib/config';

/**
 * 队列信息响应DTO
 */
export interface QueueInfoResponse {
    task_id: string;
    queue_position: number;
    total_tasks_in_queue: number;
    estimated_wait_time_seconds?: number;
    estimated_start_time?: Date;
    estimated_completion_time?: Date;
    is_processing?: boolean;
    elapsed_time_seconds?: number;
    remaining_time_seconds?: number;
}

/**
 * 队列API服务
 */
export const queueApi = {
    /**
     * 获取队列信息
     * 从后端服务获取任务在队列中的位置和等待时间信息
     */
    getQueueInfo: async (taskId: string): Promise<QueueInfoResponse> => {
        try {
            const response = await apiClient.get<ApiResponse<QueueInfoResponse>>(
                `${API_CONFIG.ENDPOINTS.QUEUE.GET_QUEUE_INFO}/${taskId}`
            );

            // 直接返回data字段中的数据
            return response.data;
        } catch (error) {
            console.error('Failed to fetch queue info:', error);

            // 返回一个默认的队列信息以避免UI错误
            return {
                task_id: taskId,
                queue_position: 0,
                total_tasks_in_queue: 0,
                is_processing: false
            };
        }
    }
}; 