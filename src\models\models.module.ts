import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ModelsController } from './models.controller';
import { ModelsAdminController } from './models.admin.controller';
import { FalAiController } from './fal-ai.controller';
import { ModelsService } from './models.service';
import { CustomLogger } from '../common/services/logger.service';

@Module({
    controllers: [ModelsController, ModelsAdminController, FalAiController],
    providers: [ModelsService, CustomLogger],
    exports: [ModelsService],
})
export class ModelsModule {}