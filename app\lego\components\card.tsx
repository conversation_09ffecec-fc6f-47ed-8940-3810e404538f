"use client";

import { useState, useRef, useEffect } from "react";
import { Upload, Edit, Check, Trash2 } from "lucide-react";
import Image from "next/image";
import { uploadGenerationImage } from "@/lib/upload";
import { CardData, CardType, SectionType } from "../types";

// 卡片组件属性接口
interface CardProps {
  card: CardData;
  section: SectionType;
  onDelete: (id: string) => void;
  onToggleSelect: (id: string) => void;
  onUpdateContent: (id: string, type: CardType, content: string) => void;
  onStartUpload: (id: string) => void;
  onFinishUpload: (id: string, success: boolean, url?: string) => void;
  sectionIcon: React.ReactNode;
}

// 卡片组件
export const Card = ({
  card,
  section,
  onDelete,
  onToggleSelect,
  onUpdateContent,
  onStartUpload,
  onFinishUpload,
  sectionIcon
}: CardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [textContent, setTextContent] = useState(card.content);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 当卡片内容变化时，更新本地状态
  useEffect(() => {
    if (card.type === "text") {
      setTextContent(card.content);
    }
  }, [card.content, card.type]);

  // 处理文本输入变化
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setTextContent(newText);
    onUpdateContent(card.id, "text", newText);
  };

  // 处理图片上传
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // 标记为上传中
      onStartUpload(card.id);

      // 上传到Cloudflare
      const publicUrl = await uploadGenerationImage(file);

      // 上传成功，更新卡片内容
      onFinishUpload(card.id, true, publicUrl);
    } catch (error) {
      console.error("Failed to upload image:", error);
      onFinishUpload(card.id, false);
    } finally {
      // 清空文件输入
      if (e.target) {
        e.target.value = "";
      }
    }
  };

  // 触发文件选择
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 渲染文本卡片
  if (card.type === "text") {
    return (
      <div className={`relative w-full h-70 border ${card.selected ? 'border-indigo-300 border-1' : 'border-dashed border-[#222]/30'} rounded-md mb-3 transition-colors`}>
        {/* 删除按钮 */}
        <div className="absolute top-2 left-2 z-10">
          <button
            onClick={() => onDelete(card.id)}
            className="p-1 bg-black/40 rounded-full hover:bg-red-500/80 transition-colors"
            title="Delete"
          >
            <Trash2 size={14} className="text-gray-300 hover:text-white" />
          </button>
        </div>

        {/* 选择按钮 */}
        <div className="absolute top-2 right-2 z-10">
          <button
            onClick={() => onToggleSelect(card.id)}
            className={`p-1 rounded-full transition-colors ${card.selected ? 'bg-indigo-300' : 'bg-black/40 hover:bg-black/60'}`}
            title={card.selected ? "Selected" : "Select"}
          >
            <Check size={14} className="text-white" />
          </button>
        </div>

        {/* 文本输入框 */}
        <textarea
          placeholder={`Describe ${section.toLowerCase()}...`}
          className={`w-full h-full p-3 pt-9 resize-none border-none ${card.selected ? 'bg-indigo-300/10' : 'bg-[#1a1a1a]'} focus:outline-none rounded-md transition-colors`}
          value={textContent}
          onChange={handleTextChange}
          autoFocus={card.content === ""}
        />
      </div>
    );
  }

  // 渲染图片卡片
  if (card.type === "image") {
    return (
      <div className="flex flex-col items-center justify-center relative group mb-3">
        {/* 删除按钮 */}
        <div className="absolute top-2 left-2 z-10">
          <button
            onClick={() => onDelete(card.id)}
            className="p-1 bg-black/40 rounded-full hover:bg-red-500/80 transition-colors"
            title="Delete"
          >
            <Trash2 size={14} className="text-gray-300 hover:text-white" />
          </button>
        </div>

        {/* 选择按钮 */}
        <div className="absolute top-2 right-2 z-10">
          <button
            onClick={() => onToggleSelect(card.id)}
            className={`p-1 rounded-full transition-colors ${card.selected ? 'bg-indigo-300' : 'bg-black/40 hover:bg-black/60'}`}
            title={card.selected ? "Selected" : "Select"}
          >
            <Check size={14} className="text-white" />
          </button>
        </div>

        {/* 图片显示 */}
        <div className={`relative w-70 h-70 rounded-md overflow-hidden border-1 ${card.selected ? 'border-indigo-300' : 'border-[#222]/30'}`}>
          {card.isUploading ? (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="w-8 h-8 border-1 border-t-transparent border-white rounded-full animate-spin"></div>
            </div>
          ) : (
            <Image
              src={card.content}
              alt={`${section} reference`}
              fill
              className="object-cover"
            />
          )}
        </div>
      </div>
    );
  }

  // 渲染空卡片（初始状态）
  return (
    <div className="mb-3 relative">
      <div
        className={`flex flex-col items-center justify-center h-66 border ${card.selected ? 'border-foreground/30 border-1' : 'border-[#222]/30'} rounded-md cursor-pointer hover:bg-[#222]/5 transition-colors relative`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* 删除按钮 */}
        <button
          onClick={() => onDelete(card.id)}
          className="absolute top-2 left-2 p-1 bg-black/40 rounded-full hover:bg-red-500/80 transition-colors opacity-0 group-hover:opacity-100"
          title="Delete"
        >
          <Trash2 size={14} className="text-gray-300 hover:text-white" />
        </button>

        {isHovered ? (
          <div className="flex gap-6">
            {/* 图片上传选项 */}
            <button
              className="flex flex-col items-center justify-center w-16 h-16"
              onClick={triggerFileInput}
            >
              <Upload size={20} className="mb-1" />
              <span className="text-xs">Image</span>
            </button>

            {/* 文本输入选项 */}
            <button
              className="flex flex-col items-center justify-center w-16 h-16"
              onClick={() => onUpdateContent(card.id, "text", "")}
            >
              <Edit size={20} className="mb-1" />
              <span className="text-xs">Text</span>
            </button>
          </div>
        ) : (
          <>{sectionIcon}</>
        )}

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleImageUpload}
          disabled={card.isUploading}
        />
      </div>
    </div>
  );
};
