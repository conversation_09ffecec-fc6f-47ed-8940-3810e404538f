import { apiClient, ApiResponse } from './client';
import { API_CONFIG } from '@/lib/config';
import type { Model } from '@/types/model';

// 后端模型列表数据
export interface ModelListData {
    models: Model[];
    total: number;
}

/**
 * 模型API服务
 */
export const modelApi = {
    /**
     * 获取所有视频模型，支持分页
     * @param page 页码，从1开始
     * @param limit 每页项目数量
     */
    getModels: async (page: number = 1, limit: number = 20): Promise<ModelListData> => {
        const params: Record<string, string> = {
            page: page.toString(),
            limit: limit.toString(),
        };

        // 请求接口
        const response = await apiClient.get<ApiResponse<ModelListData>>(
            API_CONFIG.ENDPOINTS.MODEL.GET_MODELS,
            { params },
        );

        // API客户端已经处理了包装响应，直接返回data部分
        return response.data;
    },

    /**
     * 获取单个模型详情
     * @param modelId 模型ID
     */
    getModelById: async (modelId: string): Promise<Model> => {
        // 请求接口
        const response = await apiClient.get<ApiResponse<Model>>(
            `${API_CONFIG.ENDPOINTS.MODEL.GET_MODEL}/${modelId}`
        );

        // 返回data部分
        return response.data;
    },

    /**
     * 搜索模型
     * @param query 搜索关键词
     * @param filters 过滤条件
     * @param page 页码，从1开始
     * @param limit 每页项目数量
     */
    searchModels: async (
        query: string,
        filters: {
            type?: string[];
            features?: string[];
            source?: string[];
        } = {},
        page: number = 1,
        limit: number = 20
    ): Promise<ModelListData> => {
        // 构建查询参数
        const params: Record<string, string> = {
            query: query || '',
            page: page.toString(),
            limit: limit.toString(),
        };

        // 添加过滤条件 - 将type映射到model_types
        if (filters.type && filters.type.length > 0) {
            params.model_types = filters.type.join(',');
        }

        // 添加features过滤条件
        if (filters.features && filters.features.length > 0) {
            params.features = filters.features.join(',');
        }

        // 注意：后端API目前不支持source过滤，我们可以在前端过滤source

        // 请求接口
        const response = await apiClient.get<ApiResponse<ModelListData>>(
            `${API_CONFIG.ENDPOINTS.MODEL.GET_MODELS}/search`,
            { params },
        );

        // 如果有source过滤条件，在前端进行过滤
        let models = response.data.models;
        if (filters.source && filters.source.length > 0) {
            models = models.filter(model =>
                model.source && filters.source?.includes(model.source)
            );
        }

        return {
            models,
            total: response.data.total
        };
    }
};