import {
    Controller,
    Get,
    Param,
    Query,
} from '@nestjs/common';
import { ModelsService } from './models.service';
import {
    ModelDto,
    ModelListResponseDto
} from './dto/model.dto';
import { EffectDto } from './dto/effect.dto';
import { SearchModelDto, SearchModelResponseDto } from './dto/search-model.dto';

@Controller('models')
export class ModelsController {
    constructor(private readonly modelsService: ModelsService) {}

    /**
     * 获取所有视频模型
     * @param page 页码
     * @param limit 每页数量
     */
    @Get()
    async getAllModels(
        @Query('page') page?: number,
        @Query('limit') limit?: number,
    ): Promise<ModelListResponseDto> {
        return this.modelsService.getAllModels(
            page ? parseInt(page.toString(), 10) : 1,
            limit ? parseInt(limit.toString(), 10) : 20
        );
    }

    @Get('effects')
    async getEffects(): Promise<EffectDto[]> {
        return this.modelsService.getEffects();
    }

    /**
     * 搜索模型 (GET方式)
     * @param query 搜索关键词
     * @param model_types 模型类型，逗号分隔
     * @param features 功能，逗号分隔
     * @param page 页码
     * @param limit 每页数量
     */
    @Get('search')
    async searchModelsGet(
        @Query('query') query?: string,
        @Query('model_types') model_types?: string,
        @Query('features') features?: string,
        @Query('page') page?: number,
        @Query('limit') limit?: number,
    ): Promise<SearchModelResponseDto> {
        // 解析逗号分隔的字符串为数组
        const modelTypesArray = model_types ? model_types.split(',') : undefined;
        const featuresArray = features ? features.split(',') : undefined;

        return this.modelsService.searchModels({
            query,
            model_types: modelTypesArray,
            features: featuresArray,
            page: page ? parseInt(page.toString(), 10) : 1,
            limit: limit ? parseInt(limit.toString(), 10) : 20
        });
    }

    /**
     * 获取单个模型详情
     * @param model_id 模型ID
     */
    @Get(':model_id')
    async getModelById(
        @Param('model_id') model_id: string,
    ): Promise<ModelDto> {
        return this.modelsService.getModelById(model_id);
    }
}