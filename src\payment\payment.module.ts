import { Module, forwardRef } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { StripeService } from './stripe.service';
import { MembershipModule } from '../membership/membership.module';
import { ConfigModule } from '@nestjs/config';
import { CustomLogger } from '../common/services/logger.service';
import { CreditsModule } from '../credits/credits.module';
import { CouponModule } from '../coupon/coupon.module';
import { StripeCouponValidatorService } from './services/stripe-coupon-validator.service';
import { EnvironmentConfig } from '../common/config/environment.config';

@Module({
    imports: [
        forwardRef(() => MembershipModule),
        forwardRef(() => CreditsModule),
        CouponModule,
        ConfigModule
    ],
    controllers: [PaymentController],
    providers: [PaymentService, StripeService, StripeCouponValidatorService, CustomLogger, EnvironmentConfig],
    exports: [PaymentService, StripeService]
})
export class PaymentModule {
}