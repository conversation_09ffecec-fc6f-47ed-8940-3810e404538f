import { Modu<PERSON> } from '@nestjs/common';
import { VideoController } from './video.controller';
import { VideoService } from './video.service';
import { LoggerModule } from '../common/services/logger.module';
import { ErrorAuditModule } from '../common/module/error-audit.module';

@Module({
    imports: [LoggerModule, ErrorAuditModule],
    controllers: [VideoController],
    providers: [VideoService],
    exports: [VideoService],
})
export class VideoModule {} 