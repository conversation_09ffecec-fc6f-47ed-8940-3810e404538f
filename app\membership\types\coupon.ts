// 优惠券状态枚举
export enum CouponStatus {
    ACTIVE = 'active',
    USED = 'used',
    EXPIRED = 'expired'
}

// 优惠券类型枚举
export enum CouponType {
    FIRST_MONTH_DISCOUNT = 'first_month_discount'
}

// 用户优惠券接口
export interface UserCoupon {
    id: string;
    user_id: string;
    coupon_type: CouponType;
    discount_percentage?: number; // 折扣百分比，30表示7折
    discount_amount?: number; // 固定折扣金额
    discount_type: 'percentage' | 'fixed_amount'; // 折扣类型
    status: CouponStatus;
    claimed_at: string;
    expires_at: string;
    used_at?: string;
    payment_id?: string;
    created_at: string;
    updated_at: string;
}

// 优惠券使用历史接口
export interface CouponUsageHistory {
    id: string;
    coupon_id: string;
    user_id: string;
    payment_id: string;
    original_amount: number;
    discount_amount: number;
    final_amount: number;
    used_at: string;
    created_at: string;
}

// 领取优惠券请求接口
export interface ClaimCouponRequest {
    coupon_type: CouponType;
}

// 领取优惠券响应接口
export interface ClaimCouponResponse {
    success: boolean;
    coupon?: UserCoupon;
    message: string;
}

// 优惠券状态检查响应接口
export interface CouponStatusResponse {
    has_coupon: boolean;
    coupon?: UserCoupon;
    is_eligible: boolean; // 是否有资格领取优惠券
    message?: string;
}

// 价格计算接口
export interface PriceCalculation {
    original_price: number;
    discount_percentage: number;
    discount_amount: number;
    final_price: number;
    has_coupon: boolean;
    discount_type?: 'percentage' | 'fixed_amount';
    billing_cycle?: string;
    plan_name?: string;
}



// 优惠券应用到支付请求接口
export interface ApplyCouponToPaymentRequest {
    coupon_id: string;
    payment_id: string;
}

// 优惠券应用到支付响应接口
export interface ApplyCouponToPaymentResponse {
    success: boolean;
    original_amount: number;
    discount_amount: number;
    final_amount: number;
    message: string;
}
