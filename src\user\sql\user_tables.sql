-- 用户个人资料表
CREATE TABLE IF NOT EXISTS public.user_profiles (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    nickname VARCHAR(50),
    bio TEXT,
    avatar TEXT,
    links JSONB DEFAULT '[]'::jsonb, -- 存储用户的多个链接，格式为 [{"title": "链接标题", "url": "链接地址"}]
    stripe_customer_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- 用户设置表
CREATE TABLE IF NOT EXISTS public.user_settings (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    nsfw_enabled BOOLEAN NOT NULL DEFAULT false,
    nsfw_level SMALLINT NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- 用户上传的视频表
CREATE TABLE IF NOT EXISTS public.user_uploaded_videos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    storage_path TEXT NOT NULL,
    thumbnail_path TEXT,
    duration INTEGER,
    width INTEGER,
    height INTEGER,
    nsfw_level SMALLINT NOT NULL DEFAULT 1,
    is_public BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_user_uploaded_videos_user_id ON public.user_uploaded_videos(user_id);
CREATE INDEX IF NOT EXISTS idx_user_uploaded_videos_created_at ON public.user_uploaded_videos(created_at);
CREATE INDEX IF NOT EXISTS idx_user_uploaded_videos_is_public ON public.user_uploaded_videos(is_public);

-- 模型表
create table public.models (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  name character varying(100) not null,
  description text null,
  source character varying(20) not null,
  model_type character varying(20) not null,
  storage_path text not null,
  is_public boolean not null default false,
  nsfw_level smallint not null default 1,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  meta jsonb null,
  constraint user_models_pkey primary key (id),
  constraint user_models_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE,
  constraint user_models_source_check check (
    (
      (source)::text = any (
        (
          array[
            'upload'::character varying,
            'train'::character varying
          ]
        )::text[]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_user_models_user_id on public.models using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_user_models_created_at on public.models using btree (created_at) TABLESPACE pg_default;

create index IF not exists idx_user_models_is_public on public.models using btree (is_public) TABLESPACE pg_default;

create index IF not exists idx_user_models_source on public.models using btree (source) TABLESPACE pg_default;

-- 用户收藏表
create table public.user_favorites (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  post_id uuid null,
  item_type text not null,
  created_at timestamp with time zone not null default now(),
  model_id uuid null,
  constraint user_favorites_pkey primary key (id),
  constraint user_favorites_unique unique (user_id, post_id, item_type),
  constraint user_favorites_model_id_fkey foreign KEY (model_id) references models (id),
  constraint user_favorites_post_id_fkey foreign KEY (post_id) references posts (id),
  constraint user_favorites_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_user_favorites_user_id on public.user_favorites using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_user_favorites_item_id on public.user_favorites using btree (post_id) TABLESPACE pg_default;

create index IF not exists idx_user_favorites_item_type on public.user_favorites using btree (item_type) TABLESPACE pg_default;

-- 创建用户角色索引
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON public.user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role ON public.user_roles(role);

-- 创建用户注销账户的存储过程
CREATE OR REPLACE FUNCTION public.delete_user_account(user_id_param UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- 删除用户个人资料
    DELETE FROM public.user_profiles WHERE user_id = user_id_param;
    
    -- 删除用户设置
    DELETE FROM public.user_settings WHERE user_id = user_id_param;
    
    -- 删除用户收藏
    DELETE FROM public.user_favorites WHERE user_id = user_id_param;
    
    -- 注意：不删除用户的视频和模型，只是将其标记为匿名
    UPDATE public.user_uploaded_videos
    SET user_id = '********-0000-0000-0000-********0000'
    WHERE user_id = user_id_param;
    
    UPDATE public.user_models
    SET user_id = '********-0000-0000-0000-********0000'
    WHERE user_id = user_id_param;
    
    UPDATE public.video_gen_tasks
    SET user_id = '********-0000-0000-0000-********0000'
    WHERE user_id = user_id_param;
END;
$$;

-- 创建用户注册触发器，自动创建用户个人资料和设置
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- 创建用户个人资料
    INSERT INTO public.user_profiles (user_id, nickname, links)
    VALUES (NEW.id, SPLIT_PART(NEW.email, '@', 1), '[]'::jsonb);
    
    -- 创建用户设置
    INSERT INTO public.user_settings (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$;

-- 创建触发器
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_user(); 