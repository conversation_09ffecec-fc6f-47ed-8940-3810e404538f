import { apiClient } from './client';
import { createClient } from '@/lib/supabase/client';
import type { ApiResponse } from './client';
import type { Model } from '@/types/model';

// 获取认证头（仅用于Next.js API路由）
async function getAuthHeaders(): Promise<Record<string, string>> {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
        throw new Error('No authentication token found');
    }

    return {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
    };
}

// Next.js API调用函数（仅用于fal.ai相关接口）
async function nextApiCall<T>(
    endpoint: string,
    options: RequestInit = {}
): Promise<T> {
    const headers = await getAuthHeaders();

    const response = await fetch(`/api${endpoint}`, {
        ...options,
        headers: {
            ...headers,
            ...options.headers,
        },
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const result: { success: boolean; data: T; error?: string } = await response.json();

    if (!result.success) {
        throw new Error(result.error || 'API call failed');
    }

    return result.data;
}

export interface AdminModelListData {
    models: Model[];
    total: number;
    page: number;
    limit: number;
}

export interface UpdateModelData {
    name?: string;
    description?: string;
    price?: number;
    weight?: number;
    cover_img?: string;
    cover_video?: string;
    is_public?: boolean;
    nsfw_level?: number;
    category?: string;
    group?: string;
    metadata?: any;
    default_config?: any;
}

export interface FalAiModel {
    id: string;
    title: string;
    category: string;
    shortDescription: string;
    thumbnailUrl: string;
    thumbnailAnimatedUrl?: string;
    modelUrl: string;
    licenseType: string;
    creditsRequired: number;
    group?: {
        key: string;
        label: string;
    };
    tags: string[];
    pricingInfoOverride?: string;
    durationEstimate?: number;
    pinned: boolean;
    highlighted: boolean;
    deprecated: boolean;
    kind: string;
    machineType?: string;
}

/**
 * Admin模型管理API服务
 */
export const adminModelApi = {
    /**
     * 获取所有模型（管理员）
     */
    getModels: async (page: number = 1, limit: number = 1000, search?: string): Promise<AdminModelListData> => {
        const params: Record<string, string> = {
            page: page.toString(),
            limit: limit.toString(),
        };

        if (search) {
            params.search = search;
        }

        const response = await apiClient.get<ApiResponse<AdminModelListData>>(
            '/admin/models',
            { params }
        );

        return response.data;
    },

    /**
     * 获取所有模型（管理员，无分页）
     */
    getAllModels: async (search?: string): Promise<AdminModelListData> => {
        const params: Record<string, string> = {
            page: '1',
            limit: '1000', // 设置一个足够大的数字来获取所有模型
        };

        if (search) {
            params.search = search;
        }

        const response = await apiClient.get<ApiResponse<AdminModelListData>>(
            '/admin/models',
            { params }
        );

        return response.data;
    },

    /**
     * 获取单个模型详情（管理员）
     */
    getModelById: async (modelId: string): Promise<Model> => {
        const response = await apiClient.get<ApiResponse<Model>>(
            `/admin/models/${modelId}`
        );

        return response.data;
    },

    /**
     * 更新模型信息（管理员）
     */
    updateModel: async (modelId: string, data: UpdateModelData): Promise<Model> => {
        const response = await apiClient.put<ApiResponse<Model>>(
            `/admin/models/${modelId}`,
            data
        );

        return response.data;
    },

    /**
     * 创建新模型（管理员）
     */
    createModel: async (data: any): Promise<Model> => {
        const response = await apiClient.post<ApiResponse<Model>>(
            '/admin/models',
            data
        );

        return response.data;
    },

    /**
     * 删除模型（管理员）
     */
    deleteModel: async (modelId: string): Promise<void> => {
        await apiClient.delete(`/admin/models/${modelId}`);
    },

    /**
     * 获取fal.ai最新模型列表（通过Next.js API中转）
     */
    getFalAiModels: async (): Promise<FalAiModel[]> => {
        try {
            return await nextApiCall<FalAiModel[]>('/admin/fal-ai/models');
        } catch (error) {
            console.error('Failed to fetch fal.ai models:', error);
            throw new Error('Failed to fetch fal.ai models');
        }
    },

    /**
     * 检查模型是否已存在
     */
    checkModelExists: async (storagePathOrId: string): Promise<boolean> => {
        try {
            const response = await apiClient.get<ApiResponse<{ exists: boolean }>>(
                `/admin/models/check-exists`,
                { params: { storage_path: storagePathOrId } }
            );
            return response.data.exists;
        } catch (error) {
            console.error('Failed to check model existence:', error);
            return false;
        }
    },

    /**
     * 同步fal.ai模型到数据库（按照sync-fal-ai-models.ts的逻辑）
     */
    syncFalAiModel: async (falAiModel: FalAiModel): Promise<Model> => {
        // 检查模型是否已存在
        const exists = await adminModelApi.checkModelExists(falAiModel.id);
        if (exists) {
            throw new Error(`Model "${falAiModel.title}" already exists`);
        }

        // 准备模型数据，完全按照sync-fal-ai-models.ts的格式
        const modelData = {
            name: falAiModel.title,
            description: falAiModel.shortDescription || '',
            source: 'fal.ai',
            model_type: falAiModel.category,
            storage_path: falAiModel.id,
            is_public: true,
            nsfw_level: 1,
            group: falAiModel.group?.key,
            metadata: {
                original_id: falAiModel.id,
                model_url: falAiModel.modelUrl,
                thumbnail_url: falAiModel.thumbnailUrl,
                thumbnail_animated_url: falAiModel.thumbnailAnimatedUrl,
                tags: falAiModel.tags,
                license_type: falAiModel.licenseType,
                credits_required: falAiModel.creditsRequired,
                machine_type: falAiModel.machineType,
                duration_estimate: falAiModel.durationEstimate,
                kind: falAiModel.kind,
                group: falAiModel.group
            },
            supported_features: [],
            default_config: {
                min_guidance_scale: 0,
                max_guidance_scale: 20,
                default_guidance_scale: 7,
                min_steps: 10,
                max_steps: 50,
                default_steps: 30,
                allow_negative_prompt: true,
                allow_seed_input: true,
                allow_reference_image: falAiModel.category === 'image-to-video'
            },
            cover_img: falAiModel.thumbnailUrl,
            trainable: false,
            category: falAiModel.category
        };

        return await adminModelApi.createModel(modelData);
    },


};
