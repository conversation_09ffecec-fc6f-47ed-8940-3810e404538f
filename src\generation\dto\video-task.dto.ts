import { IsString, <PERSON>NotEmpty, IsUUID, IsOptional, IsEnum, IsNumber, IsObject, IsBoolean, IsDate } from 'class-validator';
import { VideoTaskStatus, VideoTaskOutputResult, VideoTaskErrorLog } from 'src/common/constants/video';


export class VideoTaskDto {
    @IsUUID()
    id: string;

    @IsUUID()
    user_id: string;

    @IsString()
    @IsOptional()
    handler?: string;

    @IsString()
    @IsOptional()
    request_id?: string;

    @IsObject()
    @IsOptional()
    client_info?: Record<string, any>;

    @IsEnum(VideoTaskStatus)
    status: VideoTaskStatus;

    @IsNumber()
    progress: number;

    @IsNumber()
    @IsOptional()
    retry_count?: number;

    @IsNumber()
    @IsOptional()
    priority?: number;

    @IsObject()
    input_params: Record<string, any>;

    @IsObject()
    @IsOptional()
    output_result?: Record<string, any>;

    @IsObject()
    @IsOptional()
    error_log?: Record<string, any>;

    @IsDate()
    created_at: Date;

    @IsDate()
    @IsOptional()
    queued_at?: Date;

    @IsDate()
    @IsOptional()
    started_at?: Date;

    @IsDate()
    @IsOptional()
    completed_at?: Date;

    @IsDate()
    @IsOptional()
    last_activity_at?: Date;

    @IsString()
    @IsOptional()
    storage_path?: string;

    @IsBoolean()
    @IsOptional()
    notification_sent?: boolean;

    @IsUUID()
    @IsOptional()
    tx_id?: string; // 关联的积分交易ID（扣款）

    @IsUUID()
    @IsOptional()
    refund_tx_id?: string; // 关联的积分退款交易ID
}

export class VideoTaskResponseDto {
    // id status progress queued_at started_at completed_at last_activity_at
    @IsString()
    id: string;

    @IsString()
    status: VideoTaskStatus;

    @IsNumber()
    progress: number;

    @IsDate()
    queued_at: Date;

    @IsDate()
    started_at: Date;

    @IsDate()
    completed_at: Date;

    @IsDate()
    last_activity_at: Date;

    @IsNumber()
    duration_estimate: number;

    @IsObject()
    input_params: Record<string, any>;
}

export class PopTaskDto {
    @IsBoolean()
    @IsOptional()
    has_refer_img?: boolean;
}

export class PopTaskResponseDto {
    @IsObject()
    task: VideoTaskDto;
}

export class FinishTaskDto {
    @IsNotEmpty()
    task_id: string;

    @IsObject()
    @IsOptional()
    output_result?: VideoTaskOutputResult;

    @IsString()
    @IsNotEmpty()
    storage_path: string;

    @IsEnum(VideoTaskStatus)
    @IsNotEmpty()
    status: VideoTaskStatus.COMPLETED | VideoTaskStatus.FAILED;

    @IsObject()
    @IsOptional()
    error_log?: VideoTaskErrorLog;
}