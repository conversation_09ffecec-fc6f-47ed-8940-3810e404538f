import { create } from 'zustand';
import { postApi, postToContentItem } from '@/lib/api/post';
import { PostItem } from '@/types/posts';
import { PostsResponse } from '@/lib/api/post';

interface FeedState {
    items: PostItem[];
    isLoading: boolean;
    hasMore: boolean;
    page: number;
    total: number;
    error: boolean;
    tag_id: string;
    selectedCategoryId: string; // 存储当前选中的分类ID

    // 初始化store
    initialize: (initialData: PostsResponse) => void;

    // 加载更多内容
    loadMore: () => Promise<void>;

    // 重置feed
    resetFeed: () => void;

    // 设置分类标签并重新加载feed
    setCategory: (categoryId: string) => Promise<void>;
}

const ITEMS_PER_PAGE = 10;

// 创建store
const useFeedStore = create<FeedState>((set, get) => ({
    items: [],
    isLoading: false,
    hasMore: true,
    page: 0,
    total: 0,
    error: false,
    tag_id: '',
    selectedCategoryId: 'all', // 默认选中"全部"分类

    // 从服务端或初始化数据初始化状态
    initialize: (initialData: PostsResponse) => {
        const initialItems = initialData.posts.map(postToContentItem);
        set({
            items: initialItems,
            total: initialData.total,
            hasMore: initialItems.length < initialData.total,
            page: 1,
            isLoading: false,
            error: false
        });
    },

    loadMore: async () => {
        const { isLoading, hasMore, page, tag_id } = get();

        if (isLoading || !hasMore) return;

        set({ isLoading: true });

        try {
            const { data } = await postApi.getFeed({
                limit: ITEMS_PER_PAGE,
                offset: page * ITEMS_PER_PAGE,
                tag_id: tag_id || undefined
            });

            // 将后端返回的Post数据转换为前端使用的ContentItem格式
            const newItems = data.posts.map((post) => postToContentItem(post));

            set(state => ({
                items: [...state.items, ...newItems],
                total: data.total,
                hasMore: state.items.length + newItems.length < data.total,
                page: state.page + 1,
                isLoading: false,
                error: false
            }));
        } catch (error) {
            console.error("Failed to load feed items:", error);
            set({ isLoading: false, error: true });
        }
    },

    resetFeed: () => {
        set({
            items: [],
            isLoading: false,
            hasMore: true,
            page: 0,
            total: 0,
            error: false,
            // 保留当前的分类id
            tag_id: get().tag_id,
            selectedCategoryId: get().selectedCategoryId
        });
    },

    setCategory: async (categoryId: string) => {
        // 如果选择的是"All"分类，则传递空字符串给API
        const categoryValue = categoryId === 'all' ? '' : categoryId;

        // 如果分类没有改变，不进行任何操作
        if (categoryValue === get().tag_id && categoryId === get().selectedCategoryId) return;

        // 设置新的分类标签和选中状态
        set({
            tag_id: categoryValue,
            selectedCategoryId: categoryId,
            items: [],
            isLoading: true,
            hasMore: true,
            page: 0,
            total: 0,
            error: false
        });

        try {
            const { data } = await postApi.getFeed({
                limit: ITEMS_PER_PAGE,
                offset: 0,
                tag_id: categoryValue || undefined
            });

            const newItems = data.posts.map((post) => postToContentItem(post));

            set({
                items: newItems,
                total: data.total,
                hasMore: newItems.length < data.total,
                page: 1,
                isLoading: false,
                error: false
            });
        } catch (error) {
            console.error("Failed to load feed items for category:", error);
            set({ isLoading: false, error: true });
        }
    }
}));

export default useFeedStore; 