'use client'

import { useEffect } from 'react'

// 字体预加载组件 - 优化字体加载策略
export function FontPreload() {
  useEffect(() => {
    // 检查是否支持FontFace API
    if (typeof window !== 'undefined' && 'FontFace' in window) {
      // 预加载主要字体
      const fontUrls = [
        '/fonts/inter-var.woff2', // 假设使用Inter变量字体
      ]

      // 使用Promise.all并行加载字体
      Promise.all(
        fontUrls.map(url => {
          // 创建一个新的FontFace实例
          const font = new FontFace(
            'Inter', // 字体名称
            `url(${url})`, // 字体URL
            {
              display: 'swap', // 使用字体交换策略
              weight: '100 900', // 支持所有字重
              style: 'normal',
              unicodeRange: 'U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD',
            }
          )

          // 加载字体
          return font.load().then(loadedFont => {
            // 将字体添加到文档中
            document.fonts.add(loadedFont)
            return loadedFont
          })
        })
      )
        .then(loadedFonts => {
          // 添加一个类来触发使用已加载的字体
          document.documentElement.classList.add('fonts-loaded')
        })
        .catch(error => {
          console.error('load font failed:', error)
        })
    }
  }, [])

  return null
}
