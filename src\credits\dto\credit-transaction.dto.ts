import { <PERSON>Enum, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, IsUUID } from 'class-validator';
import { CreditTransactionType, CreditTransactionStatus } from '../constant';

export class CreateCreditTransactionDto {
    @IsNotEmpty()
    @IsUUID()
    userId: string;

    @IsNotEmpty()
    @IsEnum(CreditTransactionType)
    type: CreditTransactionType;

    @IsNotEmpty()
    @IsNumber()
    amount: number;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @IsString()
    referenceId?: string;

    @IsOptional()
    @IsString()
    paymentId?: string;

    @IsOptional()
    @IsEnum(CreditTransactionStatus)
    status?: CreditTransactionStatus;
}

export class PurchaseCreditsDto {
    @IsNotEmpty()
    @IsNumber()
    amount: number;
}

export class ConsumeCreditsDto {
    @IsNotEmpty()
    @IsUUID()
    userId: string;

    @IsNotEmpty()
    @IsEnum(CreditTransactionType)
    type: CreditTransactionType;

    @IsNotEmpty()
    @IsNumber()
    amount: number;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @IsString()
    paymentId?: string;
} 