import { createClient } from '@supabase/supabase-js';
import type { BlogPost, PaginatedBlogResponse, BlogList, BlogSitemapEntry } from '@/types/blog';

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 博客内容存储桶名称
const BLOG_CONTENT_BUCKET = 'blog-content';

/**
 * 获取博客文章列表，支持分页
 * @param page 页码，从1开始
 * @param pageSize 每页文章数量
 * @param category 可选的分类过滤
 * @returns 分页的博客文章数据
 */
export async function getBlogPosts(page = 1, pageSize = 10, category?: string): Promise<PaginatedBlogResponse> {
    const fields = 'id, excerpt, cover_image, title, slug, published_at, created_at, category, tags';

    try {
        let query = supabase
            .from('blog_posts')
            .select(fields, { count: 'exact' })
            .order('published_at', { ascending: false });

        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;

        const { data, error } = await query.range(from, to);

        if (error) {
            console.error('Error fetching blog posts:', error);
            throw new Error(error.message);
        }

        return {
            posts: data as BlogList,
            page,
            pageSize,
        };
    } catch (error) {
        console.error('Error in getBlogPosts:', error);
        throw error;
    }
}

/**
 * 根据ID获取单个博客文章
 * @param id 文章ID
 * @returns 博客文章详情或null
 */
export async function getBlogPostById(id: string): Promise<BlogPost | null> {
    try {
        const { data, error } = await supabase
            .from('blog_posts')
            .select('*')
            .eq('id', id)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return null; // 文章不存在
            }
            console.error('Error fetching blog post by ID:', error);
            throw new Error(error.message);
        }

        // 获取到基本博客数据后，尝试从Storage获取内容
        const post = data as BlogPost;
        return await getBlogPostWithContent(post);
    } catch (error) {
        console.error(`Error in getBlogPostById (${id}):`, error);
        throw error;
    }
}

/**
 * 根据Slug获取单个博客文章
 * @param slug 文章slug
 * @returns 博客文章详情或null
 */
export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
    try {
        const { data, error } = await supabase
            .from('blog_posts_duplicate') // 目前先从备份表里读取
            .select('*')
            .eq('slug', slug)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return null; // 文章不存在
            }
            console.error('Error fetching blog post by slug:', error);
            throw new Error(error.message);
        }

        // 获取到基本博客数据后，尝试从Storage获取内容
        const post = data as BlogPost;
        return await getBlogPostWithContent(post);
    } catch (error) {
        console.error(`Error in getBlogPostBySlug (${slug}):`, error);
        throw error;
    }
}

/**
 * 获取所有博客文章的slug和created_at，专门用于生成网站地图
 * 使用分批处理方式处理大量数据
 * @param batchSize 每批处理的数量
 * @returns 所有博客文章的slug和created_at
 */
/**
 * 获取博客文章总数，专门用于计算sitemap分页
 * @returns 博客文章总数
 */
export async function getBlogPostsCount(): Promise<number> {
    try {
        const { count, error } = await supabase
            .from('blog_posts')
            .select('*', { count: 'exact', head: true });

        if (error) {
            console.error('Error counting blog posts:', error);
            throw new Error(error.message);
        }

        return count || 0;
    } catch (error) {
        console.error('Error in getBlogPostsCount:', error);
        throw error;
    }
}

/**
 * 获取特定范围的博客文章，专门用于生成sitemap
 * 使用并发请求和游标分页方式处理大量数据
 * @param start 起始索引
 * @param limit 获取数量
 * @returns 指定范围的博客文章slug和created_at
 */
/**
 * 获取特定范围的博客文章，专门用于生成sitemap
 * 使用优化的游标分页和控制并发度的方式处理大量数据
 * @param start 起始索引
 * @param limit 获取数量
 * @returns 指定范围的博客文章slug和created_at
 */
export async function getBlogSlugsForSitemapPaginated(start: number, limit: number): Promise<BlogSitemapEntry[]> {
    try {
        // 只选择网站地图所需的最小字段集
        const fields = 'id, slug, created_at, cid';

        // 每个批次的大小，Supabase单次查询的最大限制通常为1000
        const BATCH_SIZE = 1000;

        // 最大并发查询数量，避免并发过高导致数据库连接问题
        const MAX_CONCURRENT_QUERIES = 3;

        // 查询超时时间（毫秒）
        const QUERY_TIMEOUT = 30000;

        // 最大重试次数
        const MAX_RETRIES = 3;

        // 重试延迟基数（毫秒）
        const BASE_RETRY_DELAY = 1000;

        // 创建一个执行查询的函数，包含重试逻辑
        async function executeQueryWithRetry(
            queryFn: () => any,
            retryCount = 0
        ): Promise<{ data: any; error: any }> {
            try {
                // 创建一个超时Promise
                const timeoutPromise = new Promise<{ data: null; error: any }>((_, reject) => {
                    setTimeout(() => {
                        reject({ data: null, error: new Error('查询超时') });
                    }, QUERY_TIMEOUT);
                });

                // 执行查询，并与超时Promise竞争
                const result = await Promise.race([queryFn(), timeoutPromise]);

                // 如果查询成功或已达到最大重试次数，直接返回结果
                if (!result.error || retryCount >= MAX_RETRIES) {
                    return result;
                }

                // 计算重试延迟（指数退避策略）
                const delay = BASE_RETRY_DELAY * Math.pow(2, retryCount);
                console.log(`查询失败，${delay}ms后重试(${retryCount + 1}/${MAX_RETRIES})...`);

                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, delay));
                return executeQueryWithRetry(queryFn, retryCount + 1);
            } catch (error) {
                if (retryCount < MAX_RETRIES) {
                    const delay = BASE_RETRY_DELAY * Math.pow(2, retryCount);
                    console.log(`查询异常，${delay}ms后重试(${retryCount + 1}/${MAX_RETRIES})...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return executeQueryWithRetry(queryFn, retryCount + 1);
                }
                return { data: null, error };
            }
        }

        // 计算需要查询的cid范围
        const startCid = start + 1; // cid从1开始
        const endCid = start + limit;

        // 计算需要多少个批次
        const batchCount = Math.ceil(limit / BATCH_SIZE);

        let allData: BlogSitemapEntry[] = [];
        let remainingLimit = limit;

        // 分批处理所有批次，每次并发处理MAX_CONCURRENT_QUERIES个批次
        for (let batchStart = 0; batchStart < batchCount; batchStart += MAX_CONCURRENT_QUERIES) {
            // 计算当前批次的并发数量
            const currentBatchSize = Math.min(MAX_CONCURRENT_QUERIES, batchCount - batchStart);

            // 创建当前批次的查询数组
            const batchQueries = Array.from({ length: currentBatchSize }, (_, i) => {
                const batchIndex = batchStart + i;
                const batchStartCid = startCid + batchIndex * BATCH_SIZE;
                const batchEndCid = Math.min(batchStartCid + BATCH_SIZE - 1, endCid);

                return async () => {
                    // 使用cid范围查询，直接获取指定范围的数据
                    return supabase
                        .from('blog_posts')
                        .select(fields)
                        .gte('cid', batchStartCid)
                        .lte('cid', batchEndCid)
                        .order('cid', { ascending: true })
                        .limit(BATCH_SIZE);
                };
            });

            // 并发执行当前批次的查询
            const batchResults = await Promise.all(
                batchQueries.map(queryFn => executeQueryWithRetry(queryFn))
            );

            // 处理查询结果
            for (const result of batchResults) {
                if (result.error) {
                    console.error(`批次查询出错:`, result.error);
                    continue;
                }

                if (result.data && result.data.length > 0) {
                    allData = [...allData, ...result.data as BlogSitemapEntry[]];
                    remainingLimit -= result.data.length;
                }

                // 如果已经获取到足够的数据，就停止处理
                if (remainingLimit <= 0) {
                    break;
                }
            }

            // 如果已经获取到足够的数据，就停止查询
            if (remainingLimit <= 0) {
                break;
            }
        }

        // 确保不超过请求的limit
        if (allData.length > limit) {
            allData = allData.slice(0, limit);
        }

        // 按照cid排序，确保数据顺序正确
        allData.sort((a: any, b: any) => a.cid - b.cid);

        return allData;
    } catch (error) {
        console.error(`Error in getBlogSlugsForSitemapPaginated(${start}, ${limit}):`, error);
        throw error;
    }
}

/**
 * 从Supabase Storage获取博客内容
 * @param postId 博客文章ID
 * @returns 博客内容或null
 */
export async function getBlogContentFromStorage(postId: string): Promise<string | null> {
    try {
        // 构建文件路径
        const filePath = `${postId}.md`;

        // 从Storage下载文件
        const { data, error } = await supabase
            .storage
            .from(BLOG_CONTENT_BUCKET)
            .download(filePath);

        // 如果出错或没有数据，返回null
        if (error || !data) {
            return null;
        }

        // 将Blob转换为文本
        const content = await data.text();
        return content;
    } catch (error) {
        console.error(`Error fetching blog content from storage (${postId}):`, error);
        return null; // 出错时返回null，允许回退到数据库内容
    }
}

/**
 * 获取博客文章内容，优先从Storage获取，如果不存在则使用数据库中的content字段
 * @param post 博客文章对象
 * @returns 带有最新内容的博客文章对象
 */
export async function getBlogPostWithContent(post: BlogPost): Promise<BlogPost> {
    if (!post) return post;

    // 尝试从Storage获取内容
    const storageContent = await getBlogContentFromStorage(post.id);

    // 如果从Storage获取到内容，则替换post中的content
    if (storageContent) {
        return {
            ...post,
            content: storageContent
        };
    }

    // 否则返回原始post对象（使用数据库中的content字段）
    return post;
}

export async function getAllBlogSlugsForSitemap(batchSize = 1000): Promise<BlogSitemapEntry[]> {
    try {
        // 只选择网站地图所需的最小字段集
        const fields = 'id, slug, created_at, cid';
        let allEntries: BlogSitemapEntry[] = [];
        let lastCid = 0; // 从cid=0开始查询（实际上cid从1开始）
        let hasMore = true;

        // 使用分批查询处理所有数据
        while (hasMore) {
            const { data, error } = await supabase
                .from('blog_posts')
                .select(fields)
                .gt('cid', lastCid) // 使用cid进行范围查询
                .order('cid', { ascending: true }) // 按cid排序
                .limit(batchSize);

            if (error) {
                console.error('Error fetching blog slugs for sitemap:', error);
                throw new Error(error.message);
            }

            if (data && data.length > 0) {
                // 添加当前批次的数据
                allEntries = [...allEntries, ...data as BlogSitemapEntry[]];
                // 记录最后一条记录的cid，用于下一次查询
                lastCid = data[data.length - 1].cid as number;
            }

            // 如果返回的数据少于请求的数量，说明已经没有更多数据了
            hasMore = data && data.length === batchSize;
        }

        return allEntries;
    } catch (error) {
        console.error('Error in getAllBlogSlugsForSitemap:', error);
        throw error;
    }
}