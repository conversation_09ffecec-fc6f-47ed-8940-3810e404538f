import { useState, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { feedbackApi, CreateFeedbackParams, FeedbackType } from '@/lib/api/feedback';
import { toast } from 'sonner';
import useAuthStore from '@/store/useAuthStore';
import { InputSanitizer } from '@/lib/security/input-sanitizer';
import { RateLimiter } from '@/lib/security/rate-limiter';

// 表单状态接口 - 简化为3个字段
export interface FeedbackFormState {
    type: FeedbackType;
    subject: string;
    message: string;
}

// 表单验证错误接口
export interface FeedbackFormErrors {
    subject?: string;
    message?: string;
}

// 初始表单状态
const initialFormState: FeedbackFormState = {
    type: FeedbackType.BUG,
    subject: '',
    message: '',
};

/**
 * 反馈表单自定义Hook
 * 提供表单状态管理、验证和提交功能
 */
export const useFeedbackForm = () => {
    const [formState, setFormState] = useState<FeedbackFormState>(initialFormState);
    const [errors, setErrors] = useState<FeedbackFormErrors>({});
    const [isSubmitted, setIsSubmitted] = useState(false);

    const { isAuthenticated } = useAuthStore();

    // 创建反馈的mutation - 超级简化
    const createFeedbackMutation = useMutation({
        mutationFn: (params: CreateFeedbackParams) => feedbackApi.createFeedback(params),
        onSuccess: () => {
            setIsSubmitted(true);
            toast.success('Feedback submitted successfully!');

            // 3秒后重置表单
            setTimeout(() => {
                setFormState(initialFormState);
                setErrors({});
                setIsSubmitted(false);
            }, 3000);
        },
        onError: (error: any) => {
            toast.error('Failed to submit feedback. Please try again.');
        },
    });

    /**
     * 验证表单数据 - 增强安全验证
     */
    const validateForm = useCallback((data: FeedbackFormState): FeedbackFormErrors => {
        const newErrors: FeedbackFormErrors = {};

        // 验证反馈类型
        const typeValidation = InputSanitizer.validateFeedbackType(data.type);
        if (!typeValidation.isValid) {
            newErrors.subject = typeValidation.error;
        }

        // 验证主题 - 使用安全验证器
        const subjectValidation = InputSanitizer.validateSubject(data.subject);
        if (!subjectValidation.isValid) {
            newErrors.subject = subjectValidation.error;
        }

        // 验证消息内容 - 使用安全验证器
        const messageValidation = InputSanitizer.validateMessage(data.message);
        if (!messageValidation.isValid) {
            newErrors.message = messageValidation.error;
        }

        return newErrors;
    }, []);

    /**
     * 处理表单字段变化 - 增强安全性
     */
    const handleFieldChange = useCallback((field: keyof FeedbackFormState, value: string | FeedbackType) => {
        // Security: Sanitize string inputs
        let sanitizedValue = value;
        if (typeof value === 'string') {
            // Basic sanitization for real-time input
            sanitizedValue = value.replace(/[<>]/g, ''); // Remove basic HTML brackets

            // Limit length during input
            if (field === 'subject' && sanitizedValue.length > 200) {
                sanitizedValue = sanitizedValue.substring(0, 200);
            } else if (field === 'message' && sanitizedValue.length > 2000) {
                sanitizedValue = sanitizedValue.substring(0, 2000);
            }
        }

        setFormState(prev => ({
            ...prev,
            [field]: sanitizedValue,
        }));

        // 清除对应字段的错误
        if (errors[field as keyof FeedbackFormErrors]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined,
            }));
        }
    }, [errors]);

    /**
     * 处理表单提交 - 增强安全性
     */
    const handleSubmit = useCallback(async (e?: React.FormEvent) => {
        if (e) {
            e.preventDefault();
        }

        // 检查用户是否已登录
        if (!isAuthenticated) {
            toast.error('Please login to submit feedback.');
            return;
        }

        // Security: Check rate limiting (client-side check)
        const user = useAuthStore.getState().user;
        if (user?.id) {
            const rateLimitCheck = RateLimiter.canSubmit(user.id);
            if (!rateLimitCheck.allowed) {
                toast.error(rateLimitCheck.reason || 'Too many submissions. Please try again later.');
                return;
            }
        }

        // 验证表单 - 使用增强的安全验证
        const validationErrors = validateForm(formState);
        setErrors(validationErrors);

        // 如果有验证错误，不提交
        if (Object.keys(validationErrors).length > 0) {
            toast.error('Please fix the errors in the form.');
            return;
        }

        // Security: Final comprehensive validation
        const comprehensiveValidation = InputSanitizer.validateFeedbackInput(formState);
        if (!comprehensiveValidation.isValid) {
            setErrors(comprehensiveValidation.errors || {});
            toast.error('Please check your input and try again.');
            return;
        }

        // 提交表单 - 使用清理后的数据
        const sanitizedData = comprehensiveValidation.sanitizedData!;
        createFeedbackMutation.mutate({
            type: sanitizedData.type as FeedbackType,
            subject: sanitizedData.subject,
            message: sanitizedData.message,
        });

        // Record submission for rate limiting
        if (user?.id) {
            RateLimiter.recordSubmission(user.id);
        }
    }, [isAuthenticated, formState, validateForm, createFeedbackMutation]);

    /**
     * 重置表单
     */
    const resetForm = () => {
        setFormState(initialFormState);
        setErrors({});
        setIsSubmitted(false);
    };

    /**
     * 获取字段错误信息
     */
    const getFieldError = (field: keyof FeedbackFormErrors): string | undefined => {
        return errors[field];
    };

    /**
     * 检查字段是否有错误
     */
    const hasFieldError = (field: keyof FeedbackFormErrors): boolean => {
        return !!errors[field];
    };

    /**
     * 检查表单是否有效
     */
    const isFormValid = (): boolean => {
        const validationErrors = validateForm(formState);
        return Object.keys(validationErrors).length === 0;
    };

    return {
        // 表单状态
        formState,
        errors,
        isSubmitted,

        // 提交状态
        isSubmitting: createFeedbackMutation.isPending,
        isSuccess: createFeedbackMutation.isSuccess,
        isError: createFeedbackMutation.isError,

        // 表单操作
        handleFieldChange,
        handleSubmit,
        resetForm,

        // 验证相关
        getFieldError,
        hasFieldError,
        isFormValid,

        // 认证状态
        isAuthenticated,
    };
};
