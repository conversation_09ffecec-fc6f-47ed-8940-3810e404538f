import { Injectable, Inject, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { GenVideoRequestDto, TickProgressDto } from './dto/gen-video.dto';
import { VideoTaskDto, FinishTaskDto, VideoTaskResponseDto } from './dto/video-task.dto';
import { VideoTaskStatus, VideoDuration } from '../common/constants/video';
import { CustomLogger } from '../common/services/logger.service';
import { MembershipService } from '../membership/membership.service';
import { CreditsService } from '../credits/credits.service';
import { CreditAmount, CreditTransactionType } from '../credits/constant';
import { v4 as uuidv4 } from 'uuid';
import { ErrorAuditService, ErrorSeverity } from '../common/services/error-audit.service';
import { QueueInfoResponseDto } from './dto/queue-info.dto';
import { EnhancePromptRequestDto, EnhancePromptResponseDto } from './dto/enhance-prompt.dto';
import { ConfigService } from '@nestjs/config';
import { OpenAI } from 'openai';
import { SystemPromptVideo } from './constants';
import { FalAiService } from './services/fal-ai.service';
import { FAL_AI_SOURCE } from '../common/constants/fal-ai';
import { FalAiWebhookRequestDto } from './dto/fal-ai-webhook.dto';
import { TaskPriceRequestDto, TaskPriceResponseDto } from './dto/task-price.dto';
import { ModelInfo } from './interfaces/model.interface';
import {
    CreditConsumptionResult,
    CreditRefundResult
} from './interfaces/credit-transaction.interface';
import {
    TaskCreationFailureResult
} from './interfaces/task-creation.interface';
import { Model } from 'src/models/model.type';

@Injectable()
export class GenerationService {
    private readonly deepseekApiKey: string;
    private readonly deepseekBaseUrl: string;
    private readonly openaiClient: OpenAI;

    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
        private readonly membershipService: MembershipService,
        private readonly creditsService: CreditsService,
        private readonly errorAuditService: ErrorAuditService,
        private readonly configService: ConfigService,
        private readonly falAiService: FalAiService,
    ) {
        this.logger.setContext(GenerationService.name);
        this.deepseekApiKey = this.configService.get<string>('DEEPSEEK_API_KEY');
        this.deepseekBaseUrl = 'https://api.deepseek.com';
        this.openaiClient = new OpenAI({
            apiKey: this.deepseekApiKey,
            baseURL: this.deepseekBaseUrl,
        });
    }

    /**
     * 获取模型信息
     * @param modelId 模型ID
     * @returns 模型信息
     */
    private async getModelInfo(modelId: string): Promise<ModelInfo> {
        try {
            // 从数据库获取模型信息
            const { data: model, error } = await this.supabase
                .from('models')
                .select('*')
                .eq('id', modelId)
                .filter('is_public', 'eq', true)
                .single();

            if (error) {
                throw new NotFoundException(`模型未找到: ${modelId}`);
            }

            return model as ModelInfo;
        } catch (error) {
            this.logger.error(`获取模型信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 计算视频生成所需的积分
     * @param model 模型信息
     * @param duration 视频时长，默认为5秒
     * @returns 所需积分数量
     */
    private async calculateRequiredCredits(
        model: Model,
        duration: string = VideoDuration.SHORT
    ): Promise<number> {
        try {
            // 基础价格（5秒视频的价格）
            const basePrice = model.price || CreditAmount.VIDEO_GENERATION_SD;

            // 将时长字符串转换为数字
            const durationSeconds = parseInt(duration, 10);

            // 验证时长是否有效
            if (isNaN(durationSeconds) || durationSeconds <= 0) {
                this.logger.warn(`无效的视频时长: ${duration}，使用默认5秒`);
                return basePrice;
            }

            // 基于5秒基准计算单价（每秒价格）
            const pricePerSecond = basePrice / 5;

            // 计算实际价格，向上取整确保不会出现小数积分
            const actualPrice = Math.ceil(pricePerSecond * durationSeconds);

            this.logger.debug(`价格计算: 基础价格=${basePrice}, 时长=${durationSeconds}秒, 每秒价格=${pricePerSecond}, 实际价格=${actualPrice}`);

            return actualPrice;
        } catch (error) {
            this.logger.error(`计算所需积分失败: ${error.message}`, error.stack);
            // 出错时使用默认价格50积分
            return CreditAmount.VIDEO_GENERATION_SD;
        }
    }

    /**
     * 消费用户积分用于视频生成
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param model 模型信息
     * @param duration 视频时长
     * @returns 消费结果
     */
    private async consumeCreditsForVideoGeneration(
        userId: string,
        taskId: string,
        model: Model,
        duration: string = VideoDuration.SHORT
    ): Promise<CreditConsumptionResult> {
        try {
            const creditsAmount = await this.calculateRequiredCredits(model, duration);
            const transactionType = CreditTransactionType.VIDEO_GENERATION;
            const description = `generate ${duration} video using model ${model.id}`;

            // 使用taskId作为paymentId（幂等性键）确保不会重复扣费
            const result = await this.creditsService.consumeCredits({
                userId,
                type: transactionType,
                amount: creditsAmount,
                description,
                paymentId: taskId
            });

            return {
                transaction: result.transaction,
                remainingBalance: result.remainingBalance,
                transactionId: result.transaction.id
            };
        } catch (error) {
            this.logger.error(`消费积分失败: ${error.message}`, error.stack);

            // 记录到错误审计系统
            await this.errorAuditService.logCreditError(
                userId,
                `消费积分失败: ${error.message}`,
                error,
                {
                    taskId,
                    modelId: model.id,
                    duration,
                    operation: 'consumeCredits'
                },
                ErrorSeverity.HIGH
            );

            throw error;
        }
    }

    /**
     * 退还用户积分（生成失败或取消时）
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param modelId 模型ID
     * @param reason 退款原因
     * @param duration 视频时长
     * @returns 退款结果
     */
    private async refundCreditsForVideoGeneration(
        userId: string,
        taskId: string,
        modelId: string,
        reason: string = 'video generation failed',
        duration: string = VideoDuration.SHORT
    ): Promise<CreditRefundResult> {
        try {
            const model = await this.getModelInfo(modelId);
            const creditsAmount = await this.calculateRequiredCredits(model, duration);
            const durationText = duration === VideoDuration.SHORT ? '5s' : '10s';

            // 创建一个退款交易记录
            const refundId = uuidv4(); // 生成唯一的退款ID
            const result = await this.creditsService.createCreditTransaction({
                userId,
                type: CreditTransactionType.REFUND, // 使用退款类型进行退款
                amount: creditsAmount,
                description: `refund for ${durationText} video generation using model ${modelId}`,
                paymentId: refundId,
                referenceId: taskId // 关联到原始任务ID
            });

            return {
                transaction: result.transaction,
                newBalance: result.newBalance,
                transactionId: result.transaction.id
            };
        } catch (error) {
            this.logger.error(`退还积分失败: ${error.message}`, error.stack);

            // 记录到错误审计系统 - 这是一个严重错误，因为用户可能无法获得退款
            const errorId = await this.errorAuditService.logRefundError(
                userId,
                taskId,
                `退还积分失败: ${error.message}`,
                error,
                {
                    modelId,
                    duration,
                    reason,
                    operation: 'refundCredits'
                },
                ErrorSeverity.CRITICAL // 退款失败是严重问题
            );

            this.logger.warn(`已记录退款失败到错误审计系统，错误ID: ${errorId}，请运营人员尽快处理`);

            throw error;
        }
    }

    /**
     * 创建视频生成任务
     * @param userId 用户ID
     * @param genVideoDto 视频生成参数
     * @returns 创建的任务信息
     */
    async createVideoGenerationTask(
        userId: string,
        genVideoDto: GenVideoRequestDto,
    ): Promise<VideoTaskResponseDto> {
        try {
            // 生成任务ID（用于幂等性）
            const taskId = uuidv4();

            // 获取模型信息
            const model = await this.getModelInfo(genVideoDto.model_id);

            // 先扣除积分 - 这一步会检查积分是否足够
            let creditResult: CreditConsumptionResult;
            try {
                // 获取视频时长参数，默认为5秒
                const duration = genVideoDto.duration || VideoDuration.SHORT;

                // 消费积分时考虑视频时长
                creditResult = await this.consumeCreditsForVideoGeneration(userId, taskId, model, duration);
            } catch (creditError) {
                // 特别处理积分不足的错误
                if (creditError instanceof BadRequestException &&
                    creditError.message.includes('Insufficient credits')) {
                    throw new ForbiddenException('Insufficient credits');
                }
                // 其他积分相关错误，保持原始错误
                throw creditError;
            }
            // 获取用户会员优先级
            const priority = await this.membershipService.getUserMembershipPriority(userId);

            const inputParams = {
                ...genVideoDto,
                model_name: model.name
            }

            // 如果是fal.ai模型，设置handler为fal.ai
            if (model.source === FAL_AI_SOURCE) {
                let request_id = '';
                try {
                    const falModelName = model.storage_path
                    // 调用fal.ai API
                    const falAiResponse: { request_id: string } = await this.falAiService.generateVideo(falModelName, genVideoDto);
                    request_id = falAiResponse.request_id;
                } catch (falAiError) {
                    // fal.ai API调用失败，退还积分
                    await this.handleTaskCreationFailure(userId, taskId, genVideoDto.model_id, falAiError, genVideoDto.duration);
                    console.error('调用fal.ai API失败', falAiError);
                    throw new BadRequestException(`调用fal.ai API失败: ${falAiError.message}`);
                }

                // 创建视频生成任务记录
                const { data } = await this.supabase
                    .from('video_gen_tasks')
                    .insert({
                        id: taskId,
                        user_id: userId,
                        model_id: model.id,
                        model_name: model.name,
                        handler: FAL_AI_SOURCE, // 设置handler为fal.ai
                        status: request_id ? VideoTaskStatus.PROCESSING : VideoTaskStatus.FAILED,
                        progress: 0, // 设置初始进度
                        input_params: inputParams,
                        created_at: new Date(),
                        started_at: new Date(),
                        priority,
                        request_id: request_id, // 保存fal.ai的request_id
                        tx_id: creditResult.transactionId, // 保存积分交易ID
                        duration_estimate: model.metadata?.duration_estimate,
                    })
                    .select('id, status, progress, queued_at, started_at, completed_at, last_activity_at, duration_estimate, input_params')
                    .single();

                // 使用webhook方式，不需要主动监控任务状态
                this.logger.log(`已提交fal.ai任务，等待webhook回调，任务ID: ${taskId}, 请求ID: ${request_id}`);

                return data as VideoTaskResponseDto;
            } else {
                // 内部处理 - 创建普通视频生成任务
                const { data, error } = await this.supabase
                    .from('video_gen_tasks')
                    .insert({
                        id: taskId, // 使用预先生成的ID
                        model_id: model.id,
                        model_name: model.name,
                        user_id: userId,
                        handler: null, // 内部处理，handler为NULL
                        status: VideoTaskStatus.PENDING,
                        progress: 0,
                        input_params: inputParams,
                        created_at: new Date(),
                        priority, // 添加优先级字段
                        tx_id: creditResult.transactionId // 保存积分交易ID
                    })
                    .select('id, status, progress, queued_at, started_at, completed_at, last_activity_at')
                    .single();

                // 处理任务创建错误
                if (error) {
                    // 任务创建失败，退还积分
                    await this.handleTaskCreationFailure(userId, taskId, genVideoDto.model_id, error, genVideoDto.duration);
                    throw new BadRequestException('创建任务失败');
                }

                return data as VideoTaskResponseDto;
            }
        } catch (error) {
            // 记录错误并重新抛出
            this.logger.error('创建视频生成任务出错', error);

            // 保持原始错误类型，便于前端区分处理
            if (error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }

            // 未知错误转换为通用错误
            throw new BadRequestException('创建任务出错');
        }
    }

    /**
     * 处理任务创建失败，负责积分退款和错误日志
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param modelId 模型ID
     * @param error 错误信息
     * @param duration 视频时长
     * @returns 处理结果
     */
    private async handleTaskCreationFailure(
        userId: string,
        taskId: string,
        modelId: string,
        error: any,
        duration: string = VideoDuration.SHORT
    ): Promise<TaskCreationFailureResult> {
        this.logger.error('创建视频生成任务失败', error);

        try {
            // 尝试退还积分，考虑视频时长
            const refundResult = await this.refundCreditsForVideoGeneration(
                userId,
                taskId,
                modelId,
                'Generate video failed',
                duration
            );

            return {
                success: true,
                refundResult
            };
        } catch (refundError) {
            // 记录退款失败，但不影响主流程
            this.logger.error(
                `任务创建失败后退还积分出错: ${refundError.message}`,
                refundError.stack
            );

            // 记录到错误审计系统 - 这是一个严重错误，需要人工干预
            const errorId = await this.errorAuditService.logRefundError(
                userId,
                taskId,
                `任务创建失败后退还积分出错: ${refundError.message}`,
                refundError,
                {
                    originalError: error,
                    modelId,
                    duration,
                    operation: 'handleTaskCreationFailure'
                },
                ErrorSeverity.CRITICAL
            );

            this.logger.warn(`已记录退款失败到错误审计系统，错误ID: ${errorId}，请运营人员尽快处理`);

            return {
                success: false,
                error: refundError
            };
        }
    }

    /**
     * 获取下一个待处理任务
     * @returns 下一个待处理任务
     */
    async popNextPendingTask({
        has_refer_img = false
    }): Promise<VideoTaskDto | null> {
        // 事务获取并更新任务状态
        const { data: result, error } = await this.supabase.rpc('get_next_pending_task', {
            has_refer_img
        });

        const data = result[0];

        if (error) {
            this.logger.error('获取待处理任务失败', error);
            throw new BadRequestException('获取待处理任务失败');
        }

        if (!data) {
            return null;
        }

        const {
            id,
            user_id,
            input_params,
            status,
            progress,
            storage_path,
            priority,
        } = data as VideoTaskDto;

        return {
            id,
            user_id,
            input_params,
            status,
            progress,
            storage_path,
            priority,
        } as VideoTaskDto;
    }

    /**
     * 完成视频生成任务
     * @param finishTaskDto 任务完成信息
     * @returns 更新后的任务
     */
    async finishVideoGenerationTask(finishTaskDto: FinishTaskDto): Promise<VideoTaskDto> {
        const { task_id, output_result, storage_path, status, error_log } = finishTaskDto;

        // 检查任务是否存在
        const { data: existingTask, error: checkError } = await this.supabase
            .from('video_gen_tasks')
            .select('*')
            .eq('id', task_id)
            .single();

        if (checkError || !existingTask) {
            this.logger.error(`任务不存在: ${task_id}`, checkError);
            throw new NotFoundException('任务不存在');
        }

        // 检查任务是否已经处于终态（已完成、已失败或已取消）
        const finalStatuses = [VideoTaskStatus.COMPLETED, VideoTaskStatus.FAILED, VideoTaskStatus.CANCELED];
        if (finalStatuses.includes(existingTask.status as VideoTaskStatus)) {
            this.logger.warn(`尝试更新已处于终态的任务: ${task_id}, 当前状态: ${existingTask.status}`);
            return existingTask as VideoTaskDto;
        }

        // 更新任务状态和结果
        const updateData: any = {
            status,
            completed_at: new Date(),
            last_activity_at: new Date(),
            progress: status === VideoTaskStatus.COMPLETED ? 100 : existingTask.progress,
            output_result,
            storage_path,
            error_log,
        };

        const { data, error } = await this.supabase
            .from('video_gen_tasks')
            .update(updateData)
            .eq('id', task_id)
            .select()
            .single();

        if (error) {
            this.logger.error('更新任务状态失败', error);

            // 记录到错误审计系统
            await this.errorAuditService.logTaskError(
                existingTask.user_id,
                task_id,
                `更新任务状态失败: ${error.message}`,
                error,
                {
                    updateData,
                    existingTaskStatus: existingTask.status,
                    operation: 'finishVideoGenerationTask'
                },
                ErrorSeverity.HIGH
            );

            throw new BadRequestException('更新任务状态失败');
        }

        // 检查是否为fal.ai 400 422错误，如果是则不退款
        const isFalAi400Error = existingTask.handler === 'fal.ai' &&
            error_log &&
            error_log.message &&
            (error_log.message.includes('400') || error_log.message.includes('422'));

        // 如果任务失败，退还用户积分
        if (status === VideoTaskStatus.FAILED && !isFalAi400Error) {
            try {
                const userId = existingTask.user_id;
                const modelId = existingTask.input_params?.model_id;
                const duration = existingTask.input_params?.duration || VideoDuration.SHORT;
                const errorReason = error_log?.message || '视频生成失败';

                const refundResult = await this.refundCreditsForVideoGeneration(
                    userId,
                    task_id,
                    modelId,
                    errorReason,
                    duration
                );

                await this.supabase
                    .from('video_gen_tasks')
                    .update({
                        refund_tx_id: refundResult.transactionId // 添加退款交易ID
                    })
                    .eq('id', task_id);

            } catch (refundError) {
                // 记录错误但不抛出异常，避免影响正常流程
                this.logger.error(`退还积分失败: ${refundError.message}`, refundError.stack);

                // 记录到错误审计系统 - 这是一个严重错误，需要人工干预
                const userId = existingTask.user_id;
                const modelId = existingTask.input_params?.model_id;
                const errorId = await this.errorAuditService.logRefundError(
                    userId,
                    task_id,
                    `任务失败后退还积分出错: ${refundError.message}`,
                    refundError,
                    {
                        taskStatus: status,
                        modelId,
                        errorLog: error_log,
                        operation: 'finishVideoGenerationTask-refund'
                    },
                    ErrorSeverity.CRITICAL
                );

                this.logger.warn(`已记录退款失败到错误审计系统，错误ID: ${errorId}，请运营人员尽快处理`);
            }
        }

        // 如果任务完成，将视频信息存储到videos表中
        if (status === VideoTaskStatus.COMPLETED && output_result.video_url) {
            try {
                // 从任务中提取需要的信息
                const userId = existingTask.user_id;
                const prompt = existingTask.input_params?.prompt || '';
                const videoUrl = output_result.video_url; // 使用storage_path作为视频URL

                // 创建视频记录
                const { data: videoData, error: videoError } = await this.supabase
                    .from('videos')
                    .insert({
                        user_id: userId,
                        prompt: prompt,
                        url: videoUrl,
                        task_id: task_id,
                        input_params: existingTask.input_params,
                        source: existingTask.handler, // 标记为系统生成
                        created_at: new Date()
                    })
                    .select('id')
                    .single();

                if (videoError) {
                    this.logger.error(`将视频信息存储到videos表失败: ${videoError.message}`, videoError);

                    // 记录到错误审计系统
                    await this.errorAuditService.logTaskError(
                        userId,
                        task_id,
                        `将视频信息存储到videos表失败: ${videoError.message}`,
                        videoError,
                        {
                            storage_path: storage_path,
                            prompt: prompt,
                            operation: 'finishVideoGenerationTask-createVideo'
                        },
                        ErrorSeverity.HIGH
                    );
                } else {
                    this.logger.log(`成功将视频信息存储到videos表，视频ID: ${videoData.id}, 任务ID: ${task_id}`);
                }
            } catch (videoCreateError) {
                // 记录错误但不抛出异常，避免影响主流程
                this.logger.error(`创建视频记录时发生异常: ${videoCreateError.message}`, videoCreateError.stack);

                // 记录到错误审计系统
                await this.errorAuditService.logTaskError(
                    existingTask.user_id,
                    task_id,
                    `创建视频记录时发生异常: ${videoCreateError.message}`,
                    videoCreateError,
                    {
                        storage_path: storage_path,
                        operation: 'finishVideoGenerationTask-createVideo'
                    },
                    ErrorSeverity.HIGH
                );
            }
        }

        return data as VideoTaskDto;
    }

    /**
     * 根据用户ID查询任务列表
     * @param userId 用户ID
     * @param status 可选的状态过滤
     * @param limit 分页限制
     * @param offset 分页偏移
     * @returns 任务列表及总数
     */
    async getUserTasks(
        userId: string,
        status?: VideoTaskStatus,
        limit: number = 10,
        offset: number = 0
    ): Promise<{ tasks: VideoTaskDto[]; total: number }> {
        let query = this.supabase
            .from('video_gen_tasks')
            .select('*', { count: 'estimated' })
            .eq('user_id', userId)
            .order('created_at', { ascending: false });

        if (status) {
            query = query.eq('status', status);
        }

        const { data, error, count } = await query
            .range(offset, offset + limit - 1);

        if (error) {
            this.logger.error('获取用户任务列表失败', error);
            throw new BadRequestException('获取任务列表失败');
        }

        return {
            tasks: data as VideoTaskDto[],
            total: count || 0,
        };
    }

    /**
     * 根据ID获取任务详情
     * @param taskId 任务ID
     * @returns 任务详情
     */
    async getTaskById(taskId: string): Promise<VideoTaskDto | null> {
        try {
            const { data, error } = await this.supabase
                .from('video_gen_tasks')
                .select('*')
                .eq('id', taskId)
                .single();

            if (error || !data) {
                this.logger.error(`获取任务详情失败: ${error?.message}`, error);
                return null;
            }

            return data as VideoTaskDto;
        } catch (error) {
            this.logger.error(`获取任务详情出错: ${error.message}`, error.stack);
            return null;
        }
    }

    /**
     * 根据request_id获取任务详情
     * @param requestId 外部请求ID
     * @returns 任务详情
     */
    async getTaskByRequestId(requestId: string): Promise<VideoTaskDto | null> {
        try {
            const { data, error } = await this.supabase
                .from('video_gen_tasks')
                .select('*')
                .eq('request_id', requestId)
                .single();

            if (error || !data) {
                this.logger.error(`通过request_id获取任务详情失败: ${error?.message}`, error);
                return null;
            }

            return data as VideoTaskDto;
        } catch (error) {
            this.logger.error(`通过request_id获取任务详情出错: ${error.message}`, error.stack);
            return null;
        }
    }

    /**
     * 取消任务
     * @param taskId 任务ID
     * @param userId 用户ID（用于权限验证）
     * @returns 更新后的任务
     */
    async cancelTask(taskId: string, userId: string): Promise<VideoTaskDto> {
        // 检查任务是否存在并属于该用户
        const { data: existingTask, error: checkError } = await this.supabase
            .from('video_gen_tasks')
            .select('*')
            .eq('id', taskId)
            .eq('user_id', userId)
            .single();

        if (checkError || !existingTask) {
            this.logger.error(`任务不存在或无权限: ${taskId}`, checkError);
            throw new NotFoundException('任务不存在或无权限');
        }

        // 检查任务是否可以取消（只有pending或queued状态可以取消）
        if (![VideoTaskStatus.PENDING, VideoTaskStatus.QUEUED].includes(existingTask.status as VideoTaskStatus)) {
            throw new BadRequestException('当前任务状态无法取消');
        }

        // 更新任务状态为取消
        const { data, error } = await this.supabase
            .from('video_gen_tasks')
            .update({
                status: VideoTaskStatus.CANCELED,
                last_activity_at: new Date(),
            })
            .eq('id', taskId)
            .select()
            .single();

        if (error) {
            this.logger.error('取消任务失败', error);

            // 记录到错误审计系统
            await this.errorAuditService.logTaskError(
                userId,
                taskId,
                `取消任务失败: ${error.message}`,
                error,
                {
                    existingTaskStatus: existingTask.status,
                    operation: 'cancelTask'
                },
                ErrorSeverity.MEDIUM
            );

            throw new BadRequestException('取消任务失败');
        }

        // 退还用户积分
        try {
            const modelId = existingTask.input_params?.model_id;
            const duration = existingTask.input_params?.duration || VideoDuration.SHORT;

            if (modelId) {
                const refundResult = await this.refundCreditsForVideoGeneration(
                    userId,
                    taskId,
                    modelId,
                    '用户取消任务',
                    duration
                );

                // 更新任务记录中的refund_tx_id字段，记录退款交易ID
                try {
                    await this.supabase
                        .from('video_gen_tasks')
                        .update({
                            refund_tx_id: refundResult.transactionId // 添加退款交易ID
                        })
                        .eq('id', taskId);

                } catch (updateError) {
                    this.logger.error(`更新任务退款交易ID失败: ${updateError.message}`, updateError.stack);
                }
            } else {
                this.logger.error(`任务${taskId}缺少模型ID，无法退还积分`);
            }
        } catch (refundError) {
            // 记录错误但不抛出异常，避免影响正常流程
            this.logger.error(`退还积分失败: ${refundError.message}`, refundError.stack);

            // 记录到错误审计系统 - 这是一个严重错误，需要人工干预
            const modelId = existingTask.input_params?.model_id;
            const errorId = await this.errorAuditService.logRefundError(
                userId,
                taskId,
                `取消任务后退还积分出错: ${refundError.message}`,
                refundError,
                {
                    modelId,
                    operation: 'cancelTask-refund'
                },
                ErrorSeverity.CRITICAL
            );

            this.logger.warn(`已记录退款失败到错误审计系统，错误ID: ${errorId}，请运营人员尽快处理`);
        }

        return data as VideoTaskDto;
    }

    /**
     * 获取任务队列信息
     * @param taskId 任务ID
     * @returns 队列信息
     */
    async getQueueInfo(taskId: string): Promise<QueueInfoResponseDto> {
        try {
            // 调用数据库存储过程获取队列信息
            const { data, error } = await this.supabase.rpc('get_task_queue_info', {
                p_task_id: taskId
            });

            if (error) {
                this.logger.error(`调用存储过程获取队列信息失败: ${error.message}`, error);
                throw new BadRequestException('获取队列信息失败');
            }

            if (!data || data.length === 0) {
                this.logger.warn(`任务${taskId}不存在或不在队列中，返回数据为空`);
                throw new NotFoundException('任务不存在或不在队列中');
            }

            // 手动处理返回数据，确保类型匹配
            const queueInfo = data[0];

            // 从存储过程返回的数据中提取信息并转换类型
            const queue_position = Number(queueInfo.queue_position);
            const total_tasks_in_queue = Number(queueInfo.total_tasks_in_queue);
            const task_priority = Number(queueInfo.task_priority);
            const ahead_tasks_json = queueInfo.ahead_tasks_json || [];
            const avg_processing_time_seconds = Number(queueInfo.avg_processing_time_seconds);
            const is_processing = Boolean(queueInfo.is_processing);
            const started_at = queueInfo.started_at ? new Date(queueInfo.started_at) : null;

            // 计算预计等待和完成时间
            let estimatedStartTime: Date;
            let estimatedCompletionTime: Date;

            if (is_processing) {
                // 任务已在处理中，直接计算预计完成时间
                const now = new Date();
                estimatedStartTime = started_at || now;

                // 计算已处理时间（秒）
                const processedTimeSeconds = started_at ? (now.getTime() - started_at.getTime()) / 1000 : 0;

                // 计算当前任务的优先级因子
                const currentTaskPriorityFactor = 1 - (task_priority - 1) * 0.1;

                // 计算预计总处理时间
                const totalProcessingTimeSeconds = avg_processing_time_seconds * currentTaskPriorityFactor;

                // 计算剩余处理时间
                const remainingTimeSeconds = Math.max(0, totalProcessingTimeSeconds - processedTimeSeconds);

                // 计算预计完成时间
                estimatedCompletionTime = new Date(now.getTime() + remainingTimeSeconds * 1000);

                return {
                    task_id: taskId,
                    queue_position: 0, // 处理中的任务队列位置为0
                    total_tasks_in_queue: 0,
                    estimated_wait_time_seconds: 0, // 已经在处理中，等待时间为0
                    estimated_start_time: estimatedStartTime,
                    estimated_completion_time: estimatedCompletionTime,
                    is_processing: true,
                    elapsed_time_seconds: Math.round(processedTimeSeconds),
                    remaining_time_seconds: Math.round(remainingTimeSeconds)
                };
            } else {
                // 任务在队列中，计算等待时间和预计完成时间
                // 解析前面任务的JSON数据
                const tasksAhead = Array.isArray(ahead_tasks_json) ? ahead_tasks_json : [];

                // 计算总等待时间
                let totalEstimatedWaitTimeSeconds = 0;

                for (const task of tasksAhead) {
                    const taskPriority = Number(task.priority) || 1;
                    const priorityFactor = 1 - (taskPriority - 1) * 0.1; // 优先级越高，处理越快
                    totalEstimatedWaitTimeSeconds += avg_processing_time_seconds * priorityFactor;
                }

                // 计算当前任务预计开始和完成时间
                const now = new Date();
                estimatedStartTime = new Date(now.getTime() + totalEstimatedWaitTimeSeconds * 1000);

                // 当前任务的预计处理时间
                const currentTaskPriorityFactor = 1 - (task_priority - 1) * 0.1;
                const currentTaskEstimatedProcessTime = avg_processing_time_seconds * currentTaskPriorityFactor;

                estimatedCompletionTime = new Date(estimatedStartTime.getTime() + currentTaskEstimatedProcessTime * 1000);

                return {
                    task_id: taskId,
                    queue_position,
                    total_tasks_in_queue,
                    estimated_wait_time_seconds: Math.round(totalEstimatedWaitTimeSeconds),
                    estimated_start_time: estimatedStartTime,
                    estimated_completion_time: estimatedCompletionTime,
                    is_processing: false
                };
            }
        } catch (error) {
            this.logger.error(`获取队列信息时出错: ${error.message}`, error.stack);

            // 记录到错误审计系统 - 但仅作为低级别错误
            if (!(error instanceof NotFoundException || error instanceof BadRequestException)) {
                await this.errorAuditService.logTaskError(
                    '系统',
                    taskId,
                    `获取队列信息时出错: ${error.message}`,
                    error,
                    {
                        operation: 'getQueueInfo'
                    },
                    ErrorSeverity.LOW
                );
            }

            throw error;
        }
    }

    async tickProgress(body: TickProgressDto): Promise<boolean> {
        const { task_id, progress } = body;

        const { error } = await this.supabase
            .from('video_gen_tasks')
            .update({ progress })
            .eq('id', task_id)
            .select()
            .single();

        if (error) {
            this.logger.error('更新任务进度失败', error);
            throw new BadRequestException('更新任务进度失败');
        }

        return true;
    }

    /**
     * 使用DeepSeek API增强提示词
     * @param enhancePromptDto 原始提示词
     * @returns 增强后的提示词
     */
    async enhancePrompt(enhancePromptDto: EnhancePromptRequestDto): Promise<EnhancePromptResponseDto> {
        try {
            const { prompt } = enhancePromptDto;

            const response = await this.openaiClient.chat.completions.create({
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'system',
                        content: SystemPromptVideo
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                stream: false,
                temperature: 1.5
            });

            const enhancedPrompt = response.choices[0].message.content?.replace(/^"|"$/g, '') || '';

            return { enhancedPrompt };
        } catch (error) {
            this.logger.error(`增强提示词失败: ${error.message}`, error.stack);

            throw new BadRequestException('增强提示词失败');
        }
    }

    async handleFalWebhook(webhookData: FalAiWebhookRequestDto) {
        try {
            this.logger.log(`收到fal.ai webhook回调，请求ID: ${webhookData.request_id}, 状态: ${webhookData.status}`);

            // 通过request_id查找任务
            const task = await this.getTaskByRequestId(webhookData.request_id);

            // 检查任务是否存在
            if (!task) {
                this.logger.error(`找不到对应的任务，请求ID: ${webhookData.request_id}`);
                return { success: false, message: `找不到对应的任务，请求ID: ${webhookData.request_id}` };
            }

            const taskId = task.id;
            this.logger.log(`找到对应的任务，任务ID: ${taskId}`);

            // 检查任务的handler
            if (task.handler !== 'fal.ai') {
                this.logger.warn(`任务handler不匹配，期望fal.ai，实际为: ${task.handler}`);
                return { success: false, message: '任务handler不匹配' };
            }

            // 根据webhook响应类型处理任务
            let finishTaskData: any;

            // 1. 处理响应错误 - status为ERROR
            if (webhookData.status === 'ERROR') {
                finishTaskData = {
                    task_id: taskId,
                    status: VideoTaskStatus.FAILED,
                    storage_path: '',
                    error_log: {
                        message: webhookData.error || 'fal.ai任务失败',
                        code: 'FAL_AI_RESPONSE_ERROR',
                        details: webhookData.payload
                    }
                };
            }
            // 2. 处理负载错误 - status为OK但有payload_error
            else if (webhookData.status === 'OK' && webhookData.payload_error) {
                finishTaskData = {
                    task_id: taskId,
                    status: VideoTaskStatus.FAILED,
                    storage_path: '',
                    error_log: {
                        message: `Payload错误: ${webhookData.payload_error}`,
                        code: 'FAL_AI_PAYLOAD_ERROR',
                    }
                };
            }
            // 3. 处理成功结果 - status为OK且有payload
            else if (webhookData.status === 'OK' && webhookData.payload) {
                // 检查payload中是否包含视频URL
                if (webhookData.payload.video && webhookData.payload.video.url) {
                    finishTaskData = {
                        task_id: taskId,
                        status: VideoTaskStatus.COMPLETED,
                        storage_path: webhookData.payload.video.url,
                        output_result: {
                            video_url: webhookData.payload.video.url,
                            ...webhookData.payload.video
                        }
                    };
                } else {
                    // payload存在但缺少视频URL
                    finishTaskData = {
                        task_id: taskId,
                        status: VideoTaskStatus.FAILED,
                        storage_path: '',
                        error_log: {
                            message: '任务完成但缺少视频URL',
                            code: 'FAL_AI_MISSING_VIDEO_URL',
                            retryable: false,
                            details: webhookData.payload
                        }
                    };
                }
            }
            // 4. 处理其他未知情况
            else {
                finishTaskData = {
                    task_id: taskId,
                    status: VideoTaskStatus.FAILED,
                    storage_path: '',
                    error_log: {
                        message: '未知的webhook响应格式',
                        code: 'FAL_AI_UNKNOWN_RESPONSE',
                        retryable: false,
                        details: webhookData
                    }
                };
            }

            // 更新任务状态
            await this.finishVideoGenerationTask(finishTaskData);

            // 记录处理结果
            const resultMessage = finishTaskData.status === VideoTaskStatus.COMPLETED
                ? '任务成功完成'
                : `任务失败: ${finishTaskData.error_log?.message}`;

            this.logger.log(`处理fal.ai webhook完成，任务ID: ${taskId}, 结果: ${resultMessage}`);

            return {
                success: true,
                message: resultMessage
            };
        } catch (error) {
            this.logger.error(`处理fal.ai webhook回调出错: ${error.message}`, error.stack);
            return { success: false, message: `处理webhook出错: ${error.message}` };
        }
    }

    /**
     * 获取任务价格
     * @param taskPriceRequestDto 任务价格请求
     * @returns 任务价格信息
     */
    async getTaskPrice(taskPriceRequestDto: TaskPriceRequestDto): Promise<TaskPriceResponseDto> {
        try {
            const { model_id, duration = VideoDuration.SHORT } = taskPriceRequestDto;

            // 获取模型信息
            const model = await this.getModelInfo(model_id);

            // 计算价格，考虑视频时长
            const price = await this.calculateRequiredCredits(model, duration);

            const response: TaskPriceResponseDto = {
                model_id,
                price,
                duration,
                model_name: model.name || '未知模型',
                currency: 'credits'
            };

            return response;
        } catch (error) {
            this.logger.error(`获取任务价格失败: ${error.message}`, error.stack);

            // 如果是模型不存在的错误，直接抛出
            if (error instanceof NotFoundException) {
                throw error;
            }

            // 其他错误转换为BadRequestException
            throw new BadRequestException(`获取任务价格失败: ${error.message}`);
        }
    }
}