/**
 * 全局优惠券状态管理器
 * 防止多个组件同时调用相同的优惠券 API
 */

interface CouponCheckState {
    isCheckingStatus: boolean;
    isCheckingEligibility: boolean;
    lastStatusCheck: number;
    lastEligibilityCheck: number;
}

class CouponManager {
    private state: CouponCheckState = {
        isCheckingStatus: false,
        isCheckingEligibility: false,
        lastStatusCheck: 0,
        lastEligibilityCheck: 0
    };

    private readonly CACHE_DURATION = 5000; // 5秒内不重复检查

    /**
     * 检查是否可以执行状态检查
     */
    canCheckStatus(): boolean {
        const now = Date.now();
        return !this.state.isCheckingStatus && 
               (now - this.state.lastStatusCheck) > this.CACHE_DURATION;
    }

    /**
     * 检查是否可以执行资格检查
     */
    canCheckEligibility(): boolean {
        const now = Date.now();
        return !this.state.isCheckingEligibility && 
               (now - this.state.lastEligibilityCheck) > this.CACHE_DURATION;
    }

    /**
     * 标记状态检查开始
     */
    startStatusCheck(): void {
        this.state.isCheckingStatus = true;
        console.log('[CouponManager] Started status check');
    }

    /**
     * 标记状态检查结束
     */
    endStatusCheck(): void {
        this.state.isCheckingStatus = false;
        this.state.lastStatusCheck = Date.now();
        console.log('[CouponManager] Ended status check');
    }

    /**
     * 标记资格检查开始
     */
    startEligibilityCheck(): void {
        this.state.isCheckingEligibility = true;
        console.log('[CouponManager] Started eligibility check');
    }

    /**
     * 标记资格检查结束
     */
    endEligibilityCheck(): void {
        this.state.isCheckingEligibility = false;
        this.state.lastEligibilityCheck = Date.now();
        console.log('[CouponManager] Ended eligibility check');
    }

    /**
     * 重置所有状态
     */
    reset(): void {
        this.state = {
            isCheckingStatus: false,
            isCheckingEligibility: false,
            lastStatusCheck: 0,
            lastEligibilityCheck: 0
        };
        console.log('[CouponManager] Reset all states');
    }

    /**
     * 获取当前状态
     */
    getState(): Readonly<CouponCheckState> {
        return { ...this.state };
    }
}

// 全局实例
export const couponManager = new CouponManager();

// 在用户登出时重置状态
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        couponManager.reset();
    });
}
