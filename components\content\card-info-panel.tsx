import { useState } from "react"
import { <PERSON>geIn<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Wand2, Maximize2, Cpu, Scale } from "lucide-react"
import type { VideoInputParams } from "@/types/posts"
import { motion, AnimatePresence } from "framer-motion"

interface CardInfoPanelProps {
    inputParams?: VideoInputParams
    isExpanded: boolean
    showPrompt?: boolean
}

export function CardInfoPanel({ inputParams, isExpanded, showPrompt = true }: CardInfoPanelProps) {
    const [showFullPrompt, setShowFullPrompt] = useState(false)

    // 如果没有参数，返回空
    if (!inputParams) return null

    // 处理点击提示词展示
    const togglePrompt = (e: React.MouseEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setShowFullPrompt(!showFullPrompt)
    }

    // 简化参数显示
    const simplifyText = (text: string | undefined, maxLength: number = 15) => {
        if (!text) return "-";
        return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
    }

    return (
        <motion.div
            className="w-full text-white overflow-hidden rounded-md backdrop-blur-[2px]"
            initial={{ opacity: 0, height: 0 }}
            animate={{
                opacity: isExpanded ? 1 : 0,
                height: isExpanded ? "auto" : 0
            }}
            transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                opacity: { duration: 0.2 }
            }}
        >
            {/* 提示词区域 - 仅当showPrompt为true时显示 */}
            {showPrompt && (
                <motion.div
                    className="mb-2 overflow-hidden cursor-pointer bg-white/5 rounded-md p-1.5"
                    onClick={togglePrompt}
                    whileHover={{ backgroundColor: "rgba(255,255,255,0.1)" }}
                    transition={{ duration: 0.3 }}
                >
                    <div className="flex items-center gap-1 text-blue-200 text-xs mb-1">
                        <Wand2 size={12} className="text-indigo-300" />
                        <span className="font-medium">PROMPT</span>
                    </div>
                    <AnimatePresence initial={false}>
                        <motion.p
                            className="text-xs text-white/80 pl-1"
                            initial={{ height: "1.8rem" }}
                            animate={{
                                height: showFullPrompt ? "auto" : "1.8rem"
                            }}
                            transition={{ duration: 0.3 }}
                        >
                            {inputParams.prompt || "No prompt available"}
                        </motion.p>
                    </AnimatePresence>
                </motion.div>
            )}

            {/* 参数网格 - 使用更紧凑的布局 */}
            <div className="grid grid-cols-3 gap-1.5 text-xs">
                {/* 模型名称 */}
                <div className="bg-white/5 rounded-md p-1.5">
                    <div className="flex items-center gap-1 text-blue-300 mb-1">
                        <Cpu size={10} />
                        <span className="font-medium uppercase">MODEL</span>
                    </div>
                    <p className="text-white/80 pl-1 truncate">
                        {simplifyText(inputParams.model_name, 10) || "Reelmind"}
                    </p>
                </div>

                {/* 尺寸比例 */}
                <div className="bg-white/5 rounded-md p-1.5">
                    <div className="flex items-center gap-1 text-emerald-300 mb-1">
                        <Maximize2 size={10} />
                        <span className="font-medium uppercase">RATIO</span>
                    </div>
                    <p className="text-white/80 pl-1">
                        {inputParams.ratio || "1:1"}
                    </p>
                </div>

                {/* 创意指数 */}
                <div className="bg-white/5 rounded-md p-1.5">
                    <div className="flex items-center gap-1 text-purple-300 mb-1">
                        <Scale size={10} />
                        <span className="font-medium uppercase">GUIDANCE</span>
                    </div>
                    <p className="text-white/80 pl-1">
                        {inputParams.guidance_scale || "7.5"}
                    </p>
                </div>

                {/* 分辨率 - 仅在有值时显示 */}
                {inputParams.definition && (
                    <div className="bg-white/5 rounded-md p-1.5">
                        <div className="flex items-center gap-1 text-emerald-300 mb-1">
                            <BadgeInfo size={10} />
                            <span className="font-medium uppercase">DEFINITION</span>
                        </div>
                        <p className="text-white/80 pl-1">
                            {inputParams.definition || "Standard"}
                        </p>
                    </div>
                )}

                {/* 步数 */}
                <div className="bg-white/5 rounded-md p-1.5">
                    <div className="flex items-center gap-1 text-orange-300 mb-1">
                        <Layers size={10} />
                        <span className="font-medium uppercase">STEPS</span>
                    </div>
                    <p className="text-white/80 pl-1">
                        {inputParams.steps || "25"}
                    </p>
                </div>

                {/* 种子 */}
                <div className="bg-white/5 rounded-md p-1.5">
                    <div className="flex items-center gap-1 text-rose-300 mb-1">
                        <Hash size={10} />
                        <span className="font-medium uppercase">SEED</span>
                    </div>
                    <p className="text-white/80 pl-1 truncate">
                        {inputParams.seed !== undefined && inputParams.seed !== null 
                            ? (typeof inputParams.seed === 'number' || inputParams.seed.length <= 8 
                                ? inputParams.seed 
                                : inputParams.seed.substring(0, 8) + "...") 
                            : "Random"}
                    </p>
                </div>
            </div>
        </motion.div>
    )
} 