# 反滥用服务重构说明

## 背景

原有的 `SimpleAntiAbuseService` 存在以下严重问题：

1. **内存缓存不可靠**：服务重启后所有缓存丢失，用户可以重新领取积分
2. **多实例不一致**：多实例部署时缓存不同步，同一用户可以在不同实例上重复领取
3. **时间窗口漏洞**：24小时TTL设计有问题，用户可以等待过期后重新领取
4. **并发安全问题**：Map操作不是原子的，高并发下可能出现竞态条件
5. **数据库依赖不完整**：只在启动时加载24小时内的用户记录，更早的记录会被忽略

## 新设计方案

### 设计原则

1. **数据库为主**：主要依赖数据库约束确保可靠性
2. **原子性操作**：使用存储过程保证原子性操作
3. **无状态设计**：最小化内存缓存，避免多实例不一致
4. **数据持久化**：支持服务重启后数据持久化

### 核心改进

#### 1. 数据库表结构

```sql
-- 滥用检查日志表
CREATE TABLE abuse_logs (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    fingerprint_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    action VARCHAR(50) NOT NULL, -- 'claim_success', 'validation_passed', 'validation_failed'
    risk_score INTEGER DEFAULT 0,
    risk_factors JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. 索引优化

- 为常用查询路径创建复合索引
- 支持高效的时间窗口查询
- 优化设备指纹和IP地址查询

#### 3. 存储过程

- `check_user_claim()`: 快速检查用户是否已领取
- `get_fingerprint_usage_count()`: 获取设备指纹使用次数
- `get_ip_usage_count()`: 获取IP使用次数
- `log_abuse_check()`: 记录滥用检查日志

### 服务实现

#### AntiAbuseService 主要方法

1. **validateCreditClaim()**: 积分领取验证

   - 优先执行机器人检测（无需数据库查询）
   - 并行执行所有数据库查询（用户领取记录、设备指纹使用次数、IP使用次数）
   - 基于查询结果进行综合风险评估

2. **recordSuccessfulClaim()**: 记录成功领取

   - 记录到数据库日志表
   - 支持后续审计和分析

3. **getUserClaimStatus()**: 获取用户领取状态
   - 直接查询数据库，确保准确性
   - 不依赖内存缓存

## 部署步骤

### 1. 执行数据库脚本

```bash
# 执行新的数据库表和函数创建脚本
psql -f src/credits/sql/anti-abuse.sql
```

### 2. 配置Nginx代理（重要）

确保Nginx正确传递客户端真实IP，参考 `nginx-config-example.conf`：

```nginx
# 关键配置
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
```

### 3. 代码更新

代码已自动更新：

- `AntiAbuseService` 替换了 `SimpleAntiAbuseService`
- 更新了所有相关引用
- 移除了内存缓存相关代码
- **新增IP工具函数**：`src/common/utils/ip.utils.ts`
- **统一IP获取逻辑**：所有controller和middleware都使用统一的IP获取函数

### 4. 配置参数

可在 `AntiAbuseService` 中调整以下参数：

```typescript
private readonly MAX_CLAIMS_PER_FINGERPRINT = 3; // 每个设备指纹最多3次
private readonly MAX_CLAIMS_PER_IP = 5; // 每个IP最多5次
private readonly MIN_FINGERPRINT_CONFIDENCE = 0.6; // 最低指纹置信度
private readonly FINGERPRINT_WINDOW_HOURS = 24; // 设备指纹限制时间窗口
private readonly IP_WINDOW_HOURS = 24; // IP限制时间窗口
```

## 性能优化

### 1. 数据库优化

- 复合索引优化常用查询
- 存储过程减少网络往返
- 异步日志记录不阻塞主流程

### 2. 查询优化

- **并行查询**：使用 `Promise.all` 并行执行所有数据库查询，显著提升性能
- 使用 `LIMIT 1` 优化存在性检查
- 时间窗口查询使用索引
- 错误处理避免误杀正常用户
- 机器人检测优先执行，避免不必要的数据库查询

### 3. 监控和维护

- `abuse_stats` 视图提供统计信息
- `cleanup_abuse_logs()` 函数定期清理旧数据
- 详细的错误日志和监控

## 可靠性保证

### 1. 数据一致性

- 所有关键检查都基于数据库查询
- 不依赖可能丢失的内存状态
- 支持多实例部署

### 2. 错误处理

- 数据库查询失败时返回保守结果
- 详细的错误日志记录（仅服务端）
- 优雅降级，避免系统崩溃
- **安全的错误信息**：对用户返回模糊的错误信息，避免暴露检测逻辑

### 3. 审计能力

- 所有操作都有日志记录
- 支持后续分析和调试
- 风险因素详细记录

## 监控建议

### 1. 关键指标

- 每日领取成功/失败数量
- 风险评分分布
- 设备指纹和IP重复使用情况

### 2. 告警设置

- 异常高的风险评分
- 数据库查询失败率
- 单个设备/IP的异常活动

### 3. 定期维护

- 运行 `cleanup_abuse_logs()` 清理旧数据
- 监控数据库表大小和性能
- 定期检查索引使用情况

## 安全性设计

### 1. 信息泄露防护

- **模糊错误信息**：对用户返回统一的模糊错误信息，不暴露具体检测逻辑
- **详细日志分离**：详细的检测信息只记录在服务端日志中，不返回给客户端
- **风险因素隐藏**：`riskFactors` 字段仅用于内部分析，不暴露给攻击者

### 2. 常见错误信息映射

| 实际原因     | 返回给用户的信息       |
| ------------ | ---------------------- |
| 机器人检测   | "无法完成验证"         |
| 重复领取     | "无法重复领取"         |
| 风险评分过高 | "当前无法完成操作"     |
| 数据库错误   | "系统繁忙，请稍后重试" |
| 系统异常     | "系统繁忙，请稍后重试" |

### 3. 攻击者分析难度

- 所有拒绝情况都返回相似的错误信息
- 无法通过错误信息推断具体的检测规则
- 增加了攻击者进行规律分析的难度

## IP获取优化

### 1. 代理环境下的IP获取

在Nginx代理环境下，系统按以下优先级获取真实客户端IP：

1. **X-Forwarded-For** (最常用，取第一个IP)
2. **X-Real-IP** (Nginx常用)
3. **X-Client-IP** (其他代理)
4. **CF-Connecting-IP** (Cloudflare)
5. **req.ip** (Express默认)
6. **connection.remoteAddress** (备用)

### 2. IP验证机制

- **格式验证**：支持IPv4和IPv6格式
- **有效性检查**：过滤无效IP和localhost
- **内网IP处理**：保留内网IP用于测试环境
- **端口号处理**：自动移除端口号部分

### 3. 常见问题解决

| 问题            | 原因              | 解决方案                       |
| --------------- | ----------------- | ------------------------------ |
| 获取到127.0.0.1 | Nginx未配置代理头 | 配置X-Real-IP和X-Forwarded-For |
| 获取到内网IP    | 多层代理          | 检查X-Forwarded-For链          |
| IP格式异常      | 包含端口号        | 自动分离IP和端口               |

### 4. IP工具函数使用

```typescript
import {
  getRealClientIP,
  isValidIP,
  isPublicIP,
} from '../common/utils/ip.utils';

// 在Controller中使用
const clientIP = getRealClientIP(req);

// 验证IP格式
if (isValidIP(clientIP)) {
  console.log('有效的IP地址');
}

// 判断是否为公网IP
if (isPublicIP(clientIP)) {
  console.log('公网IP地址');
}
```

## 迁移注意事项

1. **数据迁移**：旧的内存缓存数据无法迁移，但这不影响系统正常运行
2. **配置调整**：可能需要根据实际使用情况调整风险评分阈值
3. **监控更新**：更新监控脚本以适应新的数据库表结构
4. **测试验证**：在生产环境部署前充分测试各种边界情况

## 总结

新的反滥用服务通过以下方式解决了原有问题：

- ✅ **可靠性**：基于数据库的持久化存储
- ✅ **一致性**：多实例部署时数据一致
- ✅ **可扩展性**：支持水平扩展
- ✅ **可维护性**：清晰的数据结构和日志
- ✅ **可监控性**：完整的统计和审计能力

这个重构大大提高了系统的可靠性和可维护性，为防止积分滥用提供了坚实的基础。
