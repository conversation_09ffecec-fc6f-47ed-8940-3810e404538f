import { Module, forwardRef } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { MembershipCreditsScheduler } from './membership-credits.scheduler';
import { CreditsModule } from '../credits/credits.module';

@Module({
    imports: [
        ScheduleModule.forRoot(),
        forwardRef(() => CreditsModule),
    ],
    providers: [
        MembershipCreditsScheduler
    ],
    exports: [
        MembershipCreditsScheduler
    ]
})
export class SchedulerModule {} 