import { useQuery } from "@tanstack/react-query"
import { modelApi } from "@/lib/api/model"
import type { Model } from "@/types/model"
import { Effect } from "@/types/model"
import { effectApi } from '@/lib/api/effect'
import { generationApi } from '@/lib/api/generation'
import type { APIDuration } from '@/types/video'

// 准备模型数据，适配前端组件需要的格式
const prepareModelData = (model: Model): Model => {
    return {
        ...model,
        // 使用默认封面图片替代null值
        cover_img: model.cover_img || '/placeholder.svg?height=400&width=300&text=Model',
        // 将model_type复制到type属性，并转为小写(兼容前端现有代码)
        type: model.model_type.toLowerCase(),
        // 为新模型添加标签
        tags: ['AI'],
        // 为最近30天添加的模型添加early access标记
    };
}

// 获取模型列表的查询
export const useModelList = () => {
    return useQuery<{ models: Model[] }>({
        queryKey: ["models"],
        queryFn: async () => {
            const modelData = await modelApi.getModels();

            // 处理后端返回的数据，适配前端组件需要的格式
            const processedModels = modelData.models.map(model => {
                // 处理模型数据
                const processedModel = prepareModelData(model);

                // 检查是否为fal.ai模型（source为fal.ai）
                if (processedModel.source === 'fal.ai') {
                    // 在meta中标记为fal.ai模型，以便在生成视频时使用fal.ai API
                    processedModel.metadata = {
                        ...processedModel.metadata,
                        is_fal_ai: true
                    };
                }

                return processedModel;
            });

            return {
                models: processedModels
            };
        },
        staleTime: 1000 * 60 * 5, // 5分钟
    })
}

// 加载任务参数的查询
export const useTaskParams = (remixTaskId: string | null) => {
    return useQuery({
        queryKey: ["taskParams", remixTaskId],
        queryFn: async () => {
            if (!remixTaskId) {
                throw new Error("任务ID不能为空");
            }
            // 这里需要调整为实际的API
            const task = await fetch(`/api/tasks/${remixTaskId}`).then(res => res.json());
            return task;
        },
        enabled: !!remixTaskId,
        staleTime: 1000 * 60 * 5, // 5分钟
    })
}

// 获取效果列表的查询hook
export function useEffectList() {
    return useQuery<{ effects: Effect[] }>({
        queryKey: ["effects"],
        queryFn: async () => {
            try {
                const effectData = await effectApi.getEffects();

                return {
                    effects: effectData
                };
            } catch (error) {
                console.error("获取效果列表失败:", error);
                return { effects: [] };
            }
        },
        staleTime: 1000 * 60 * 5, // 5分钟
        // 如果API请求失败，返回空数组而不是抛出错误
        retry: 1,
    });
}

// 获取特定模型的查询hook
export function useModelById(modelId: string | null) {
    return useQuery<Model | null>({
        queryKey: ["model", modelId],
        queryFn: async () => {
            if (!modelId) return null;

            try {
                const model = await modelApi.getModelById(modelId);

                // 处理模型数据
                if (model) {
                    const processedModel = prepareModelData(model);

                    // 检查是否为fal.ai模型
                    if (processedModel.source === 'fal.ai') {
                        processedModel.metadata = {
                            ...processedModel.metadata,
                            is_fal_ai: true
                        };
                    }

                    return processedModel;
                }

                return null;
            } catch (error) {
                console.error(`获取模型(ID: ${modelId})失败:`, error);
                return null;
            }
        },
        enabled: !!modelId, // 只有当modelId存在时才启用查询
        staleTime: 1000 * 60 * 5, // 5分钟
    });
}

// 获取任务价格的查询hook
export function useTaskPrice(modelId: string | null, duration: string) {
    return useQuery({
        queryKey: ["taskPrice", modelId, duration],
        queryFn: async () => {
            if (!modelId) return null;

            try {
                // 调用API获取价格
                const priceData = await generationApi.getTaskPrice(modelId, duration as APIDuration);
                return priceData;
            } catch (error) {
                console.error(`获取任务价格失败:`, error);
                return null;
            }
        },
        enabled: !!modelId, // 只有当modelId存在时才启用查询
        staleTime: 1000 * 60, // 1分钟
    });
}