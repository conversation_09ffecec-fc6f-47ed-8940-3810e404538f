import "./globals.css"
import '../styles/mobile-styles.css'
import '../styles/coming-soon-animations.css'
import { ThemeContextProvider } from "@/contexts/theme-context";
import { AuthProvider } from "@/contexts/auth-context";
import { ToastProvider } from "@/components/ui/toast";
import { VideoModalProvider } from "../providers/VideoModalProvider";
import { QueryProvider } from "@/providers/query-provider";
import { DeviceProvider } from "@/contexts/DeviceContext";
import { DeviceSync } from "@/components/device/device-sync";
import { Metadata, Viewport } from "next";
import { LoginDialog } from "@/components/auth/login-dialog";
import { Montserrat } from "next/font/google";
import { Header } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import GoogleAnalytics from "@/components/analytics/GoogleAnalytics";
import { Analytics } from "@vercel/analytics/next"
import { SpeedInsights } from '@vercel/speed-insights/next';

const montserrat = Montserrat({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
})

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover"
};

export const metadata: Metadata = {
  title: {
    default: "ReelMind - Open Source AI Video Models Community",
    template: "%s | ReelMind"
  },
  description: "ReelMind is an open-source AI video models community for AI creators to generate and share AIGC videos.Free AI image generator. Free AI art generator. Free AI video generator. 100+ models and styles to choose from. 10+ fun effects to choose from. Train your personalized model. Most popular AI apps: inpainting, outpainting, model fine-tuning, text to video, image to video, video to video and more!",
  keywords: ["AI video", "AI video generator", "AI model", "AI-generated videos", "video creation", "text to video", "image to video", "short films"],
  authors: [{ name: "ReelMind Team" }],
  creator: "ReelMind",
  publisher: "ReelMind",
  robots: {
    index: true,
    follow: true,
  },
  metadataBase: new URL("https://reelmind.ai"),
  alternates: {
    languages: {
      "en-US": "/",
    },
  },
  openGraph: {
    type: "website",
    locale: "en-US",
    url: "https://reelmind.ai",
    title: "ReelMind - Open Source AI Video Models Community",
    description: "The platform for AI creators to generate and share AIGC videos",
    siteName: "ReelMind",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "ReelMind - Open Source AI Video Models Community",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "ReelMind - Open Source AI Video Models Community",
    description: "The platform for AI creators to generate and share AIGC videos",
    images: ["/og-image.png"],
  },
  icons: {
    icon: "/favicon.ico",
    apple: "/apple-icon.png",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="alternate" hrefLang="en" href="https://reelmind.ai" />
        <link rel="alternate" hrefLang="x-default" href="https://reelmind.ai" />
        <GoogleAnalytics />
      </head>
      <body className={`overflow-x-hidden ${montserrat.className}`}>
        <ThemeContextProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <QueryProvider>
              <DeviceProvider>
                <DeviceSync />
                <ToastProvider>
                  <VideoModalProvider>
                    <div className="h-screen bg-background flex flex-col overflow-hidden">
                      <Header />
                      <div className="pt-1 mt-[var(--header-height)] flex-1 flex w-full overflow-hidden transition-all duration-300">
                        <Sidebar />
                        <main className="w-full h-full overflow-y-auto">{children}</main>
                      </div>
                    </div>
                    <LoginDialog />
                  </VideoModalProvider>
                </ToastProvider>
              </DeviceProvider>
            </QueryProvider>
          </AuthProvider>
        </ThemeContextProvider>

        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  )
}