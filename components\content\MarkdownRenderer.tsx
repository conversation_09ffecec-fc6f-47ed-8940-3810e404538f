import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import Image from 'next/image';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/cjs/styles/prism';

interface MarkdownRendererProps {
  content: string;
  noTitle: boolean;
}

export default function MarkdownRenderer({ content, noTitle = false }: MarkdownRendererProps) {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      rehypePlugins={[rehypeRaw, rehypeSanitize]}
      components={{
        img: ({ node, ...props }) => {
          const { src, alt } = props;
          if (!src) return null;

          return (
            <div className="my-8 relative rounded-lg overflow-hidden">
              <div className="relative w-full" style={{
                aspectRatio: '16/9',
                maxHeight: 'calc(100vh - 200px)'
              }}>
                <Image
                  src={src}
                  alt={alt || ''}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1000px"
                />
              </div>
              {alt && (
                <p className="text-center text-sm text-gray-500 mt-2 italic">
                  {alt}
                </p>
              )}
            </div>
          );
        },
        a: ({ node, ...props }) => {
          return (
            <a
              {...props}
              className="text-[color:var(--tw-prose-links)] hover:opacity-80 transition-colors font-medium underline underline-offset-2"
              target="_blank"
              rel="noopener noreferrer"
            />
          );
        },
        h1: ({ node, ...props }) => (
          noTitle ? null : <h1 {...props} className="text-3xl font-bold mt-10 mb-4 text-gray-900 dark:text-white" />
        ),
        h2: ({ node, ...props }) => (
          <h2 {...props} className="text-2xl font-bold mt-8 mb-4 text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2" />
        ),
        h3: ({ node, ...props }) => (
          <h3 {...props} className="text-xl font-bold mt-6 mb-3 text-gray-900 dark:text-white" />
        ),
        h4: ({ node, ...props }) => (
          <h4 {...props} className="text-lg font-bold mt-5 mb-2 text-gray-900 dark:text-white" />
        ),
        p: ({ node, ...props }) => (
          <p {...props} className="mb-6 text-gray-700 dark:text-gray-300 leading-relaxed" />
        ),
        ul: ({ node, ...props }) => (
          <ul {...props} className="list-disc pl-8 mb-6 text-gray-700 dark:text-gray-300 space-y-2" />
        ),
        ol: ({ node, ...props }) => (
          <ol {...props} className="list-decimal pl-8 mb-6 text-gray-700 dark:text-gray-300 space-y-2" />
        ),
        li: ({ node, ...props }) => (
          <li {...props} className="mb-1" />
        ),
        blockquote: ({ node, ...props }) => (
          <blockquote {...props} className="border-l-4 border-primary-500 pl-4 italic my-6 text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 py-2 px-3 rounded-r-md" />
        ),
        hr: ({ node, ...props }) => (
          <hr {...props} className="my-8 border-t border-gray-200 dark:border-gray-700" />
        ),
        table: ({ node, ...props }) => (
          <div className="overflow-x-auto my-6">
            <table {...props} className="w-full border-collapse text-gray-700 dark:text-gray-300" />
          </div>
        ),
        thead: ({ node, ...props }) => (
          <thead {...props} className="bg-gray-100 dark:bg-gray-800" />
        ),
        th: ({ node, ...props }) => (
          <th {...props} className="border border-gray-200 dark:border-gray-700 px-4 py-2 text-left" />
        ),
        td: ({ node, ...props }) => (
          <td {...props} className="border border-gray-200 dark:border-gray-700 px-4 py-2" />
        ),
        code: ({ node, inline, className, children, ...props }: any) => {
          const match = /language-(\w+)/.exec(className || '');
          const language = match ? match[1] : '';

          return !inline && language ? (
            <div className="my-6 rounded-md overflow-hidden">
              <div className="flex items-center justify-between bg-gray-800 px-4 py-2 text-xs text-gray-200">
                <span>{language}</span>
              </div>
              <SyntaxHighlighter
                style={oneDark}
                language={language}
                PreTag="div"
                className="!mt-0 !rounded-t-none"
                showLineNumbers
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            </div>
          ) : (
            <code {...props} className="bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-sm font-mono text-gray-800 dark:text-gray-200">
              {children}
            </code>
          );
        },
        pre: ({ node, ...props }) => (
          <pre {...props} className="overflow-auto" />
        ),
      }}
    >
      {content}
    </ReactMarkdown>
  );
}
