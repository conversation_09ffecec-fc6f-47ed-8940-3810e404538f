# Contact Page Refactor

## 概述

本次重构完全重新设计了contact页面，从一个复杂的营销页面转变为一个简洁、功能性的用户反馈系统。

## 主要改进

### 🎨 设计风格
- **极简设计**: 采用项目的zen-style设计风格，去除了冗余的装饰元素
- **统一主题**: 使用项目的暗色主题和配色方案
- **响应式布局**: 优化了移动端和桌面端的显示效果
- **一致性**: 与项目整体UI风格保持一致

### 🔧 技术栈升级
- **React Query**: 用于API调用和状态管理
- **自定义Hook**: 使用`useFeedbackForm`管理表单逻辑
- **shadcn/ui组件**: 使用项目统一的UI组件库
- **Toast通知**: 替代原始的alert弹窗
- **TypeScript**: 完整的类型安全支持

### 📝 功能完善
- **用户认证集成**: 通过token自动获取用户信息，无需手动填写姓名和邮箱
- **反馈分类**: 支持多种反馈类型（Bug报告、功能请求、技术支持等）
- **表单验证**: 客户端和服务端双重验证
- **错误处理**: 统一的错误处理和用户友好的错误提示
- **加载状态**: 清晰的加载状态指示

### 🗑️ 删除冗余内容
- 移除了联系方式信息（邮箱、电话、地址）
- 删除了营业时间显示
- 去除了社交媒体链接
- 移除了FAQ部分
- 简化了页面结构

## 文件结构

```
app/contact/
├── page.tsx              # 主页面组件
├── layout.tsx            # 页面布局和元数据
└── README.md             # 本文档

hooks/
└── use-feedback-form.ts  # 反馈表单自定义Hook

lib/api/
├── feedback.ts           # 反馈API接口
├── client.ts            # API客户端（已存在）
└── index.ts             # API导出（已更新）

app/api/feedback/
└── route.ts             # Next.js API路由
```

## API接口

### 反馈类型
- `GENERAL_FEEDBACK`: 一般反馈
- `BUG_REPORT`: Bug报告
- `FEATURE_REQUEST`: 功能请求
- `TECHNICAL_SUPPORT`: 技术支持
- `OTHER`: 其他

### 主要接口
- `POST /api/feedback` - 创建反馈
- `GET /api/feedback/user` - 获取用户反馈列表
- `GET /api/feedback/:id` - 获取反馈详情
- `PUT /api/feedback/:id/status` - 更新反馈状态
- `DELETE /api/feedback/:id` - 删除反馈

## 使用方式

### 用户端
1. 用户需要先登录才能提交反馈
2. 选择反馈类型
3. 填写主题和详细信息
4. 提交后会收到确认通知

### 开发者
```typescript
import { useFeedbackForm } from '@/hooks/use-feedback-form';

const MyComponent = () => {
  const {
    formState,
    isSubmitting,
    handleFieldChange,
    handleSubmit,
    getFieldError,
  } = useFeedbackForm();

  // 使用表单状态和方法
};
```

## 表单验证

### 客户端验证
- 主题：5-200字符
- 消息：10-2000字符
- 必填字段检查

### 服务端验证
- 用户认证检查
- 数据格式验证
- 安全性检查

## 错误处理

- **认证错误**: 提示用户登录
- **验证错误**: 显示具体的字段错误
- **网络错误**: 显示友好的错误信息
- **服务器错误**: 记录日志并显示通用错误信息

## 性能优化

- **防抖处理**: 避免频繁的API调用
- **缓存管理**: 使用React Query缓存数据
- **懒加载**: 组件按需加载
- **代码分割**: 优化打包大小

## 安全考虑

- **用户认证**: 所有API调用都需要有效的JWT token
- **输入验证**: 防止XSS和注入攻击
- **速率限制**: 防止垃圾邮件和滥用
- **数据清理**: 自动清理和格式化用户输入

## 后续计划

### 短期
- [ ] 添加文件上传功能（截图、日志文件）
- [ ] 实现反馈状态跟踪
- [ ] 添加邮件通知功能

### 长期
- [ ] 管理员反馈管理界面
- [ ] 反馈分析和统计
- [ ] 自动分类和优先级设置
- [ ] 集成客服系统

## 测试

### 手动测试
1. 访问 `/contact` 页面
2. 测试未登录状态的提示
3. 登录后测试表单提交
4. 测试各种验证场景

### 自动化测试
```bash
# 运行测试
pnpm test contact

# 运行E2E测试
pnpm test:e2e contact
```

## 部署注意事项

1. 确保环境变量 `REELAPI_SERVER_URL` 已配置
2. 后端需要实现对应的反馈API接口
3. 数据库需要创建反馈相关的表结构
4. 配置邮件服务（如果需要邮件通知）

## 贡献指南

1. 遵循项目的代码规范
2. 添加适当的TypeScript类型
3. 编写测试用例
4. 更新相关文档
