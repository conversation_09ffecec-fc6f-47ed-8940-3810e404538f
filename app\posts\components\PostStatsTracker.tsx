'use client';

import { useEffect } from 'react';
import { usePostStats } from '@/hooks/use-post-stats';
import { PostItemDto } from '@/types/posts';

interface PostStatsTrackerProps {
  post: PostItemDto;
}

/**
 * 帖子统计跟踪组件
 * 用于在帖子详情页记录浏览量
 * 这是一个客户端组件，不会影响服务端渲染
 */
export function PostStatsTracker({ post }: PostStatsTrackerProps) {
  const { recordView, isStatsEnabled } = usePostStats();

  // 在组件挂载时记录浏览量，但仅在统计功能启用时执行
  useEffect(() => {
    if (post?.id && isStatsEnabled) {
      // 使用setTimeout延迟记录，确保不影响页面加载性能
      const timer = setTimeout(() => {
        recordView(post.id);
      });

      return () => clearTimeout(timer);
    }
  }, [post?.id, recordView, isStatsEnabled]);

  // 这是一个无UI组件，不渲染任何内容
  return null;
}
