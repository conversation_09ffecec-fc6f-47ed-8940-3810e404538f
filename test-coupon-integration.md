# 优惠券系统集成测试指南（使用固定Stripe优惠券）

## 系统配置

### Stripe优惠券配置

- 优惠券ID: `Grvt44lb`
- 类型: 首月7折优惠券
- 配置文件: `src/payment/config/stripe-coupons.config.ts`

## 测试流程

### 1. 领取优惠券

```bash
# 检查用户优惠券状态
curl -X GET "http://localhost:3000/coupon/status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 领取首月7折优惠券
curl -X POST "http://localhost:3000/coupon/claim" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"coupon_type": "first_month_discount"}'
```

### 2. 计算优惠价格

```bash
# 计算应用优惠券后的价格
curl -X POST "http://localhost:3000/coupon/calculate-price" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "original_price": 999,
    "coupon_id": "YOUR_COUPON_ID"
  }'
```

### 3. 创建带优惠券的会员订阅

```bash
# 创建会员订阅（带优惠券）
curl -X POST "http://localhost:3000/membership/subscribe" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "price_id": "price_1234567890",
    "billing_cycle": "monthly",
    "coupon_id": "YOUR_COUPON_ID"
  }'
```

### 4. 查看优惠券使用历史

```bash
# 获取用户优惠券列表
curl -X GET "http://localhost:3000/coupon/user-coupons" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 预期结果

### 1. 优惠券状态检查

- 新用户应该能够领取首月7折优惠券
- 已购买过会员的用户不能领取

### 2. 价格计算

- 原价999元，应用30%折扣后为699.3元
- 返回详细的价格计算信息

### 3. 支付流程

- 创建Stripe会话时应用优惠券折扣
- 支付完成后优惠券状态变为"已使用"
- 创建优惠券使用历史记录

### 4. 数据库验证

检查以下表的数据：

- `user_coupons`: 优惠券状态更新
- `payments`: 支付记录包含优惠券信息

## 系统验证

### 启动时验证

系统启动时会自动验证Stripe优惠券配置：

```
开始验证Stripe优惠券配置...
✓ 优惠券 FIRST_MONTH_DISCOUNT 验证成功
Stripe优惠券验证完成: 1 个有效, 0 个无效
```

### 手动健康检查

可以通过StripeCouponValidatorService进行手动验证

## 注意事项

1. **固定优惠券ID**：使用预先在Stripe中创建的优惠券 `Grvt44lb`
2. **优惠券只能使用一次**：首月7折优惠券只能在首次购买会员时使用
3. **过期时间**：优惠券在领取后24小时内有效
4. **用户资格**：只有从未购买过会员的用户才能领取首月折扣优惠券
5. **Stripe集成**：直接使用Stripe中的固定优惠券，不再动态创建
6. **性能优化**：减少了API调用，提高了支付流程的性能
