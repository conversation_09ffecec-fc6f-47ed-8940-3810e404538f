# ReelMind 积分系统文档

本文档概述了 **Credits** 系统在 ReelMind 平台上的设计与实现指南。文档内容涵盖总体架构、业务逻辑、API 接口、数据库设计以及集成流程，旨在为前端与后端开发提供指导。

---

## 1. 概述

**Credits** 是平台内用于生成 AI 视频或图片的虚拟积分。用户可通过以下方式获得积分：

- **会员赠送**：订阅会员每月获得固定额度的积分。
- **直接购买**：用户可以直接充值积分（转换示例：1 个货币单位 = 100 积分）。
- **平台奖励**：通过平台活动、邀请、竞赛等方式获得的奖励积分。

系统设计支持订阅制和按次消费两种模式。

---

## 2. 关键术语

- **积分（Credit）**：用于生成 AI 内容的基本计量单位。
- **会员**：订阅制用户，每月享有固定积分额度。
- **直接购买**：用户通过充值方式获取积分。
- **奖励**：平台因活动或促销发放的额外积分。

---

## 3. 系统架构

### 3.1 组件

- **前端**：

  - 显示积分余额及交易记录。
  - 提供购买积分、查看会员详情和启动内容生成的用户界面。
  - 实时反馈积分消费情况。

- **后端**：

  - 实现管理积分交易的 RESTful API 接口。
  - 处理积分的发放、购买、消费和奖励等操作。
  - 执行业务逻辑和验证各类交易。

- **数据库**：
  - 存储用户数据、会员信息以及所有积分交易记录。
  - 确保事务的 ACID 特性，以保证数据一致性和可审计性。

---

## 4. 积分获取方式详情

### 4.1 每月会员赠送

- **描述**：
  - 订阅会员在每个计费周期开始时自动获得预设数量的积分。
  - PRO 会员：每月赠送 10USD 等值(1000Credits)
  - MAX 会员：每月赠送 20USD 等值(2000Credits)
- **要点**：
  - 自动发放机制，每月定时发放积分。
  - 会员套餐决定赠送积分的具体数量。
  - 数值可根据 membership-interest 的设定灵活配置

### 4.2 直接购买

- **描述**：
  - 用户可以通过充值直接获得额外的 Credits
- **转换示例**：
  - 1 USD = 100 Credits
- **要点**：
  - 购买页面提供几个面额的 Credits Pack 方便用户快捷购买：
    - 5$等值 500Credits
    - 20$等值 2000Credits
    - 50$等值 5000Credits
    - 100$等值 10000Credits
  - 集成支付网关以处理购买流程。
  - 记录所有交易，便于审计及余额更新。

### 4.3 平台奖励

- **描述**：
  - 针对平台活动（如邀请、竞赛等）发放的奖励积分。
- **要点**：
  - 奖励可以自动发放或通过后台管理手动发放。
  - 遵循平台规定的条件与限制。

### 4.4 新用户注册

- **描述**
- 新用户注册时，赠送 100 积分
- 一次性

### 4.5 发帖

- **描述**
- 用户成功发布一条贴文（post）时，奖励 50 Credits
- 每天仅有一次

---

## 5. 积分消耗

| 功能               | 消耗量     | 说明                         |
| ------------------ | ---------- | ---------------------------- |
| 标准视频生成(480P) | 50 Credits | 基础功能（480P，最长 10 秒） |
| 高清生成（720P）   | 80 Credits | 基础功能（720P，最长 10 秒） |

---

## 6. 业务逻辑

### 6.1 每月会员赠送

触发条件：每个计费周期开始时自动执行。
验证：确认用户的会员状态有效。
操作：将预设的积分数量添加到用户账户，并记录交易。

### 6.2 直接购买流程

支付处理：验证支付信息并将货币转换为积分。
转换逻辑：应用设定的转换比例（例如 1:100）。
操作：更新用户积分余额，并记录购买交易。

### 6.3 内容生成（消费积分）

预检查：验证用户是否拥有足够的积分。
操作：扣除生成内容所需积分，并记录消费交易。
错误处理：若积分不足，则返回相应错误信息。

### 6.4 奖励积分

资格判断：确认用户是否符合奖励条件（如邀请、竞赛等）。
操作：更新用户积分余额，并记录奖励交易。
审核流程：可选的手动审核奖励发放流程。

---

## 7. 错误处理与安全性

### 7.1 错误处理

余额不足：当用户尝试消费超出余额的积分时，返回明确错误提示。
输入验证：确保所有 API 输入经过严格验证，防止注入攻击和数据不一致。
事务原子性：使用数据库事务确保积分更新与交易记录同时成功。

### 7.2 安全性考虑

认证：所有 API 接口均需用户认证（例如 JWT 令牌）。
授权：确保用户只能操作自己的积分账户。
审计日志：记录所有积分交易，便于后续审计和问题排查。
数据加密：在数据传输中使用 HTTPS，并对敏感数据进行存储加密。

## 生产实践建议

独立余额表（推荐）
实现方式
维护一个独立的 balance 表（或用户表的余额字段），直接记录当前用户积分余额。

积分变动时：原子性更新余额 + 记录流水日志。

数据结构示例
sql
Copy
-- 用户余额表（users）
CREATE TABLE users (
user_id UUID PRIMARY KEY,
credits INT NOT NULL DEFAULT 100 -- 当前余额
);

-- 积分流水表（credit_transactions）
CREATE TABLE credit_transactions (
transaction_id UUID PRIMARY KEY,
user_id UUID REFERENCES users(user_id),
delta INT NOT NULL, -- 变动值（+/-）
reason VARCHAR(50), -- 操作类型（如 "video_generate"）
created_at TIMESTAMPTZ DEFAULT NOW()
);
优势
高性能：查询余额只需一次 SELECT（时间复杂度 O(1)）。

事务安全：通过数据库事务保证余额更新和流水记录的原子性。

sql
Copy
BEGIN;
UPDATE users SET credits = credits + 50 WHERE user_id = 'xxx';
INSERT INTO credit_transactions (...) VALUES (...);
COMMIT;
监控友好：可直接对余额设置阈值告警（如负数异常）。

劣势
需处理并发更新（如使用 SELECT FOR UPDATE 或乐观锁）。

需定期对账（验证 balance = SUM(transactions)）。

1. 核心原则
   使用独立余额字段 + 记录完整流水（二者缺一不可）。

通过事务保证一致性（余额更新和流水记录必须原子化）。

2. 并发控制
   悲观锁（适合高并发）：

sql
Copy
BEGIN;
SELECT credits FROM users WHERE user_id = 'xxx' FOR UPDATE;
-- 计算新余额并更新
COMMIT;
乐观锁（适合低频操作）：

sql
Copy
UPDATE users
SET credits = credits + 50, version = version + 1
WHERE user_id = 'xxx' AND version = 3; -- 检查版本号 3. 幂等性设计
在流水表中添加 request_id 字段，避免重复操作：

sql
Copy
CREATE UNIQUE INDEX uniq_request ON credit_transactions (request_id); 4. 对账机制
每日定时任务验证余额与流水的一致性：

sql
Copy
-- 校验逻辑（伪代码）
SELECT
u.user_id,
u.credits AS current_balance,
(100 + COALESCE(SUM(t.delta), 0)) AS calculated_balance
FROM users u
LEFT JOIN credit_transactions t ON u.user_id = t.user_id
WHERE u.credits != (100 + COALESCE(SUM(t.delta), 0)); 5. 扩展优化
分库分表：按 user_id 哈希分片（单表超千万级时使用）。

缓存层：对高频访问的用户余额加 Redis 缓存（注意缓存穿透/雪崩）。

最终结论
必选方案：独立余额表 + 流水表

开发复杂度可控（事务 + 幂等性）。

性能满足 99% 的生产场景需求（TPS < 10k 时无需分库分表）。

可扩展性强（后续可引入缓存或异步对账）。

实时计算方案仅限特殊场景使用（如强审计需求）。
