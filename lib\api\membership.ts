import { apiClient, ApiResponse } from './client';
import { API_CONFIG } from '@/lib/config';
import {
    MembershipPlan,
    UserMembership,
} from '@/app/membership/types';

/**
 * 会员系统API服务
 */
export const membershipApi = {
    /**
     * 获取用户会员信息
     */
    getUserMembership: async (): Promise<UserMembership> => {
        const response = await apiClient.get<ApiResponse<UserMembership>>(
            API_CONFIG.ENDPOINTS.MEMBERSHIP.GET_USER_MEMBERSHIP
        );
        return response.data;
    },

    /**
     * 获取会员计划列表
     */
    getMembershipPlans: async (): Promise<MembershipPlan[]> => {
        const response = await apiClient.get<ApiResponse<MembershipPlan[]>>(
            API_CONFIG.ENDPOINTS.MEMBERSHIP.GET_PLANS,
            {
                requireAuth: false,
            }
        );
        return response.data;
    },
}; 