import Image from "next/image"
import { PostItemDto } from "@/types/posts"
import { formatTimeAgo } from "@/lib/utils"
import { Clock } from "lucide-react"

interface AuthorInfoProps {
    post: PostItemDto
}

export function AuthorInfo({ post }: AuthorInfoProps) {
    const author = post.user_profiles;
    const time = formatTimeAgo(post.created_at);

    return (
        <div className="p-6 bg-gradient-to-b from-gray-900/40 to-transparent backdrop-blur-[2px]">

            <div className="flex items-center gap-4">
                <div className="relative">
                    <div className="absolute -inset-1 bg-gradient-to-tr from-purple-500/20 to-blue-500/20 rounded-full blur-sm" />
                    <Image
                        src={author.avatar || "/placeholder.svg"}
                        alt={author.nickname}
                        width={56}
                        height={56}
                        className="rounded-full object-cover relative ring-1 ring-white/10"
                        unoptimized
                    />
                </div>
                <div>
                    <h3 className="font-medium text-white text-lg tracking-tight">{author.nickname}</h3>
                    <div className="flex items-center text-sm text-gray-400 mt-1">
                        <Clock size={12} className="mr-1.5 text-gray-500" />
                        <span>Uploaded {time}</span>
                    </div>
                </div>
            </div>
        </div>
    )
}