"use client";

import Image from "next/image";
import { Suspense } from "react";
import { Badge } from "@/components/ui/badge";
import { VideoPlayer } from "./video-player";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { Effect } from "@/types/model";

interface EffectCardProps {
    effect: Effect;
    showUseButton?: boolean;
    onSelectEffect?: (effect: Effect) => void;
    autoPlay?: boolean;
}

export function EffectCard({
    effect,
    showUseButton = true,
    onSelectEffect,
    autoPlay = true,
}: EffectCardProps) {
    const router = useRouter();
    const pathname = usePathname();
    const isVideo =
        effect.cover_img?.endsWith(".mp4") ||
        effect.cover_img?.endsWith(".webm") ||
        effect.cover_img?.endsWith(".mov");
    const isCreatePage = pathname?.startsWith("/create");

    const handleUseEffect = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        if (isCreatePage && onSelectEffect) {
            // 如果在create页面，使用传入的处理函数选择效果
            onSelectEffect(effect);
        } else {
            // 否则，跳转到create页面并使用该效果
            router.push(`/create?effectId=${effect.id}`);
        }
    };

    return (
        <div
            className="relative aspect-[4/5] rounded-xl overflow-hidden group h-full cursor-pointer"
            onClick={handleUseEffect}
        >
            {/* Cover Media (Video or Image) */}
            {isVideo ? (
                <Suspense
                    fallback={
                        <Image
                            src={"/placeholder.svg"}
                            alt={effect.name}
                            fill
                            className="object-cover"
                        />
                    }
                >
                    <VideoPlayer
                        src={effect.cover_img || ""}
                        poster="/placeholder.svg"
                        autoPlay={autoPlay}
                    />
                </Suspense>
            ) : (
                <Image
                    src={effect.cover_img || "/placeholder.svg"}
                    alt={effect.name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
            )}

            {/* Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent" />

            {/* Top Badges */}
            <div className="absolute top-3 left-3 flex gap-2 items-center z-10">
                <Badge className="bg-teal-500/90 text-xs font-medium border-0 backdrop-blur-sm">
                    EFFECT
                </Badge>
            </div>

            {/* Bottom Content */}
            <div className="absolute bottom-0 left-0 right-0 p-4 space-y-3 z-10">
                {/* Effect Name */}
                <h3 className="text-white font-medium text-xl leading-tight">
                    {effect.name}
                </h3>

                {/* Description */}
                <p className="text-white/80 text-sm line-clamp-2">{effect.desc}</p>
            </div>

            {/* Hover Overlay with Use Effect Button - Only show on desktop */}
            {showUseButton && (
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center z-20">
                    <Button
                        onClick={handleUseEffect}
                        className="bg-teal-500 hover:bg-teal-600 text-white font-medium"
                        size="lg"
                    >
                        Use Effect
                    </Button>
                </div>
            )}
        </div>
    );
}

export function EffectCardLink({ effect }: { effect: Effect }) {
    return (
        <div className="h-full w-full">
            <EffectCard effect={effect} />
        </div>
    );
}
