import React from "react";
import { cn } from "@/lib/utils";

interface ActionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    icon: React.ElementType;
    label?: string;
    variant?: "primary" | "secondary" | "outline";
    isLoading?: boolean;
}

/**
 * 通用操作按钮组件
 * 支持多种样式变体和加载状态
 */
const ActionButton = React.forwardRef<HTMLButtonElement, ActionButtonProps>(
    ({ icon: Icon, label, children, variant = "outline", isLoading = false, className, disabled, ...props }, ref) => {
        return (
            <button
                ref={ref}
                className={cn(
                    "flex items-center justify-center gap-2 px-4 py-2 rounded-md transition-all duration-200",
                    "text-sm font-medium",
                    "hover:shadow-sm active:translate-y-px",
                    className
                )}
                disabled={disabled || isLoading}
                {...props}
            >
                <span className="flex items-center justify-center">
                    {isLoading ? (
                        <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                            <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                                fill="none"
                            />
                            <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            />
                        </svg>
                    ) : (
                        <Icon className="h-4 w-4" />
                    )}
                </span>
                <span>{label || children}</span>
            </button>
        );
    }
);

ActionButton.displayName = "ActionButton";

export { ActionButton }; 