import { create } from 'zustand';

interface Item {
    id: number;
    // 其他属性可以根据需要添加
}

interface StoreState {
    items: Item[];
    addItem: (item: Item) => void;
    removeItem: (id: number) => void;
}

const useStore = create<StoreState>((set: (fn: (state: StoreState) => StoreState) => void) => ({
    items: [],
    addItem: (item: Item) => set((state: StoreState) => ({ ...state, items: [...state.items, item] })),
    removeItem: (id: number) => set((state: StoreState) => ({ ...state, items: state.items.filter(item => item.id !== id) })),
}));

export default useStore; 