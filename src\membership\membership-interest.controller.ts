import { Controller, Get, Post, Body, Param, Query, UseGuards, BadRequestException } from '@nestjs/common';
import { JwtGuard } from '../common/guards/jwt.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { RolesGuard } from '../common/guards/roles.guard';
import { MembershipInterestService } from './membership-interest.service';
import { MembershipInterestDto, MembershipInterestQueryDto } from './dto/membership.dto';
import { CustomLogger } from '../common/services/logger.service';

@Controller('membership-interests')
export class MembershipInterestController {
    constructor(
        private readonly membershipInterestService: MembershipInterestService,
        private readonly logger: CustomLogger,
    ) {
        this.logger.setContext(MembershipInterestController.name);
    }

    /**
     * 获取所有会员权益
     */
    @Get()
    @UseGuards(JwtGuard, RolesGuard)
    @Roles('admin')
    async getAllInterests(@Query() query: MembershipInterestQueryDto) {
        try {
            const { data, error } = await this.membershipInterestService['supabase']
                .from('membership_interests')
                .select('*')
                .order('plan_id', { ascending: true })
                .order('interest_type', { ascending: true })
                .order('interest_key', { ascending: true });

            if (error) {
                throw new BadRequestException('获取会员权益列表失败');
            }

            return data;
        } catch (error) {
            this.logger.error('获取会员权益列表失败', error);
            throw new BadRequestException('获取会员权益列表失败');
        }
    }

    /**
     * 获取指定会员计划的权益
     */
    @Get('plan/:planId')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles('admin')
    async getPlanInterests(@Param('planId') planId: string) {
        return await this.membershipInterestService.getPlanInterests(planId);
    }

    /**
     * 创建或更新会员权益
     */
    @Post()
    @UseGuards(JwtGuard, RolesGuard)
    @Roles('admin')
    async createOrUpdateInterest(@Body() interestDto: MembershipInterestDto) {
        const { plan_id, interest_type, interest_key, interest_value, description } = interestDto;

        const success = await this.membershipInterestService.upsertInterest(
            plan_id,
            interest_type,
            interest_key,
            interest_value,
            description || '会员权益'
        );

        if (!success) {
            throw new BadRequestException('更新会员权益失败');
        }

        return { success: true, message: '会员权益更新成功' };
    }

    /**
     * 禁用会员权益
     */
    @Post(':id/deactivate')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles('admin')
    async deactivateInterest(@Param('id') id: string) {
        const success = await this.membershipInterestService.deactivateInterest(id);

        if (!success) {
            throw new BadRequestException('禁用会员权益失败');
        }

        return { success: true, message: '会员权益已禁用' };
    }
} 