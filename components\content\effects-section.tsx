import { Suspense } from "react"
import { EffectsSlider } from "./effects-slider"
import { effectApi } from "@/lib/api/effect"

// 获取效果数据的服务端函数
async function getEffects() {
    try {
        const response = await effectApi.getEffects();
        return response;
    } catch (error) {
        console.error('获取效果数据出错:', error);
        return [];
    }
}

// 效果加载骨架屏组件
function EffectsLoadingSkeleton() {
    return <EffectsSlider effects={[]} title="Featured Effects" isLoading={true} />;
}

// 服务端渲染的效果展示组件
export async function EffectsSection() {
    return (
        <div className="pl-8 mb-6">
            <Suspense fallback={<EffectsLoadingSkeleton />}>
                {/* 流式渲染，不阻塞页面其他部分 */}
                <EffectsContent />
            </Suspense>
        </div>
    );
}

// 流式渲染内容组件
async function EffectsContent() {
    try {
        // 等待数据加载但不会阻塞页面其他部分
        const effects = await getEffects();
        return <EffectsSlider effects={effects} title="Featured Effects" />;
    } catch (error) {
        console.error('加载效果数据失败:', error);
        // 返回空数据但不显示加载状态
        return <EffectsSlider effects={[]} title="Featured Effects" />;
    }
} 