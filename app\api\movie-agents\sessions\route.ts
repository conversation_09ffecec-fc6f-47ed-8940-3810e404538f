import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

// 获取用户的会话历史
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // 获取当前用户
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // 检查用户是否有访问权限
    const { data: permission } = await supabase
      .from('user_access_permissions')
      .select('has_movie_agent_access')
      .eq('user_id', user.id)
      .single()

    if (!permission?.has_movie_agent_access) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // 获取用户的会话历史
    const { data: sessions, error: sessionsError } = await supabase
      .from('movie_agent_sessions')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (sessionsError) {
      return NextResponse.json({ error: 'Failed to fetch sessions' }, { status: 500 })
    }

    // 获取总数
    const { count, error: countError } = await supabase
      .from('movie_agent_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)

    if (countError) {
      return NextResponse.json({ error: 'Failed to count sessions' }, { status: 500 })
    }

    return NextResponse.json({
      sessions,
      total: count,
      limit,
      offset
    })

  } catch (error) {
    console.error('Error fetching sessions:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// 创建新会话
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const sessionData = await request.json()

    // 获取当前用户
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // 检查用户是否有访问权限
    const { data: permission } = await supabase
      .from('user_access_permissions')
      .select('has_movie_agent_access')
      .eq('user_id', user.id)
      .single()

    if (!permission?.has_movie_agent_access) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // 创建新会话
    const { data: session, error: sessionError } = await supabase
      .from('movie_agent_sessions')
      .insert({
        user_id: user.id,
        prompt: sessionData.prompt,
        think_content: sessionData.think_content || null,
        plan_content: sessionData.plan_content || null,
        keyframe_prompts_content: sessionData.keyframe_prompts_content || null,
        keyframe_prompts: sessionData.keyframe_prompts || null,
        keyframe_tasks: sessionData.keyframe_tasks || null,
        search_result: sessionData.search_result || null,
        status: sessionData.status || 'active'
      })
      .select()
      .single()

    if (sessionError) {
      return NextResponse.json({ error: 'Failed to create session' }, { status: 500 })
    }

    return NextResponse.json({ session })

  } catch (error) {
    console.error('Error creating session:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 