'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
    src: string;
    alt: string;
    width?: number;
    height?: number;
    className?: string;
    priority?: boolean;
    fill?: boolean;
    sizes?: string;
    objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
}

/**
 * 优化的图片组件 - 支持渐进式加载和过渡效果
 * 使用Next.js Image组件增强性能和SEO
 */
export function OptimizedImage({
    src,
    alt,
    width,
    height,
    className,
    priority = false,
    fill = false,
    sizes = '100vw',
    objectFit = 'cover',
}: OptimizedImageProps) {
    const [isLoaded, setIsLoaded] = useState(false);
    const [validSrc, setValidSrc] = useState(src);

    // 默认回退图片
    const fallbackSrc = '/images/placeholder.jpg';

    // 当src改变时重置加载状态
    useEffect(() => {
        setIsLoaded(false);
        setValidSrc(src);
    }, [src]);

    return (
        <div className={cn(
            'overflow-hidden relative',
            fill ? 'w-full h-full' : '',
            className
        )}>
            {/* 使用Next.js Image组件加载图片 */}
            <Image
                src={validSrc || fallbackSrc}
                alt={alt}
                width={fill ? undefined : (width || 300)}
                height={fill ? undefined : (height || 200)}
                className={cn(
                    'transition-opacity duration-500 ease-in-out',
                    isLoaded ? 'opacity-100' : 'opacity-0',
                    objectFit === 'contain' ? 'object-contain' :
                        objectFit === 'cover' ? 'object-cover' :
                            objectFit === 'fill' ? 'object-fill' :
                                objectFit === 'none' ? 'object-none' :
                                    'object-scale-down'
                )}
                sizes={sizes}
                priority={priority}
                fill={fill}
                loading={priority ? 'eager' : 'lazy'}
                onLoad={() => setIsLoaded(true)}
                onError={() => setValidSrc(fallbackSrc)}
            />

            {/* 加载占位符 - 淡入淡出效果 */}
            {!isLoaded && (
                <div className="absolute inset-0 bg-slate-200 dark:bg-slate-700/40 animate-pulse" />
            )}
        </div>
    );
} 