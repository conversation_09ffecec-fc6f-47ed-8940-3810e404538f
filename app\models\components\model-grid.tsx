import { useEffect, useState } from "react"
import { ModelCard } from "./model-card"
import { Loader2 } from "lucide-react"
import { useModelList } from "@/app/models/queries"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { EffectCard } from "./effect-card"
import { useEffectList } from "@/app/models/queries"

interface ModelGridProps {
    initialTab?: string;
}

export function ModelGrid({ initialTab = "models" }: ModelGridProps) {
    const { data: modelData, isLoading: isModelLoading } = useModelList()
    const { data: effectData, isLoading: isEffectLoading } = useEffectList()
    const [activeTab, setActiveTab] = useState(initialTab)

    // 当初始选项卡更改时更新activeTab
    useEffect(() => {
        setActiveTab(initialTab)
    }, [initialTab])

    const isLoading = activeTab === "models" ? isModelLoading : isEffectLoading

    return (
        <div className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <div className="flex justify-center sm:justify-start mb-4">
                    <TabsList>
                        <TabsTrigger value="models">Models</TabsTrigger>
                        <TabsTrigger value="effects">Effects</TabsTrigger>
                    </TabsList>
                </div>

                <TabsContent value="models" className="mt-0">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
                        {modelData?.models?.map((model) => (
                            <ModelCard key={model.id} model={model} />
                        ))}
                    </div>
                </TabsContent>

                <TabsContent value="effects" className="mt-0">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
                        {effectData?.effects?.map((effect) => (
                            <EffectCard key={effect.id} effect={effect} />
                        ))}
                    </div>
                </TabsContent>
            </Tabs>

            {isLoading && (
                <div className="flex justify-center py-8">
                    <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
                </div>
            )}
        </div>
    )
}

