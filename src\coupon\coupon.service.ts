import { Injectable, BadRequestException, Inject } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import {
    ClaimCouponDto,
    CouponResponseDto,
    CouponStatusResponseDto,
    CalculatePriceDto,
    PriceCalculationResponseDto,
    ApplyCouponToPaymentDto,
    ApplyCouponToPaymentResponseDto,
    CouponType,
    CouponStatus
} from './dto/coupon.dto';

@Injectable()
export class CouponService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {}

    /**
     * 检查用户优惠券状态
     */
    async getCouponStatus(userId: string): Promise<CouponStatusResponseDto> {
        try {
            // 检查用户是否有活跃的优惠券
            const { data: activeCoupon, error: couponError } = await this.supabase
                .from('user_coupons')
                .select('*')
                .eq('user_id', userId)
                .eq('status', CouponStatus.ACTIVE)
                .gte('expires_at', new Date().toISOString())
                .single();

            if (couponError && couponError.code !== 'PGRST116') {
                this.logger.error(`检查优惠券状态失败: ${couponError.message}`, couponError);
                throw new BadRequestException('检查优惠券状态失败');
            }

            // 检查用户是否有资格领取首月折扣
            const isEligible = await this.checkFirstMonthDiscountEligibility(userId);

            return {
                has_coupon: !!activeCoupon,
                coupon: activeCoupon ? this.mapToCouponResponse(activeCoupon) : undefined,
                is_eligible: isEligible,
                message: activeCoupon ? 'You have an active coupon' : isEligible ? 'You are eligible for a first month discount coupon' : 'No coupon available'
            };
        } catch (error) {
            this.logger.error(`检查优惠券状态失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 领取优惠券
     */
    async claimCoupon(userId: string, claimCouponDto: ClaimCouponDto): Promise<{ success: boolean; coupon?: CouponResponseDto; message: string }> {
        try {
            const { coupon_type } = claimCouponDto;

            // 检查资格（包含所有必要的验证：会员历史、优惠券领取历史等）
            if (coupon_type === CouponType.FIRST_MONTH_DISCOUNT) {
                const isEligible = await this.checkFirstMonthDiscountEligibility(userId);
                if (!isEligible) {
                    return {
                        success: false,
                        message: '您不符合领取首月折扣优惠券的条件'
                    };
                }
            }

            // 创建优惠券
            const { data: newCoupon, error } = await this.supabase
                .from('user_coupons')
                .insert({
                    user_id: userId,
                    coupon_type,
                    discount_percentage: 90, // 90% off
                    status: CouponStatus.ACTIVE,
                    claimed_at: new Date().toISOString(),
                    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24小时后过期
                })
                .select('*')
                .single();

            if (error) {
                this.logger.error(`创建优惠券失败: ${error.message}`, error);
                throw new BadRequestException('Failed to claim coupon');
            }

            return {
                success: true,
                coupon: this.mapToCouponResponse(newCoupon),
                message: 'Successfully claimed coupon'
            };
        } catch (error) {
            this.logger.error(`领取优惠券失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 获取用户的优惠券列表
     */
    async getUserCoupons(userId: string): Promise<CouponResponseDto[]> {
        try {
            const { data: coupons, error } = await this.supabase
                .from('user_coupons')
                .select('*')
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (error) {
                this.logger.error(`获取用户优惠券列表失败: ${error.message}`, error);
                throw new BadRequestException('Failed to get user coupons');
            }

            return coupons.map(coupon => this.mapToCouponResponse(coupon));
        } catch (error) {
            this.logger.error(`获取用户优惠券列表失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 计算应用优惠券后的价格
     */
    async calculatePrice(userId: string, calculatePriceDto: CalculatePriceDto): Promise<PriceCalculationResponseDto> {
        try {
            const { original_price, coupon_id } = calculatePriceDto;

            if (!coupon_id) {
                return {
                    original_price,
                    discount_percentage: 0,
                    discount_amount: 0,
                    final_price: original_price,
                    has_coupon: false
                };
            }

            // 获取优惠券信息
            const { data: coupon, error } = await this.supabase
                .from('user_coupons')
                .select('*')
                .eq('id', coupon_id)
                .eq('user_id', userId)
                .eq('status', CouponStatus.ACTIVE)
                .gte('expires_at', new Date().toISOString())
                .single();

            if (error || !coupon) {
                return {
                    original_price,
                    discount_percentage: 0,
                    discount_amount: 0,
                    final_price: original_price,
                    has_coupon: false
                };
            }

            const discountAmount = (original_price * coupon.discount_percentage) / 100;
            const finalPrice = original_price - discountAmount;

            return {
                original_price,
                discount_percentage: coupon.discount_percentage,
                discount_amount: discountAmount,
                final_price: Math.max(0, finalPrice), // 确保价格不为负数
                has_coupon: true
            };
        } catch (error) {
            this.logger.error(`计算价格失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 检查用户是否有资格领取首月折扣优惠券
     * 条件：1. 用户从未购买过会员 2. 用户从未领取过首月折扣优惠券
     */
    async checkFirstMonthDiscountEligibility(userId: string): Promise<boolean> {
        try {
            // 1. 检查用户是否曾经购买过会员
            const { data: membershipHistory, error: membershipError } = await this.supabase
                .from('user_memberships')
                .select('id')
                .eq('user_id', userId)
                .limit(1);

            if (membershipError) {
                this.logger.error(`检查会员历史失败: ${membershipError.message}`, membershipError);
                return false;
            }

            // 如果用户已经购买过会员，则没有资格领取首月折扣
            if (membershipHistory.length > 0) {
                return false;
            }

            // 2. 检查用户是否已经领取过首月折扣优惠券
            const { data: existingCoupon, error: couponError } = await this.supabase
                .from('user_coupons')
                .select('id')
                .eq('user_id', userId)
                .eq('coupon_type', CouponType.FIRST_MONTH_DISCOUNT)
                .limit(1);

            if (couponError) {
                this.logger.error(`检查优惠券历史失败: ${couponError.message}`, couponError);
                return false;
            }

            // 如果用户已经领取过首月折扣优惠券，则没有资格再次领取
            if (existingCoupon.length > 0) {
                this.logger.debug(`用户 ${userId} 已领取过首月折扣优惠券，不能重复领取`);
                return false;
            }

            // 用户既没有会员历史，也没有领取过首月折扣优惠券，有资格领取
            this.logger.debug(`用户 ${userId} 符合首月折扣优惠券领取条件`);
            return true;
        } catch (error) {
            this.logger.error(`检查首月折扣资格失败: ${error.message}`, error.stack);
            return false;
        }
    }

    /**
     * 将数据库记录映射为响应DTO
     */
    private mapToCouponResponse(coupon: any): CouponResponseDto {
        return {
            id: coupon.id,
            user_id: coupon.user_id,
            coupon_type: coupon.coupon_type,
            discount_percentage: coupon.discount_percentage,
            status: coupon.status,
            claimed_at: new Date(coupon.claimed_at),
            expires_at: new Date(coupon.expires_at),
            used_at: coupon.used_at ? new Date(coupon.used_at) : undefined,
            payment_id: coupon.payment_id,
            created_at: new Date(coupon.created_at),
            updated_at: new Date(coupon.updated_at)
        };
    }

    /**
     * 标记优惠券为已使用
     */
    async markCouponAsUsed(couponId: string, paymentId: string): Promise<void> {
        try {
            const { error } = await this.supabase
                .from('user_coupons')
                .update({
                    status: CouponStatus.USED,
                    used_at: new Date().toISOString(),
                    payment_id: paymentId,
                    updated_at: new Date().toISOString()
                })
                .eq('id', couponId);

            if (error) {
                this.logger.error(`标记优惠券为已使用失败: ${error.message}`, error);
                throw new BadRequestException('更新优惠券状态失败');
            }
        } catch (error) {
            this.logger.error(`标记优惠券为已使用失败: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * 验证优惠券是否可用于指定支付
     */
    async validateCouponForPayment(userId: string, couponId: string, paymentType: string): Promise<{
        isValid: boolean;
        coupon?: any;
        message?: string;
    }> {
        try {
            // 获取优惠券信息
            const { data: coupon, error } = await this.supabase
                .from('user_coupons')
                .select('*')
                .eq('id', couponId)
                .eq('user_id', userId)
                .eq('status', CouponStatus.ACTIVE)
                .gte('expires_at', new Date().toISOString())
                .single();

            if (error || !coupon) {
                return {
                    isValid: false,
                    message: 'Coupon is invalid or expired'
                };
            }

            // 检查优惠券类型是否适用于当前支付类型
            if (coupon.coupon_type === CouponType.FIRST_MONTH_DISCOUNT && paymentType !== 'subscription') {
                return {
                    isValid: false,
                    message: 'First month discount coupon can only be used for membership subscriptions'
                };
            }

            return {
                isValid: true,
                coupon,
                message: 'Coupon is valid'
            };
        } catch (error) {
            this.logger.error(`验证优惠券失败: ${error.message}`, error.stack);
            return {
                isValid: false,
                message: 'Cannot validate coupon'
            };
        }
    }

    /**
     * 更新过期的优惠券状态
     */
    async updateExpiredCoupons(): Promise<void> {
        try {
            const { error } = await this.supabase
                .from('user_coupons')
                .update({
                    status: CouponStatus.EXPIRED,
                    updated_at: new Date().toISOString()
                })
                .eq('status', CouponStatus.ACTIVE)
                .lt('expires_at', new Date().toISOString());

            if (error) {
                this.logger.error(`更新过期优惠券失败: ${error.message}`, error);
            }
        } catch (error) {
            this.logger.error(`更新过期优惠券失败: ${error.message}`, error.stack);
        }
    }
}
