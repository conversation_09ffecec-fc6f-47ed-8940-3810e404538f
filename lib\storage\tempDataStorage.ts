/**
 * 临时数据存储服务
 * 用于在页面之间传递临时数据，避免使用URL参数传递大量数据
 */

// 存储键名常量
export const STORAGE_KEYS = {
  REFERENCE_IMAGE: 'reelmind_reference_image',
  REFERENCE_PROMPT: 'reelmind_reference_prompt',
  REFERENCE_PARAMS: 'reelmind_reference_params',
};

/**
 * 保存参考图片信息
 * @param imageUrl 图片URL
 * @param prompt 提示词
 * @param params 其他参数
 */
export function saveReferenceData(imageUrl: string, prompt: string, params?: Record<string, any>): void {
  try {
    // 保存图片URL
    localStorage.setItem(STORAGE_KEYS.REFERENCE_IMAGE, imageUrl);
    
    // 保存提示词
    if (prompt) {
      localStorage.setItem(STORAGE_KEYS.REFERENCE_PROMPT, prompt);
    }
    
    // 保存其他参数
    if (params) {
      localStorage.setItem(STORAGE_KEYS.REFERENCE_PARAMS, JSON.stringify(params));
    }
  } catch (error) {
    console.error('Failed to save reference data to localStorage:', error);
  }
}

/**
 * 获取参考图片URL
 * @returns 图片URL或null
 */
export function getReferenceImage(): string | null {
  try {
    return localStorage.getItem(STORAGE_KEYS.REFERENCE_IMAGE);
  } catch (error) {
    console.error('Failed to get reference image from localStorage:', error);
    return null;
  }
}

/**
 * 获取参考提示词
 * @returns 提示词或null
 */
export function getReferencePrompt(): string | null {
  try {
    return localStorage.getItem(STORAGE_KEYS.REFERENCE_PROMPT);
  } catch (error) {
    console.error('Failed to get reference prompt from localStorage:', error);
    return null;
  }
}

/**
 * 获取参考参数
 * @returns 参数对象或null
 */
export function getReferenceParams(): Record<string, any> | null {
  try {
    const paramsString = localStorage.getItem(STORAGE_KEYS.REFERENCE_PARAMS);
    if (!paramsString) return null;
    return JSON.parse(paramsString);
  } catch (error) {
    console.error('Failed to get reference params from localStorage:', error);
    return null;
  }
}

/**
 * 清除所有参考数据
 */
export function clearReferenceData(): void {
  try {
    localStorage.removeItem(STORAGE_KEYS.REFERENCE_IMAGE);
    localStorage.removeItem(STORAGE_KEYS.REFERENCE_PROMPT);
    localStorage.removeItem(STORAGE_KEYS.REFERENCE_PARAMS);
  } catch (error) {
    console.error('Failed to clear reference data from localStorage:', error);
  }
}

/**
 * 检查是否有参考数据
 * @returns 是否有参考图片数据
 */
export function hasReferenceData(): boolean {
  try {
    return !!localStorage.getItem(STORAGE_KEYS.REFERENCE_IMAGE);
  } catch (error) {
    console.error('Failed to check reference data in localStorage:', error);
    return false;
  }
}
