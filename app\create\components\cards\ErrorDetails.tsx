"use client"

import { Alert<PERSON>riangle } from "lucide-react";
import { cn } from "@/lib/utils";
import type { VideoTask } from "@/types/video-task";

interface ErrorDetailsProps {
    video: VideoTask;
    className?: string;
}

/**
 * ErrorDetails Component - 显示错误详情信息
 * 只显示用户友好的错误信息，不显示技术细节
 */
export const ErrorDetails = ({ video, className }: ErrorDetailsProps) => {
    if (!video.error_log) {
        return null;
    }

    const { error_log } = video;

    // 获取用户友好的错误信息
    const getUserFriendlyMessage = () => {
        if (!error_log.details?.detail?.[0]) {
            return error_log.message || "An unknown error occurred";
        }

        const detail = error_log.details.detail[0];

        // 根据错误类型返回用户友好的信息
        switch (detail.type) {
            case "content_policy_violation":
                return "Content policy violation: The prompt or image contains content that is not allowed by our content policy.";
            case "invalid_input":
                return "Invalid input: Please check your prompt and image settings.";
            case "quota_exceeded":
                return "Quota exceeded: You have reached your usage limit.";
            case "model_error":
                return "Model error: There was an issue with the AI model processing.";
            case "value_error.missing":
                return "Runtime error, we are fixing it, please check back later";
            default:
                return detail.msg || error_log.message || "An error occurred during generation";
        }
    };

    return (
        <div className={cn("space-y-2", className)}>
            {/* 错误信息 */}
            <div className="flex items-start gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-md">
                <AlertTriangle size={16} className="text-red-500 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                    <div className="text-sm text-red-700 dark:text-red-300">
                        {getUserFriendlyMessage()}
                    </div>
                </div>
            </div>
        </div>
    );
};
