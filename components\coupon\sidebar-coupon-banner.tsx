"use client"

import { <PERSON>, <PERSON>, <PERSON>rk<PERSON>, ArrowRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useCouponLogic } from "@/hooks/useCouponLogic"
import { useRouter } from "next/navigation"
import useLoginDialogStore from "@/store/useLoginDialogStore"

interface SidebarCouponBannerProps {
    className?: string;
    isCollapsed?: boolean;
}

export function SidebarCouponBanner({ className, isCollapsed }: SidebarCouponBannerProps) {
    const router = useRouter()
    const { openLoginDialog } = useLoginDialogStore()
    const {
        timeLeft,
        activeCoupon,
        isLoading,
        shouldShow,
        canClaim,
        isAuthenticated,
        handleClaimCoupon
    } = useCouponLogic()

    // Don't show if conditions not met or if collapsed
    if (!shouldShow || isCollapsed) return null

    // Handle click to membership page when coupon is active
    const handleMembershipRedirect = () => {
        if (!isAuthenticated) {
            openLoginDialog({
                pendingAction: {
                    type: 'membership_redirect',
                    payload: {},
                    onSuccess: () => {
                        router.push('/membership')
                    }
                }
            })
            return
        }
        router.push('/membership')
    }

    // Active coupon display - show membership promotion
    if (activeCoupon) {
        return (
            <div className={cn("relative overflow-hidden w-full cursor-pointer", className)} onClick={handleMembershipRedirect}>
                <div className="relative bg-gradient-to-br from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-lg p-3 backdrop-blur-sm hover:border-purple-500/30 transition-all duration-300">
                    {/* Animated background */}
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-blue-500/5 animate-pulse rounded-lg" />

                    <div className="relative z-10">
                        <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                                <Sparkles className="w-4 h-4 text-purple-400" />
                                <span className="text-sm font-medium">90% OFF Ready</span>
                            </div>
                            <ArrowRight className="w-3 h-3 text-muted-foreground" />
                        </div>

                        <p className="text-xs text-muted-foreground mb-2">
                            Get membership with your discount
                        </p>

                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="w-3 h-3" />
                            <span>{timeLeft}</span>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    // Claim coupon display (only show if eligible and no active coupon)
    if (canClaim) {
        return (
            <div className={cn("relative overflow-hidden w-full", className)}>
                <div className="relative bg-gradient-to-br from-orange-500/10 to-pink-500/10 border border-orange-500/20 rounded-lg p-3 backdrop-blur-sm">
                    {/* Animated background effects */}
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 to-pink-500/5 animate-pulse rounded-lg" />
                    <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/50 to-transparent" />

                    <div className="relative z-10">
                        <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                                <Gift className="w-4 h-4 text-orange-400" />
                                <span className="text-sm font-medium">90% OFF</span>
                            </div>
                        </div>

                        <p className="text-xs text-muted-foreground mb-3">
                            First month membership discount for new users
                        </p>

                        <Button
                            onClick={handleClaimCoupon}
                            disabled={isLoading}
                            size="sm"
                            className="w-full bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white border-0 text-xs h-8"
                        >
                            Claim Coupon
                        </Button>

                        <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground mt-2">
                            <Clock className="w-3 h-3" />
                            <span>Valid for 24 hours</span>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    return null
}
