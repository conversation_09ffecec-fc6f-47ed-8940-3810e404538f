import { GenerateImageParams, GenerateImageResponse, LegoQueueInfo, ASPECT_RATIO_DIMENSIONS, LegoHistoryItem } from './types';
import { apiClient, ApiResponse } from '@/lib/api/client';
import { queryDeduplicationManager } from '@/lib/query-deduplication';

// API 路径配置（如果实际项目中没有，可以添加到 config.ts 中）
const LEGO_API_ENDPOINTS = {
    GENERATE_IMAGE: '/lego/gen-pic',
    GET_USER_HISTORY: '/lego/user-tasks',
    DELETE_HISTORY_ITEM: '/lego/history',
    GET_QUEUE_INFO: '/lego/task/queue-info',
    GET_TASK_DETAIL: '/lego/task',
    GET_MODELS: '/lego/models',
    GET_FLUX_USAGE_LIMIT: '/lego/flux-usage-limit',
};

/**
 * 调用AI生成图片的API
 */
export async function generateImage(params: GenerateImageParams): Promise<GenerateImageResponse> {
    try {
        // 处理参数转换，适配新的API格式
        const apiParams: GenerateImageParams = {
            model_id: params.model_id,
            prompt: params.prompt,
            negative_prompt: params.negative_prompt,
            guidance_scale: params.guidance_scale,
            steps: params.steps,
            seed: params.seed,
            refer_img_urls: [],
            aspect_ratio: params.aspectRatio,
        };

        // 处理宽高
        if (params.aspectRatio && ASPECT_RATIO_DIMENSIONS[params.aspectRatio as keyof typeof ASPECT_RATIO_DIMENSIONS]) {
            const dimensions = ASPECT_RATIO_DIMENSIONS[params.aspectRatio as keyof typeof ASPECT_RATIO_DIMENSIONS];
            apiParams.width = dimensions.width;
            apiParams.height = dimensions.height;
        }

        // 处理参考图片 - 根据图片数量调整参数格式
        if (params.refer_img_urls && params.refer_img_urls.length > 0) {
            if (params.refer_img_urls.length === 1) {
                // 单张图片使用 image_url
                apiParams.image_url = params.refer_img_urls[0];
            } else if (params.refer_img_urls.length >= 2) {
                // 多张图片使用 image_urls 数组
                apiParams.image_urls = params.refer_img_urls;
            }
            // 如果没有图片，则不传递任何图片相关字段
        }

        // 添加模型参数
        if (params.model) {
            apiParams.model = params.model;
        }

        // 调用API
        const response = await apiClient.post<ApiResponse<{
            task_id: string;
            task: {
                id: string;
                user_id: string;
                status: string;
                progress: number;
                input_params: any;
                output_result: any;
                storage_path: string | null;
                created_at: string;
                started_at: string | null;
                completed_at: string | null;
                [key: string]: any;
            };
            status: string;
            estimated_wait_time: number;
        }>>(
            LEGO_API_ENDPOINTS.GENERATE_IMAGE,
            apiParams
        );

        // 处理后端返回的数据，转换为前端需要的格式
        if (response.data) {
            return {
                success: true,
                taskId: response.data.task_id,
                task: response.data.task, // 保存完整的任务数据
                status: response.data.status,
                estimatedWaitTime: response.data.estimated_wait_time
                // 注意：后端不返回imageUrl字段
            };
        }

        return {
            success: false,
            error: 'No data returned from server'
        };
    } catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error occurred',
        };
    }
}

/**
 * 获取用户历史记录
 */
export async function getUserHistory(limit: number = 20, offset: number = 0): Promise<{
    tasks: any[];
    total: number;
}> {
    const queryKey = `lego-user-history-${limit}-${offset}`;

    return queryDeduplicationManager.executeQuery(queryKey, async () => {
        try {
            console.log(`[getUserHistory] Fetching history: limit=${limit}, offset=${offset}`);
            const response = await apiClient.get<ApiResponse<{
                tasks: any[];
                total: number;
            }>>(LEGO_API_ENDPOINTS.GET_USER_HISTORY, {
                params: {
                    limit: limit.toString(),
                    offset: offset.toString(),
                }
            });

            return response.data;
        } catch (error) {
            console.error('Failed to fetch user history:', error);
            return { tasks: [], total: 0 };
        }
    });
}

/**
 * 获取队列信息
 */
export async function getQueueInfo(taskId: string): Promise<LegoQueueInfo> {
    try {
        const response = await apiClient.get<ApiResponse<{
            task_id: string;
            queue_position: number;
            total_tasks_in_queue: number;
            estimated_wait_time_seconds?: number;
            is_processing?: boolean;
            result?: {
                image_url?: string;
                [key: string]: any;
            };
        }>>(`${LEGO_API_ENDPOINTS.GET_QUEUE_INFO}/${taskId}`);

        return {
            taskId: response.data.task_id,
            position: response.data.queue_position,
            totalInQueue: response.data.total_tasks_in_queue,
            estimatedTimeSeconds: response.data.estimated_wait_time_seconds,
            isProcessing: !!response.data.is_processing,
            result: response.data.result ? {
                imageUrl: response.data.result.image_url,
                ...response.data.result
            } : undefined
        };
    } catch (error) {
        console.error('Failed to fetch queue info:', error);
        return {
            taskId,
            position: 0,
            totalInQueue: 0,
            isProcessing: false
        };
    }
}

/**
 * 处理图片下载
 */
export async function downloadImage(imageUrl: string, filename: string = 'lego-creation.png'): Promise<boolean> {
    try {
        console.log('Starting download for:', imageUrl);

        // 检查URL是否有效
        if (!imageUrl || imageUrl === '/placeholder.svg') {
            console.error('Invalid image URL:', imageUrl);
            // Note: This function should accept a toast callback parameter
            // TODO: Refactor to use toast instead of alert
            alert('Image not available for download');
            return false;
        }

        // 尝试直接下载（适用于同源或支持CORS的图片）
        try {
            const response = await fetch(imageUrl, {
                mode: 'cors',
                credentials: 'omit'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const blob = await response.blob();

            // 创建一个临时链接用于下载
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = filename;

            // 添加到DOM，触发下载，然后移除
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            console.log('Download completed successfully');
            return true;
        } catch (fetchError) {
            console.warn('Direct fetch failed, trying alternative method:', fetchError);

            // 如果直接fetch失败（可能是CORS问题），尝试使用代理下载
            try {
                // 使用我们的API作为代理来下载图片
                const proxyResponse = await fetch('/api/download-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ imageUrl, filename })
                });

                if (proxyResponse.ok) {
                    const blob = await proxyResponse.blob();

                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;

                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    console.log('Proxy download completed successfully');
                    return true;
                } else {
                    throw new Error(`Proxy download failed: ${proxyResponse.status}`);
                }
            } catch (proxyError) {
                console.warn('Proxy download failed, trying direct link method:', proxyError);

                // 最后的备用方案：直接打开图片链接让用户手动保存
                const a = document.createElement('a');
                a.href = imageUrl;
                a.download = filename;
                a.target = '_blank';
                a.rel = 'noopener noreferrer';

                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                console.log('Opened image in new tab for manual download');
                return true;
            }
        }
    } catch (error) {
        console.error('Failed to download image:', error);
        // Note: This function should accept a toast callback parameter
        // TODO: Refactor to use toast instead of alert
        alert('Failed to download image. Please try again or right-click the image to save manually.');
        return false;
    }
}

/**
 * 上传文件到Cloudflare R2存储
 * @deprecated 请使用 @/lib/upload 中的 uploadGenerationImage 函数
 */
export async function uploadToCloudflare(file: File): Promise<string> {
    // 为了向后兼容性，重新导入并使用新的统一函数
    const { uploadGenerationImage } = await import('@/lib/upload');
    return uploadGenerationImage(file);
}

/**
 * 获取任务详情
 */
export async function getTaskDetail(taskId: string): Promise<LegoHistoryItem | null> {
    try {
        const response = await apiClient.get<ApiResponse<LegoHistoryItem>>(
            `${LEGO_API_ENDPOINTS.GET_TASK_DETAIL}/${taskId}`
        );

        return response.data;
    } catch (error) {
        console.error('Failed to fetch task detail:', error);
        return null;
    }
}

/**
 * 获取lego模型列表
 */
export async function getLegoModels(): Promise<{
    models: any[];
    total: number;
}> {
    try {
        const response = await apiClient.get<ApiResponse<{
            models: any[];
        }>>(LEGO_API_ENDPOINTS.GET_MODELS);

        return {
            models: response.data.models || [],
            total: response.data.models?.length || 0
        };
    } catch (error) {
        console.error('Failed to fetch lego models:', error);
        return { models: [], total: 0 };
    }
}

/**
 * 获取用户FLUX模型使用限制信息
 */
export async function getFluxUsageLimit(): Promise<{
    canUse: boolean;
    usageCount: number;
    limit: number;
}> {
    try {
        const response = await apiClient.get<ApiResponse<{
            canUse: boolean;
            usageCount: number;
            limit: number;
        }>>(LEGO_API_ENDPOINTS.GET_FLUX_USAGE_LIMIT);

        return response.data;
    } catch (error) {
        console.error('Failed to fetch FLUX usage limit:', error);
        // 返回保守的默认值
        return {
            canUse: true,
            usageCount: 0,
            limit: 2
        };
    }
}