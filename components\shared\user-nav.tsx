"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import {
  User,
  LogOut,
  BookMarked,
  Moon,
  Sun,
  Crown,
  Sparkles,
  Zap
} from "lucide-react";
import { Avatar } from "./avatar";
import { useThemeContext } from "@/contexts/theme-context";
import { ClientOnlyWithFallback } from "@/components/client-only";
import { MembershipPlanName } from "@/app/membership/types";
import useLoginDialogStore from "@/store/useLoginDialogStore";
import useAuthStore from "@/store/useAuthStore";
import { useQuery } from "@tanstack/react-query";
import { membershipApi } from "@/lib/api/membership";
import { creditsApi } from "@/lib/api/credits";
import { GiftIcon } from "@/components/credits/gift-icon";

export function UserNav() {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user, signOut } = useAuth();
  const { theme, setTheme } = useThemeContext();
  const { openLoginDialog } = useLoginDialogStore();
  const { isAuthenticated } = useAuthStore();

  // 使用 react-query 获取会员信息
  const { data: userMembership } = useQuery({
    queryKey: ["membership", "user"],
    queryFn: async () => {
      const data = await membershipApi.getUserMembership();
      return data;
    },
    enabled: !!isAuthenticated,
  });

  // 使用 react-query 获取积分余额
  const { data: creditBalance, isLoading } = useQuery({
    queryKey: ["credits", "balance"],
    queryFn: async () => {
      const data = await creditsApi.getUserBalance();
      return data.data.balance;
    },
    enabled: !!isAuthenticated,
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleLogout = async () => {
    try {
      await signOut();
      setShowDropdown(false);
    } catch (error) {
      console.error("Failed to sign out:", error);
    }
  };

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // 组合会员和积分的胶囊UI
  const renderMembershipAndCredits = () => {
    if (!user) return null;
    const membershipIcon = userMembership?.plan_name === MembershipPlanName.PRO
      ? <Sparkles className="h-4 w-4 text-zinc-800 dark:text-zinc-100" />
      : userMembership?.plan_name === MembershipPlanName.MAX
        ? <Crown className="h-4 w-4 text-amber-500" />
        : <Crown className="h-4 w-4 text-zinc-600 dark:text-zinc-400" />;

    const membershipText = userMembership?.plan_name;

    const membershipClass = userMembership?.plan_name === MembershipPlanName.PRO
      ? "text-zinc-800 dark:text-zinc-100"
      : userMembership?.plan_name === MembershipPlanName.MAX
        ? "text-amber-500"
        : "text-zinc-600 dark:text-zinc-400";

    const capsuleGradient = userMembership?.plan_name === MembershipPlanName.PRO
      ? "bg-gradient-to-r from-zinc-200/80 to-zinc-300/80 dark:from-zinc-500/30 dark:to-zinc-600/30 border-zinc-300 dark:border-zinc-400/30 hover:from-zinc-200/90 hover:to-zinc-300/90 dark:hover:from-zinc-500/40 dark:hover:to-zinc-600/40"
      : userMembership?.plan_name === MembershipPlanName.MAX
        ? "bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-500/40 hover:from-amber-500/30 hover:to-orange-500/30"
        : "bg-card/40 hover:bg-card/60 border-border/50";

    return (
      <Link
        href="/membership"
        className={`px-2 lg:px-3 py-2 text-sm rounded-full transition-all flex items-center gap-1 lg:gap-2 border backdrop-blur-sm ${capsuleGradient}`}
      >
        <div className={`flex items-center ${membershipClass}`}>
          {membershipIcon}
          <span className="ml-1 hidden lg:inline font-medium">{membershipText}</span>
        </div>
        <div className="h-4 w-px bg-border/30 hidden lg:block"></div>
        <div className="flex items-center text-zinc-800 dark:text-zinc-100">
          <Zap className="h-4 w-4 mr-0.5 lg:mr-1" />
          <span className="font-medium">
            {isLoading ? "..." : creditBalance}
          </span>
        </div>
      </Link>
    );
  };

  return (
    <div className="flex justify-end gap-1.5 lg:gap-2 flex-1">
      <ClientOnlyWithFallback
        fallback={<div className="w-[36px] lg:w-[106px] h-[36px]" />}
      >
        <button
          onClick={toggleTheme}
          className="flex items-center px-2 lg:px-3 py-2 text-sm text-muted-foreground rounded-md hover:bg-gray-100 dark:hover:bg-gray-800"
          aria-label={theme === "dark" ? "Switch to Light Mode" : "Switch to Dark Mode"}
        >
          {theme === "dark" ? (
            <>
              <Sun className="h-4 w-4 lg:mr-2" />
              <span className="hidden lg:inline">Light Mode</span>
            </>
          ) : (
            <>
              <Moon className="h-4 w-4 lg:mr-2" />
              <span className="hidden lg:inline">Dark Mode</span>
            </>
          )}
        </button>
      </ClientOnlyWithFallback>

      {/* 组合会员和积分的胶囊 */}
      {user && renderMembershipAndCredits()}

      {/* 礼品图标 - 新用户积分领取 */}
      {user && (
        <div className="flex items-center">
          <GiftIcon variant="header" size="md" />
        </div>
      )}

      {user ? (
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className="relative ml-2 hover:ring-2 hover:ring-primary/20 rounded-full transition-all"
            title="User menu"
            aria-label="Open user menu"
          >
            <Avatar
              size={32}
            />
          </button>

          {showDropdown && (
            <div className="absolute right-0 mt-2 w-56 rounded-lg bg-background border shadow-lg py-1 z-50">
              <div className="px-4 py-2 border-b">
                <p className="text-sm font-medium">
                  {user.user_metadata?.full_name || "Creator"}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.email}
                </p>
              </div>

              <div className="py-1">
                <Link
                  href="/favorite"
                  className="flex items-center px-4 py-2 text-sm hover:bg-muted transition-colors"
                  onClick={() => setShowDropdown(false)}
                >
                  <BookMarked className="mr-2 h-4 w-4" />
                  <span>My Favorites</span>
                </Link>
              </div>

              <div className="border-t py-1">
                <Link
                  href="/profile"
                  className="flex items-center px-4 py-2 text-sm hover:bg-muted transition-colors"
                  onClick={() => setShowDropdown(false)}
                >
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
                {/* <Link
                  href="/settings/preferences"
                  className="flex items-center px-4 py-2 text-sm hover:bg-muted transition-colors"
                  onClick={() => setShowDropdown(false)}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Preferences</span>
                </Link> */}
              </div>

              <div className="border-t py-1">
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Sign Out</span>
                </button>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="flex items-center gap-2">
          <Link
            href="/membership"
            className="px-3 py-1.5 text-sm text-muted-foreground hover:text-foreground border border-border hover:border-border/80 rounded-md transition-colors"
          >
            Pricing
          </Link>
          <button
            onClick={() => openLoginDialog()}
            className="px-4 py-1.5 text-sm bg-gray-800 hover:bg-gray-700 text-white rounded-md transition-colors"
          >
            Sign In
          </button>
        </div>
      )}
    </div>
  );
}
