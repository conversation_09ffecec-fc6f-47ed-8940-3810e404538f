import { useQuery } from '@tanstack/react-query';
import { queueApi } from '@/lib/api/queue';
import useQueueInfoStore from '@/store/useQueueInfoStore';

/**
 * 使用队列信息钩子
 * 
 * 基于React Query获取队列信息并自动缓存
 * 适用于任务在队列中或处理中的场景
 * 
 * @param taskId 任务ID
 */
export function useQueueInfo(taskId: string | undefined) {
    const { setQueueInfo } = useQueueInfoStore();

    return useQuery({
        queryKey: ['queueInfo', taskId],
        queryFn: async () => {
            if (!taskId) {
                throw new Error('Task ID is required');
            }

            try {
                const result = await queueApi.getQueueInfo(taskId);
                console.log("Queue info fetched:", result); // 添加日志，帮助调试

                // 将结果同步到Zustand store中
                setQueueInfo(taskId, result);

                return result;
            } catch (error) {
                console.error("Error fetching queue info:", error);
                throw error;
            }
        },
        // 只有当任务ID存在时才启用查询
        enabled: !!taskId,
        // 默认10秒刷新一次 - 更频繁检查状态
        refetchInterval: 10000,
        // 当组件失去焦点时继续刷新
        refetchIntervalInBackground: true,
        // 使用前一次的数据
        staleTime: 1 * 60 * 1000, // 1分钟内数据视为新鲜
        gcTime: 5 * 60 * 1000,    // 5分钟后删除缓存
        // 重试配置
        retry: 3,
        retryDelay: 1000,
    });
} 