import { CreditTransactionStatus, CreditTransactionType } from '../constant';

/**
 * 创建积分交易记录
 */
export function createCreditTransactionSql(
    userId: string,
    type: CreditTransactionType,
    amount: number,
    description: string = '',
    referenceId: string = null,
    status: CreditTransactionStatus = CreditTransactionStatus.COMPLETED,
): string {
    return `
    INSERT INTO credit_transactions (
      user_id,
      type,
      amount,
      description,
      reference_id,
      status,
      created_at
    ) VALUES (
      '${userId}',
      '${type}',
      ${amount},
      '${description}',
      ${referenceId ? `'${referenceId}'` : 'NULL'},
      '${status}',
      NOW()
    ) RETURNING id, user_id, type, amount, status, created_at;
  `;
}

/**
 * 查询用户积分余额
 */
export function getUserCreditBalanceSql(userId: string): string {
    return `
    SELECT 
      COALESCE(SUM(amount), 0) as balance
    FROM 
      credit_transactions
    WHERE 
      user_id = '${userId}'
      AND status = '${CreditTransactionStatus.COMPLETED}';
  `;
}

/**
 * 查询用户积分交易记录
 */
export function getCreditTransactionsSql(
    userId: string,
    type: CreditTransactionType = null,
    status: CreditTransactionStatus = null,
    limit: number = 20,
    offset: number = 0,
    fromDate: string = null,
    toDate: string = null,
): string {
    let whereClause = `user_id = '${userId}'`;

    if (type) {
        whereClause += ` AND type = '${type}'`;
    }

    if (status) {
        whereClause += ` AND status = '${status}'`;
    }

    if (fromDate) {
        whereClause += ` AND created_at >= '${fromDate}'`;
    }

    if (toDate) {
        whereClause += ` AND created_at <= '${toDate}'`;
    }

    return `
    SELECT 
      id, 
      user_id, 
      type, 
      amount, 
      description, 
      reference_id, 
      status, 
      created_at
    FROM 
      credit_transactions
    WHERE 
      ${whereClause}
    ORDER BY 
      created_at DESC
    LIMIT ${limit} OFFSET ${offset};
  `;
}

/**
 * 查询用户今日发帖奖励次数
 */
export function getUserDailyPostRewardCountSql(
    userId: string,
    type: CreditTransactionType = CreditTransactionType.POST_REWARD,
): string {
    return `
    SELECT 
      COUNT(*) as count
    FROM 
      credit_transactions
    WHERE 
      user_id = '${userId}'
      AND type = '${type}'
      AND status = '${CreditTransactionStatus.COMPLETED}'
      AND created_at >= DATE_TRUNC('day', NOW())
      AND created_at < DATE_TRUNC('day', NOW()) + INTERVAL '1 day';
  `;
}

/**
 * 更新积分交易状态
 */
export function updateCreditTransactionStatusSql(
    transactionId: string,
    status: CreditTransactionStatus,
): string {
    return `
    UPDATE credit_transactions
    SET 
      status = '${status}',
      updated_at = NOW()
    WHERE 
      id = '${transactionId}'
    RETURNING id, user_id, type, amount, status, updated_at;
  `;
} 