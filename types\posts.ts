enum Ratio {
    "16:9" = "16:9",
    "9:16" = "9:16",
    "1:1" = "1:1"
}

// LoRa模型接口
export interface LoRaModel {
    name: string;
    weight?: number;
}

// 视频生成参数接口
export interface VideoInputParams {
    model_id: string;
    model_name?: string;
    seed: number;
    ratio: Ratio;
    steps: number;
    prompt: string;
    duration: string;
    gen_type: string;
    definition: string;
    guidance_scale: number;
    negative_prompt: string;
    refer_img_url?: string; // 参考图片URL
    lora_models?: LoRaModel[]; // LoRa模型列表
    effect_name?: string; // 使用的效果名称
}

export interface PostItemDto {
    id: string;             // 帖子ID
    title: string;          // 标题
    description?: string;   // 描述
    video_url: string;       // 视频URL
    user_id: string;         // 发布者ID
    username: string;       // 发布者用户名
    user_avatar?: string;    // 发布者头像
    like_count: number;      // 点赞数
    comment_count: number;   // 评论数
    created_at: string;        // 创建时间
    task_id?: string;        // 关联的任务ID
    likes?: {
        user_id: string;
    }[];
    videos?: {
        input_params: VideoInputParams
        cover_img: string
    }
    user_profiles: {
        nickname: string;
        avatar: string;
        bio: string;
    }
}

export interface PostItem {
    id: string;             // 帖子ID
    title: string;          // 标题
    description?: string;   // 描述
    videoUrl: string;       // 视频URL
    thumbnailUrl?: string;  // 缩略图URL
    userId: string;         // 发布者ID
    username: string;       // 发布者用户名
    userAvatar?: string;    // 发布者头像
    likeCount: number;      // 点赞数
    commentCount: number;   // 评论数
    createdAt: string;        // 创建时间
    taskId?: string;        // 关联的任务ID
    inputParams?: VideoInputParams
    coverImg?: string
}

export interface Comment {
    content: string;
    created_at: string;
    id: string;
    parent_id: string | null;
    target_id: string;
    target_type: string;
    updated_at: string;
    user_id: string;
    user_profiles: {
        avatar: string | null;
        nickname: string;
    }
}