import { IsString, IsNotEmpty, IsO<PERSON>al, IsEnum, IsObject, IsBoolean } from 'class-validator';

/**
 * fal.ai Webhook请求DTO
 */
export class FalAiWebhookRequestDto {
    @IsString()
    @IsNotEmpty()
    request_id: string;

    @IsString()
    @IsNotEmpty()
    gateway_request_id: string;

    @IsString()
    @IsNotEmpty()
    status: 'OK' | 'ERROR';

    @IsString()
    @IsOptional()
    error?: string;

    @IsObject()
    @IsOptional()
    payload?: any;

    @IsString()
    @IsOptional()
    payload_error?: string;
}

/**
 * fal.ai Webhook响应DTO
 */
export class FalAiWebhookResponseDto {
    @IsBoolean()
    success: boolean;

    @IsString()
    @IsOptional()
    message?: string;
}
