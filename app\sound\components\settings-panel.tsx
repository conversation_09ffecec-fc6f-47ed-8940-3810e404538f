"use client";

import { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useSoundStore, VoiceModel } from "@/store/useSoundStore";
import { useVoiceModels } from "../hooks/useVoiceModels";
import { Volume2, Play, Pause, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";



export function SettingsPanel() {
    const {
        selectedVoiceModel,
        speed,
        stability,
        similarityBoost,
        styleExaggeration,
        setSpeed,
        setStability,
        setSimilarityBoost,
        setStyleExaggeration,
        resetSettings,
    } = useSoundStore();

    const { availableVoiceModels, selectVoiceModel } = useVoiceModels();
    const [isPlayingPreview, setIsPlayingPreview] = useState(false);

    const handlePlayPreview = useCallback(() => {
        if (!selectedVoiceModel) return;

        setIsPlayingPreview(true);
        // TODO: Implement voice preview playback
        setTimeout(() => {
            setIsPlayingPreview(false);
        }, 2000);
    }, [selectedVoiceModel]);

    return (
        <div className="w-80 bg-white dark:bg-black border-l border-gray-200 dark:border-gray-800 p-6 overflow-y-auto">
            <div className="space-y-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <h2 className="text-lg font-medium text-black dark:text-white">Settings</h2>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={resetSettings}
                        className="text-gray-500 hover:text-black dark:hover:text-white text-xs h-auto p-2"
                    >
                        Reset
                    </Button>
                </div>

                {/* Voice Selection */}
                <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                        <Volume2 className="w-4 h-4 text-gray-500" />
                        <h3 className="text-sm font-medium text-black dark:text-white">Voice</h3>
                    </div>
                    <div className="space-y-3">
                        <Select
                            value={selectedVoiceModel?.id || ""}
                            onValueChange={selectVoiceModel}
                        >
                            <SelectTrigger className="bg-transparent border border-gray-200 dark:border-gray-800 text-black dark:text-white">
                                <SelectValue placeholder="Select a voice model" />
                            </SelectTrigger>
                            <SelectContent className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
                                {availableVoiceModels.map((model) => (
                                    <SelectItem
                                        key={model.id}
                                        value={model.id}
                                        className="text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-900"
                                    >
                                        <div className="flex flex-col">
                                            <span className="font-medium">{model.name}</span>
                                            <span className="text-xs text-gray-500">{model.description}</span>
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        {selectedVoiceModel && (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handlePlayPreview}
                                disabled={isPlayingPreview}
                                className="w-full bg-transparent border border-gray-200 dark:border-gray-800 text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-900"
                            >
                                {isPlayingPreview ? (
                                    <>
                                        <Pause className="w-3 h-3 mr-2" />
                                        Playing...
                                    </>
                                ) : (
                                    <>
                                        <Play className="w-3 h-3 mr-2" />
                                        Preview Voice
                                    </>
                                )}
                            </Button>
                        )}
                    </div>
                </div>

                {/* Model Selection */}
                <div className="space-y-3">
                    <h3 className="text-sm font-medium text-black dark:text-white">Model</h3>
                    <Select defaultValue="eleven-multilingual-v2">
                        <SelectTrigger className="bg-transparent border border-gray-200 dark:border-gray-800 text-black dark:text-white">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
                            <SelectItem value="eleven-multilingual-v2" className="text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-900">
                                Eleven Multilingual v2
                            </SelectItem>
                            <SelectItem value="eleven-turbo-v2" className="text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-900">
                                Eleven Turbo v2
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                {/* Speed Control */}
                <div className="space-y-3">
                    <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium text-black dark:text-white">Speed</h3>
                        <span className="text-xs text-gray-500 font-mono">{speed.toFixed(2)}</span>
                    </div>
                    <div className="space-y-2">
                        <Slider
                            value={[speed]}
                            onValueChange={(value) => setSpeed(value[0])}
                            min={0.25}
                            max={4.0}
                            step={0.05}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500 font-mono">
                            <span>0.25x</span>
                            <span>4.0x</span>
                        </div>
                    </div>
                </div>

                {/* Stability Control */}
                <div className="space-y-3">
                    <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium text-black dark:text-white">Stability</h3>
                        <span className="text-xs text-gray-500 font-mono">{(stability * 100).toFixed(0)}%</span>
                    </div>
                    <div className="space-y-2">
                        <Slider
                            value={[stability]}
                            onValueChange={(value) => setStability(value[0])}
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500">
                            <span>Variable</span>
                            <span>Stable</span>
                        </div>
                    </div>
                </div>

                {/* Similarity Boost Control */}
                <div className="space-y-3">
                    <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium text-black dark:text-white">Similarity Boost</h3>
                        <span className="text-xs text-gray-500 font-mono">{(similarityBoost * 100).toFixed(0)}%</span>
                    </div>
                    <div className="space-y-2">
                        <Slider
                            value={[similarityBoost]}
                            onValueChange={(value) => setSimilarityBoost(value[0])}
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500">
                            <span>Low</span>
                            <span>High</span>
                        </div>
                    </div>
                </div>

                {/* Style Exaggeration Control */}
                <div className="space-y-3">
                    <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium text-black dark:text-white">Style Exaggeration</h3>
                        <span className="text-xs text-gray-500 font-mono">{(styleExaggeration * 100).toFixed(0)}%</span>
                    </div>
                    <div className="space-y-2">
                        <Slider
                            value={[styleExaggeration]}
                            onValueChange={(value) => setStyleExaggeration(value[0])}
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500">
                            <span>None</span>
                            <span>Exaggerated</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
