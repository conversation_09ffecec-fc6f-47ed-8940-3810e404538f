import { CircleX, Upload, CheckCircle } from "lucide-react";
import { UploadState } from "@/types/upload";

interface VideoPreviewProps {
    uploadState: UploadState;
    triggerFileInput: () => void;
    handleRemoveVideo: () => void;
}

export const VideoPreview = ({
    uploadState,
    triggerFileInput,
    handleRemoveVideo
}: VideoPreviewProps) => {
    const { videoPreviewUrl, isUploading, uploadProgress, uploadComplete, videoFile } = uploadState;

    return (
        <div className="w-1/2 bg-muted flex items-center justify-center cursor-pointer rounded-md relative">
            {/* 上传进度条 - 显示在顶部 */}
            {isUploading && (
                <div className="absolute top-0 left-0 right-0 h-1.5 bg-muted-foreground/20 z-20">
                    <div
                        className="absolute left-0 top-0 h-full bg-primary transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                    ></div>
                </div>
            )}

            {/* 文件名悬浮显示 - 顶部 */}
            {videoFile && (
                <div className="absolute top-0 left-0 right-0 p-3 bg-linear-to-b from-black/70 to-transparent text-white z-10">
                    <div className="flex items-center justify-between">
                        <div className="truncate mr-2">
                            <p className="font-medium">{videoFile.name}</p>
                            <p className="text-xs opacity-80">{(videoFile.size / 1024 / 1024).toFixed(2)} MB</p>
                        </div>
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveVideo();
                            }}
                            className="p-1 rounded-full hover:bg-white/20 transition-colors"
                        >
                            <CircleX size={20} />
                        </button>
                    </div>
                </div>
            )}

            {videoPreviewUrl ? (
                <div className="relative w-full h-full">
                    <video src={videoPreviewUrl} controls className="w-full h-full object-contain" />

                    {/* 底部渐变底色 */}
                    <div className="absolute bottom-0 left-0 right-0 p-4 bg-linear-to-t from-black/70 to-transparent text-white">
                        {/* 上传状态指示器 */}
                        {(isUploading || uploadComplete) && (
                            <div>
                                {uploadComplete ? (
                                    <div className="flex items-center text-green-400">
                                        <CheckCircle className="w-5 h-5 mr-2" />
                                        <span>上传完成</span>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>上传中...</span>
                                            <span>{uploadProgress}%</span>
                                        </div>
                                        <div className="w-full bg-white/30 rounded-full h-1.5">
                                            <div
                                                className="bg-white h-1.5 rounded-full transition-all duration-300"
                                                style={{ width: `${uploadProgress}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            ) : (
                <div className="text-center flex-1 w-full h-full flex flex-col items-center justify-center" onClick={triggerFileInput}>
                    <Upload size={64} className="mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground text-lg">点击上传视频</p>
                </div>
            )}
        </div>
    );
}; 