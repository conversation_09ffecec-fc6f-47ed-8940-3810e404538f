-- 创建邀请码表
CREATE TABLE IF NOT EXISTS public.invitation_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    used_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    max_uses INTEGER DEFAULT 1,
    current_uses INTEGER DEFAULT 0
);

-- 创建用户会话历史表
CREATE TABLE IF NOT EXISTS public.movie_agent_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    prompt TEXT NOT NULL,
    think_content TEXT DEFAULT NULL,
    plan_content TEXT DEFAULT NULL,
    keyframe_prompts_content TEXT DEFAULT NULL,
    keyframe_prompts JSONB DEFAULT NULL,
    keyframe_tasks JSONB DEFAULT NULL,
    search_result TEXT DEFAULT NULL,
    status VARCHAR(20) DEFAULT 'active', -- active, completed, archived
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 创建用户访问权限表
CREATE TABLE IF NOT EXISTS public.user_access_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID UNIQUE NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    has_movie_agent_access BOOLEAN DEFAULT FALSE,
    invitation_code_used VARCHAR(50) REFERENCES public.invitation_codes(code),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS invitation_codes_code_idx ON public.invitation_codes(code);
CREATE INDEX IF NOT EXISTS invitation_codes_used_idx ON public.invitation_codes(is_used);
CREATE INDEX IF NOT EXISTS movie_agent_sessions_user_id_idx ON public.movie_agent_sessions(user_id);
CREATE INDEX IF NOT EXISTS movie_agent_sessions_created_at_idx ON public.movie_agent_sessions(created_at);
CREATE INDEX IF NOT EXISTS user_access_permissions_user_id_idx ON public.user_access_permissions(user_id);

-- 启用行级安全
ALTER TABLE public.invitation_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.movie_agent_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_access_permissions ENABLE ROW LEVEL SECURITY;

-- 创建策略
-- 邀请码策略：任何人都可以查看未使用的邀请码
CREATE POLICY "Anyone can view unused invitation codes"
    ON public.invitation_codes
    FOR SELECT
    TO authenticated
    USING (is_used = FALSE OR used_by_user_id = auth.uid());

-- 用户会话策略：用户只能查看自己的会话
CREATE POLICY "Users can view their own sessions"
    ON public.movie_agent_sessions
    FOR ALL
    TO authenticated
    USING (user_id = auth.uid());

-- 用户访问权限策略：用户只能查看自己的权限
CREATE POLICY "Users can view their own access permissions"
    ON public.user_access_permissions
    FOR ALL
    TO authenticated
    USING (user_id = auth.uid());

-- 服务角色可以操作所有表
CREATE POLICY "Service role can do everything on invitation_codes"
    ON public.invitation_codes
    FOR ALL
    TO service_role
    USING (true);

CREATE POLICY "Service role can do everything on movie_agent_sessions"
    ON public.movie_agent_sessions
    FOR ALL
    TO service_role
    USING (true);

CREATE POLICY "Service role can do everything on user_access_permissions"
    ON public.user_access_permissions
    FOR ALL
    TO service_role
    USING (true);

-- 删除旧的测试邀请码并重新插入（确保使用正确的默认值）
DELETE FROM public.invitation_codes WHERE code IN ('REELMIND2024', 'BETAUSER', 'AI4MOVIES', 'EARLYACCESS', 'VIPCODE');

-- 插入一些测试邀请码（每个只能使用一次）
INSERT INTO public.invitation_codes (code, max_uses) VALUES 
    ('REELMIND2024', 1),
    ('BETAUSER', 1),
    ('AI4MOVIES', 1),
    ('EARLYACCESS', 1),
    ('VIPCODE', 1),
    ('TESTCODE1', 1),
    ('TESTCODE2', 1),
    ('TESTCODE3', 1),
    ('TESTCODE4', 1),
    ('TESTCODE5', 1)
ON CONFLICT (code) DO NOTHING; 