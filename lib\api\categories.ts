import { API_CONFIG } from "@/lib/config";
import type { Category } from "@/types";

// Default "All" category option
export const ALL_CATEGORY: Category = {
    id: "all",
    name: "All"
};

// Define tag response data type
interface TagResponse {
    id: string
    name: string
    usage_count: number
    created_at: string
}

interface ApiResponse<T> {
    code: number;
    message: string;
    data: T;
}

/**
 * 获取所有类别标签
 * 这个函数在服务端运行，直接获取类别数据
 */
export async function getCategories(): Promise<Category[]> {
    try {
        const response = await fetch(`${API_CONFIG.BASE_URL}/tags`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            next: { revalidate: 300 }, // 缓存1小时
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch categories: ${response.status}`);
        }

        const data = await response.json() as ApiResponse<TagResponse[]>;

        // 转换数据格式
        const categories: Category[] = data.data.map(tag => ({
            id: tag.id,
            name: tag.name
        }));

        // 添加"全部"类别选项
        return [ALL_CATEGORY, ...categories];
    } catch (error) {
        console.error("Error fetching categories:", error);
        // 出错时返回只有"全部"的数组
        return [ALL_CATEGORY];
    }
} 