"use client";

import { motion } from "framer-motion";
import { Send, MessageSquare, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useFeedbackForm } from "@/hooks/use-feedback-form";
import { FeedbackType } from "@/lib/api/feedback";
import useAuthStore from "@/store/useAuthStore";

// 反馈类型选项 - 简化为3种
const feedbackTypeOptions = [
  { value: FeedbackType.BUG, label: 'Bug Report' },
  { value: FeedbackType.SUGGESTION, label: 'Suggestion' },
  { value: FeedbackType.OTHER, label: 'Other' },
];

export default function ContactPage() {
  const { isAuthenticated } = useAuthStore();
  const {
    formState,
    isSubmitting,
    isSubmitted,
    handleField<PERSON><PERSON><PERSON>,
    handleSubmit,
    getFieldError,
    hasFieldError,
  } = useFeedbackForm();



  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-3xl md:text-4xl font-light text-foreground mb-4">
            Quick Feedback
          </h1>
          <p className="text-muted-foreground max-w-lg mx-auto">
            Found a bug or have a suggestion? Let us know in 30 seconds.
          </p>
        </motion.div>

        {/* Contact Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-card rounded-lg border border-border p-8"
        >
          <div className="flex items-center mb-8">
            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3">
              <MessageSquare className="w-5 h-5 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-medium text-foreground">Quick Feedback</h2>
              <p className="text-sm text-muted-foreground">Just 3 fields, takes 30 seconds</p>
            </div>
          </div>

          {isSubmitted ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center py-12"
            >
              <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
              <h3 className="text-lg font-medium text-foreground mb-2">
                Feedback Submitted
              </h3>
              <p className="text-muted-foreground text-sm">
                Thank you for your feedback. We'll review it and get back to you if needed.
              </p>
            </motion.div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Authentication Check */}
              {!isAuthenticated && (
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 mb-6">
                  <p className="text-sm text-yellow-600 dark:text-yellow-400">
                    Please login to submit feedback.
                  </p>
                </div>
              )}

              {/* Feedback Type */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Type
                </label>
                <Select
                  value={formState.type}
                  onValueChange={(value) => handleFieldChange('type', value as FeedbackType)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select feedback type" />
                  </SelectTrigger>
                  <SelectContent>
                    {feedbackTypeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Subject */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Subject *
                </label>
                <Input
                  value={formState.subject}
                  onChange={(e) => handleFieldChange('subject', e.target.value)}
                  placeholder="What's the issue or suggestion?"
                  className={hasFieldError('subject') ? 'border-red-500' : ''}
                  maxLength={200}
                  autoComplete="off"
                />
                {hasFieldError('subject') && (
                  <p className="text-sm text-red-500">{getFieldError('subject')}</p>
                )}
              </div>

              {/* Message */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Details *
                </label>
                <Textarea
                  value={formState.message}
                  onChange={(e) => handleFieldChange('message', e.target.value)}
                  placeholder="Tell us more..."
                  rows={4}
                  className={hasFieldError('message') ? 'border-red-500' : ''}
                  maxLength={2000}
                  autoComplete="off"
                />
                {hasFieldError('message') && (
                  <p className="text-sm text-red-500">{getFieldError('message')}</p>
                )}
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isSubmitting || !isAuthenticated}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Submit Feedback
                  </>
                )}
              </Button>
            </form>
          )}
        </motion.div>
      </div>
    </div>
  );
}