import { Modu<PERSON> } from '@nestjs/common';
import { GenerationController } from './generation.controller';
import { GenerationService } from './generation.service';
import { CustomLogger } from 'src/common/services/logger.service';
import { ClientInfoService } from 'src/common/services/client-info.service';
import { UserModule } from 'src/user/user.module';
import { MembershipModule } from 'src/membership/membership.module';
import { CreditsModule } from 'src/credits/credits.module';
import { ErrorAuditModule } from 'src/common/module/error-audit.module';
import { FalAiService } from './services/fal-ai.service';

@Module({
    imports: [UserModule, MembershipModule, CreditsModule, ErrorAuditModule],
    controllers: [GenerationController],
    providers: [GenerationService, ClientInfoService, CustomLogger, FalAiService],
    exports: [GenerationService, FalAiService],
})
export class GenerationModule {}