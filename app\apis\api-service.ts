import { createClient } from "@/lib/supabase/client";
import { API_CONFIG } from "@/lib/config";

// API 密钥接口
export interface ApiKey {
  id: string;
  name: string;
  key: string;
  created_at: string;
  last_used_at: string | null;
}

// API 会员资格使用情况接口
export interface MembershipUsage {
  tier: string;
  monthly_credits: number;
  used_credits: number;
  remaining_credits: number;
  reset_date: string;
}

// API 积分余额接口
export interface CreditBalance {
  total_credits: number;
  purchased_credits: number;
  membership_credits: number;
}

// API 积分交易记录接口
export interface CreditTransaction {
  id: string;
  type: 'purchase' | 'usage' | 'refund';
  amount: number;
  description: string;
  created_at: string;
}

// API 服务
export const apiService = {
  // 获取 API 密钥列表
  async getApiKeys(): Promise<ApiKey[]> {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error || !data.session) {
        throw new Error("Please login first");
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/keys`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.session.access_token}`
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '获取 API 密钥失败');
      }

      const responseData = await response.json();
      console.log('API密钥响应数据:', responseData);

      // 处理不同的响应格式
      if (Array.isArray(responseData)) {
        // 如果响应已经是数组
        return responseData;
      } else if (responseData.data && responseData.data.keys) {
        // 如果响应有data.keys属性（直接从后端透传）
        return responseData.data.keys.map(key => ({
          id: key.id,
          name: key.name,
          key: key.api_key,
          created_at: key.created_at,
          last_used_at: key.last_used_at
        }));
      } else if (responseData.keys) {
        // 如果响应有keys属性
        return responseData.keys;
      } else {
        // 无法识别的格式，返回空数组
        console.warn('无法识别的API密钥响应格式:', responseData);
        return [];
      }
    } catch (error) {
      console.error('获取 API 密钥失败:', error);
      throw error;
    }
  },

  // 创建 API 密钥
  async createApiKey(name: string, description: string = ""): Promise<ApiKey> {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error || !data.session) {
        throw new Error("Please login first");
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/keys`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.session.access_token}`
        },
        body: JSON.stringify({ name, description }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '创建 API 密钥失败');
      }

      return await response.json();
    } catch (error) {
      console.error('创建 API 密钥失败:', error);
      throw error;
    }
  },

  // 删除 API 密钥
  async deleteApiKey(id: string): Promise<void> {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error || !data.session) {
        throw new Error("Please login first");
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/keys`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.session.access_token}`
        },
        body: JSON.stringify({ id }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '删除 API 密钥失败');
      }
    } catch (error) {
      console.error('删除 API 密钥失败:', error);
      throw error;
    }
  },

  // 获取 API 会员资格使用情况
  async getMembershipUsage(): Promise<MembershipUsage> {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error || !data.session) {
        throw new Error("Please login first");
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/membership/usage`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.session.access_token}`
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '获取 API 会员资格使用情况失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取 API 会员资格使用情况失败:', error);
      throw error;
    }
  },

  // 更新 API 会员资格
  async updateMembership(tier: string): Promise<MembershipUsage> {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error || !data.session) {
        throw new Error("Please login first");
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/membership`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.session.access_token}`
        },
        body: JSON.stringify({ tier }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '更新 API 会员资格失败');
      }

      return await response.json();
    } catch (error) {
      console.error('更新 API 会员资格失败:', error);
      throw error;
    }
  },

  // 获取 API 积分余额
  async getCreditBalance(): Promise<CreditBalance> {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error || !data.session) {
        throw new Error("Please login first");
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/credits/balance`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.session.access_token}`
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '获取 API 积分余额失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取 API 积分余额失败:', error);
      throw error;
    }
  },

  // 获取 API 积分交易记录
  async getCreditTransactions(limit: number = 10): Promise<CreditTransaction[]> {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error || !data.session) {
        throw new Error("Please login first");
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/credits/transactions?limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.session.access_token}`
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '获取 API 积分交易记录失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取 API 积分交易记录失败:', error);
      throw error;
    }
  },

  // 购买 API 积分
  async purchaseCredits(amount: number): Promise<CreditBalance> {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error || !data.session) {
        throw new Error("Please login first");
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/credits/purchase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.session.access_token}`
        },
        body: JSON.stringify({ amount }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '购买 API 积分失败');
      }

      return await response.json();
    } catch (error) {
      console.error('购买 API 积分失败:', error);
      throw error;
    }
  },
}; 