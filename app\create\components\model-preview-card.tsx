"use client"

import { Model } from "@/types/model";
import Image from "next/image";
import { useState, useEffect } from "react";
import useModelSelectorStore from "@/store/useModelSelectorStore";

interface ModelPreviewCardProps {
    model: Model;
    index: number;
    onClick?: (e: React.MouseEvent) => void;
}

export function ModelPreviewCard({ model, index, onClick }: ModelPreviewCardProps) {
    // 视频相关状态
    const [isVideoLoaded, setIsVideoLoaded] = useState(false);
    const [shouldLoadVideo, setShouldLoadVideo] = useState(false);

    // 处理视频加载完成事件
    const handleVideoLoaded = () => {
        setIsVideoLoaded(true);
    };

    // 使用 requestAnimationFrame 延迟加载视频
    useEffect(() => {
        requestAnimationFrame(() => {
            setShouldLoadVideo(true);
        });
    }, []);

    // 处理选择模型的点击事件
    const handleSelectClick = (e: React.MouseEvent) => {
        e.stopPropagation(); // 阻止冒泡，避免触发父元素的onClick
        // 通过store更新选中的模型ID
        useModelSelectorStore.getState().setSelectedModelId(model.id);
        
        // 如果提供了自定义点击处理函数，则调用它
        if (onClick) {
            onClick(e);
        }
    };

    return (
        <div
            key={model.id}
            className="relative rounded-lg overflow-hidden aspect-square bg-background/50 cursor-pointer transition-all hover:scale-[1.02] group"
        >
            {/* 模型标签 */}
            <div className={`absolute top-1 left-1 z-10 ${index === 0
                ? 'bg-black/30 backdrop-blur-sm border border-amber-700/60' // 黑金风格毛玻璃效果
                : index === 1
                    ? 'bg-gradient-to-r from-red-500 to-amber-500' // 热门火焰色调
                    : 'bg-gradient-to-r from-emerald-600 to-teal-600' // 高端/昂贵色调
                } ${index === 0 ? 'text-amber-300/90' : 'text-white'} text-xs font-bold ${index === 0 ? 'px-2 py-0.5 rounded-md md:w-auto w-[120px] truncate' : 'px-2 py-1 rounded-full'} ${index === 0 ? '' : 'shadow-lg'} flex items-center`}>
                {index === 0 ? (
                    <>
                        <span style={{ textShadow: '0 0 2px rgba(217, 119, 6, 0.4)' }}>Reelmind Selected</span>
                    </>
                ) : index === 1 ? (
                    <>
                        <span className="mr-1">🔥</span>
                        <span>Hot</span>
                    </>
                ) : (
                    <>
                        <span className="mr-1">💎</span>
                        <span>Expensive</span>
                    </>
                )}
            </div>

            {/* 模型封面图 - 当视频未加载完成时显示 */}
            {(!isVideoLoaded || !model.cover_video) && (
                <Image
                    src={model.cover_img || "/placeholder.svg"}
                    alt={model.name}
                    fill
                    className="object-cover"
                />
            )}

            {/* 懒加载视频元素 */}
            {model.cover_video && shouldLoadVideo && (
                <video
                    src={model.cover_video}
                    preload="metadata"
                    muted
                    playsInline
                    autoPlay
                    loop
                    onLoadedData={handleVideoLoaded}
                    className={`absolute inset-0 w-full h-full object-cover ${isVideoLoaded ? 'opacity-100' : 'opacity-0'
                        } transition-opacity duration-300`}
                    poster={model.cover_img || "/placeholder.svg"}
                    data-silent="true"
                    disablePictureInPicture
                />
            )}

            {/* 渐变叠加层 */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent" />

            {/* 底部渐变和模型名称 */}
            <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black via-black/70 to-transparent py-2 px-1">
                <p className="text-base text-white text-center font-medium line-clamp-2">{model.name}</p>
            </div>

            {/* Reelmind Selected 特殊效果 - 黑金风格叠加层 */}
            {index === 0 && (
                <div className="absolute inset-0 bg-gradient-to-b from-amber-900/5 to-black/10 pointer-events-none"></div>
            )}

            {/* 悬停叠加层 - 选择模型按钮 */}
            <div
                className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                onClick={handleSelectClick}
            >
                <div className="px-4 py-2 bg-white text-black font-medium rounded-lg hover:bg-white/90 active:scale-95 transition-all">
                    Select
                </div>
            </div>
        </div>
    );
}
