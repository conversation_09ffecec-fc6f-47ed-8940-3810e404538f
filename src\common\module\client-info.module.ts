import { Module, Global } from '@nestjs/common';
import { ClientInfoService } from '../services/client-info.service';
import { SupabaseModule } from '../providers/supabase.module';
import { CustomLogger } from '../services/logger.service';

@Global()
@Module({
    imports: [SupabaseModule],
    providers: [ClientInfoService, CustomLogger],
    exports: [ClientInfoService, CustomLogger],
})
export class ClientInfoModule {} 