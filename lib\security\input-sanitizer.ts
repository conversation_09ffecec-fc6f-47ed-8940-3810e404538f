import DOMPurify from 'isomorphic-dompurify';

/**
 * Security utility for input validation and sanitization
 */
export class InputSanitizer {
  // Maximum lengths for different fields
  static readonly MAX_SUBJECT_LENGTH = 200;
  static readonly MAX_MESSAGE_LENGTH = 2000;
  static readonly MIN_SUBJECT_LENGTH = 3;
  static readonly MIN_MESSAGE_LENGTH = 5;

  // Dangerous patterns to detect
  private static readonly DANGEROUS_PATTERNS = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, // Script tags
    /javascript:/gi, // JavaScript protocol
    /vbscript:/gi, // VBScript protocol
    /on\w+\s*=/gi, // Event handlers (onclick, onload, etc.)
    /<iframe\b[^>]*>/gi, // Iframe tags
    /<object\b[^>]*>/gi, // Object tags
    /<embed\b[^>]*>/gi, // Embed tags
    /<form\b[^>]*>/gi, // Form tags
    /data:text\/html/gi, // Data URLs with HTML
    /expression\s*\(/gi, // CSS expressions
  ];

  // SQL injection patterns
  private static readonly SQL_INJECTION_PATTERNS = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
    /(--|\/\*|\*\/|;|'|"|`)/g,
    /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi,
    /\b(UNION|SELECT)\b.*\b(FROM|WHERE)\b/gi,
  ];

  /**
   * Sanitize HTML content to prevent XSS
   */
  static sanitizeHtml(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    // Use DOMPurify to sanitize HTML
    const sanitized = DOMPurify.sanitize(input, {
      ALLOWED_TAGS: [], // No HTML tags allowed
      ALLOWED_ATTR: [], // No attributes allowed
      KEEP_CONTENT: true, // Keep text content
    });

    return sanitized.trim();
  }

  /**
   * Validate and sanitize subject field
   */
  static validateSubject(subject: string): { isValid: boolean; sanitized: string; error?: string } {
    if (!subject || typeof subject !== 'string') {
      return { isValid: false, sanitized: '', error: 'Subject is required' };
    }

    const sanitized = this.sanitizeHtml(subject);

    // Length validation
    if (sanitized.length < this.MIN_SUBJECT_LENGTH) {
      return { 
        isValid: false, 
        sanitized, 
        error: `Subject must be at least ${this.MIN_SUBJECT_LENGTH} characters` 
      };
    }

    if (sanitized.length > this.MAX_SUBJECT_LENGTH) {
      return { 
        isValid: false, 
        sanitized, 
        error: `Subject must be less than ${this.MAX_SUBJECT_LENGTH} characters` 
      };
    }

    // Check for dangerous patterns
    const dangerousPattern = this.DANGEROUS_PATTERNS.find(pattern => pattern.test(sanitized));
    if (dangerousPattern) {
      return { 
        isValid: false, 
        sanitized, 
        error: 'Subject contains invalid content' 
      };
    }

    // Check for SQL injection patterns
    const sqlPattern = this.SQL_INJECTION_PATTERNS.find(pattern => pattern.test(sanitized));
    if (sqlPattern) {
      return { 
        isValid: false, 
        sanitized, 
        error: 'Subject contains invalid content' 
      };
    }

    return { isValid: true, sanitized };
  }

  /**
   * Validate and sanitize message field
   */
  static validateMessage(message: string): { isValid: boolean; sanitized: string; error?: string } {
    if (!message || typeof message !== 'string') {
      return { isValid: false, sanitized: '', error: 'Message is required' };
    }

    const sanitized = this.sanitizeHtml(message);

    // Length validation
    if (sanitized.length < this.MIN_MESSAGE_LENGTH) {
      return { 
        isValid: false, 
        sanitized, 
        error: `Message must be at least ${this.MIN_MESSAGE_LENGTH} characters` 
      };
    }

    if (sanitized.length > this.MAX_MESSAGE_LENGTH) {
      return { 
        isValid: false, 
        sanitized, 
        error: `Message must be less than ${this.MAX_MESSAGE_LENGTH} characters` 
      };
    }

    // Check for dangerous patterns
    const dangerousPattern = this.DANGEROUS_PATTERNS.find(pattern => pattern.test(sanitized));
    if (dangerousPattern) {
      return { 
        isValid: false, 
        sanitized, 
        error: 'Message contains invalid content' 
      };
    }

    // Check for SQL injection patterns
    const sqlPattern = this.SQL_INJECTION_PATTERNS.find(pattern => pattern.test(sanitized));
    if (sqlPattern) {
      return { 
        isValid: false, 
        sanitized, 
        error: 'Message contains invalid content' 
      };
    }

    return { isValid: true, sanitized };
  }

  /**
   * Validate feedback type
   */
  static validateFeedbackType(type: string): { isValid: boolean; error?: string } {
    const validTypes = ['bug', 'suggestion', 'other'];
    
    if (!type || typeof type !== 'string') {
      return { isValid: false, error: 'Feedback type is required' };
    }

    if (!validTypes.includes(type.toLowerCase())) {
      return { isValid: false, error: 'Invalid feedback type' };
    }

    return { isValid: true };
  }

  /**
   * Comprehensive validation for all feedback fields
   */
  static validateFeedbackInput(input: {
    type: string;
    subject: string;
    message: string;
  }): {
    isValid: boolean;
    sanitizedData?: {
      type: string;
      subject: string;
      message: string;
    };
    errors?: {
      type?: string;
      subject?: string;
      message?: string;
    };
  } {
    const errors: any = {};
    
    // Validate type
    const typeValidation = this.validateFeedbackType(input.type);
    if (!typeValidation.isValid) {
      errors.type = typeValidation.error;
    }

    // Validate subject
    const subjectValidation = this.validateSubject(input.subject);
    if (!subjectValidation.isValid) {
      errors.subject = subjectValidation.error;
    }

    // Validate message
    const messageValidation = this.validateMessage(input.message);
    if (!messageValidation.isValid) {
      errors.message = messageValidation.error;
    }

    const hasErrors = Object.keys(errors).length > 0;

    if (hasErrors) {
      return { isValid: false, errors };
    }

    return {
      isValid: true,
      sanitizedData: {
        type: input.type.toLowerCase(),
        subject: subjectValidation.sanitized,
        message: messageValidation.sanitized,
      },
    };
  }

  /**
   * Escape HTML entities for safe display
   */
  static escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Check if input contains suspicious patterns
   */
  static containsSuspiciousContent(input: string): boolean {
    return this.DANGEROUS_PATTERNS.some(pattern => pattern.test(input)) ||
           this.SQL_INJECTION_PATTERNS.some(pattern => pattern.test(input));
  }
}
