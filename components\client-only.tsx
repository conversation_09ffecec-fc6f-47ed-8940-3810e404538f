"use client";

import { ReactNode } from 'react';
import { ClientOnly as ThemeClientOnly, ClientOnlyWithFallback as ThemeClientOnlyWithFallback } from '@/contexts/theme-context';

/**
 * 仅在客户端渲染的组件包装器
 * 用于包装需要浏览器API或需要防止水合错误的组件
 */
export function ClientOnly({ children }: { children: ReactNode }) {
    return <ThemeClientOnly>{children}</ThemeClientOnly>;
}

/**
 * 带有占位符的客户端渲染组件包装器
 * 服务端渲染时会显示fallback内容，客户端挂载后显示实际内容
 */
export function ClientOnlyWithFallback({
    children,
    fallback
}: {
    children: ReactNode;
    fallback: ReactNode;
}) {
    return (
        <ThemeClientOnlyWithFallback fallback={fallback}>
            {children}
        </ThemeClientOnlyWithFallback>
    );
} 