"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { useProfile } from "@/hooks/use-profile";
import { Loader2, Save, CheckCircle } from "lucide-react";
import { Avatar } from "@/components/shared/avatar";
import { useAuth } from "@/contexts/auth-context";
import type { UpdateProfileParams } from "@/lib/api/user";

export function ProfileForm() {
    const { user } = useAuth();
    const { profile, error, isLoading, isUpdating, updateProfile } = useProfile();
    const [formData, setFormData] = useState<UpdateProfileParams>({
        nickname: "",
        bio: "",
        website: "",
        location: "",
    });
    const [isSaved, setIsSaved] = useState(false);

    // 当资料数据加载时填充表单
    useEffect(() => {
        if (profile) {
            setFormData({
                nickname: profile.nickname || "",
                bio: profile.bio || "",
                website: profile.website || "",
                location: profile.location || "",
            });
        }
    }, [profile]);

    // 处理表单输入变化
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    // 处理表单提交
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        updateProfile(formData, {
            onSuccess: () => {
                // 显示成功提示，并在2秒后消失
                setIsSaved(true);
                setTimeout(() => setIsSaved(false), 2000);
            }
        });
    };

    return (
        <div className="w-full max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-8 items-start">
                {/* 头像部分 */}
                <div className="flex flex-col items-center space-y-4">
                    <Avatar size={128} className="border-2 border-primary/10" />
                    {/* 这里可以扩展添加头像上传功能 */}
                </div>

                {/* 表单部分 */}
                <div className="flex-1 w-full">
                    <h1 className="text-2xl font-bold">{profile?.nickname || user?.email}</h1>
                    <p className="text-muted-foreground">{user?.email}</p>

                    {error && (
                        <div className="mt-4 p-3 bg-destructive/10 text-destructive rounded-md">
                            {error}
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="mt-8 space-y-6">
                        <div className="space-y-2">
                            <label htmlFor="nickname" className="text-sm font-medium">
                                Full Name
                            </label>
                            <Input
                                id="nickname"
                                name="nickname"
                                value={formData.nickname}
                                onChange={handleChange}
                                placeholder="Your full name"
                                disabled={isLoading || isUpdating}
                            />
                        </div>

                        <div className="space-y-2">
                            <label htmlFor="bio" className="text-sm font-medium">
                                Bio
                            </label>
                            <textarea
                                id="bio"
                                name="bio"
                                value={formData.bio || ""}
                                onChange={handleChange}
                                placeholder="Tell us about yourself"
                                className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[100px]"
                                disabled={isLoading || isUpdating}
                            />
                        </div>

                        <div className="space-y-2">
                            <label htmlFor="website" className="text-sm font-medium">
                                Website
                            </label>
                            <Input
                                id="website"
                                name="website"
                                value={formData.website || ""}
                                onChange={handleChange}
                                placeholder="https://example.com"
                                disabled={isLoading || isUpdating}
                            />
                        </div>

                        <div className="space-y-2">
                            <label htmlFor="location" className="text-sm font-medium">
                                Location
                            </label>
                            <Input
                                id="location"
                                name="location"
                                value={formData.location || ""}
                                onChange={handleChange}
                                placeholder="City, Country"
                                disabled={isLoading || isUpdating}
                            />
                        </div>

                        <div className="flex justify-end mt-8">
                            <Button
                                type="submit"
                                disabled={isLoading || isUpdating || isSaved}
                                className="min-w-[120px]"
                            >
                                {isUpdating ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Saving...
                                    </>
                                ) : isSaved ? (
                                    <>
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Saved!
                                    </>
                                ) : (
                                    <>
                                        <Save className="mr-2 h-4 w-4" />
                                        Save
                                    </>
                                )}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
} 