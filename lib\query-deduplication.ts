/**
 * 查询去重管理器
 * 防止相同的 API 请求在短时间内被重复调用
 */

interface PendingQuery {
    promise: Promise<any>;
    timestamp: number;
}

class QueryDeduplicationManager {
    private pendingQueries = new Map<string, PendingQuery>();
    private readonly CACHE_DURATION = 5000; // 5秒内的重复请求会被去重

    /**
     * 执行去重的查询
     * @param key 查询的唯一标识符
     * @param queryFn 查询函数
     * @returns Promise<T>
     */
    async executeQuery<T>(key: string, queryFn: () => Promise<T>): Promise<T> {
        const now = Date.now();
        const existing = this.pendingQueries.get(key);

        // 如果存在未过期的查询，返回现有的 Promise
        if (existing && (now - existing.timestamp) < this.CACHE_DURATION) {
            console.log(`[QueryDedup] Reusing existing query for key: ${key}`);
            return existing.promise;
        }

        // 创建新的查询
        console.log(`[QueryDedup] Creating new query for key: ${key}`);
        const promise = queryFn().finally(() => {
            // 查询完成后清理
            this.pendingQueries.delete(key);
        });

        // 存储查询
        this.pendingQueries.set(key, {
            promise,
            timestamp: now
        });

        return promise;
    }

    /**
     * 清理过期的查询
     */
    cleanup(): void {
        const now = Date.now();
        for (const [key, query] of this.pendingQueries.entries()) {
            if (now - query.timestamp > this.CACHE_DURATION) {
                this.pendingQueries.delete(key);
            }
        }
    }

    /**
     * 清理所有查询
     */
    clear(): void {
        this.pendingQueries.clear();
    }
}

// 全局实例
export const queryDeduplicationManager = new QueryDeduplicationManager();

// 定期清理过期查询
if (typeof window !== 'undefined') {
    setInterval(() => {
        queryDeduplicationManager.cleanup();
    }, 10000); // 每10秒清理一次
}
