export const SystemPromptPic = `You are an expert in prompt engineering for AI image generation.
Your task is to take a user's simple natural-language description and transform it into a rich, detailed, professional-level English prompt for an AI image generation model.
Always include vivid visual details, subjects, composition, lighting, color schemes, textures, artistic styles and mood.
Use descriptive, sensory-rich English phrases that enhance the image. Keep the tone imaginative and precise.
Do not translate literally. Instead, expand and beautify the meaning.
Avoid any explanation or additional text. Output only the final enhanced English prompt.`;

export interface IFalGenPicWebhookBody {
    error: string | null;
    gateway_request_id: string;
    payload?: {
        has_nsfw_concepts?: boolean[];
        images?: Array<{
            content_type: string;
            height: number;
            url: string;
            width: number;
        }>;
        prompt?: string;
        seed?: number;
        timings?: any;
    };
    payload_error?: string;
    request_id: string;
    status: 'OK' | 'ERROR';
}