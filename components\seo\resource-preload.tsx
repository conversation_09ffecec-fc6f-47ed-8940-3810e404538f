'use client'

import { useEffect } from 'react'
import Head from 'next/head'

// 关键资源预加载组件
export function ResourcePreload() {
  // 使用useEffect在客户端执行预加载
  useEffect(() => {
    // 预加载关键图片
    const preloadImages = () => {
      // 首屏关键图片
      const criticalImages = [
        '/images/og-image.jpg',
        '/images/twitter-image.jpg',
        // 添加其他关键图片
      ]

      // 使用requestIdleCallback在浏览器空闲时预加载图片
      if ('requestIdleCallback' in window) {
        // @ts-ignore - TypeScript可能不认识requestIdleCallback
        window.requestIdleCallback(() => {
          criticalImages.forEach(src => {
            const img = new Image()
            img.src = src
          })
        }, { timeout: 2000 })
      } else {
        // 回退到setTimeout
        setTimeout(() => {
          criticalImages.forEach(src => {
            const img = new Image()
            img.src = src
          })
        }, 200)
      }
    }

    // 预加载关键脚本
    const preloadScripts = () => {
      // 延迟加载非关键脚本
      const nonCriticalScripts: any[] = [
        // 添加需要延迟加载的脚本
      ]

      if ('requestIdleCallback' in window) {
        // @ts-ignore
        window.requestIdleCallback(() => {
          nonCriticalScripts.forEach(src => {
            const script = document.createElement('script')
            script.src = src
            script.async = true
            document.body.appendChild(script)
          })
        }, { timeout: 3000 })
      } else {
        setTimeout(() => {
          nonCriticalScripts.forEach(src => {
            const script = document.createElement('script')
            script.src = src
            script.async = true
            document.body.appendChild(script)
          })
        }, 300)
      }
    }

    // 执行预加载
    preloadImages()
    preloadScripts()
  }, [])

  return (
    <Head>
      {/* 预加载关键CSS */}
      <link
        rel="preload"
        href="/styles/feature-cards.css"
        as="style"
        fetchPriority="high"
      />

      {/* 预加载关键字体 */}
      <link
        rel="preload"
        href="/fonts/inter-var.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      />

      {/* DNS预解析 */}
      <link rel="dns-prefetch" href="https://nestapi.reelmind.ai" />

      {/* 预连接 */}
      <link rel="preconnect" href="https://nestapi.reelmind.ai" crossOrigin="anonymous" />
    </Head>
  )
}
