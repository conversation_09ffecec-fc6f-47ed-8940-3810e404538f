"use client"

import React, { useState, useEffect, ReactNode } from 'react';

interface ResponsiveLayoutProps {
  mobileView: ReactNode;
  desktopView: ReactNode;
}

export default function ResponsiveLayout({ mobileView, desktopView }: ResponsiveLayoutProps) {
  const [screenWidth, setScreenWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 1024);
  const isMobile = screenWidth < 768;
  
  // Monitor screen width changes
  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };
    
    // Set initial width
    handleResize();
    
    // Add event listener for window resize
    window.addEventListener('resize', handleResize);
    
    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return (
    <>
      {isMobile ? (
        <div className="block h-full">{mobileView}</div>
      ) : (
        <div className="h-full">{desktopView}</div>
      )}
    </>
  );
} 