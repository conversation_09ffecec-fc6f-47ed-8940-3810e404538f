import { Module, forwardRef } from '@nestjs/common';
import { MembershipController } from './membership.controller';
import { MembershipService } from './membership.service';
import { CustomLogger } from '../common/services/logger.service';
import { MembershipPlanService } from './membership-plan.service';
import { MembershipInterestService } from './membership-interest.service';
import { MembershipInterestController } from './membership-interest.controller';
import { PaymentModule } from '../payment/payment.module';
import { CreditsModule } from '../credits/credits.module';
import { EnvironmentConfig } from '../common/config/environment.config';

@Module({
    imports: [
        forwardRef(() => PaymentModule),
        forwardRef(() => CreditsModule),
    ],
    controllers: [
        MembershipController,
        MembershipInterestController
    ],
    providers: [
        MembershipService,
        MembershipPlanService,
        MembershipInterestService,
        CustomLogger,
        EnvironmentConfig
    ],
    exports: [
        MembershipService,
        MembershipPlanService,
        MembershipInterestService
    ],
})
export class MembershipModule {}