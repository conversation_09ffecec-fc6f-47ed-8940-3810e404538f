import { IsNotEmpty, IsString, IsUUID, IsOptional, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 帖子统计事件DTO
 */
export class PostStatsEventDto {
  @ApiProperty({
    description: '帖子ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  post_id: string;

  @ApiProperty({
    description: '事件类型，view表示浏览，click表示点击',
    example: 'view',
    enum: ['view', 'click'],
  })
  @IsString()
  @IsNotEmpty()
  event_type: 'view' | 'click';

  @ApiProperty({
    description: '客户端信息，如设备类型、浏览器等',
    example: { userAgent: 'Mozilla/5.0...', device: 'desktop' },
    required: false,
  })
  @IsObject()
  @IsOptional()
  client_info?: Record<string, any>;
}

/**
 * 帖子统计响应DTO
 */
export class PostStatsResponseDto {
  @ApiProperty({
    description: '操作是否成功',
    example: true,
  })
  success: boolean;
}
