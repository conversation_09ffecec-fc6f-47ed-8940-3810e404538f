import { Injectable, Inject, Logger } from '@nestjs/common';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { SupabaseClient } from '@supabase/supabase-js';

export interface DeviceFingerprintData {
    visitorId: string;
    confidence: {
        score: number;
    };
    components: any;
}

export interface AbuseCheckResult {
    allowed: boolean;
    reason?: string;
    riskFactors: string[];
    riskScore: number;
}

export interface ClaimValidationData {
    userId: string;
    fingerprint: DeviceFingerprintData;
    ipAddress: string;
    userAgent: string;
}

/**
 * 反滥用服务
 *
 * 设计原则：
 * 1. 主要依赖数据库约束确保可靠性
 * 2. 使用存储过程保证原子性操作
 * 3. 最小化内存缓存，避免多实例不一致
 * 4. 支持服务重启后数据持久化
 */
@Injectable()
export class AntiAbuseService {
    private readonly logger = new Logger(AntiAbuseService.name);

    // 配置参数
    private readonly MAX_CLAIMS_PER_FINGERPRINT = 2; // 每个设备指纹最多2次
    private readonly MAX_CLAIMS_PER_IP = 3; // 每个IP最多3次
    private readonly MAX_CLAIMS_PER_USER_AGENT = 2; // 每个UserAgent最多2次（加强限制）
    private readonly MAX_CLAIMS_PER_IP_SUBNET = 5; // 每个IP子网最多5次
    private readonly MIN_FINGERPRINT_CONFIDENCE = 0.6; // 最低指纹置信度
    private readonly FINGERPRINT_WINDOW_HOURS = 48; // 设备指纹限制时间窗口（小时）
    private readonly IP_WINDOW_HOURS = 48; // IP限制时间窗口（小时）
    private readonly USER_AGENT_WINDOW_HOURS = 72; // UserAgent限制时间窗口（小时）- 延长到72小时
    private readonly IP_SUBNET_WINDOW_HOURS = 72; // IP子网限制时间窗口（小时）


    // 高风险IP段（Cloudflare等CDN/代理服务）
    private readonly HIGH_RISK_IP_PREFIXES = [
        '162.158.', // Cloudflare
        '104.16.',  // Cloudflare
        '104.17.',  // Cloudflare
        '172.64.',  // Cloudflare
        '173.245.', // Cloudflare
        '103.21.',  // Cloudflare
        '103.22.',  // Cloudflare
        '103.31.',  // Cloudflare
        '141.101.', // Cloudflare
        '108.162.', // Cloudflare
        '190.93.',  // Cloudflare
        '188.114.', // Cloudflare
        '197.234.', // Cloudflare
        '198.41.',  // Cloudflare
        '104.24.',  // Cloudflare
        '131.0.',  // Cloudflare
    ];

    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
    ) {}

    /**
     * 积分领取验证
     * 基于数据库的可靠性检查
     */
    async validateCreditClaim(data: ClaimValidationData): Promise<AbuseCheckResult> {
        const { userId, fingerprint, ipAddress, userAgent } = data;
        const riskFactors: string[] = [];
        let riskScore = 0;

        try {
            // 1. 机器人检测（快速检查，无需数据库查询）
            if (this.isBot(userAgent)) {
                return {
                    allowed: false,
                    reason: 'Forbidden',
                    riskFactors: ['bot_user_agent'],
                    riskScore: 100
                };
            }

            // 2. 并行执行所有数据库查询
            const [existingClaimResult, fingerprintCount, ipCount, userAgentCount, ipSubnetCount] = await Promise.all([
                this.supabase
                    .from('credit_transactions')
                    .select('id')
                    .eq('user_id', userId)
                    .eq('type', 'new_user_bonus')
                    .eq('status', 'completed')
                    .limit(1),
                this.getDeviceFingerprintUsageCount(fingerprint.visitorId),
                this.getIPUsageCount(ipAddress),
                this.getUserAgentUsageCount(userAgent),
                this.getIPSubnetUsageCount(ipAddress)
            ]);

            // 3. 检查用户是否已经领取过
            const { data: existingClaim, error: claimError } = existingClaimResult;
            if (claimError) {
                this.logger.error(`查询用户领取记录失败: ${claimError.message}`, claimError);
                return {
                    allowed: false,
                    reason: 'Error occurred while processing your request. Please try again later.',
                    riskFactors: ['database_error'],
                    riskScore: 100
                };
            }

            if (existingClaim && existingClaim.length > 0) {
                return {
                    allowed: false,
                    reason: 'Cannot claim bonus more than once',
                    riskFactors: ['duplicate_user_claim'],
                    riskScore: 100
                };
            }

            // // 5. 检查高风险IP段
            // if (this.isHighRiskIP(ipAddress)) {
            //     riskFactors.push('high_risk_ip_range');
            //     riskScore += 10;
            // }

            // 6. 检查设备指纹置信度
            if (fingerprint.confidence.score < this.MIN_FINGERPRINT_CONFIDENCE) {
                riskFactors.push('low_fingerprint_confidence');
                riskScore += 40;
            }

            // 7. 检查设备指纹、IP和UserAgent使用频率
            if (fingerprintCount >= this.MAX_CLAIMS_PER_FINGERPRINT) {
                riskFactors.push('high_device_usage');
                riskScore += 40;
            }

            if (ipCount >= this.MAX_CLAIMS_PER_IP) {
                riskFactors.push('high_ip_usage');
                riskScore += 50;
            }

            if (userAgentCount >= this.MAX_CLAIMS_PER_USER_AGENT) {
                riskFactors.push('high_user_agent_usage');
                riskScore += 40;
            }

            if (ipSubnetCount >= this.MAX_CLAIMS_PER_IP_SUBNET) {
                riskFactors.push('high_ip_subnet_usage');
                riskScore += 50;
            }

            // 8. 综合评估 - 降低阈值，更严格
            const allowed = riskScore < 60;
            const reason = allowed ? undefined : 'You are not eligible to claim bonus at the moment.';

            // 9. 异步记录检查日志（不阻塞主流程）
            this.logAbuseCheck(data, allowed ? 'validation_passed' : 'validation_failed', riskScore, riskFactors);

            return {
                allowed,
                reason,
                riskFactors,
                riskScore
            };

        } catch (error) {
            this.logger.error(`风控检查失败: ${error.message}`, error.stack);
            return {
                allowed: false,
                reason: 'Cannot process your request at the moment. Please try again later.',
                riskFactors: ['system_error'],
                riskScore: 100
            };
        }
    }

    /**
     * 记录成功的积分领取
     * 记录到数据库日志表
     */
    async recordSuccessfulClaim(data: ClaimValidationData): Promise<void> {
        try {
            // 记录成功领取日志
            await this.logAbuseCheck(data, 'claim_success', 0, []);
        } catch (error) {
            this.logger.error(`记录积分领取失败: ${error.message}`, error.stack);
        }
    }

    /**
     * 获取设备指纹使用次数（查询数据库）
     */
    private async getDeviceFingerprintUsageCount(fingerprintId: string): Promise<number> {
        try {
            const windowStart = new Date(Date.now() - this.FINGERPRINT_WINDOW_HOURS * 60 * 60 * 1000).toISOString();

            const { data, error } = await this.supabase
                .from('abuse_logs')
                .select('id', { count: 'exact' })
                .eq('fingerprint_id', fingerprintId)
                .eq('action', 'claim_success')
                .gte('created_at', windowStart);

            if (error) {
                this.logger.error(`查询设备指纹使用次数失败: ${error.message}`, error);
                return 0; // 查询失败时返回0，避免误杀正常用户
            }

            return data?.length || 0;
        } catch (error) {
            this.logger.error(`查询设备指纹使用次数异常: ${error.message}`, error.stack);
            return 0;
        }
    }

    /**
     * 获取IP使用次数（查询数据库）
     */
    private async getIPUsageCount(ipAddress: string): Promise<number> {
        try {
            const windowStart = new Date(Date.now() - this.IP_WINDOW_HOURS * 60 * 60 * 1000).toISOString();

            const { data, error } = await this.supabase
                .from('abuse_logs')
                .select('id', { count: 'exact' })
                .eq('ip_address', ipAddress)
                .eq('action', 'claim_success')
                .gte('created_at', windowStart);

            if (error) {
                this.logger.error(`查询IP使用次数失败: ${error.message}`, error);
                return 0; // 查询失败时返回0，避免误杀正常用户
            }

            return data?.length || 0;
        } catch (error) {
            this.logger.error(`查询IP使用次数异常: ${error.message}`, error.stack);
            return 0;
        }
    }

    /**
     * 获取UserAgent使用次数（查询数据库）
     */
    private async getUserAgentUsageCount(userAgent: string): Promise<number> {
        try {
            const windowStart = new Date(Date.now() - this.USER_AGENT_WINDOW_HOURS * 60 * 60 * 1000).toISOString();

            const { data, error } = await this.supabase
                .from('abuse_logs')
                .select('id', { count: 'exact' })
                .eq('user_agent', userAgent)
                .eq('action', 'claim_success')
                .gte('created_at', windowStart);

            if (error) {
                this.logger.error(`查询UserAgent使用次数失败: ${error.message}`, error);
                return 0; // 查询失败时返回0，避免误杀正常用户
            }

            return data?.length || 0;
        } catch (error) {
            this.logger.error(`查询UserAgent使用次数异常: ${error.message}`, error.stack);
            return 0;
        }
    }

    /**
     * 获取IP子网使用次数（查询数据库）
     * 检查同一IP段（前三个八位组）的使用情况
     */
    private async getIPSubnetUsageCount(ipAddress: string): Promise<number> {
        try {
            // 提取IP的前三个八位组作为子网
            const ipParts = ipAddress.split('.');
            if (ipParts.length !== 4) {
                return 0; // 无效IP格式
            }

            const subnet = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}.`;
            const windowStart = new Date(Date.now() - this.IP_SUBNET_WINDOW_HOURS * 60 * 60 * 1000).toISOString();

            // 使用LIKE查询匹配子网
            const { data, error } = await this.supabase
                .from('abuse_logs')
                .select('id', { count: 'exact' })
                .like('ip_address', `${subnet}%`)
                .eq('action', 'claim_success')
                .gte('created_at', windowStart);

            if (error) {
                this.logger.error(`查询IP子网使用次数失败: ${error.message}`, error);
                return 0;
            }

            return data?.length || 0;
        } catch (error) {
            this.logger.error(`查询IP子网使用次数异常: ${error.message}`, error.stack);
            return 0;
        }
    }

    /**
     * 简单的机器人检测
     */
    private isBot(userAgent: string): boolean {
        if (!userAgent || userAgent.length < 10) {
            return true;
        }

        const botPatterns = [
            'bot', 'crawler', 'spider', 'scraping', 'http client', 'scraper',
            'HeadlessChrome', 'PhantomJS', 'puppeteer', 'axios', 'curl',
            'wget', 'Go-http-client', 'okhttp', 'Python-urllib', 'Java/',
            'facebookexternalhit', 'Googlebot', 'Bingbot', 'YandexBot', 'Baiduspider'
        ];
        const lowerUA = userAgent.toLowerCase();

        return botPatterns.some(pattern => lowerUA.includes(pattern));
    }

    /**
     * 记录滥用检查日志
     */
    private async logAbuseCheck(
        data: ClaimValidationData,
        action: string,
        riskScore: number,
        riskFactors: string[]
    ): Promise<void> {
        try {
            const { error } = await this.supabase
                .from('abuse_logs')
                .insert({
                    user_id: data.userId,
                    fingerprint_id: data.fingerprint.visitorId,
                    ip_address: data.ipAddress,
                    user_agent: data.userAgent,
                    action,
                    risk_score: riskScore,
                    risk_factors: riskFactors.length > 0 ? riskFactors : null
                });

            if (error) {
                this.logger.error(`记录滥用检查日志失败: ${error.message}`, error);
            }
        } catch (error) {
            this.logger.error(`记录滥用检查日志异常: ${error.message}`, error.stack);
        }
    }

    /**
     * 获取用户领取状态
     * 直接查询数据库，确保准确性
     */
    async getUserClaimStatus(userId: string): Promise<{ canClaim: boolean; reason?: string }> {
        try {
            const { data, error } = await this.supabase
                .from('credit_transactions')
                .select('id')
                .eq('user_id', userId)
                .eq('type', 'new_user_bonus')
                .eq('status', 'completed')
                .limit(1);

            if (error) {
                this.logger.error(`查询用户领取状态失败: ${error.message}`, error);
                return {
                    canClaim: false,
                    reason: '系统繁忙'
                };
            }

            if (data && data.length > 0) {
                return {
                    canClaim: false,
                    reason: '无法重复领取'
                };
            }

            return {
                canClaim: true
            };
        } catch (error) {
            this.logger.error(`查询用户领取状态异常: ${error.message}`, error.stack);
            return {
                canClaim: false,
                reason: '系统繁忙'
            };
        }
    }
}
