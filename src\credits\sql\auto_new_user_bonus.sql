-- 为新用户自动发放注册奖励积分的触发器函数

-- 首先删除现有触发器和函数(如果存在)
DROP TRIGGER IF EXISTS trg_new_user_bonus ON auth.users;
DROP FUNCTION IF EXISTS public.grant_new_user_bonus();

-- 创建改进版的函数
CREATE OR REPLACE FUNCTION public.grant_new_user_bonus()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- 增强安全性
AS $$
DECLARE
    v_bonus_amount INT := 88; -- 新用户奖励积分数量
    v_bonus_type VARCHAR := 'new_user_bonus'; -- 奖励类型
    v_description TEXT := '新用户注册奖励积分';
    v_transaction_id UUID;
    v_log_id UUID;
BEGIN
    -- 记录开始执行日志
    INSERT INTO public.error_audit_logs (
        user_id,
        category,
        severity,
        error_message,
        context_data,
        created_at,
        resolved
    ) VALUES (
        NEW.id,
        'new_user_bonus',
        'info',
        '开始执行新用户积分奖励触发器',
        jsonb_build_object(
            'user_id', NEW.id,
            'email', NEW.email,
            'trigger_op', TG_OP
        ),
        clock_timestamp(),  -- 使用clock_timestamp确保准确的时间记录
        TRUE
    ) RETURNING id INTO v_log_id;

    -- 确保是新创建的用户
    IF TG_OP <> 'INSERT' THEN
        RETURN NEW;
    END IF;

    -- 检查是否已经为该用户发放过奖励 - 避免重复发放
    IF EXISTS (
        SELECT 1 
        FROM public.credit_transactions 
        WHERE user_id = NEW.id 
        AND type = v_bonus_type
        LIMIT 1
    ) THEN
        -- 记录跳过原因
        UPDATE public.error_audit_logs 
        SET error_message = '用户已有奖励记录，跳过处理'
        WHERE id = v_log_id;
        
        RETURN NEW;
    END IF;
    
    -- 直接处理，不调用add_credits函数以避免潜在问题
    BEGIN
        -- 1. 创建积分交易记录
        INSERT INTO public.credit_transactions (
            user_id,
            type,
            amount,
            description,
            status,
            created_at
        ) VALUES (
            NEW.id,
            v_bonus_type,
            v_bonus_amount,
            v_description || ' ' || v_bonus_amount || '积分',
            'completed',
            clock_timestamp()
        )
        RETURNING id INTO v_transaction_id;
        
        -- 2. 为用户添加积分余额记录 - 使用直接插入+更新模式而非UPSERT
        IF EXISTS (SELECT 1 FROM public.user_credit_balances WHERE user_id = NEW.id) THEN
            -- 更新现有记录
            UPDATE public.user_credit_balances
            SET 
                credits = credits + v_bonus_amount,
                last_transaction_id = v_transaction_id,
                updated_at = clock_timestamp()
            WHERE user_id = NEW.id;
        ELSE
            -- 创建新记录
            INSERT INTO public.user_credit_balances (
                user_id, 
                credits, 
                last_transaction_id, 
                updated_at
            ) VALUES (
                NEW.id, 
                v_bonus_amount, 
                v_transaction_id, 
                clock_timestamp()
            );
        END IF;
        
        -- 更新日志记录操作成功
        UPDATE public.error_audit_logs 
        SET 
            error_message = '新用户积分奖励发放成功',
            context_data = jsonb_build_object(
                'user_id', NEW.id,
                'bonus_amount', v_bonus_amount,
                'transaction_id', v_transaction_id,
                'timestamp', clock_timestamp()
            )
        WHERE id = v_log_id;
        
    EXCEPTION WHEN OTHERS THEN
        -- 捕获交易和余额更新中的错误
        UPDATE public.error_audit_logs 
        SET 
            severity = 'error',
            error_message = '发放新用户积分过程中出错',
            error_details = jsonb_build_object(
                'error', SQLERRM,
                'stack', PG_EXCEPTION_CONTEXT(),
                'state', PG_EXCEPTION_DETAIL
            ),
            resolved = FALSE
        WHERE id = v_log_id;
    END;
    
    RETURN NEW;
    
EXCEPTION WHEN OTHERS THEN
    -- 捕获所有异常，但不阻止用户创建
    BEGIN
        INSERT INTO public.error_audit_logs (
            user_id,
            category,
            severity,
            error_message,
            error_details,
            created_at,
            resolved
        ) VALUES (
            NEW.id,
            'new_user_bonus_error',
            'error',
            '新用户积分奖励触发器出现未捕获异常',
            jsonb_build_object(
                'error', SQLERRM,
                'stack', PG_EXCEPTION_CONTEXT(),
                'state', PG_EXCEPTION_DETAIL
            ),
            clock_timestamp(),
            FALSE
        );
    EXCEPTION WHEN OTHERS THEN
        -- 忽略日志错误
        NULL;
    END;
    
    RETURN NEW;
END;
$$;

-- 创建触发器
CREATE TRIGGER trg_new_user_bonus
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.grant_new_user_bonus(); 

-- 确保函数有权限(可能需要根据Supabase环境调整)
COMMENT ON FUNCTION public.grant_new_user_bonus() IS '为新注册用户自动发放88积分的奖励';