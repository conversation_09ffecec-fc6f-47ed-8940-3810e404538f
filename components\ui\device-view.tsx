'use client';

import React, { ReactNode } from 'react';
import { useDeviceContext } from '@/contexts/DeviceContext';

interface DeviceViewProps {
    children: ReactNode;
    fallback?: ReactNode;
}

/**
 * 仅在移动设备上渲染内容的组件
 */
export function MobileOnly({ children, fallback = null }: DeviceViewProps) {
    const { isMobile, isClient } = useDeviceContext();

    // 在客户端，根据设备类型渲染
    if (isClient) {
        return isMobile ? <>{children}</> : <>{fallback}</>;
    }

    // 服务端渲染时隐藏，避免水合不匹配
    return <div className="hidden sm:hidden">{children}</div>;
}

/**
 * 仅在平板设备上渲染内容的组件
 */
export function TabletOnly({ children, fallback = null }: DeviceViewProps) {
    const { isTablet, isClient } = useDeviceContext();

    if (isClient) {
        return isTablet ? <>{children}</> : <>{fallback}</>;
    }

    return <div className="hidden sm:block md:block lg:hidden">{children}</div>;
}

/**
 * 仅在桌面设备上渲染内容的组件
 */
export function DesktopOnly({ children, fallback = null }: DeviceViewProps) {
    const { isDesktop, isClient } = useDeviceContext();

    if (isClient) {
        return isDesktop ? <>{children}</> : <>{fallback}</>;
    }

    return <div className="hidden lg:block">{children}</div>;
}

/**
 * 在移动设备上不渲染内容的组件
 */
export function NotMobile({ children, fallback = null }: DeviceViewProps) {
    const { isMobile, isClient } = useDeviceContext();

    if (isClient) {
        return !isMobile ? <>{children}</> : <>{fallback}</>;
    }

    return <div className="hidden sm:block">{children}</div>;
}

/**
 * 根据设备类型选择性渲染内容的组件
 */
interface ResponsiveProps {
    mobile?: ReactNode;
    tablet?: ReactNode;
    desktop?: ReactNode;
    fallback?: ReactNode;
}

export function Responsive({ mobile, tablet, desktop, fallback = null }: ResponsiveProps) {
    const { device, isClient } = useDeviceContext();

    if (!isClient) {
        // 服务端渲染时提供一个 SSR 友好的回退方案
        return (
            <>
                {mobile && <div className="sm:hidden">{mobile}</div>}
                {tablet && <div className="hidden sm:block md:block lg:hidden">{tablet}</div>}
                {desktop && <div className="hidden lg:block">{desktop}</div>}
                {!mobile && !tablet && !desktop && fallback}
            </>
        );
    }

    switch (device) {
        case 'mobile':
            return <>{mobile || fallback}</>;
        case 'tablet':
            return <>{tablet || fallback}</>;
        case 'desktop':
            return <>{desktop || fallback}</>;
        default:
            return <>{fallback}</>;
    }
} 