"use client";

import Image from "next/image";
import { useThemeContext } from "@/contexts/theme-context";
import { ClientOnlyWithFallback } from "@/components/client-only";
import { cn } from "@/lib/utils";

interface LogoProps {
    className?: string;
    width?: number;
    height?: number;
}

export function Logo({ className, width = 160, height = 40 }: LogoProps) {
    const { theme = 'dark' } = useThemeContext();

    return (
        <div className={cn("flex items-center w-[var(--sidebar-width)]", className)}>
            <ClientOnlyWithFallback
                fallback={
                    <Image
                        src="/logo_dark.png"
                        alt="ReelMind Logo"
                        width={width}
                        height={height}
                        className="object-contain rounded-lg"
                    />
                }
            >
                <Image
                    src={theme === "dark" ? "/logo_dark.png" : "/logo_light.png"}
                    alt="ReelMind Logo"
                    width={width}
                    height={height}
                    className="object-contain rounded-lg"
                    key={theme}
                />
            </ClientOnlyWithFallback>
        </div>
    );
} 