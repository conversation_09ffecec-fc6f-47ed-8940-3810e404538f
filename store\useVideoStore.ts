import { create } from 'zustand';
import { PostItemDto } from '@/types/posts';
import { postApi } from '@/lib/api/post';
import { favoriteApi, FavoriteTargetType } from '@/lib/api/favorite';

interface VideoState {
    // 状态
    currentVideo: PostItemDto | null;
    isLoading: boolean;
    error: string | null;
    favorites: string[];
    isLoadingFavorites: boolean;

    // 操作
    fetchVideo: (id: string) => Promise<void>;
    toggleFavorite: (id: string) => Promise<void>;
    fetchFavorites: () => Promise<void>;
    clearVideo: () => void;
}

export const useVideoStore = create<VideoState>((set, get) => ({
    // 初始状态
    currentVideo: null,
    isLoading: false,
    error: null,
    favorites: [],
    isLoadingFavorites: false,

    // 获取视频详情
    fetchVideo: async (id: string) => {
        try {
            set({ isLoading: true, error: null });
            const { data } = await postApi.getPost(id);
            set({ currentVideo: data, isLoading: false });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to fetch video',
                isLoading: false
            });
        }
    },

    // 切换收藏状态
    toggleFavorite: async (id: string) => {
        const { favorites } = get();
        const isFavorite = favorites.includes(id);

        try {
            set({ isLoadingFavorites: true });

            if (isFavorite) {
                await favoriteApi.removeFavorite({
                    postId: id,
                    itemType: FavoriteTargetType.POST
                });
                set({
                    favorites: favorites.filter(favId => favId !== id),
                    isLoadingFavorites: false
                });
            } else {
                await favoriteApi.addFavorite({
                    postId: id,
                    itemType: FavoriteTargetType.POST
                });
                set({
                    favorites: [...favorites, id],
                    isLoadingFavorites: false
                });
            }
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update favorite status',
                isLoadingFavorites: false
            });
        }
    },

    // 获取收藏列表
    fetchFavorites: async () => {
        try {
            set({ isLoadingFavorites: true });
            const result = await favoriteApi.getFavorites({ item_type: 'post' });
            set({
                favorites: result.posts.map(post => post.postId),
                isLoadingFavorites: false
            });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to fetch favorites',
                isLoadingFavorites: false
            });
        }
    },

    // 清除当前视频
    clearVideo: () => {
        set({ currentVideo: null });
    }
})); 