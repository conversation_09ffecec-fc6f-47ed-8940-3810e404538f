/**
 * 处理视频下载的工具函数
 */
export async function downloadVideo(videoUrl: string, filename: string = 'reelmind-video.mp4'): Promise<boolean> {
    try {
        console.log('Starting video download for:', videoUrl);
        
        // 检查URL是否有效
        if (!videoUrl || videoUrl === '/placeholder.mp4') {
            console.error('Invalid video URL:', videoUrl);
            alert('Video not available for download');
            return false;
        }

        // 尝试直接下载（适用于同源或支持CORS的视频）
        try {
            const response = await fetch(videoUrl, {
                mode: 'cors',
                credentials: 'omit'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const blob = await response.blob();
            
            // 创建一个临时链接用于下载
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = filename;

            // 添加到DOM，触发下载，然后移除
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            console.log('Video download completed successfully');
            return true;
        } catch (fetchError) {
            console.warn('Direct fetch failed, trying alternative method:', fetchError);
            
            // 如果直接fetch失败（可能是CORS问题），尝试使用代理下载
            try {
                // 使用我们的API作为代理来下载视频
                const proxyResponse = await fetch('/api/download-video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ videoUrl, filename })
                });

                if (proxyResponse.ok) {
                    const blob = await proxyResponse.blob();
                    
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;

                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    console.log('Proxy video download completed successfully');
                    return true;
                } else {
                    throw new Error(`Proxy download failed: ${proxyResponse.status}`);
                }
            } catch (proxyError) {
                console.warn('Proxy download failed, trying direct link method:', proxyError);
                
                // 最后的备用方案：直接打开视频链接让用户手动保存
                const a = document.createElement('a');
                a.href = videoUrl;
                a.download = filename;
                a.target = '_blank';
                a.rel = 'noopener noreferrer';
                
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                
                console.log('Opened video in new tab for manual download');
                return true;
            }
        }
    } catch (error) {
        console.error('Failed to download video:', error);
        alert('Failed to download video. Please try again or right-click the video to save manually.');
        return false;
    }
} 