create table public.videos (
  user_id uuid null,
  created_at timestamp with time zone not null default now(),
  prompt text null,
  url text null,
  task_id uuid null,
  input_params jsonb null,
  source text null,
  id uuid not null default gen_random_uuid (),
  cover_img text null,
  constraint videos_pkey primary key (id),
  constraint videos_task_id_fkey foreign KEY (task_id) references video_gen_tasks (id),
  constraint videos_user_id_fkey foreign KEY (user_id) references user_profiles (user_id)
) TABLESPACE pg_default;