"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { userApi, type UserProfile, type UpdateProfileParams } from "@/lib/api/user";
import { useState } from "react";
import { useAuth } from "@/contexts/auth-context";

export const useProfile = () => {
    const { user } = useAuth();
    const queryClient = useQueryClient();
    const [error, setError] = useState<string | null>(null);

    // 获取用户资料查询
    const {
        data: profile,
        isLoading,
        isFetching,
        refetch
    } = useQuery<UserProfile>({
        queryKey: ["profile", user?.id],
        queryFn: async () => {
            try {
                setError(null);
                return await userApi.getProfile();
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : "Failed to fetch profile";
                setError(errorMessage);
                throw err;
            }
        },
        enabled: !!user,
    });

    // 更新用户资料mutation
    const { mutate: updateProfile, isPending: isUpdating } = useMutation({
        mutationFn: (data: UpdateProfileParams) => userApi.updateProfile(data),
        onSuccess: (updatedProfile) => {
            queryClient.setQueryData(["profile", user?.id], updatedProfile);
            setError(null);
        },
        onError: (err) => {
            const errorMessage = err instanceof Error ? err.message : "Failed to update profile";
            setError(errorMessage);
        },
    });

    return {
        profile,
        isLoading,
        isFetching,
        isUpdating,
        error,
        updateProfile,
        refetch,
    };
}; 