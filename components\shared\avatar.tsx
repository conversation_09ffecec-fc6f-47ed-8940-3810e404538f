"use client"

import { useAuth } from "@/contexts/auth-context"
import Image from "next/image"

interface AvatarProps {
    size?: number
    className?: string
}

export function Avatar({ size = 32, className = "" }: AvatarProps) {
    const { user } = useAuth();

    const src = user?.user_metadata?.avatar_url;
    const name = user?.user_metadata?.full_name;
    const email = user?.email;

    // 如果有头像，直接显示
    if (user?.user_metadata?.avatar_url) {
        return (
            <div
                className={`relative overflow-hidden rounded-full ${className}`}
                style={{ width: size, height: size }}
            >
                <Image
                    src={src}
                    alt={name || email}
                    fill
                    className="object-cover"
                />
            </div>
        )
    }

    // 获取邮箱前两个字符
    const initials = (name || email).substring(0, 2).toUpperCase()

    // 根据邮箱生成一个固定的背景色
    const colors = [
        "bg-red-500",
        "bg-yellow-500",
        "bg-green-500",
        "bg-blue-500",
        "bg-indigo-500",
        "bg-purple-500",
        "bg-pink-500"
    ]

    const colorIndex = email
        ? email.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length
        : 0

    return (
        <div
            className={`flex items-center justify-center rounded-full text-white font-medium ${colors[colorIndex]} ${className}`}
            style={{ width: size, height: size, fontSize: size * 0.4 }}
        >
            {initials}
        </div>
    )
} 