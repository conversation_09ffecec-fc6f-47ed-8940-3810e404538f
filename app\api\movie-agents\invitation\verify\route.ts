import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { code } = await request.json()

    if (!code) {
      return NextResponse.json({ error: 'Invitation code is required' }, { status: 400 })
    }

    // 获取当前用户
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // 检查用户是否已经有访问权限
    const { data: existingPermission } = await supabase
      .from('user_access_permissions')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (existingPermission?.has_movie_agent_access) {
      return NextResponse.json({ 
        success: true, 
        message: 'You already have access to Movie Agents',
        alreadyHasAccess: true 
      })
    }

    // 检查邀请码是否存在且有效
    const { data: invitationCode, error: codeError } = await supabase
      .from('invitation_codes')
      .select('*')
      .eq('code', code.toUpperCase())
      .single()

    if (codeError || !invitationCode) {
      return NextResponse.json({ error: 'Invalid invitation code' }, { status: 400 })
    }

    // 检查邀请码是否已达到使用上限
    if (invitationCode.current_uses >= invitationCode.max_uses) {
      return NextResponse.json({ error: 'This invitation code has already been used up' }, { status: 400 })
    }

    // 检查该用户是否已经使用过任何邀请码
    const { data: userPermissions } = await supabase
      .from('user_access_permissions')
      .select('invitation_code_used')
      .eq('user_id', user.id)
      .single()

    if (userPermissions?.invitation_code_used) {
      return NextResponse.json({ 
        error: 'You have already used an invitation code', 
        usedCode: userPermissions.invitation_code_used 
      }, { status: 400 })
    }

    // 开始事务：更新邀请码使用次数
    const newCurrentUses = invitationCode.current_uses + 1
    const shouldMarkAsUsed = newCurrentUses >= invitationCode.max_uses

    const { error: updateError } = await supabase
      .from('invitation_codes')
      .update({ 
        current_uses: newCurrentUses,
        is_used: shouldMarkAsUsed,
        ...(shouldMarkAsUsed ? { 
          used_by_user_id: user.id, 
          used_at: new Date().toISOString() 
        } : {})
      })
      .eq('id', invitationCode.id)

    if (updateError) {
      console.error('Error updating invitation code:', updateError)
      return NextResponse.json({ error: 'Failed to update invitation code' }, { status: 500 })
    }

    // 创建或更新用户访问权限
    const { error: permissionError } = await supabase
      .from('user_access_permissions')
      .upsert({
        user_id: user.id,
        has_movie_agent_access: true,
        invitation_code_used: code.toUpperCase(),
        granted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (permissionError) {
      console.error('Error granting permissions:', permissionError)
      return NextResponse.json({ error: 'Failed to grant access permissions' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Access granted successfully!',
      alreadyHasAccess: false
    })

  } catch (error) {
    console.error('Error verifying invitation code:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET() {
  try {
    const supabase = await createClient()

    // 获取当前用户
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // 检查用户是否有访问权限
    const { data: permission, error: permissionError } = await supabase
      .from('user_access_permissions')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (permissionError && permissionError.code !== 'PGRST116') {
      return NextResponse.json({ error: 'Failed to check permissions' }, { status: 500 })
    }

    return NextResponse.json({ 
      hasAccess: permission?.has_movie_agent_access || false,
      invitationCodeUsed: permission?.invitation_code_used || null,
      grantedAt: permission?.granted_at || null
    })

  } catch (error) {
    console.error('Error checking access permissions:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 