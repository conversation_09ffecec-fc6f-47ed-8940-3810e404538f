"use client";

import { useState } from "react";
import { CommentSection } from "./CommentSection";
import { postApi } from "@/lib/api/post";

interface CommentSectionWrapperProps {
    videoId: string;
    initialComments: any[];
}

/**
 * 评论区包装组件
 * 用于服务端组件中，在客户端注入交互功能
 */
export function CommentSectionWrapper({ videoId, initialComments }: CommentSectionWrapperProps) {
    // 在客户端维护状态
    const [comments, setComments] = useState(initialComments || []);
    const [commentContent, setCommentContent] = useState("");
    const [submittingComment, setSubmittingComment] = useState(false);
    const [loadingComments, setLoadingComments] = useState(false);

    // 客户端处理提交评论
    const submitComment = async (postId: string, content: string) => {
        if (!content.trim() || submittingComment) return;

        setSubmittingComment(true);
        try {
            const response = await postApi.createComment({
                content,
                targetType: 'post',
                targetId: postId
            });

            if (response) {
                // 更新评论列表
                const { data: updatedComments } = await postApi.getPostComments(postId);
                setComments(updatedComments.comments || []);
                setCommentContent("");
            }
        } catch (error) {
            console.error("Failed to submit comment:", error);
        } finally {
            setSubmittingComment(false);
        }
    };

    return (
        <CommentSection
            videoId={videoId}
            comments={comments}
            loadingComments={loadingComments}
            commentContent={commentContent}
            submittingComment={submittingComment}
            setCommentContent={setCommentContent}
            submitComment={submitComment}
        />
    );
} 