/**
 * 统一的文件上传工具函数
 * 整合项目中的各种上传逻辑，避免重复代码
 */

// 支持的存储桶类型
export type BucketName = 'gen-refer-img' | 'train-videos' | 'blog-img' | 'gen-img-task';

// 上传配置接口
export interface UploadConfig {
  bucketName?: BucketName;
  maxSize?: number; // 最大文件大小（字节）
  allowedTypes?: string[]; // 允许的文件类型
}

// 默认配置
const DEFAULT_CONFIG: Required<UploadConfig> = {
  bucketName: 'gen-img-task',
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
};

/**
 * 验证文件类型
 */
export function validateFileType(file: File, allowedTypes?: string[]): boolean {
  const types = allowedTypes || DEFAULT_CONFIG.allowedTypes;
  return types.some(type => {
    if (type.endsWith('/*')) {
      return file.type.startsWith(type.slice(0, -1));
    }
    return file.type === type;
  });
}

/**
 * 验证文件大小
 */
export function validateFileSize(file: File, maxSize?: number): boolean {
  const limit = maxSize || DEFAULT_CONFIG.maxSize;
  return file.size <= limit;
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 上传图片到 Cloudflare R2 存储
 * 这是项目中最常用的图片上传函数
 */
export async function uploadToCloudflare(
  file: File, 
  config: UploadConfig = {}
): Promise<string> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  // 验证文件类型
  if (!validateFileType(file, finalConfig.allowedTypes)) {
    throw new Error('Unsupported file type. Please select an image file.');
  }

  // 验证文件大小
  if (!validateFileSize(file, finalConfig.maxSize)) {
    throw new Error(`File size cannot exceed ${formatFileSize(finalConfig.maxSize)}`);
  }

  try {
    // 1. 获取预签名URL
    const response = await fetch('/api/upload/presign', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        filename: file.name,
        fileType: file.type,
        fileSize: file.size,
        bucketName: finalConfig.bucketName
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to get upload URL');
    }

    const { data } = await response.json();

    // 2. 使用预签名URL上传文件到Cloudflare R2
    const uploadResponse = await fetch(data.url, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    });

    if (!uploadResponse.ok) {
      throw new Error('Failed to upload file to cloud storage');
    }

    // 3. 返回公共URL
    return data.publicUrl;
  } catch (error) {
    console.error('Upload failed:', error);
    throw error instanceof Error ? error : new Error('Upload failed');
  }
}

/**
 * 上传图片到博客存储桶
 * 专门用于博客图片上传
 */
export async function uploadBlogImage(file: File): Promise<string> {
  return uploadToCloudflare(file, {
    bucketName: 'blog-img',
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/*']
  });
}

/**
 * 上传参考图片
 * 用于生成任务的参考图片
 */
export async function uploadReferenceImage(file: File): Promise<string> {
  return uploadToCloudflare(file, {
    bucketName: 'gen-refer-img',
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/*']
  });
}

/**
 * 上传生成任务图片
 * 用于 Lego 页面等生成任务
 */
export async function uploadGenerationImage(file: File): Promise<string> {
  return uploadToCloudflare(file, {
    bucketName: 'gen-img-task',
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/*']
  });
}

/**
 * 批量上传图片
 */
export async function uploadMultipleImages(
  files: File[], 
  config: UploadConfig = {}
): Promise<string[]> {
  const uploadPromises = files.map(file => uploadToCloudflare(file, config));
  return Promise.all(uploadPromises);
}

/**
 * 上传进度回调类型
 */
export type UploadProgressCallback = (progress: number) => void;

/**
 * 带进度的上传函数（简化版本）
 * 注意：由于使用预签名URL，无法获取真实的上传进度
 * 这里提供模拟进度用于UI反馈
 */
export async function uploadWithProgress(
  file: File,
  onProgress?: UploadProgressCallback,
  config: UploadConfig = {}
): Promise<string> {
  // 模拟进度更新
  const updateProgress = (progress: number) => {
    onProgress?.(progress);
  };

  try {
    updateProgress(10);
    
    // 验证文件
    const finalConfig = { ...DEFAULT_CONFIG, ...config };
    if (!validateFileType(file, finalConfig.allowedTypes)) {
      throw new Error('Unsupported file type');
    }
    if (!validateFileSize(file, finalConfig.maxSize)) {
      throw new Error(`File size cannot exceed ${formatFileSize(finalConfig.maxSize)}`);
    }
    
    updateProgress(30);
    
    // 获取预签名URL
    const response = await fetch('/api/upload/presign', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        filename: file.name,
        fileType: file.type,
        fileSize: file.size,
        bucketName: finalConfig.bucketName
      }),
    });
    
    updateProgress(50);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to get upload URL');
    }
    
    const { data } = await response.json();
    updateProgress(70);
    
    // 上传文件
    const uploadResponse = await fetch(data.url, {
      method: 'PUT',
      headers: { 'Content-Type': file.type },
      body: file,
    });
    
    updateProgress(90);
    
    if (!uploadResponse.ok) {
      throw new Error('Failed to upload file');
    }
    
    updateProgress(100);
    return data.publicUrl;
    
  } catch (error) {
    updateProgress(0);
    throw error;
  }
}
