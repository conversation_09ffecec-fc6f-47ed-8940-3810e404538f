# 平台介绍

ReelMind 是一个面向 AI 创作者的 AIGC 视频创作和分享平台
Reelmind, Open Source AI Video Models Community.

## 项目介绍

本项目是ReelMind的后端服务，采用Nest.js+supabase

## 技术选型

后端框架：Nest.js
包管理工具:pnpm
数据库服务:supabase
身份认证:supabase
oss存储：cloudflare

## 项目结构

```
src/
├── generation/                 # 视频生成模块
│   ├── dto/                    # 数据传输对象
│   │   ├── gen-video.dto.ts    # 视频生成请求DTO
│   │   └── video-task.dto.ts   # 视频任务DTO
│   ├── generation.controller.ts # 视频生成控制器
│   ├── generation.module.ts    # 视频生成模块定义
│   └── generation.service.ts   # 视频生成服务
├── common/                     # 通用模块
│   ├── guards/                 # 守卫
│   │   └── jwt.guard.ts        # JWT认证守卫
│   ├── services/               # 服务
│   │   └── logger.service.ts   # 日志服务
├── providers/                  # 提供者
│   ├── supabase.module.ts      # Supabase模块
│   └── supabase.provider.ts    # Supabase提供者
└── app.module.ts               # 应用主模块
```

## AIGC任务队列

因为生成视频的GPU资源有限，所以需要对来自全平台用户的视频生成需求进行队列化处理，按照FIFO的原则排队生成。

1. 用户在视频生成页面，发起一个视频生成请求/generation/gen-video；
2. Nest.js后端收到请求后，校验参数是否合规；
3. Nest.js生成一个VideoGeneration任务，并且写入supabase数据库的gen_video_task表中，状态为排队中（pending）；
4. Python视频生成服务调用/gen_video_task/pop接口，用于查询任务队列中接下来该处理的一条视频生成任务，开始执行，同时Nest.js负责将该任务的状态置为处理中（processing）；
5. Python视频生成服务调用/gen_video_task/finish接口，用于将完成的任务结果更新到gen_video_task中对应的项目中，将任务结果（storage_path）写入到数据库中，同时任务状态置为已完成(completed)；

### API构成

- 创建视频生成任务
- 获取用户任务列表
- 获取任务详情
- 取消任务
- 获取待处理任务（供Python服务调用）
- 完成任务（供Python服务调用）

### 参数约定

1. 生成视频的参数input_params:

   - gen_type: 生成类型，值为枚举类型，text2video或img2video
   - prompt: 创意描述，文本类型
   - negative_prompt: 不希望视频中出现的内容，文本类型
   - guidance_scale: 创意相关性，Number类型
   - steps: 迭代步数，Number类型
   - seed: 初始值，Number类型
   - definition: 清晰度，480P或720P
   - duration: 视频长度，5s或10s
   - ratio: 视频比例，9:16/16:9/1:1

2. storage_path

生成规则：storage_path="/gen_video_task/${task-id}.mp4"

## 用户模块

### 账户设置

1. 注销账户
2. NSFW开关

### 个人资料设置

1. 头像设置
2. 昵称设置
3. 个人链接，用户可以添加一些网页链接来补充介绍自己，例如自己的facebook、patreon页面等

### 用户的作品列表

1. 用户在平台上生成的视频作品
2. 用户从本地上传到平台的视频作品

### 用户的模型列表

1. 用户自己上传的视频生成模型
2. 用户在平台训练的视频生成模型

### 用户的收藏列表

1. 用户收藏的平台上的模型
2. 用户收藏的平台上的视频

## 灵感社区模块

1. 用户发布本地视频到社区feeds流
2. 用户将平台上自己生成的视频发布到社区feeds流

## 支付模块

1. 对接stripe支付体系
2. 用户可以购买积分
3. 用户可以购买会员

## 积分模块

### 积分购买

用户通过支付模块，实现从平台购买积分

### 积分扣费

用户在平台执行下列操作时，需要扣费

1. 生成视频
2. 训练模型

### 查询用户的积分余额

### 查询用户积分使用明细

## 视频模型模块

1. 查询用户上传的视频模型列表
2. 查询某个视频模型的详情
3. 上传视频模型
4. 下载视频模型
5. 删除视频模型

## 搜索模块

1. 根据关键字搜索全平台公开的视频和视频模型

### NSFW分级设置

```ts
export enum NsfwLevel {
  PG = 1,
  PG13 = 2,
  R = 4,
  X = 8,
  XXX = 16,
  Blocked = 32,
}
```

## API 文档

详细API文档请查看 [api.md](api.md)

stripe listen --forward-to http://localhost:8080/payment/webhooks/stripe
